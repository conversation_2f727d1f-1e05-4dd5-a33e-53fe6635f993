# 默认配置文件
# 本文件由AI辅助生成 - Generated with AI Assistance
# 生成时间: 2025-01-27 CST
# AI模型: Claude-4-Sonnet
# 审核状态: 待人工安全审查
# 编码标准: UTF-8无BOM

# 应用程序基本配置
app:
  name: "YOLO OpenCV 屏幕识别工具"
  version: "1.0.0"
  debug: false
  log_level: "INFO"

# YOLO检测器配置
yolo:
  model_path: "models/yolov8n.pt"
  confidence_threshold: 0.5
  nms_threshold: 0.4
  input_size: [640, 640]
  device: "auto"  # auto, cpu, cuda:0
  max_detections: 100

# 模板匹配配置
template_matching:
  method: "cv2.TM_CCOEFF_NORMED"
  threshold: 0.8
  scale_range: [0.5, 2.0]
  scale_steps: 10
  angle_range: [-15, 15]
  angle_steps: 7
  enable_preprocessing: true

# 屏幕截图配置
screen_capture:
  format: "RGB"
  quality: 95
  cache_size: 10
  enable_multi_monitor: true
  capture_cursor: false

# 结果融合配置
fusion:
  iou_threshold: 0.5
  confidence_weight: 0.7
  template_weight: 0.3
  enable_nms: true
  max_results: 50

# GUI配置
gui:
  window_size: [1200, 800]
  theme: "default"
  language: "zh_CN"
  auto_save_settings: true
  show_confidence: true
  show_class_names: true

# 脚本生成配置
script_generation:
  default_language: "python"
  include_comments: true
  add_error_handling: true
  click_delay: 0.1
  drag_duration: 0.5

# 性能监控配置
performance:
  enable_monitoring: true
  log_interval: 5.0
  memory_threshold: 200  # MB
  cpu_threshold: 80      # %
  gpu_threshold: 90      # %

# 数据库配置
database:
  path: "data/templates.db"
  backup_interval: 3600  # seconds
  max_backup_files: 5

# 日志配置
logging:
  level: "INFO"
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
  rotation: "10 MB"
  retention: "7 days"
  file_path: "logs/app.log"
