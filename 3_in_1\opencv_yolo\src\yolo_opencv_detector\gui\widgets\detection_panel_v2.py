# -*- coding: utf-8 -*-
"""
重构的检测面板 - 简化布局避免重叠
作者: Cursor AI
创建时间: 2025-07-05
编码标准: UTF-8无BOM
"""

from typing import Dict, Any
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, QLabel,
    QPushButton, QComboBox, QCheckBox, QSlider, QDoubleSpinBox
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QFont

from ...utils.logger import Logger
from ...utils.config_manager import ConfigManager
from ...core.smart_detection_manager import SmartDetectionManager
from .floating_detection_widget import FloatingDetectionWidget
from .screen_overlay_widget import get_overlay_manager
from ...core.screen_capture_v2 import ScreenCaptureServiceV2
from ...core.yolo_detector_v2 import YOLODetectorV2


class DetectionPanelV2(QWidget):
    """重构的检测面板类 - 简化布局"""
    
    # 信号定义
    detection_requested = pyqtSignal(dict)
    screenshot_requested = pyqtSignal(dict)
    
    def __init__(self, config_manager: ConfigManager):
        super().__init__()
        self.config_manager = config_manager
        self.logger = Logger()

        # 设置固定尺寸避免布局问题
        self.setMinimumWidth(260)
        self.setMaximumWidth(320)

        # 初始化检测服务
        self.screen_capture = None
        self.yolo_detector = None
        self.detection_timer = QTimer()
        self.detection_timer.timeout.connect(self._perform_detection)
        self.is_detecting = False

        # 性能统计
        self.detection_count = 0
        self.current_fps = 0.0

        # 悬浮模式组件
        self.floating_widget = None
        self.smart_detection_manager = SmartDetectionManager()
        self.overlay_manager = get_overlay_manager()
        self.is_floating_mode = False
        self.main_window_ref = None

        self._init_ui()
        self._load_config()
        self._init_detection_services()

        self.logger.info("重构检测面板初始化完成")
    
    def _init_ui(self) -> None:
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("🎯 实时检测")
        title_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # 控制组
        self._create_control_group(layout)
        
        # 设置组
        self._create_settings_group(layout)
        
        # 状态组
        self._create_status_group(layout)
        
        # 添加弹性空间
        layout.addStretch()
    
    def _create_control_group(self, parent_layout: QVBoxLayout) -> None:
        """创建控制组"""
        group = QGroupBox("🎮 检测控制")
        group.setMaximumHeight(160)  # 增加高度给更多空间
        layout = QVBoxLayout(group)
        layout.setSpacing(12)  # 增加间距
        layout.setContentsMargins(15, 15, 15, 15)  # 增加内边距

        # 第一行：开始/停止按钮
        button_row1 = QHBoxLayout()
        button_row1.setSpacing(10)  # 按钮间距

        # 开始检测按钮
        self.start_button = QPushButton("▶️ 开始检测")
        self.start_button.setMinimumHeight(40)  # 增加按钮高度
        self.start_button.setToolTip("开始目标检测\n"
                                    "根据当前配置进行YOLO或模板匹配检测\n"
                                    "支持自动模式和手动模式")
        self.start_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                font-size: 13px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
            QPushButton:disabled {
                background-color: #95a5a6;
            }
        """)
        self.start_button.clicked.connect(self._start_detection)
        button_row1.addWidget(self.start_button)

        # 停止检测按钮
        self.stop_button = QPushButton("⏹️ 停止检测")
        self.stop_button.setMinimumHeight(40)  # 增加按钮高度
        self.stop_button.setEnabled(False)
        self.stop_button.setToolTip("停止当前检测\n"
                                   "结束检测进程并返回就绪状态\n"
                                   "保存已检测的结果")
        self.stop_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                font-size: 13px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:disabled {
                background-color: #95a5a6;
            }
        """)
        self.stop_button.clicked.connect(self._stop_detection)
        button_row1.addWidget(self.stop_button)

        layout.addLayout(button_row1)

        # 第二行：检测截图按钮 - 专门用于检测相关的截图
        self.screenshot_button = QPushButton("📷 检测截图")
        self.screenshot_button.setMinimumHeight(40)  # 增加按钮高度
        self.screenshot_button.setToolTip(
            "📷 检测专用截图工具\n\n"
            "🎯 专门功能：\n"
            "• 🔍 目标检测：截图后自动进行YOLO检测分析\n"
            "• 📊 结果显示：检测结果直接显示在中央面板\n"
            "• ✂️ 区域分析：支持选择特定区域进行精确检测\n"
            "• 🏷️ 模板创建：从检测目标创建匹配模板\n"
            "• 📈 性能优化：针对检测任务优化的截图流程\n\n"
            "💡 最佳场景：\n"
            "• 需要快速检测屏幕上的目标对象\n"
            "• 实时分析游戏界面或应用程序\n"
            "• 验证检测模型的准确性和效果\n"
            "• 创建基于检测结果的自动化脚本\n\n"
            "⚡ 特点：检测结果自动显示，无需手动操作"
        )
        self.screenshot_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                font-size: 13px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        self.screenshot_button.clicked.connect(self._take_detection_screenshot)
        layout.addWidget(self.screenshot_button)

        # 第三行：自动检测模式
        self.auto_detect_checkbox = QCheckBox("🔄 自动检测模式")
        self.auto_detect_checkbox.setMinimumHeight(25)  # 增加复选框高度
        self.auto_detect_checkbox.setToolTip("开启后按设定间隔自动进行检测\n"
                                            "关闭则需要手动点击检测按钮\n"
                                            "自动模式适合实时监控场景")
        self.auto_detect_checkbox.setStyleSheet("""
            QCheckBox {
                font-weight: bold;
                color: #34495e;
                font-size: 12px;
                padding: 5px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 3px;
                border: 2px solid #bdc3c7;
            }
            QCheckBox::indicator:checked {
                background-color: #3498db;
                border-color: #3498db;
            }
        """)
        layout.addWidget(self.auto_detect_checkbox)

        parent_layout.addWidget(group)
    
    def _create_settings_group(self, parent_layout: QVBoxLayout) -> None:
        """创建设置组"""
        group = QGroupBox("⚙️ 检测设置")
        group.setMaximumHeight(250)  # 进一步增加高度确保数字显示完整
        layout = QVBoxLayout(group)
        layout.setSpacing(10)  # 适中的间距
        layout.setContentsMargins(12, 12, 12, 12)  # 适中的内边距

        # 显示器选择
        monitor_layout = QHBoxLayout()
        monitor_layout.setSpacing(10)
        monitor_label = QLabel("显示器:")
        monitor_label.setMinimumWidth(60)  # 固定标签宽度
        monitor_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        monitor_label.setToolTip("选择要进行检测的显示器\n"
                                "多显示器环境下可指定检测范围\n"
                                "主显示器通常是默认选择")
        monitor_layout.addWidget(monitor_label)

        self.monitor_combo = QComboBox()
        self.monitor_combo.addItems(["主显示器", "显示器1", "显示器2"])
        self.monitor_combo.setMinimumHeight(30)  # 增加高度
        self.monitor_combo.setToolTip("选择检测目标显示器\n"
                                     "主显示器：系统主屏幕\n"
                                     "显示器1/2：扩展屏幕\n"
                                     "只检测选中显示器的内容")
        self.monitor_combo.setStyleSheet("""
            QComboBox {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                padding: 5px;
                background-color: white;
            }
            QComboBox:focus {
                border-color: #3498db;
            }
        """)
        monitor_layout.addWidget(self.monitor_combo)
        layout.addLayout(monitor_layout)

        # 置信度阈值
        conf_layout = QVBoxLayout()
        conf_layout.setSpacing(8)  # 增加间距

        conf_header = QHBoxLayout()
        conf_label = QLabel("置信度阈值:")
        conf_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 12px;")
        conf_header.addWidget(conf_label)
        conf_header.addStretch()
        self.confidence_label = QLabel("0.50")
        self.confidence_label.setMinimumWidth(50)  # 进一步增加最小宽度
        self.confidence_label.setMinimumHeight(28)  # 增加最小高度
        self.confidence_label.setToolTip("当前置信度阈值\n"
                                        "显示实时设置的数值\n"
                                        "影响检测结果的筛选")
        self.confidence_label.setStyleSheet("""
            QLabel {
                background-color: #3498db;
                color: white;
                padding: 8px 12px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
                text-align: center;
                qproperty-alignment: AlignCenter;
            }
        """)
        conf_header.addWidget(self.confidence_label)
        conf_layout.addLayout(conf_header)

        self.confidence_slider = QSlider(Qt.Orientation.Horizontal)
        self.confidence_slider.setRange(1, 100)
        self.confidence_slider.setValue(50)
        self.confidence_slider.setMinimumHeight(30)  # 增加滑块高度
        self.confidence_slider.setToolTip("调整检测置信度阈值，范围0.01-1.00\n"
                                         "左侧(低值)：检测更宽松，可能有误检\n"
                                         "右侧(高值)：检测更严格，可能漏检\n"
                                         "推荐范围：0.3-0.7，默认0.5")
        self.confidence_slider.valueChanged.connect(self._update_confidence_label)
        self.confidence_slider.setStyleSheet("""
            QSlider::groove:horizontal {
                border: 1px solid #bdc3c7;
                height: 10px;
                background: #ecf0f1;
                border-radius: 5px;
            }
            QSlider::handle:horizontal {
                background: #3498db;
                border: 2px solid #2980b9;
                width: 20px;
                margin: -6px 0;
                border-radius: 10px;
            }
            QSlider::handle:horizontal:hover {
                background: #2980b9;
            }
        """)
        conf_layout.addWidget(self.confidence_slider)
        layout.addLayout(conf_layout)

        # NMS阈值
        nms_layout = QVBoxLayout()
        nms_layout.setSpacing(8)  # 增加间距

        nms_header = QHBoxLayout()
        nms_label = QLabel("NMS阈值:")
        nms_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 12px;")
        nms_label.setToolTip("非极大值抑制阈值\n"
                            "用于去除重复的检测框\n"
                            "控制检测结果的去重程度")
        nms_header.addWidget(nms_label)
        nms_header.addStretch()
        self.nms_label = QLabel("0.40")
        self.nms_label.setMinimumWidth(50)  # 进一步增加最小宽度
        self.nms_label.setMinimumHeight(28)  # 增加最小高度
        self.nms_label.setToolTip("当前NMS阈值\n"
                                 "显示实时设置的数值\n"
                                 "影响重复检测框的过滤")
        self.nms_label.setStyleSheet("""
            QLabel {
                background-color: #e74c3c;
                color: white;
                padding: 8px 12px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
                text-align: center;
                qproperty-alignment: AlignCenter;
            }
        """)
        nms_header.addWidget(self.nms_label)
        nms_layout.addLayout(nms_header)

        self.nms_slider = QSlider(Qt.Orientation.Horizontal)
        self.nms_slider.setRange(1, 100)
        self.nms_slider.setValue(40)
        self.nms_slider.setMinimumHeight(30)  # 增加滑块高度
        self.nms_slider.setToolTip("调整NMS阈值，范围0.01-1.00\n"
                                  "左侧(低值)：去重更严格，可能合并相近目标\n"
                                  "右侧(高值)：去重更宽松，可能保留重复框\n"
                                  "推荐范围：0.3-0.6，默认0.4")
        self.nms_slider.valueChanged.connect(self._update_nms_label)
        self.nms_slider.setStyleSheet("""
            QSlider::groove:horizontal {
                border: 1px solid #bdc3c7;
                height: 10px;
                background: #ecf0f1;
                border-radius: 5px;
            }
            QSlider::handle:horizontal {
                background: #e74c3c;
                border: 2px solid #c0392b;
                width: 20px;
                margin: -6px 0;
                border-radius: 10px;
            }
            QSlider::handle:horizontal:hover {
                background: #c0392b;
            }
        """)
        nms_layout.addWidget(self.nms_slider)
        layout.addLayout(nms_layout)

        # 检测间隔
        interval_layout = QHBoxLayout()
        interval_layout.setSpacing(12)  # 增加间距
        interval_label = QLabel("检测间隔(秒):")
        interval_label.setMinimumWidth(80)  # 增加标签宽度
        interval_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 12px;")
        interval_label.setToolTip("自动检测模式下的时间间隔\n"
                                 "控制检测频率和系统负载\n"
                                 "单位：秒，范围0.1-10.0")
        interval_layout.addWidget(interval_label)

        self.interval_spinbox = QDoubleSpinBox()
        self.interval_spinbox.setRange(0.1, 10.0)
        self.interval_spinbox.setValue(1.0)
        self.interval_spinbox.setSingleStep(0.1)
        self.interval_spinbox.setMinimumHeight(32)  # 增加高度
        self.interval_spinbox.setToolTip("设置自动检测的时间间隔\n"
                                        "较小值：检测更频繁，占用更多资源\n"
                                        "较大值：检测较慢，节省系统资源\n"
                                        "推荐值：1.0-3.0秒，默认1.0秒")
        self.interval_spinbox.setStyleSheet("""
            QDoubleSpinBox {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 6px 8px;
                background-color: white;
                font-size: 12px;
                font-weight: bold;
            }
            QDoubleSpinBox:focus {
                border-color: #3498db;
                background-color: #f8f9fa;
            }
            QDoubleSpinBox:hover {
                border-color: #95a5a6;
            }
        """)
        interval_layout.addWidget(self.interval_spinbox)
        layout.addLayout(interval_layout)

        parent_layout.addWidget(group)
    
    def _create_status_group(self, parent_layout: QVBoxLayout) -> None:
        """创建状态组"""
        group = QGroupBox("📊 检测状态")
        group.setMaximumHeight(120)  # 增加高度
        layout = QVBoxLayout(group)
        layout.setSpacing(10)  # 增加间距
        layout.setContentsMargins(15, 15, 15, 15)  # 增加内边距

        # 状态标签
        self.status_label = QLabel("🟢 就绪")
        self.status_label.setMinimumHeight(35)  # 增加高度
        self.status_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #27ae60;
                padding: 10px 15px;
                background-color: #d5f4e6;
                border-radius: 8px;
                border: 2px solid #27ae60;
                text-align: center;
            }
        """)
        layout.addWidget(self.status_label)

        # 统计信息
        stats_layout = QHBoxLayout()
        stats_layout.setSpacing(15)  # 增加间距

        # FPS标签
        self.fps_label = QLabel("FPS: 0.0")
        self.fps_label.setMinimumHeight(25)
        self.fps_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                color: #3498db;
                padding: 5px 10px;
                background-color: #ebf3fd;
                border-radius: 5px;
                border: 1px solid #3498db;
                text-align: center;
            }
        """)
        stats_layout.addWidget(self.fps_label)

        # 检测数标签
        self.count_label = QLabel("检测数: 0")
        self.count_label.setMinimumHeight(25)
        self.count_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                color: #f39c12;
                padding: 5px 10px;
                background-color: #fef9e7;
                border-radius: 5px;
                border: 1px solid #f39c12;
                text-align: center;
            }
        """)
        stats_layout.addWidget(self.count_label)

        layout.addLayout(stats_layout)

        parent_layout.addWidget(group)
    
    def _load_config(self) -> None:
        """加载配置"""
        try:
            # 使用默认配置
            config = {
                'confidence_threshold': 0.5,
                'nms_threshold': 0.4,
                'detection_interval': 1.0,
                'auto_detect': False
            }

            # 设置控件值
            self.confidence_slider.setValue(int(config.get('confidence_threshold', 0.5) * 100))
            self.nms_slider.setValue(int(config.get('nms_threshold', 0.4) * 100))
            self.interval_spinbox.setValue(config.get('detection_interval', 1.0))
            self.auto_detect_checkbox.setChecked(config.get('auto_detect', False))

            # 更新标签
            self._update_confidence_label()
            self._update_nms_label()

        except Exception as e:
            self.logger.error(f"加载检测配置失败: {e}")
    
    def _update_confidence_label(self) -> None:
        """更新置信度标签"""
        value = self.confidence_slider.value() / 100.0
        self.confidence_label.setText(f"{value:.2f}")
    
    def _update_nms_label(self) -> None:
        """更新NMS标签"""
        value = self.nms_slider.value() / 100.0
        self.nms_label.setText(f"{value:.2f}")
    
    def _start_detection(self) -> None:
        """开始检测"""
        # 验证检测条件
        if not self._validate_detection_conditions():
            return

        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.status_label.setText("🔴 检测中...")
        self.status_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #e74c3c;
                padding: 10px 15px;
                background-color: #fadbd8;
                border-radius: 8px;
                border: 2px solid #e74c3c;
                text-align: center;
                qproperty-alignment: AlignCenter;
            }
        """)

        # 启动智能检测管理器
        self.smart_detection_manager.start_session()

        # 配置模板匹配模式
        self._configure_template_matching()

        # 初始化叠加层
        self.overlay_manager.initialize()

        # 启动悬浮模式
        self._start_floating_mode()

        # 发送检测请求
        config = self._get_current_config()
        self.detection_requested.emit(config)

        # 启动检测服务
        self._start_detection_service()

        self.logger.info("开始检测（悬浮模式）")

    def _validate_detection_conditions(self) -> bool:
        """验证检测条件"""
        try:
            from PyQt6.QtWidgets import QMessageBox

            # 检查是否有可用的检测方法
            has_yolo = self._check_yolo_availability()
            has_template = self._check_template_availability()

            if not has_yolo and not has_template:
                QMessageBox.warning(
                    self,
                    "检测条件不足",
                    "⚠️ 无法启动检测！\n\n"
                    "原因：\n"
                    "• YOLO模型未加载或不可用\n"
                    "• 没有选择有效的模板\n\n"
                    "解决方案：\n"
                    "1. 在配置面板中加载YOLO模型\n"
                    "2. 或在模板面板中选择一个模板\n"
                    "3. 确保模板图像文件存在且可读取"
                )
                return False

            # 如果只有模板匹配可用，检查模板有效性
            if not has_yolo and has_template:
                selected_template = self._get_selected_template()
                if not selected_template or selected_template.get('image') is None:
                    QMessageBox.warning(
                        self,
                        "模板无效",
                        "⚠️ 选择的模板无效！\n\n"
                        "问题：\n"
                        "• 模板图像文件缺失或损坏\n"
                        "• 模板数据不完整\n\n"
                        "解决方案：\n"
                        "1. 重新选择其他模板\n"
                        "2. 或重新创建模板\n"
                        "3. 检查模板文件是否存在"
                    )
                    return False

                # 显示模板匹配模式提示
                reply = QMessageBox.question(
                    self,
                    "模板匹配模式",
                    f"🎯 将使用模板匹配模式\n\n"
                    f"选择的模板：{selected_template.get('name', '未知')}\n"
                    f"匹配阈值：{selected_template.get('threshold', 0.8):.2f}\n\n"
                    f"是否继续？",
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
                )
                if reply != QMessageBox.StandardButton.Yes:
                    return False

            return True

        except Exception as e:
            self.logger.error(f"验证检测条件失败: {e}")
            return False

    def _check_yolo_availability(self) -> bool:
        """检查YOLO是否可用"""
        try:
            # 这里可以添加YOLO模型检查逻辑
            # 暂时返回True，实际应该检查模型是否加载
            return True
        except Exception:
            return False

    def _check_template_availability(self) -> bool:
        """检查模板是否可用"""
        try:
            selected_template = self._get_selected_template()
            return selected_template is not None and selected_template.get('image') is not None
        except Exception:
            return False

    def _stop_detection(self) -> None:
        """停止检测"""
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.status_label.setText("🟢 就绪")
        self.status_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #27ae60;
                padding: 10px 15px;
                background-color: #d5f4e6;
                border-radius: 8px;
                border: 2px solid #27ae60;
                text-align: center;
                qproperty-alignment: AlignCenter;
            }
        """)

        # 停止悬浮模式
        self._stop_floating_mode()

        # 结束智能检测会话
        session = self.smart_detection_manager.end_session()
        if session:
            self.logger.info(f"检测会话结束 - 持续时间: {session.duration:.1f}秒, "
                           f"保存截图: {session.saved_screenshots}张")

        # 停止检测服务
        self._stop_detection_service()

        self.logger.info("停止检测")
    
    def _take_detection_screenshot(self) -> None:
        """检测截图 - 专门用于目标检测的截图功能"""
        try:
            # 使用统一的截图服务
            from .screenshot_service import get_screenshot_service, ScreenshotContext
            screenshot_service = get_screenshot_service()

            # 连接截图完成信号
            screenshot_service.screenshot_completed.connect(self._on_detection_screenshot_completed)
            screenshot_service.screenshot_failed.connect(self._on_detection_screenshot_failed)

            # 定义状态和按钮更新回调
            def update_status(text: str):
                self.status_label.setText(text)

            def update_button(enabled: bool, text: str):
                self.screenshot_button.setEnabled(enabled)
                self.screenshot_button.setText(text)

            # 执行截图
            success = screenshot_service.take_screenshot(
                context=ScreenshotContext.DETECTION,
                parent_widget=self,
                status_callback=update_status,
                button_callback=update_button
            )

            if not success:
                self.logger.error("启动检测截图失败")

        except Exception as e:
            self.logger.error(f"执行检测截图失败: {e}")
            self.status_label.setText(f"❌ 截图失败: {e}")
            self.screenshot_button.setEnabled(True)
            self.screenshot_button.setText("📷 检测截图")

    def _on_detection_screenshot_completed(self, screenshot_data: dict):
        """处理检测截图完成事件"""
        try:
            # 发送截图信号给主窗口
            config = self._get_current_config()
            config['screenshot_image'] = screenshot_data['image']
            config['screenshot_pixmap'] = screenshot_data['pixmap']
            config['screenshot_path'] = screenshot_data['filepath']
            config['action'] = 'screenshot_taken'
            self.screenshot_requested.emit(config)

            self.logger.info("检测截图完成，已发送到主窗口")

        except Exception as e:
            self.logger.error(f"处理检测截图完成事件失败: {e}")

    def _on_detection_screenshot_failed(self, error_msg: str):
        """处理检测截图失败事件"""
        self.logger.error(f"检测截图失败: {error_msg}")
        self.status_label.setText(f"❌ {error_msg}")

    def _on_screenshot_saved(self, filepath):
        """处理截图保存事件"""
        try:
            self.logger.info(f"截图已另存为: {filepath}")
            self.status_label.setText("💾 截图已保存")
        except Exception as e:
            self.logger.error(f"处理截图保存事件失败: {e}")

    def _on_template_created(self, template_data):
        """处理模板创建事件"""
        try:
            template_name = template_data.get('name', 'Unknown')
            self.logger.info(f"模板已创建: {template_name}")
            self.status_label.setText(f"🏷️ 模板已创建: {template_name}")

            # 可以发送信号通知其他组件
            config = self._get_current_config()
            config['template_data'] = template_data
            config['action'] = 'template_created'
            self.screenshot_requested.emit(config)

        except Exception as e:
            self.logger.error(f"处理模板创建事件失败: {e}")

    def _on_region_extracted(self, image, region):
        """处理区域提取事件"""
        try:
            x, y, width, height = region
            self.logger.info(f"区域已提取: ({x}, {y}, {width}, {height})")
            self.status_label.setText(f"✂️ 区域已提取: {width}×{height}")

            # 转换为QPixmap并发送信号
            from ...utils.screenshot_helper import get_screenshot_helper
            screenshot_helper = get_screenshot_helper()
            pixmap = screenshot_helper.numpy_to_qpixmap(image)

            if pixmap and not pixmap.isNull():
                config = self._get_current_config()
                config['region_image'] = image
                config['region_pixmap'] = pixmap
                config['region'] = region
                config['action'] = 'region_extracted'
                self.screenshot_requested.emit(config)

        except Exception as e:
            self.logger.error(f"处理区域提取事件失败: {e}")

    def _on_screenshot_taken(self, image, filepath):
        """处理截图完成事件"""
        try:
            # 转换为QPixmap
            from ...utils.screenshot_helper import get_screenshot_helper
            screenshot_helper = get_screenshot_helper()
            pixmap = screenshot_helper.numpy_to_qpixmap(image)

            if pixmap:
                # 发送截图请求信号
                config = self._get_current_config()
                config['screenshot_image'] = image
                config['screenshot_pixmap'] = pixmap
                config['screenshot_path'] = filepath
                config['action'] = 'screenshot_taken'
                self.screenshot_requested.emit(config)

                # 更新状态
                self.status_label.setText("📸 截图完成")

                self.logger.info(f"截图完成: {filepath}")
            else:
                self.logger.error("图像转换失败")
                self.status_label.setText("❌ 图像转换失败")

        except Exception as e:
            self.logger.error(f"处理截图失败: {e}")
            self.status_label.setText(f"❌ 处理失败: {str(e)[:20]}")

    def _on_region_selected(self, image, region):
        """处理区域选择事件"""
        try:
            x, y, width, height = region

            # 转换为QPixmap
            from ...utils.screenshot_helper import get_screenshot_helper
            screenshot_helper = get_screenshot_helper()
            pixmap = screenshot_helper.numpy_to_qpixmap(image)

            if pixmap:
                # 发送区域选择信号
                config = self._get_current_config()
                config['region_image'] = image
                config['region_pixmap'] = pixmap
                config['region'] = region
                config['action'] = 'region_selected'
                self.screenshot_requested.emit(config)

                # 更新状态
                self.status_label.setText(f"🎯 区域选择: {width}×{height}")

                self.logger.info(f"区域选择完成: {width}×{height}")
            else:
                self.logger.error("区域图像转换失败")
                self.status_label.setText("❌ 区域转换失败")

        except Exception as e:
            self.logger.error(f"处理区域选择失败: {e}")
            self.status_label.setText(f"❌ 区域处理失败: {str(e)[:20]}")
    
    def _get_current_config(self) -> Dict[str, Any]:
        """获取当前配置"""
        return {
            'monitor_id': self.monitor_combo.currentIndex(),
            'confidence_threshold': self.confidence_slider.value() / 100.0,
            'nms_threshold': self.nms_slider.value() / 100.0,
            'auto_detect': self.auto_detect_checkbox.isChecked(),
            'detection_interval': self.interval_spinbox.value()
        }
    
    def _init_detection_services(self) -> None:
        """初始化检测服务"""
        try:
            # 初始化屏幕截图服务
            self.screen_capture = ScreenCaptureServiceV2()

            # 初始化YOLO检测器
            self.yolo_detector = YOLODetectorV2(self.config_manager)

            self.logger.info("检测服务初始化完成")

        except Exception as e:
            self.logger.error(f"初始化检测服务失败: {e}")

    def _start_detection_service(self) -> None:
        """启动检测服务"""
        try:
            if not self.is_detecting:
                self.is_detecting = True

                # 获取检测间隔
                interval = int(self.interval_spinbox.value() * 1000)  # 转换为毫秒

                # 启动定时器
                self.detection_timer.start(interval)

                self.logger.info(f"检测服务已启动，间隔: {interval}ms")

        except Exception as e:
            self.logger.error(f"启动检测服务失败: {e}")

    def _stop_detection_service(self) -> None:
        """停止检测服务"""
        try:
            if self.is_detecting:
                self.is_detecting = False
                self.detection_timer.stop()

                self.logger.info("检测服务已停止")

        except Exception as e:
            self.logger.error(f"停止检测服务失败: {e}")

    def _perform_detection(self) -> None:
        """执行一次检测"""
        try:
            if not self.is_detecting:
                return

            # 截取屏幕
            if self.screen_capture:
                # 临时最小化主窗口，避免截取到自身（防止无限递归显示）
                main_window = None
                try:
                    # 找到主窗口
                    widget = self
                    while widget.parent():
                        widget = widget.parent()
                        if hasattr(widget, 'windowTitle') and 'YOLO' in widget.windowTitle():
                            main_window = widget
                            break

                    # 临时最小化
                    was_minimized = False
                    if main_window and not main_window.isMinimized():
                        main_window.showMinimized()
                        was_minimized = True
                        # 短暂等待窗口最小化完成
                        import time
                        time.sleep(0.1)

                    # 截图
                    image = self.screen_capture.capture_fullscreen()

                finally:
                    # 恢复窗口
                    if main_window and was_minimized:
                        main_window.showNormal()
                        main_window.raise_()
                        main_window.activateWindow()

                if image is not None:
                    detections = []  # 默认空检测结果

                    # 执行YOLO检测
                    if self.yolo_detector:
                        try:
                            detections = self.yolo_detector.detect(
                                image,
                                confidence=self.confidence_slider.value() / 100.0,
                                nms_threshold=self.nms_slider.value() / 100.0
                            )

                            # 更新统计
                            self.detection_count += 1

                            # 计算FPS
                            stats = self.yolo_detector.get_performance_stats()
                            self.current_fps = stats.get('fps', 0.0)

                        except Exception as e:
                            self.logger.error(f"YOLO检测失败: {e}")

                    # 使用智能检测管理器处理结果
                    detection_result = self.smart_detection_manager.process_detections(image, detections)

                    # 更新悬浮窗口状态
                    if self.floating_widget:
                        # 使用智能检测结果中的检测数据
                        actual_detections = detection_result.get("detections", [])
                        detection_mode = detection_result.get("detection_mode", "yolo_detection")

                        if len(actual_detections) > 0:
                            self.floating_widget.set_detection_state("found_target", len(actual_detections))
                            self.logger.debug(f"悬浮窗口更新: 发现{len(actual_detections)}个目标 ({detection_mode})")
                        else:
                            self.floating_widget.set_detection_state("detecting", 0)

                        # 更新统计信息
                        stats = self.smart_detection_manager.get_session_statistics()
                        self.floating_widget.update_statistics(
                            stats.get('duration', 0),
                            stats.get('saved_screenshots', 0)
                        )

                    # 显示屏幕叠加标注
                    if detection_result.get('should_overlay', False):
                        # 使用智能检测结果中的检测数据
                        actual_detections = detection_result.get('detections', [])
                        overlay_detections = []

                        for det in actual_detections:
                            overlay_detections.append({
                                'bbox': list(det.bbox),  # 转换元组为列表
                                'confidence': det.confidence,
                                'class_name': det.class_name
                            })

                        if overlay_detections:
                            self.overlay_manager.show_detections(overlay_detections, auto_fade=True)
                            self.logger.debug(f"屏幕叠加显示: {len(overlay_detections)}个目标")

                    # 更新状态显示
                    if detection_result.get('save_path'):
                        self.update_status(f"🎯 已保存: {len(detections)}个目标", self.current_fps, self.detection_count)
                    else:
                        self.update_status("🔴 检测中...", self.current_fps, self.detection_count)

                    # 发送实时截图到主窗口显示（无论是否在悬浮模式下）
                    from ...utils.screenshot_helper import get_screenshot_helper
                    screenshot_helper = get_screenshot_helper()
                    pixmap = screenshot_helper.numpy_to_qpixmap(image)

                    if pixmap and not pixmap.isNull():
                        config = self._get_current_config()
                        config['screenshot_image'] = image
                        config['screenshot_pixmap'] = pixmap
                        config['screenshot_path'] = detection_result.get('save_path', '')
                        config['action'] = 'realtime_detection'
                        config['detections'] = detections
                        self.screenshot_requested.emit(config)

                    self.logger.debug(f"智能检测完成: {len(detections)}个目标, 保存: {detection_result.get('should_save', False)}")

        except Exception as e:
            self.logger.error(f"执行检测失败: {e}")

    def update_status(self, status: str, fps: float = 0.0, count: int = 0) -> None:
        """更新状态"""
        self.status_label.setText(status)
        self.fps_label.setText(f"FPS: {fps:.1f}")
        self.count_label.setText(f"检测数: {count}")

    def _start_floating_mode(self):
        """启动悬浮模式"""
        try:
            # 创建悬浮窗口
            if not self.floating_widget:
                self.floating_widget = FloatingDetectionWidget()

                # 连接信号
                self.floating_widget.stop_detection_requested.connect(self._stop_detection)
                self.floating_widget.pause_detection_requested.connect(self._pause_detection)
                self.floating_widget.resume_detection_requested.connect(self._resume_detection)
                self.floating_widget.show_main_window_requested.connect(self._show_main_window)
                self.floating_widget.settings_requested.connect(self._show_settings)

            # 显示悬浮窗口
            self.floating_widget.show()
            self.floating_widget.set_detection_state("detecting")

            # 获取主窗口引用并最小化
            self._minimize_main_window()

            self.is_floating_mode = True
            self.logger.info("悬浮模式已启动")

        except Exception as e:
            self.logger.error(f"启动悬浮模式失败: {e}")

    def _stop_floating_mode(self):
        """停止悬浮模式"""
        try:
            if self.floating_widget:
                self.floating_widget.hide()

            # 隐藏叠加层
            self.overlay_manager.hide_overlay()

            # 恢复主窗口
            self._restore_main_window()

            self.is_floating_mode = False
            self.logger.info("悬浮模式已停止")

        except Exception as e:
            self.logger.error(f"停止悬浮模式失败: {e}")

    def _minimize_main_window(self):
        """最小化主窗口"""
        try:
            # 找到主窗口
            widget = self
            while widget.parent():
                widget = widget.parent()
                if hasattr(widget, 'windowTitle') and 'YOLO' in widget.windowTitle():
                    self.main_window_ref = widget
                    widget.showMinimized()
                    break
        except Exception as e:
            self.logger.error(f"最小化主窗口失败: {e}")

    def _restore_main_window(self):
        """恢复主窗口"""
        try:
            if self.main_window_ref:
                self.main_window_ref.showNormal()
                self.main_window_ref.raise_()
                self.main_window_ref.activateWindow()
        except Exception as e:
            self.logger.error(f"恢复主窗口失败: {e}")

    def _pause_detection(self):
        """暂停检测"""
        if self.is_detecting:
            self.detection_timer.stop()
            if self.floating_widget:
                self.floating_widget.set_pause_state(True)
            self.logger.info("检测已暂停")

    def _resume_detection(self):
        """恢复检测"""
        if self.is_detecting:
            interval = int(self.interval_spinbox.value() * 1000)
            self.detection_timer.start(interval)
            if self.floating_widget:
                self.floating_widget.set_pause_state(False)
            self.logger.info("检测已恢复")

    def _show_main_window(self):
        """显示主窗口"""
        self._restore_main_window()

    def _show_settings(self):
        """显示设置"""
        # 这里可以添加设置对话框
        self.logger.info("显示设置对话框")

    def _configure_template_matching(self):
        """配置模板匹配模式"""
        try:
            # 获取当前选定的模板
            selected_template = self._get_selected_template()

            if selected_template:
                # 启用模板匹配模式
                template_data = {
                    'name': selected_template.get('name', 'unknown'),
                    'image': selected_template.get('image'),
                    'threshold': selected_template.get('threshold', 0.8)
                }

                self.smart_detection_manager.set_template_matching(True, template_data)
                self.logger.info(f"已启用模板匹配模式，模板: {template_data['name']}")
            else:
                # 禁用模板匹配，使用YOLO检测
                self.smart_detection_manager.set_template_matching(False)
                self.logger.info("未选择模板，使用YOLO检测模式")

        except Exception as e:
            self.logger.error(f"配置模板匹配失败: {e}")
            # 出错时默认使用YOLO检测
            self.smart_detection_manager.set_template_matching(False)

    def _get_selected_template(self):
        """获取当前选定的模板"""
        try:
            # 从主窗口获取模板面板
            main_window = self._get_main_window()
            if main_window and hasattr(main_window, 'template_panel'):
                template_panel = main_window.template_panel
                selected_template = template_panel.get_selected_template()

                if selected_template:
                    # 加载模板图像
                    template_image = self._load_template_image(selected_template)
                    if template_image is not None:
                        return {
                            'name': selected_template.get('name', 'unknown'),
                            'image': template_image,
                            'threshold': 0.8,  # 默认阈值
                            'path': selected_template.get('path', '')
                        }
                    else:
                        self.logger.warning(f"无法加载模板图像: {selected_template.get('name', 'unknown')}")
                        return None
                else:
                    self.logger.debug("未选择模板")
                    return None
            else:
                self.logger.warning("无法获取模板面板")
                return None

        except Exception as e:
            self.logger.error(f"获取选定模板失败: {e}")
            return None

    def _get_main_window(self):
        """获取主窗口引用"""
        try:
            widget = self
            while widget.parent():
                widget = widget.parent()
                if hasattr(widget, 'template_panel'):
                    return widget
            return None
        except Exception as e:
            self.logger.error(f"获取主窗口失败: {e}")
            return None

    def _load_template_image(self, template_data):
        """加载模板图像 - 修复中文文件名支持"""
        try:
            import cv2
            import numpy as np
            from pathlib import Path

            template_path = template_data.get('path', '')
            if template_path and Path(template_path).exists():
                # 使用支持中文文件名的加载方法
                image = self._load_image_with_chinese_support(template_path)
                if image is not None:
                    self.logger.debug(f"从文件加载模板图像: {template_path}")
                    return image
                else:
                    self.logger.warning(f"无法读取模板文件: {template_path}")

            # 如果没有文件路径或文件不存在，尝试从内存数据加载
            if 'image' in template_data:
                return template_data['image']

            self.logger.warning("模板数据中没有可用的图像")
            return None

        except Exception as e:
            self.logger.error(f"加载模板图像失败: {e}")
            return None

    def _load_image_with_chinese_support(self, file_path: str):
        """支持中文文件名的图像加载函数"""
        try:
            import cv2
            import numpy as np
            from pathlib import Path

            # 首先尝试标准的cv2.imread
            image = cv2.imread(file_path)
            if image is not None:
                return image

            # 如果标准方法失败，使用字节解码方式（解决中文文件名问题）
            path_obj = Path(file_path)
            if path_obj.exists():
                try:
                    # 读取文件为字节数据
                    with open(file_path, 'rb') as f:
                        data = f.read()

                    # 将字节数据转换为numpy数组
                    nparr = np.frombuffer(data, np.uint8)

                    # 使用cv2.imdecode解码图像
                    image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

                    if image is not None:
                        self.logger.debug(f"使用字节解码成功加载中文文件名图像: {file_path}")
                        return image

                except Exception as decode_error:
                    self.logger.warning(f"字节解码加载图像失败: {decode_error}")

            return None

        except Exception as e:
            self.logger.error(f"加载图像时发生异常: {e}")
            return None
