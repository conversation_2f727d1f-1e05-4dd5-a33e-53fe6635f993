# -*- coding: utf-8 -*-
"""
重构的屏幕截图服务 - 支持多种截图方法
作者: Cursor AI
创建时间: 2025-07-05
编码标准: UTF-8无BOM
"""

import sys
import time
from typing import Optional, List, Tuple, Dict, Any
import numpy as np
from pathlib import Path

from ..utils.logger import Logger


class ScreenCaptureServiceV2:
    """重构的屏幕截图服务类"""
    
    def __init__(self):
        self.logger = Logger()
        self.available_methods = []
        self._init_capture_methods()
        
        self.logger.info("屏幕截图服务初始化完成")
    
    def _init_capture_methods(self) -> None:
        """初始化可用的截图方法"""
        methods = [
            ('mss', self._test_mss),
            ('pil', self._test_pil),
            ('pyautogui', self._test_pyautogui),
            ('opencv', self._test_opencv)
        ]
        
        for method_name, test_func in methods:
            try:
                if test_func():
                    self.available_methods.append(method_name)
                    self.logger.info(f"截图方法 {method_name} 可用")
            except Exception as e:
                self.logger.warning(f"截图方法 {method_name} 不可用: {e}")
        
        if not self.available_methods:
            self.logger.error("没有可用的截图方法")
        else:
            self.logger.info(f"可用截图方法: {', '.join(self.available_methods)}")
    
    def _test_mss(self) -> bool:
        """测试MSS截图方法"""
        try:
            import mss
            with mss.mss() as sct:
                monitor = sct.monitors[0]  # 测试主显示器
                return True
        except:
            return False
    
    def _test_pil(self) -> bool:
        """测试PIL截图方法"""
        try:
            from PIL import ImageGrab
            # 尝试小区域截图测试
            ImageGrab.grab(bbox=(0, 0, 100, 100))
            return True
        except:
            return False
    
    def _test_pyautogui(self) -> bool:
        """测试PyAutoGUI截图方法"""
        try:
            import pyautogui
            pyautogui.screenshot(region=(0, 0, 100, 100))
            return True
        except:
            return False
    
    def _test_opencv(self) -> bool:
        """测试OpenCV截图方法"""
        try:
            import cv2
            # OpenCV本身不直接支持截图，需要配合其他方法
            return False
        except:
            return False
    
    def capture_fullscreen(self, method: Optional[str] = None) -> Optional[np.ndarray]:
        """
        截取全屏
        
        Args:
            method: 指定截图方法，None表示自动选择
            
        Returns:
            截图的numpy数组 (BGR格式)，失败返回None
        """
        if not self.available_methods:
            self.logger.error("没有可用的截图方法")
            return None
        
        # 选择截图方法
        if method is None:
            method = self.available_methods[0]  # 使用第一个可用方法
        elif method not in self.available_methods:
            self.logger.warning(f"指定的截图方法 {method} 不可用，使用默认方法")
            method = self.available_methods[0]
        
        try:
            if method == 'mss':
                return self._capture_with_mss()
            elif method == 'pil':
                return self._capture_with_pil()
            elif method == 'pyautogui':
                return self._capture_with_pyautogui()
            else:
                self.logger.error(f"未知的截图方法: {method}")
                return None
                
        except Exception as e:
            self.logger.error(f"使用 {method} 截图失败: {e}")
            # 尝试其他方法
            for fallback_method in self.available_methods:
                if fallback_method != method:
                    try:
                        self.logger.info(f"尝试备用截图方法: {fallback_method}")
                        return self.capture_fullscreen(fallback_method)
                    except:
                        continue
            return None
    
    def capture_region(self, x: int, y: int, width: int, height: int, 
                      method: Optional[str] = None) -> Optional[np.ndarray]:
        """
        截取指定区域
        
        Args:
            x, y: 区域左上角坐标
            width, height: 区域宽高
            method: 指定截图方法
            
        Returns:
            截图的numpy数组 (BGR格式)，失败返回None
        """
        if not self.available_methods:
            self.logger.error("没有可用的截图方法")
            return None
        
        # 选择截图方法
        if method is None:
            method = self.available_methods[0]
        elif method not in self.available_methods:
            self.logger.warning(f"指定的截图方法 {method} 不可用，使用默认方法")
            method = self.available_methods[0]
        
        try:
            if method == 'mss':
                return self._capture_region_with_mss(x, y, width, height)
            elif method == 'pil':
                return self._capture_region_with_pil(x, y, width, height)
            elif method == 'pyautogui':
                return self._capture_region_with_pyautogui(x, y, width, height)
            else:
                self.logger.error(f"未知的截图方法: {method}")
                return None
                
        except Exception as e:
            self.logger.error(f"使用 {method} 截取区域失败: {e}")
            return None
    
    def _capture_with_mss(self) -> np.ndarray:
        """使用MSS截取全屏"""
        import mss
        import cv2

        try:
            with mss.mss() as sct:
                # 调试信息：打印所有显示器
                self.logger.debug(f"可用显示器: {len(sct.monitors)} 个")
                for i, monitor in enumerate(sct.monitors):
                    self.logger.debug(f"显示器 {i}: {monitor}")

                # 使用所有显示器的组合（monitors[0]）
                monitor = sct.monitors[0]  # monitors[0]是所有显示器的组合
                self.logger.debug(f"使用显示器: {monitor}")

                screenshot = sct.grab(monitor)
                self.logger.debug(f"截图尺寸: {screenshot.width}×{screenshot.height}")

                # 转换为numpy数组
                img_array = np.array(screenshot)
                self.logger.debug(f"数组形状: {img_array.shape}, 数据范围: {img_array.min()}-{img_array.max()}")

                # 检查是否获取到有效数据
                if img_array.max() == 0:
                    self.logger.warning("MSS截图数据全为0，可能是权限问题")
                    return None

                # 转换颜色格式 (BGRA -> BGR)
                img_bgr = cv2.cvtColor(img_array, cv2.COLOR_BGRA2BGR)

                return img_bgr

        except Exception as e:
            self.logger.error(f"MSS截图失败: {e}")
            return None
    
    def _capture_with_pil(self) -> np.ndarray:
        """使用PIL截取全屏"""
        from PIL import ImageGrab
        import cv2
        
        screenshot = ImageGrab.grab()
        img_array = np.array(screenshot)
        
        # 转换颜色格式 (RGB -> BGR)
        img_bgr = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
        
        return img_bgr
    
    def _capture_with_pyautogui(self) -> np.ndarray:
        """使用PyAutoGUI截取全屏"""
        import pyautogui
        import cv2
        
        screenshot = pyautogui.screenshot()
        img_array = np.array(screenshot)
        
        # 转换颜色格式 (RGB -> BGR)
        img_bgr = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
        
        return img_bgr
    
    def _capture_region_with_mss(self, x: int, y: int, width: int, height: int) -> np.ndarray:
        """使用MSS截取指定区域"""
        import mss
        import cv2
        
        with mss.mss() as sct:
            monitor = {"top": y, "left": x, "width": width, "height": height}
            screenshot = sct.grab(monitor)
            
            # 转换为numpy数组
            img_array = np.array(screenshot)
            
            # 转换颜色格式 (BGRA -> BGR)
            img_bgr = cv2.cvtColor(img_array, cv2.COLOR_BGRA2BGR)
            
            return img_bgr
    
    def _capture_region_with_pil(self, x: int, y: int, width: int, height: int) -> np.ndarray:
        """使用PIL截取指定区域"""
        from PIL import ImageGrab
        import cv2
        
        bbox = (x, y, x + width, y + height)
        screenshot = ImageGrab.grab(bbox=bbox)
        img_array = np.array(screenshot)
        
        # 转换颜色格式 (RGB -> BGR)
        img_bgr = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
        
        return img_bgr
    
    def _capture_region_with_pyautogui(self, x: int, y: int, width: int, height: int) -> np.ndarray:
        """使用PyAutoGUI截取指定区域"""
        import pyautogui
        import cv2
        
        screenshot = pyautogui.screenshot(region=(x, y, width, height))
        img_array = np.array(screenshot)
        
        # 转换颜色格式 (RGB -> BGR)
        img_bgr = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
        
        return img_bgr
    
    def list_monitors(self) -> List[Dict[str, Any]]:
        """获取显示器列表"""
        monitors = []
        
        try:
            if 'mss' in self.available_methods:
                import mss
                with mss.mss() as sct:
                    for i, monitor in enumerate(sct.monitors):
                        if i == 0:  # 跳过虚拟显示器
                            continue
                        monitors.append({
                            'id': i,
                            'left': monitor['left'],
                            'top': monitor['top'],
                            'width': monitor['width'],
                            'height': monitor['height']
                        })
            else:
                # 使用默认显示器信息
                import tkinter as tk
                root = tk.Tk()
                width = root.winfo_screenwidth()
                height = root.winfo_screenheight()
                root.destroy()
                
                monitors.append({
                    'id': 1,
                    'left': 0,
                    'top': 0,
                    'width': width,
                    'height': height
                })
                
        except Exception as e:
            self.logger.error(f"获取显示器信息失败: {e}")
        
        return monitors
    
    def save_screenshot(self, image: np.ndarray, filepath: str) -> bool:
        """
        保存截图到文件
        
        Args:
            image: 图像数组
            filepath: 保存路径
            
        Returns:
            是否保存成功
        """
        try:
            import cv2
            
            # 确保目录存在
            Path(filepath).parent.mkdir(parents=True, exist_ok=True)
            
            # 保存图像
            success = cv2.imwrite(filepath, image)
            
            if success:
                self.logger.info(f"截图已保存: {filepath}")
                return True
            else:
                self.logger.error(f"保存截图失败: {filepath}")
                return False
                
        except Exception as e:
            self.logger.error(f"保存截图异常: {e}")
            return False
    
    def get_screen_size(self) -> Tuple[int, int]:
        """获取屏幕尺寸"""
        try:
            monitors = self.list_monitors()
            if monitors:
                monitor = monitors[0]  # 主显示器
                return monitor['width'], monitor['height']
            else:
                # 备用方法
                import tkinter as tk
                root = tk.Tk()
                width = root.winfo_screenwidth()
                height = root.winfo_screenheight()
                root.destroy()
                return width, height
                
        except Exception as e:
            self.logger.error(f"获取屏幕尺寸失败: {e}")
            return 1920, 1080  # 默认尺寸
