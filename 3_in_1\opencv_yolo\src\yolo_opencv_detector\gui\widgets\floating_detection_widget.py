#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
悬浮检测窗口组件
实现检测时的悬浮图标和状态显示
作者: Cursor AI
创建时间: 2025-07-05
编码标准: UTF-8无BOM
"""

from typing import Optional, Dict, Any
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QMenu, QApplication, QGraphicsDropShadowEffect
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer, QPropertyAnimation, QRect, QEasingCurve
from PyQt6.QtGui import QPixmap, QPainter, QColor, QBrush, QPen, QFont, QCursor

from ...utils.logger import Logger


class FloatingDetectionWidget(QWidget):
    """悬浮检测状态窗口"""
    
    # 信号定义
    stop_detection_requested = pyqtSignal()
    pause_detection_requested = pyqtSignal()
    resume_detection_requested = pyqtSignal()
    show_main_window_requested = pyqtSignal()
    settings_requested = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = Logger()
        
        # 窗口属性
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint |
            Qt.WindowType.WindowStaysOnTopHint |
            Qt.WindowType.Tool
        )
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setFixedSize(60, 60)
        
        # 状态管理
        self.detection_state = "detecting"  # detecting, paused, found_target
        self.is_paused = False
        self.target_count = 0
        self.detection_duration = 0
        self.saved_screenshots = 0
        
        # 动画效果
        self.blink_timer = QTimer()
        self.blink_timer.timeout.connect(self._toggle_blink)
        self.is_blinking = False
        self.blink_state = False
        
        # 拖拽支持
        self.dragging = False
        self.drag_position = None
        
        self._init_ui()
        self._setup_animations()
        self._setup_context_menu()
        
        # 默认位置（屏幕右上角）
        self._move_to_default_position()
        
        self.logger.info("悬浮检测窗口初始化完成")
    
    def _init_ui(self):
        """初始化UI"""
        # 主布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 状态标签（用于显示图标和状态）
        self.status_label = QLabel()
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.status_label.setFixedSize(60, 60)
        layout.addWidget(self.status_label)
        
        # 设置阴影效果
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(10)
        shadow.setColor(QColor(0, 0, 0, 100))
        shadow.setOffset(2, 2)
        self.setGraphicsEffect(shadow)
        
        # 更新显示
        self._update_display()
    
    def _setup_animations(self):
        """设置动画效果"""
        # 位置动画
        self.position_animation = QPropertyAnimation(self, b"geometry")
        self.position_animation.setDuration(300)
        self.position_animation.setEasingCurve(QEasingCurve.Type.OutCubic)
    
    def _setup_context_menu(self):
        """设置右键菜单"""
        self.context_menu = QMenu(self)
        
        # 暂停/恢复
        self.pause_action = self.context_menu.addAction("⏸️ 暂停检测")
        self.pause_action.triggered.connect(self._toggle_pause)
        
        self.context_menu.addSeparator()
        
        # 停止检测
        stop_action = self.context_menu.addAction("⏹️ 停止检测")
        stop_action.triggered.connect(self.stop_detection_requested.emit)
        
        # 显示主窗口
        show_action = self.context_menu.addAction("🏠 显示主窗口")
        show_action.triggered.connect(self.show_main_window_requested.emit)
        
        self.context_menu.addSeparator()
        
        # 设置
        settings_action = self.context_menu.addAction("⚙️ 设置")
        settings_action.triggered.connect(self.settings_requested.emit)
    
    def _move_to_default_position(self):
        """移动到默认位置（屏幕右上角）"""
        screen = QApplication.primaryScreen().geometry()
        x = screen.width() - self.width() - 20
        y = 50
        self.move(x, y)
    
    def _update_display(self):
        """更新显示状态"""
        # 创建状态图标
        pixmap = QPixmap(60, 60)
        pixmap.fill(Qt.GlobalColor.transparent)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 根据状态设置颜色和图标
        if self.detection_state == "detecting":
            if self.is_paused:
                color = QColor(255, 193, 7)  # 黄色 - 暂停
                icon_text = "⏸️"
            else:
                color = QColor(40, 167, 69)  # 绿色 - 检测中
                icon_text = "🔍"
        elif self.detection_state == "found_target":
            color = QColor(220, 53, 69)  # 红色 - 发现目标
            icon_text = "🎯"
        else:
            color = QColor(108, 117, 125)  # 灰色 - 默认
            icon_text = "❓"
        
        # 绘制背景圆圈
        if self.blink_state and self.detection_state == "found_target":
            # 闪烁效果
            painter.setBrush(QBrush(QColor(255, 255, 255, 200)))
        else:
            painter.setBrush(QBrush(color))
        
        painter.setPen(QPen(QColor(255, 255, 255), 2))
        painter.drawEllipse(5, 5, 50, 50)
        
        # 绘制图标文字
        painter.setPen(QPen(QColor(255, 255, 255)))
        painter.setFont(QFont("Arial", 16))
        painter.drawText(pixmap.rect(), Qt.AlignmentFlag.AlignCenter, icon_text)
        
        # 绘制目标计数（如果有）
        if self.target_count > 0:
            painter.setPen(QPen(QColor(255, 255, 255)))
            painter.setFont(QFont("Arial", 8, QFont.Weight.Bold))
            painter.drawText(45, 15, str(self.target_count))
        
        painter.end()
        
        self.status_label.setPixmap(pixmap)
    
    def set_detection_state(self, state: str, target_count: int = 0):
        """设置检测状态"""
        self.detection_state = state
        self.target_count = target_count
        
        # 如果发现目标，启动闪烁动画
        if state == "found_target" and not self.is_blinking:
            self.start_blink_animation()
        elif state != "found_target" and self.is_blinking:
            self.stop_blink_animation()
        
        self._update_display()
    
    def set_pause_state(self, paused: bool):
        """设置暂停状态"""
        self.is_paused = paused
        self.pause_action.setText("▶️ 恢复检测" if paused else "⏸️ 暂停检测")
        self._update_display()
    
    def start_blink_animation(self):
        """开始闪烁动画"""
        if not self.is_blinking:
            self.is_blinking = True
            self.blink_timer.start(500)  # 每500ms闪烁一次
    
    def stop_blink_animation(self):
        """停止闪烁动画"""
        if self.is_blinking:
            self.is_blinking = False
            self.blink_timer.stop()
            self.blink_state = False
            self._update_display()
    
    def _toggle_blink(self):
        """切换闪烁状态"""
        self.blink_state = not self.blink_state
        self._update_display()
    
    def _toggle_pause(self):
        """切换暂停状态"""
        if self.is_paused:
            self.resume_detection_requested.emit()
        else:
            self.pause_detection_requested.emit()
    
    def update_statistics(self, duration: int, saved_count: int):
        """更新统计信息"""
        self.detection_duration = duration
        self.saved_screenshots = saved_count
        
        # 更新工具提示
        tooltip = f"""检测状态: {'暂停' if self.is_paused else '运行中'}
检测时长: {duration}秒
发现目标: {self.target_count}次
保存截图: {saved_count}张

左键: 停止检测并返回主界面
右键: 显示菜单"""
        self.setToolTip(tooltip)
    
    # 鼠标事件处理
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            # 左键点击 - 开始拖拽（短按）或停止检测（长按）
            self.dragging = True
            self.drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()

            # 设置定时器检测长按
            from PyQt6.QtCore import QTimer
            self.click_timer = QTimer()
            self.click_timer.setSingleShot(True)
            self.click_timer.timeout.connect(self._on_long_press)
            self.click_timer.start(500)  # 500ms长按

        elif event.button() == Qt.MouseButton.RightButton:
            # 右键点击 - 显示菜单
            self.context_menu.exec(QCursor.pos())
    
    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if self.dragging and self.drag_position:
            self.move(event.globalPosition().toPoint() - self.drag_position)
    
    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if hasattr(self, 'click_timer') and self.click_timer.isActive():
            # 短按 - 停止检测并显示主窗口
            self.click_timer.stop()
            self.stop_detection_requested.emit()
            self.show_main_window_requested.emit()

        self.dragging = False
        self.drag_position = None

    def _on_long_press(self):
        """长按事件处理"""
        # 长按不做任何操作，只是为了区分短按和拖拽
        pass
    
    def enterEvent(self, event):
        """鼠标进入事件"""
        # 鼠标悬停时稍微放大
        self.setFixedSize(65, 65)
        super().enterEvent(event)
    
    def leaveEvent(self, event):
        """鼠标离开事件"""
        # 恢复原始大小
        self.setFixedSize(60, 60)
        super().leaveEvent(event)
