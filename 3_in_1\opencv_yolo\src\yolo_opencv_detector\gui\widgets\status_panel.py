# -*- coding: utf-8 -*-
"""
状态面板组件
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

from typing import Dict, Any, List
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, QLabel,
    QProgressBar, QTextEdit, QPushButton, QTabWidget
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QFont

from ...utils.logger import Logger
from ...utils.config_manager import ConfigManager


class StatusPanel(QWidget):
    """状态面板类"""
    
    # 信号定义
    status_updated = pyqtSignal(dict)  # 状态更新
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化状态面板
        
        Args:
            config_manager: 配置管理器
        """
        super().__init__()
        
        self.logger = Logger().get_logger(__name__)
        self.config_manager = config_manager
        
        # 组件初始化
        self.tab_widget = None
        
        # 系统状态组件
        self.cpu_label = None
        self.memory_label = None
        self.gpu_label = None
        self.fps_label = None
        
        # 检测状态组件
        self.detection_status_label = None
        self.total_detections_label = None
        self.avg_confidence_label = None
        self.last_detection_label = None
        
        # 性能图表组件
        self.performance_chart = None
        
        # 日志组件
        self.log_text = None
        self.clear_log_button = None
        
        # 更新定时器
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self._update_status)
        self.update_timer.start(1000)  # 每秒更新
        
        # 状态数据
        self.performance_history: List[Dict[str, float]] = []
        self.max_history_length = 60  # 保留60秒的历史数据
        
        # 初始化界面
        self._init_ui()
        
        self.logger.info("状态面板初始化完成")
    
    def _init_ui(self) -> None:
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # 系统状态标签页
        self._create_system_tab()
        
        # 检测状态标签页
        self._create_detection_tab()
        
        # 性能监控标签页
        self._create_performance_tab()
        
        # 日志标签页
        self._create_log_tab()
    
    def _create_system_tab(self) -> None:
        """创建系统状态标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 系统资源组
        resource_group = QGroupBox("系统资源")
        resource_layout = QVBoxLayout(resource_group)
        
        # CPU使用率
        cpu_layout = QHBoxLayout()
        cpu_layout.addWidget(QLabel("CPU使用率:"))
        self.cpu_label = QLabel("0%")
        self.cpu_label.setStyleSheet("QLabel { font-weight: bold; }")
        cpu_layout.addWidget(self.cpu_label)
        cpu_layout.addStretch()
        resource_layout.addLayout(cpu_layout)
        
        # 内存使用
        memory_layout = QHBoxLayout()
        memory_layout.addWidget(QLabel("内存使用:"))
        self.memory_label = QLabel("0 MB")
        self.memory_label.setStyleSheet("QLabel { font-weight: bold; }")
        memory_layout.addWidget(self.memory_label)
        memory_layout.addStretch()
        resource_layout.addLayout(memory_layout)
        
        # GPU使用率
        gpu_layout = QHBoxLayout()
        gpu_layout.addWidget(QLabel("GPU使用率:"))
        self.gpu_label = QLabel("N/A")
        self.gpu_label.setStyleSheet("QLabel { font-weight: bold; }")
        gpu_layout.addWidget(self.gpu_label)
        gpu_layout.addStretch()
        resource_layout.addLayout(gpu_layout)
        
        layout.addWidget(resource_group)
        
        # 应用状态组
        app_group = QGroupBox("应用状态")
        app_layout = QVBoxLayout(app_group)
        
        # FPS
        fps_layout = QHBoxLayout()
        fps_layout.addWidget(QLabel("检测FPS:"))
        self.fps_label = QLabel("0.0")
        self.fps_label.setStyleSheet("QLabel { font-weight: bold; color: green; }")
        fps_layout.addWidget(self.fps_label)
        fps_layout.addStretch()
        app_layout.addLayout(fps_layout)
        
        layout.addWidget(app_group)
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "系统状态")
    
    def _create_detection_tab(self) -> None:
        """创建检测状态标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 检测统计组
        stats_group = QGroupBox("检测统计")
        stats_layout = QVBoxLayout(stats_group)
        
        # 检测状态
        status_layout = QHBoxLayout()
        status_layout.addWidget(QLabel("检测状态:"))
        self.detection_status_label = QLabel("未开始")
        self.detection_status_label.setStyleSheet("QLabel { font-weight: bold; }")
        status_layout.addWidget(self.detection_status_label)
        status_layout.addStretch()
        stats_layout.addLayout(status_layout)
        
        # 总检测数
        total_layout = QHBoxLayout()
        total_layout.addWidget(QLabel("总检测数:"))
        self.total_detections_label = QLabel("0")
        self.total_detections_label.setStyleSheet("QLabel { font-weight: bold; }")
        total_layout.addWidget(self.total_detections_label)
        total_layout.addStretch()
        stats_layout.addLayout(total_layout)
        
        # 平均置信度
        avg_conf_layout = QHBoxLayout()
        avg_conf_layout.addWidget(QLabel("平均置信度:"))
        self.avg_confidence_label = QLabel("0.00")
        self.avg_confidence_label.setStyleSheet("QLabel { font-weight: bold; }")
        avg_conf_layout.addWidget(self.avg_confidence_label)
        avg_conf_layout.addStretch()
        stats_layout.addLayout(avg_conf_layout)
        
        # 最后检测时间
        last_layout = QHBoxLayout()
        last_layout.addWidget(QLabel("最后检测:"))
        self.last_detection_label = QLabel("无")
        self.last_detection_label.setStyleSheet("QLabel { font-weight: bold; }")
        last_layout.addWidget(self.last_detection_label)
        last_layout.addStretch()
        stats_layout.addLayout(last_layout)
        
        layout.addWidget(stats_group)
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "检测状态")
    
    def _create_performance_tab(self) -> None:
        """创建性能监控标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 性能图表组
        chart_group = QGroupBox("性能图表")
        chart_layout = QVBoxLayout(chart_group)
        
        # 这里可以添加实际的图表组件
        # 暂时使用标签占位
        self.performance_chart = QLabel("性能图表（待实现）")
        self.performance_chart.setMinimumHeight(200)
        self.performance_chart.setStyleSheet("QLabel { border: 1px solid gray; background-color: white; }")
        self.performance_chart.setAlignment(Qt.AlignmentFlag.AlignCenter)
        chart_layout.addWidget(self.performance_chart)
        
        layout.addWidget(chart_group)
        
        self.tab_widget.addTab(tab, "性能监控")
    
    def _create_log_tab(self) -> None:
        """创建日志标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 日志显示组
        log_group = QGroupBox("系统日志")
        log_layout = QVBoxLayout(log_group)
        
        # 日志文本
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setMaximumHeight(200)
        
        # 设置等宽字体
        font = QFont("Consolas", 9)
        if not font.exactMatch():
            font = QFont("Courier New", 9)
        self.log_text.setFont(font)
        
        log_layout.addWidget(self.log_text)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        self.clear_log_button = QPushButton("清除日志")
        self.clear_log_button.clicked.connect(self._clear_log)
        self.clear_log_button.setToolTip("清除所有日志记录\n"
                                        "清空日志显示区域\n"
                                        "不影响文件中的日志")
        button_layout.addWidget(self.clear_log_button)
        
        button_layout.addStretch()
        
        log_layout.addLayout(button_layout)
        
        layout.addWidget(log_group)
        
        self.tab_widget.addTab(tab, "系统日志")
    
    def _update_status(self) -> None:
        """更新状态信息"""
        try:
            # 获取系统资源信息
            cpu_percent = self._get_cpu_usage()
            memory_mb = self._get_memory_usage()
            gpu_percent = self._get_gpu_usage()
            
            # 更新系统状态显示
            self.cpu_label.setText(f"{cpu_percent:.1f}%")
            self.memory_label.setText(f"{memory_mb:.1f} MB")
            
            if gpu_percent is not None:
                self.gpu_label.setText(f"{gpu_percent:.1f}%")
            else:
                self.gpu_label.setText("N/A")
            
            # 更新CPU标签颜色
            if cpu_percent > 80:
                self.cpu_label.setStyleSheet("QLabel { font-weight: bold; color: red; }")
            elif cpu_percent > 60:
                self.cpu_label.setStyleSheet("QLabel { font-weight: bold; color: orange; }")
            else:
                self.cpu_label.setStyleSheet("QLabel { font-weight: bold; color: green; }")
            
            # 更新内存标签颜色
            if memory_mb > 1000:
                self.memory_label.setStyleSheet("QLabel { font-weight: bold; color: red; }")
            elif memory_mb > 500:
                self.memory_label.setStyleSheet("QLabel { font-weight: bold; color: orange; }")
            else:
                self.memory_label.setStyleSheet("QLabel { font-weight: bold; color: green; }")
            
            # 记录性能历史
            self._record_performance(cpu_percent, memory_mb, gpu_percent)
            
            # 发送状态更新信号
            status_data = {
                "cpu_percent": cpu_percent,
                "memory_mb": memory_mb,
                "gpu_percent": gpu_percent,
                "timestamp": self._get_current_timestamp()
            }
            self.status_updated.emit(status_data)
            
        except Exception as e:
            self.logger.error(f"状态更新失败: {e}")
    
    def _get_cpu_usage(self) -> float:
        """获取CPU使用率"""
        try:
            import psutil
            return psutil.cpu_percent(interval=None)
        except ImportError:
            # 如果没有psutil，返回模拟数据
            import random
            return random.uniform(10, 30)
        except Exception as e:
            self.logger.error(f"获取CPU使用率失败: {e}")
            return 0.0
    
    def _get_memory_usage(self) -> float:
        """获取内存使用量（MB）"""
        try:
            import psutil
            process = psutil.Process()
            memory_info = process.memory_info()
            return memory_info.rss / 1024 / 1024  # 转换为MB
        except ImportError:
            # 如果没有psutil，返回模拟数据
            import random
            return random.uniform(100, 500)
        except Exception as e:
            self.logger.error(f"获取内存使用量失败: {e}")
            return 0.0
    
    def _get_gpu_usage(self) -> float:
        """获取GPU使用率"""
        try:
            # 这里可以添加GPU监控代码
            # 暂时返回None表示不可用
            return None
        except Exception as e:
            self.logger.error(f"获取GPU使用率失败: {e}")
            return None
    
    def _get_current_timestamp(self) -> float:
        """获取当前时间戳"""
        import time
        return time.time()
    
    def _record_performance(self, cpu: float, memory: float, gpu: float) -> None:
        """记录性能数据"""
        try:
            performance_data = {
                "timestamp": self._get_current_timestamp(),
                "cpu": cpu,
                "memory": memory,
                "gpu": gpu
            }
            
            self.performance_history.append(performance_data)
            
            # 限制历史数据长度
            if len(self.performance_history) > self.max_history_length:
                self.performance_history.pop(0)
                
        except Exception as e:
            self.logger.error(f"记录性能数据失败: {e}")
    
    def update_detection_status(self, status: str, color: str = "black") -> None:
        """
        更新检测状态
        
        Args:
            status: 状态文本
            color: 状态颜色
        """
        self.detection_status_label.setText(status)
        self.detection_status_label.setStyleSheet(f"QLabel {{ font-weight: bold; color: {color}; }}")
    
    def update_detection_stats(self, 
                             total_detections: int,
                             avg_confidence: float,
                             last_detection_time: str) -> None:
        """
        更新检测统计
        
        Args:
            total_detections: 总检测数
            avg_confidence: 平均置信度
            last_detection_time: 最后检测时间
        """
        self.total_detections_label.setText(str(total_detections))
        self.avg_confidence_label.setText(f"{avg_confidence:.3f}")
        self.last_detection_label.setText(last_detection_time)
    
    def update_fps(self, fps: float) -> None:
        """
        更新FPS显示
        
        Args:
            fps: 帧率
        """
        self.fps_label.setText(f"{fps:.1f}")
        
        # 根据FPS设置颜色
        if fps >= 10:
            color = "green"
        elif fps >= 5:
            color = "orange"
        else:
            color = "red"
        
        self.fps_label.setStyleSheet(f"QLabel {{ font-weight: bold; color: {color}; }}")
    
    def add_log_message(self, message: str, level: str = "INFO") -> None:
        """
        添加日志消息
        
        Args:
            message: 日志消息
            level: 日志级别
        """
        try:
            import datetime
            timestamp = datetime.datetime.now().strftime("%H:%M:%S")
            
            # 根据级别设置颜色
            color_map = {
                "DEBUG": "gray",
                "INFO": "black",
                "WARNING": "orange",
                "ERROR": "red"
            }
            color = color_map.get(level, "black")
            
            # 格式化日志消息
            formatted_message = f"<span style='color: {color}'>[{timestamp}] [{level}] {message}</span>"
            
            # 添加到日志文本
            self.log_text.append(formatted_message)
            
            # 限制日志行数
            if self.log_text.document().blockCount() > 1000:
                cursor = self.log_text.textCursor()
                cursor.movePosition(cursor.MoveOperation.Start)
                cursor.select(cursor.SelectionType.BlockUnderCursor)
                cursor.removeSelectedText()
            
            # 滚动到底部
            scrollbar = self.log_text.verticalScrollBar()
            scrollbar.setValue(scrollbar.maximum())
            
        except Exception as e:
            self.logger.error(f"添加日志消息失败: {e}")
    
    def _clear_log(self) -> None:
        """清除日志"""
        self.log_text.clear()
        self.add_log_message("日志已清除", "INFO")
    
    def get_performance_history(self) -> List[Dict[str, float]]:
        """获取性能历史数据"""
        return self.performance_history.copy()
    
    def reset_stats(self) -> None:
        """重置统计信息"""
        self.performance_history.clear()
        self.update_detection_stats(0, 0.0, "无")
        self.update_fps(0.0)
        self.add_log_message("统计信息已重置", "INFO")
