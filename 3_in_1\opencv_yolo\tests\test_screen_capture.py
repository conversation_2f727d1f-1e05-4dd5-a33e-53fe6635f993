# -*- coding: utf-8 -*-
"""
屏幕截取服务测试
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

import pytest
import numpy as np
from unittest.mock import Mock, patch
import tempfile
from pathlib import Path

from yolo_opencv_detector.core.screen_capture import ScreenCaptureService
from yolo_opencv_detector.utils.data_structures import ScreenInfo


class TestScreenCaptureService:
    """屏幕截取服务测试类"""
    
    @pytest.fixture
    def capture_service(self):
        """创建截图服务实例"""
        return ScreenCaptureService(
            cache_size=5,
            enable_cache=True,
            capture_method="pil"  # 使用PIL方法确保测试稳定性
        )
    
    @pytest.fixture
    def mock_image(self):
        """创建模拟图像"""
        return np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
    
    def test_service_initialization(self, capture_service):
        """测试服务初始化"""
        assert capture_service is not None
        assert capture_service.cache_size == 5
        assert capture_service.enable_cache == True
        assert capture_service.capture_method == "pil"
        assert len(capture_service.monitors) > 0
    
    def test_capture_method_resolution(self):
        """测试截图方法解析"""
        # 测试自动选择
        service_auto = ScreenCaptureService(capture_method="auto")
        assert service_auto.capture_method in ["pil", "mss", "win32"]
        
        # 测试指定PIL
        service_pil = ScreenCaptureService(capture_method="pil")
        assert service_pil.capture_method == "pil"
    
    def test_monitor_detection(self, capture_service):
        """测试显示器检测"""
        monitors = capture_service.get_monitors()
        assert len(monitors) > 0
        
        # 检查主显示器
        primary = capture_service.get_primary_monitor()
        assert primary is not None
        assert primary.is_primary == True
        
        # 检查显示器信息
        for monitor in monitors:
            assert isinstance(monitor, ScreenInfo)
            assert monitor.width > 0
            assert monitor.height > 0
    
    @patch('yolo_opencv_detector.core.screen_capture.ImageGrab.grab')
    def test_fullscreen_capture(self, mock_grab, capture_service, mock_image):
        """测试全屏截图"""
        # 模拟PIL截图
        from PIL import Image
        mock_pil_image = Image.fromarray(mock_image)
        mock_grab.return_value.__enter__.return_value = mock_pil_image
        
        # 执行截图
        result = capture_service.grab_fullscreen()
        
        # 验证结果
        assert result is not None
        assert isinstance(result, np.ndarray)
        assert result.shape[2] == 3  # BGR格式
        
        # 验证调用
        mock_grab.assert_called_once()
    
    @patch('yolo_opencv_detector.core.screen_capture.ImageGrab.grab')
    def test_region_capture(self, mock_grab, capture_service, mock_image):
        """测试区域截图"""
        from PIL import Image
        mock_pil_image = Image.fromarray(mock_image)
        mock_grab.return_value.__enter__.return_value = mock_pil_image
        
        # 执行区域截图
        result = capture_service.grab_region(10, 10, 50, 50)
        
        # 验证结果
        assert result is not None
        assert isinstance(result, np.ndarray)
        
        # 验证调用参数
        mock_grab.assert_called_once()
        call_args = mock_grab.call_args
        assert 'bbox' in call_args.kwargs
        assert call_args.kwargs['bbox'] == (10, 10, 60, 60)  # (x, y, x+width, y+height)
    
    def test_invalid_region(self, capture_service):
        """测试无效区域处理"""
        # 测试零宽度
        result = capture_service.grab_region(10, 10, 0, 50)
        assert result is None
        
        # 测试负宽度
        result = capture_service.grab_region(10, 10, -10, 50)
        assert result is None
        
        # 测试零高度
        result = capture_service.grab_region(10, 10, 50, 0)
        assert result is None
    
    def test_cache_functionality(self, capture_service, mock_image):
        """测试缓存功能"""
        # 添加到缓存
        capture_service._add_to_cache(mock_image, 0, "test_key")
        
        # 从缓存获取
        cached_image = capture_service.get_cached_screenshot("test_key", max_age=10.0)
        assert cached_image is not None
        assert np.array_equal(cached_image, mock_image)
        
        # 测试过期
        import time
        time.sleep(0.1)
        expired_image = capture_service.get_cached_screenshot("test_key", max_age=0.05)
        assert expired_image is None
    
    def test_cache_clear(self, capture_service, mock_image):
        """测试缓存清空"""
        # 添加缓存
        capture_service._add_to_cache(mock_image, 0, "test_key")
        
        # 验证缓存存在
        cached = capture_service.get_cached_screenshot("test_key")
        assert cached is not None
        
        # 清空缓存
        capture_service.clear_cache()
        
        # 验证缓存已清空
        cached = capture_service.get_cached_screenshot("test_key")
        assert cached is None
    
    def test_monitor_operations(self, capture_service):
        """测试显示器操作"""
        # 获取显示器
        monitor = capture_service.get_monitor(0)
        assert monitor is not None
        assert monitor.monitor_id == 0
        
        # 获取不存在的显示器
        invalid_monitor = capture_service.get_monitor(999)
        assert invalid_monitor is None
        
        # 刷新显示器
        success = capture_service.refresh_monitors()
        assert success == True
    
    def test_save_screenshot(self, capture_service, mock_image):
        """测试保存截图"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 测试PNG保存
            png_path = Path(temp_dir) / "test.png"
            success = capture_service.save_screenshot(mock_image, str(png_path))
            assert success == True
            assert png_path.exists()
            
            # 测试JPEG保存
            jpg_path = Path(temp_dir) / "test.jpg"
            success = capture_service.save_screenshot(mock_image, str(jpg_path), quality=90)
            assert success == True
            assert jpg_path.exists()
    
    def test_performance_stats(self, capture_service):
        """测试性能统计"""
        # 初始状态
        stats = capture_service.get_performance_stats()
        assert isinstance(stats, dict)
        
        # 添加模拟数据
        capture_service.capture_times = [0.1, 0.2, 0.15]
        capture_service.total_captures = 3
        
        stats = capture_service.get_performance_stats()
        assert "total_captures" in stats
        assert "avg_capture_time" in stats
        assert "avg_fps" in stats
        assert stats["total_captures"] == 3
        
        # 重置统计
        capture_service.reset_stats()
        assert len(capture_service.capture_times) == 0
        assert capture_service.total_captures == 0
    
    def test_config_update(self, capture_service):
        """测试配置更新"""
        original_cache_size = capture_service.cache_size
        
        # 更新配置
        capture_service.update_config(
            cache_size=10,
            enable_cache=False,
            capture_method="pil"
        )
        
        assert capture_service.cache_size == 10
        assert capture_service.enable_cache == False
        assert capture_service.capture_method == "pil"
        
        # 恢复配置
        capture_service.update_config(
            cache_size=original_cache_size,
            enable_cache=True
        )
    
    def test_disable_cache(self):
        """测试禁用缓存"""
        service = ScreenCaptureService(enable_cache=False)
        assert service.enable_cache == False
        assert service.screenshot_cache is None
        
        # 尝试缓存操作
        mock_image = np.zeros((10, 10, 3), dtype=np.uint8)
        service._add_to_cache(mock_image, 0, "test")  # 应该不会出错
        
        cached = service.get_cached_screenshot("test")
        assert cached is None
    
    @pytest.mark.skipif(True, reason="需要实际的Win32环境")
    def test_window_capture(self, capture_service):
        """测试窗口截图（需要Win32环境）"""
        # 这个测试在没有Win32的环境中会跳过
        result = capture_service.grab_window("Notepad")
        # 结果可能为None（如果没有找到窗口）
        if result is not None:
            assert isinstance(result, np.ndarray)
    
    @pytest.mark.skipif(True, reason="需要实际的Win32环境")
    def test_active_window_capture(self, capture_service):
        """测试活动窗口截图（需要Win32环境）"""
        result = capture_service.grab_active_window()
        # 结果可能为None（如果没有活动窗口）
        if result is not None:
            assert isinstance(result, np.ndarray)
    
    def test_error_handling(self, capture_service):
        """测试错误处理"""
        # 测试无效的保存路径
        mock_image = np.zeros((10, 10, 3), dtype=np.uint8)
        success = capture_service.save_screenshot(mock_image, "/invalid/path/test.png")
        assert success == False
    
    def test_memory_cleanup(self, capture_service):
        """测试内存清理"""
        # 添加一些缓存
        mock_image = np.zeros((100, 100, 3), dtype=np.uint8)
        for i in range(10):
            capture_service._add_to_cache(mock_image, 0, f"test_{i}")
        
        # 验证缓存数量受限制
        assert len(capture_service.screenshot_cache) <= capture_service.cache_size
        
        # 清理
        capture_service.clear_cache()
        assert len(capture_service.screenshot_cache) == 0
