# -*- coding: utf-8 -*-
"""
坐标映射器测试
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

import pytest
from unittest.mock import Mock

from yolo_opencv_detector.core.coordinate_mapper import CoordinateMapper
from yolo_opencv_detector.core.multi_monitor import MultiMonitorManager
from yolo_opencv_detector.utils.data_structures import BoundingBox, DetectionResult, ScreenInfo, DetectionSource


class TestCoordinateMapper:
    """坐标映射器测试类"""
    
    @pytest.fixture
    def mock_monitor_manager(self):
        """创建模拟的多显示器管理器"""
        manager = Mock(spec=MultiMonitorManager)
        
        # 模拟两个显示器
        monitor1 = ScreenInfo(
            monitor_id=0,
            x=0, y=0,
            width=1920, height=1080,
            is_primary=True,
            name="Primary Monitor"
        )
        
        monitor2 = ScreenInfo(
            monitor_id=1,
            x=1920, y=0,
            width=1920, height=1080,
            is_primary=False,
            name="Secondary Monitor"
        )
        
        manager.get_monitor_by_id.side_effect = lambda mid: {
            0: monitor1,
            1: monitor2
        }.get(mid)
        
        manager.get_primary_monitor.return_value = monitor1
        manager.get_monitor_at_point.side_effect = lambda x, y: (
            monitor1 if 0 <= x < 1920 else
            monitor2 if 1920 <= x < 3840 else None
        )
        
        return manager
    
    @pytest.fixture
    def coordinate_mapper(self, mock_monitor_manager):
        """创建坐标映射器实例"""
        return CoordinateMapper(mock_monitor_manager)
    
    def test_mapper_initialization(self, coordinate_mapper):
        """测试映射器初始化"""
        assert coordinate_mapper is not None
        assert coordinate_mapper.monitor_manager is not None
        assert len(coordinate_mapper.transform_history) == 0
    
    def test_screen_to_virtual(self, coordinate_mapper):
        """测试屏幕坐标到虚拟坐标转换"""
        # 主显示器坐标转换
        virtual_x, virtual_y = coordinate_mapper.screen_to_virtual(100, 200, 0)
        assert virtual_x == 100  # 主显示器偏移为0
        assert virtual_y == 200
        
        # 副显示器坐标转换
        virtual_x, virtual_y = coordinate_mapper.screen_to_virtual(100, 200, 1)
        assert virtual_x == 2020  # 100 + 1920
        assert virtual_y == 200
        
        # 无效显示器ID
        virtual_x, virtual_y = coordinate_mapper.screen_to_virtual(100, 200, 999)
        assert virtual_x == 100  # 应该返回原始坐标
        assert virtual_y == 200
    
    def test_virtual_to_screen(self, coordinate_mapper):
        """测试虚拟坐标到屏幕坐标转换"""
        # 主显示器区域的点
        screen_x, screen_y, monitor_id = coordinate_mapper.virtual_to_screen(100, 200)
        assert screen_x == 100
        assert screen_y == 200
        assert monitor_id == 0
        
        # 副显示器区域的点
        screen_x, screen_y, monitor_id = coordinate_mapper.virtual_to_screen(2020, 200)
        assert screen_x == 100  # 2020 - 1920
        assert screen_y == 200
        assert monitor_id == 1
        
        # 指定目标显示器
        screen_x, screen_y, monitor_id = coordinate_mapper.virtual_to_screen(100, 200, 1)
        assert screen_x == -1820  # 100 - 1920
        assert screen_y == 200
        assert monitor_id == 1
    
    def test_scale_coordinates(self, coordinate_mapper):
        """测试坐标缩放"""
        # 正常缩放
        scaled_x, scaled_y = coordinate_mapper.scale_coordinates(
            100, 200, (1920, 1080), (960, 540)
        )
        assert scaled_x == 50  # 100 * (960/1920)
        assert scaled_y == 100  # 200 * (540/1080)
        
        # 放大缩放
        scaled_x, scaled_y = coordinate_mapper.scale_coordinates(
            50, 100, (960, 540), (1920, 1080)
        )
        assert scaled_x == 100
        assert scaled_y == 200
        
        # 零尺寸处理
        scaled_x, scaled_y = coordinate_mapper.scale_coordinates(
            100, 200, (0, 1080), (960, 540)
        )
        assert scaled_x == 100  # 应该返回原始坐标
        assert scaled_y == 200
    
    def test_transform_bounding_box(self, coordinate_mapper):
        """测试边界框变换"""
        # 创建测试边界框
        bbox = BoundingBox(x=100, y=200, width=50, height=30)
        
        # 基本变换（主显示器到副显示器）
        transformed_bbox = coordinate_mapper.transform_bounding_box(bbox, 0, 1)
        
        assert transformed_bbox.x == -1820  # (100 + 0) - 1920
        assert transformed_bbox.y == 200    # (200 + 0) - 0
        assert transformed_bbox.width == 50
        assert transformed_bbox.height == 30
        
        # 带缩放的变换
        transformed_bbox = coordinate_mapper.transform_bounding_box(
            bbox, 0, 1, scale_factor=(2.0, 1.5)
        )
        
        # 坐标应该先转换为虚拟坐标，然后缩放，最后转换到目标显示器
        expected_x = int((100 * 2.0)) - 1920  # 缩放后转换到显示器1
        expected_y = int((200 * 1.5))
        expected_width = int((150 * 2.0)) - int((100 * 2.0))  # 缩放后的宽度
        expected_height = int((230 * 1.5)) - int((200 * 1.5))  # 缩放后的高度
        
        assert transformed_bbox.x == expected_x
        assert transformed_bbox.y == expected_y
    
    def test_transform_detection_results(self, coordinate_mapper):
        """测试检测结果变换"""
        # 创建测试检测结果
        bbox = BoundingBox(x=100, y=200, width=50, height=30)
        result = DetectionResult(
            bbox=bbox,
            confidence=0.9,
            class_name="test",
            source=DetectionSource.YOLO
        )
        
        # 变换检测结果
        transformed_results = coordinate_mapper.transform_detection_results([result], 0, 1)
        
        assert len(transformed_results) == 1
        transformed_result = transformed_results[0]
        
        # 检查基本属性保持不变
        assert transformed_result.confidence == 0.9
        assert transformed_result.class_name == "test"
        assert transformed_result.source == DetectionSource.YOLO
        
        # 检查边界框已变换
        assert transformed_result.bbox.x != bbox.x
        
        # 检查元数据中包含变换信息
        assert "coordinate_transform" in transformed_result.metadata
        transform_info = transformed_result.metadata["coordinate_transform"]
        assert transform_info["from_monitor_id"] == 0
        assert transform_info["to_monitor_id"] == 1
    
    def test_relative_position(self, coordinate_mapper):
        """测试相对位置计算"""
        # 主显示器中心点
        rel_x, rel_y = coordinate_mapper.get_relative_position(960, 540, 0)
        assert abs(rel_x - 0.5) < 0.01  # 960/1920 = 0.5
        assert abs(rel_y - 0.5) < 0.01  # 540/1080 = 0.5
        
        # 左上角
        rel_x, rel_y = coordinate_mapper.get_relative_position(0, 0, 0)
        assert rel_x == 0.0
        assert rel_y == 0.0
        
        # 右下角（虚拟坐标）
        rel_x, rel_y = coordinate_mapper.get_relative_position(1920, 1080, 0)
        assert rel_x == 1.0
        assert rel_y == 1.0
        
        # 超出范围的坐标应该被限制
        rel_x, rel_y = coordinate_mapper.get_relative_position(2000, 1200, 0)
        assert rel_x == 1.0
        assert rel_y == 1.0
    
    def test_from_relative_position(self, coordinate_mapper):
        """测试从相对位置转换为绝对坐标"""
        # 中心点
        abs_x, abs_y = coordinate_mapper.from_relative_position(0.5, 0.5, 0)
        assert abs_x == 960  # 0.5 * 1920 + 0
        assert abs_y == 540  # 0.5 * 1080 + 0
        
        # 副显示器中心点
        abs_x, abs_y = coordinate_mapper.from_relative_position(0.5, 0.5, 1)
        assert abs_x == 2880  # 0.5 * 1920 + 1920
        assert abs_y == 540   # 0.5 * 1080 + 0
        
        # 边界值
        abs_x, abs_y = coordinate_mapper.from_relative_position(0.0, 0.0, 0)
        assert abs_x == 0
        assert abs_y == 0
        
        abs_x, abs_y = coordinate_mapper.from_relative_position(1.0, 1.0, 0)
        assert abs_x == 1920
        assert abs_y == 1080
    
    def test_coordinate_validation(self, coordinate_mapper):
        """测试坐标验证"""
        # 有效坐标
        assert coordinate_mapper.validate_coordinates(100, 200, 0) == True
        assert coordinate_mapper.validate_coordinates(2020, 200, 1) == True
        
        # 无效坐标
        assert coordinate_mapper.validate_coordinates(-100, 200, 0) == False
        assert coordinate_mapper.validate_coordinates(100, -200, 0) == False
        assert coordinate_mapper.validate_coordinates(2000, 200, 0) == False
        
        # 不指定显示器ID，检查所有显示器
        assert coordinate_mapper.validate_coordinates(100, 200) == True
        assert coordinate_mapper.validate_coordinates(2020, 200) == True
        assert coordinate_mapper.validate_coordinates(4000, 200) == False
    
    def test_clamp_to_monitor(self, coordinate_mapper):
        """测试坐标限制"""
        # 正常坐标
        clamped_x, clamped_y = coordinate_mapper.clamp_to_monitor(100, 200, 0)
        assert clamped_x == 100
        assert clamped_y == 200
        
        # 超出边界的坐标
        clamped_x, clamped_y = coordinate_mapper.clamp_to_monitor(-100, 200, 0)
        assert clamped_x == 0  # 限制到最小值
        assert clamped_y == 200
        
        clamped_x, clamped_y = coordinate_mapper.clamp_to_monitor(2000, 1200, 0)
        assert clamped_x == 1919  # 限制到最大值 (1920-1)
        assert clamped_y == 1079  # 限制到最大值 (1080-1)
    
    def test_transform_history(self, coordinate_mapper):
        """测试变换历史记录"""
        # 初始状态
        assert len(coordinate_mapper.get_transform_history()) == 0
        
        # 执行一些变换操作
        coordinate_mapper.screen_to_virtual(100, 200, 0)
        coordinate_mapper.virtual_to_screen(100, 200)
        coordinate_mapper.scale_coordinates(100, 200, (1920, 1080), (960, 540))
        
        # 检查历史记录
        history = coordinate_mapper.get_transform_history()
        assert len(history) == 3
        
        # 检查记录内容
        assert history[0]["type"] == "screen_to_virtual"
        assert history[1]["type"] == "virtual_to_screen"
        assert history[2]["type"] == "scale_coordinates"
        
        # 限制历史记录数量
        limited_history = coordinate_mapper.get_transform_history(limit=2)
        assert len(limited_history) == 2
        
        # 清空历史
        coordinate_mapper.clear_history()
        assert len(coordinate_mapper.get_transform_history()) == 0
    
    def test_stats(self, coordinate_mapper):
        """测试统计信息"""
        stats = coordinate_mapper.get_stats()
        
        assert "transform_history_count" in stats
        assert "max_history_size" in stats
        assert "monitor_manager_stats" in stats
        
        assert stats["transform_history_count"] == 0
        assert stats["max_history_size"] == 100
    
    def test_error_handling(self, coordinate_mapper):
        """测试错误处理"""
        # 测试无效显示器ID的处理
        virtual_x, virtual_y = coordinate_mapper.screen_to_virtual(100, 200, 999)
        assert virtual_x == 100  # 应该返回原始坐标
        assert virtual_y == 200
        
        # 测试相对位置计算中的无效显示器ID
        rel_x, rel_y = coordinate_mapper.get_relative_position(100, 200, 999)
        assert rel_x == 0.0
        assert rel_y == 0.0
