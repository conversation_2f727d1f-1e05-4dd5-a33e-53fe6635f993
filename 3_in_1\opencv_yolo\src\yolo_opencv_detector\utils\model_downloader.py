#!/usr/bin/env python3
"""
YOLO模型下载工具
提供预设YOLO模型的自动下载功能
"""

import os
import urllib.request
from pathlib import Path
from typing import Dict, Any, Optional
from urllib.parse import urlparse

class ModelDownloader:
    """YOLO模型下载器"""
    
    def __init__(self):
        """初始化下载器"""
        # YOLO模型下载URL映射（移除pose模型，专注界面检测）
        self.model_urls = {
            "yolov8n.pt": "https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8n.pt",
            "yolov8s.pt": "https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8s.pt",
            "yolov8m.pt": "https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8m.pt",
            "yolov8l.pt": "https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8l.pt",
            "yolov8x.pt": "https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8x.pt",
            "yolov8n-seg.pt": "https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8n-seg.pt"
        }

        # 模型信息（专注界面检测相关模型）
        self.model_info = {
            "yolov8n.pt": {"size": "6.2MB", "type": "detection", "use_case": "常规界面检测"},
            "yolov8s.pt": {"size": "21.5MB", "type": "detection", "use_case": "复杂界面检测"},
            "yolov8m.pt": {"size": "49.7MB", "type": "detection", "use_case": "专业软件界面"},
            "yolov8l.pt": {"size": "83.7MB", "type": "detection", "use_case": "高精度界面检测"},
            "yolov8x.pt": {"size": "136.7MB", "type": "detection", "use_case": "极限精度检测"},
            "yolov8n-seg.pt": {"size": "6.7MB", "type": "segmentation", "use_case": "复杂布局分割"}
        }
    
    def download_model(self, model_name: str, save_dir: str = "models", 
                      progress_callback: Optional[callable] = None) -> bool:
        """
        下载指定的YOLO模型
        
        Args:
            model_name: 模型文件名 (如 "yolov8n.pt")
            save_dir: 保存目录
            progress_callback: 进度回调函数
            
        Returns:
            下载是否成功
        """
        try:
            if model_name not in self.model_urls:
                raise ValueError(f"不支持的模型: {model_name}")
            
            # 创建保存目录
            save_path = Path(save_dir)
            save_path.mkdir(exist_ok=True)
            
            # 完整的文件路径
            file_path = save_path / model_name
            
            # 检查文件是否已存在
            if file_path.exists():
                print(f"模型文件已存在: {file_path}")
                return True
            
            # 获取下载URL
            url = self.model_urls[model_name]
            
            print(f"开始下载模型: {model_name}")
            print(f"下载地址: {url}")
            print(f"保存路径: {file_path}")
            
            # 下载文件
            def progress_hook(block_num, block_size, total_size):
                downloaded = block_num * block_size
                if total_size > 0:
                    progress = min(100, (downloaded / total_size) * 100)

                    if progress_callback:
                        # 调用进度回调，如果返回False则取消下载
                        if not progress_callback(progress, downloaded, total_size):
                            raise InterruptedError("下载被用户取消")
                    else:
                        # 控制台进度显示
                        if block_num % 100 == 0:  # 每100个块显示一次进度
                            print(f"下载进度: {progress:.1f}% ({downloaded}/{total_size} bytes)")

            # 执行下载
            urllib.request.urlretrieve(url, str(file_path), progress_hook)
            
            # 验证下载的文件
            if file_path.exists() and file_path.stat().st_size > 0:
                print(f"✅ 模型下载成功: {file_path}")
                return True
            else:
                print(f"❌ 模型下载失败: 文件不存在或为空")
                return False
                
        except Exception as e:
            print(f"❌ 下载模型失败: {e}")
            return False
    
    def get_model_info(self, model_name: str) -> Dict[str, Any]:
        """获取模型信息"""
        return self.model_info.get(model_name, {})
    
    def list_available_models(self) -> Dict[str, Dict[str, Any]]:
        """列出所有可用的模型"""
        return self.model_info.copy()
    
    def check_model_exists(self, model_name: str, models_dir: str = "models") -> bool:
        """检查模型文件是否存在"""
        file_path = Path(models_dir) / model_name
        return file_path.exists() and file_path.stat().st_size > 0

def download_all_models():
    """下载所有预设模型的便捷函数"""
    downloader = ModelDownloader()
    
    print("🚀 开始下载所有YOLO预设模型...")
    print("=" * 50)
    
    success_count = 0
    total_count = len(downloader.model_urls)
    
    for model_name in downloader.model_urls.keys():
        print(f"\n📥 下载模型: {model_name}")
        model_info = downloader.get_model_info(model_name)
        print(f"   大小: {model_info.get('size', '未知')}")
        print(f"   类型: {model_info.get('type', '未知')}")
        
        if downloader.download_model(model_name):
            success_count += 1
            print(f"   ✅ 下载成功")
        else:
            print(f"   ❌ 下载失败")
    
    print("\n" + "=" * 50)
    print(f"🎯 下载完成: {success_count}/{total_count} 个模型下载成功")
    
    if success_count == total_count:
        print("✅ 所有模型下载成功！")
    else:
        print(f"⚠️  有 {total_count - success_count} 个模型下载失败")

if __name__ == "__main__":
    # 直接运行此文件可以下载所有模型
    download_all_models()
