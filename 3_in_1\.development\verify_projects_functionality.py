#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
3_in_1 目录整理后的项目功能验证脚本

验证三个核心项目的基本功能是否正常工作
"""

import os
import sys
import importlib.util
from pathlib import Path

def test_project_import(project_name, main_module_path):
    """测试项目模块导入"""
    try:
        # 添加项目路径到sys.path
        project_path = Path(project_name).absolute()
        if str(project_path) not in sys.path:
            sys.path.insert(0, str(project_path))
        
        # 尝试导入主模块
        if main_module_path.exists():
            spec = importlib.util.spec_from_file_location("main", main_module_path)
            if spec and spec.loader:
                module = importlib.util.module_from_spec(spec)
                # 不执行模块，只检查是否能加载
                print(f"✅ {project_name}: 主模块加载成功")
                return True
            else:
                print(f"❌ {project_name}: 无法创建模块规范")
                return False
        else:
            print(f"❌ {project_name}: 主模块文件不存在 - {main_module_path}")
            return False
    except Exception as e:
        print(f"❌ {project_name}: 导入失败 - {str(e)}")
        return False

def test_directory_structure(project_name):
    """测试项目目录结构"""
    project_path = Path(project_name)
    if not project_path.exists():
        print(f"❌ {project_name}: 项目目录不存在")
        return False
    
    if not project_path.is_dir():
        print(f"❌ {project_name}: 不是有效目录")
        return False
    
    # 检查是否有Python文件
    python_files = list(project_path.rglob("*.py"))
    if not python_files:
        print(f"❌ {project_name}: 没有找到Python文件")
        return False
    
    print(f"✅ {project_name}: 目录结构正常 (包含 {len(python_files)} 个Python文件)")
    return True

def test_hidden_directories():
    """测试隐藏目录结构"""
    hidden_dirs = ['.project_files', '.development']
    results = []
    
    for hidden_dir in hidden_dirs:
        path = Path(hidden_dir)
        if path.exists() and path.is_dir():
            print(f"✅ 隐藏目录 {hidden_dir}: 存在且结构正常")
            results.append(True)
        else:
            print(f"❌ 隐藏目录 {hidden_dir}: 不存在或结构异常")
            results.append(False)
    
    return all(results)

def test_moved_files():
    """测试移动的文件是否在正确位置"""
    expected_files = {
        '.project_files/exported_scripts': '导出脚本目录',
        '.project_files/test_results': '测试结果目录',
        '.project_files/test_templates_preview': '测试模板预览目录',
        '.project_files/reports/代码导出功能实现报告.md': '代码导出功能报告',
        '.project_files/reports/任务完成报告_重构和优化.md': '任务完成报告',
        '.development/test_scripts/test_chinese_rendering_refactor.py': '中文渲染重构测试',
        '.development/test_scripts/test_code_export_functionality.py': '代码导出功能测试',
        '.development/test_scripts/test_template_preview_optimization.py': '模板预览优化测试'
    }
    
    all_found = True
    for file_path, description in expected_files.items():
        path = Path(file_path)
        if path.exists():
            print(f"✅ {description}: 文件位置正确")
        else:
            print(f"❌ {description}: 文件未找到 - {file_path}")
            all_found = False
    
    return all_found

def main():
    """主验证函数"""
    print("🔍 开始验证 3_in_1 目录整理后的项目功能...")
    print("=" * 60)
    
    # 切换到脚本所在目录的上级目录（3_in_1目录）
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    os.chdir(project_root)
    
    print(f"📁 当前工作目录: {os.getcwd()}")
    print()
    
    # 测试项目列表
    projects = [
        {
            'name': 'umiocr_hide',
            'main_module': 'umiocr_hide/main.py',
            'description': 'UmiOCR项目'
        },
        {
            'name': 'opencv_yolo',
            'main_module': 'opencv_yolo/src/yolo_opencv_detector/main_v2.py',
            'description': 'OpenCV YOLO项目'
        },
        {
            'name': 'pywinauto_tree',
            'main_module': 'pywinauto_tree/control_tree_viewer.py',
            'description': 'PyWinAuto项目'
        }
    ]
    
    # 验证结果
    results = {
        'directory_structure': [],
        'module_import': [],
        'hidden_directories': False,
        'moved_files': False
    }
    
    # 1. 测试目录结构
    print("1️⃣ 测试项目目录结构...")
    for project in projects:
        result = test_directory_structure(project['name'])
        results['directory_structure'].append(result)
    print()
    
    # 2. 测试模块导入（简化版，避免实际执行）
    print("2️⃣ 测试项目模块结构...")
    for project in projects:
        main_path = Path(project['main_module'])
        result = test_project_import(project['name'], main_path)
        results['module_import'].append(result)
    print()
    
    # 3. 测试隐藏目录
    print("3️⃣ 测试隐藏目录结构...")
    results['hidden_directories'] = test_hidden_directories()
    print()
    
    # 4. 测试移动的文件
    print("4️⃣ 测试文件移动结果...")
    results['moved_files'] = test_moved_files()
    print()
    
    # 5. 生成总结报告
    print("📊 验证结果总结:")
    print("=" * 60)
    
    total_tests = 0
    passed_tests = 0
    
    # 目录结构测试
    dir_passed = sum(results['directory_structure'])
    dir_total = len(results['directory_structure'])
    print(f"📁 目录结构测试: {dir_passed}/{dir_total} 通过")
    total_tests += dir_total
    passed_tests += dir_passed
    
    # 模块导入测试
    import_passed = sum(results['module_import'])
    import_total = len(results['module_import'])
    print(f"📦 模块结构测试: {import_passed}/{import_total} 通过")
    total_tests += import_total
    passed_tests += import_passed
    
    # 隐藏目录测试
    hidden_passed = 1 if results['hidden_directories'] else 0
    print(f"🔒 隐藏目录测试: {hidden_passed}/1 通过")
    total_tests += 1
    passed_tests += hidden_passed
    
    # 文件移动测试
    moved_passed = 1 if results['moved_files'] else 0
    print(f"📋 文件移动测试: {moved_passed}/1 通过")
    total_tests += 1
    passed_tests += moved_passed
    
    print("-" * 60)
    print(f"🎯 总体结果: {passed_tests}/{total_tests} 测试通过")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！目录整理成功，项目功能正常！")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关问题。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
