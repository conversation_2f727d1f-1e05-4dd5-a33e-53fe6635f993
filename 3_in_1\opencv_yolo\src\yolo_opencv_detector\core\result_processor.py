# -*- coding: utf-8 -*-
"""
检测结果处理器
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

import json
import csv
from pathlib import Path
from typing import List, Dict, Any, Optional, Union, Tuple
import xml.etree.ElementTree as ET
from datetime import datetime
import numpy as np

from ..utils.logger import Logger
from ..utils.data_structures import DetectionResult, BoundingBox, DetectionSession


class ResultProcessor:
    """检测结果处理器"""
    
    def __init__(self):
        """初始化结果处理器"""
        self.logger = Logger().get_logger(__name__)
    
    def filter_results(self, 
                      results: List[DetectionResult],
                      min_confidence: Optional[float] = None,
                      max_confidence: Optional[float] = None,
                      class_ids: Optional[List[int]] = None,
                      class_names: Optional[List[str]] = None,
                      template_ids: Optional[List[str]] = None,
                      min_area: Optional[int] = None,
                      max_area: Optional[int] = None) -> List[DetectionResult]:
        """
        过滤检测结果
        
        Args:
            results: 检测结果列表
            min_confidence: 最小置信度
            max_confidence: 最大置信度
            class_ids: 允许的类别ID列表
            class_names: 允许的类别名称列表
            template_ids: 允许的模板ID列表
            min_area: 最小面积
            max_area: 最大面积
            
        Returns:
            List[DetectionResult]: 过滤后的结果
        """
        filtered_results = []
        
        for result in results:
            # 置信度过滤
            if min_confidence is not None and result.confidence < min_confidence:
                continue
            if max_confidence is not None and result.confidence > max_confidence:
                continue
            
            # 类别过滤
            if class_ids is not None and result.class_id not in class_ids:
                continue
            if class_names is not None and result.class_name not in class_names:
                continue
            
            # 模板过滤
            if template_ids is not None and result.template_id not in template_ids:
                continue
            
            # 面积过滤
            area = result.bbox.area
            if min_area is not None and area < min_area:
                continue
            if max_area is not None and area > max_area:
                continue
            
            filtered_results.append(result)
        
        self.logger.debug(f"结果过滤完成: {len(results)} -> {len(filtered_results)}")
        return filtered_results
    
    def sort_results(self, 
                    results: List[DetectionResult],
                    sort_by: str = "confidence",
                    reverse: bool = True) -> List[DetectionResult]:
        """
        排序检测结果
        
        Args:
            results: 检测结果列表
            sort_by: 排序字段 ("confidence", "area", "x", "y", "timestamp")
            reverse: 是否降序
            
        Returns:
            List[DetectionResult]: 排序后的结果
        """
        try:
            if sort_by == "confidence":
                key_func = lambda x: x.confidence
            elif sort_by == "area":
                key_func = lambda x: x.bbox.area
            elif sort_by == "x":
                key_func = lambda x: x.bbox.x
            elif sort_by == "y":
                key_func = lambda x: x.bbox.y
            elif sort_by == "timestamp":
                key_func = lambda x: x.timestamp
            else:
                self.logger.warning(f"未知的排序字段: {sort_by}")
                return results
            
            sorted_results = sorted(results, key=key_func, reverse=reverse)
            self.logger.debug(f"结果排序完成: 按 {sort_by} {'降序' if reverse else '升序'}")
            return sorted_results
            
        except Exception as e:
            self.logger.error(f"结果排序失败: {e}")
            return results
    
    def group_results(self, 
                     results: List[DetectionResult],
                     group_by: str = "class_name") -> Dict[str, List[DetectionResult]]:
        """
        分组检测结果
        
        Args:
            results: 检测结果列表
            group_by: 分组字段 ("class_name", "class_id", "template_id", "source")
            
        Returns:
            Dict[str, List[DetectionResult]]: 分组后的结果
        """
        groups = {}
        
        try:
            for result in results:
                if group_by == "class_name":
                    key = result.class_name or "unknown"
                elif group_by == "class_id":
                    key = str(result.class_id) if result.class_id is not None else "unknown"
                elif group_by == "template_id":
                    key = result.template_id or "unknown"
                elif group_by == "source":
                    key = result.source.value
                else:
                    self.logger.warning(f"未知的分组字段: {group_by}")
                    key = "unknown"
                
                if key not in groups:
                    groups[key] = []
                groups[key].append(result)
            
            self.logger.debug(f"结果分组完成: 按 {group_by} 分为 {len(groups)} 组")
            return groups
            
        except Exception as e:
            self.logger.error(f"结果分组失败: {e}")
            return {"error": results}
    
    def calculate_statistics(self, results: List[DetectionResult]) -> Dict[str, Any]:
        """
        计算检测结果统计信息
        
        Args:
            results: 检测结果列表
            
        Returns:
            Dict[str, Any]: 统计信息
        """
        if not results:
            return {}
        
        try:
            # 基本统计
            confidences = [r.confidence for r in results]
            areas = [r.bbox.area for r in results]
            
            # 按来源分组统计
            source_counts = {}
            for result in results:
                source = result.source.value
                source_counts[source] = source_counts.get(source, 0) + 1
            
            # 按类别分组统计
            class_counts = {}
            for result in results:
                class_name = result.class_name or "unknown"
                class_counts[class_name] = class_counts.get(class_name, 0) + 1
            
            stats = {
                "total_count": len(results),
                "confidence_stats": {
                    "mean": np.mean(confidences),
                    "std": np.std(confidences),
                    "min": np.min(confidences),
                    "max": np.max(confidences),
                    "median": np.median(confidences)
                },
                "area_stats": {
                    "mean": np.mean(areas),
                    "std": np.std(areas),
                    "min": np.min(areas),
                    "max": np.max(areas),
                    "median": np.median(areas)
                },
                "source_distribution": source_counts,
                "class_distribution": class_counts
            }
            
            return stats
            
        except Exception as e:
            self.logger.error(f"统计计算失败: {e}")
            return {}
    
    def export_to_json(self, 
                      results: List[DetectionResult],
                      output_path: Union[str, Path],
                      include_metadata: bool = True) -> bool:
        """
        导出结果为JSON格式
        
        Args:
            results: 检测结果列表
            output_path: 输出文件路径
            include_metadata: 是否包含元数据
            
        Returns:
            bool: 导出是否成功
        """
        try:
            output_path = Path(output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 转换为字典格式
            export_data = {
                "export_time": datetime.now().isoformat(),
                "total_count": len(results),
                "results": []
            }
            
            for result in results:
                result_dict = result.to_dict()
                if not include_metadata:
                    result_dict.pop("metadata", None)
                export_data["results"].append(result_dict)
            
            # 保存文件
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"结果导出成功: {len(results)} 个结果导出到 {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"JSON导出失败: {e}")
            return False
    
    def export_to_csv(self, 
                     results: List[DetectionResult],
                     output_path: Union[str, Path]) -> bool:
        """
        导出结果为CSV格式
        
        Args:
            results: 检测结果列表
            output_path: 输出文件路径
            
        Returns:
            bool: 导出是否成功
        """
        try:
            output_path = Path(output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 定义CSV字段
            fieldnames = [
                "timestamp", "source", "confidence", "class_id", "class_name",
                "template_id", "bbox_x", "bbox_y", "bbox_width", "bbox_height",
                "bbox_area", "bbox_center_x", "bbox_center_y"
            ]
            
            with open(output_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                
                for result in results:
                    row = {
                        "timestamp": datetime.fromtimestamp(result.timestamp).isoformat(),
                        "source": result.source.value,
                        "confidence": result.confidence,
                        "class_id": result.class_id,
                        "class_name": result.class_name,
                        "template_id": result.template_id,
                        "bbox_x": result.bbox.x,
                        "bbox_y": result.bbox.y,
                        "bbox_width": result.bbox.width,
                        "bbox_height": result.bbox.height,
                        "bbox_area": result.bbox.area,
                        "bbox_center_x": result.bbox.center_x,
                        "bbox_center_y": result.bbox.center_y
                    }
                    writer.writerow(row)
            
            self.logger.info(f"CSV导出成功: {len(results)} 个结果导出到 {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"CSV导出失败: {e}")
            return False
    
    def export_to_xml(self, 
                     results: List[DetectionResult],
                     output_path: Union[str, Path],
                     image_info: Optional[Dict[str, Any]] = None) -> bool:
        """
        导出结果为XML格式（PASCAL VOC格式）
        
        Args:
            results: 检测结果列表
            output_path: 输出文件路径
            image_info: 图像信息
            
        Returns:
            bool: 导出是否成功
        """
        try:
            output_path = Path(output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 创建XML根元素
            root = ET.Element("annotation")
            
            # 添加图像信息
            if image_info:
                filename = ET.SubElement(root, "filename")
                filename.text = image_info.get("filename", "unknown.jpg")
                
                size = ET.SubElement(root, "size")
                width = ET.SubElement(size, "width")
                width.text = str(image_info.get("width", 0))
                height = ET.SubElement(size, "height")
                height.text = str(image_info.get("height", 0))
                depth = ET.SubElement(size, "depth")
                depth.text = str(image_info.get("depth", 3))
            
            # 添加检测结果
            for result in results:
                obj = ET.SubElement(root, "object")
                
                name = ET.SubElement(obj, "name")
                name.text = result.class_name or "unknown"
                
                confidence = ET.SubElement(obj, "confidence")
                confidence.text = str(result.confidence)
                
                bndbox = ET.SubElement(obj, "bndbox")
                xmin = ET.SubElement(bndbox, "xmin")
                xmin.text = str(result.bbox.x)
                ymin = ET.SubElement(bndbox, "ymin")
                ymin.text = str(result.bbox.y)
                xmax = ET.SubElement(bndbox, "xmax")
                xmax.text = str(result.bbox.x2)
                ymax = ET.SubElement(bndbox, "ymax")
                ymax.text = str(result.bbox.y2)
            
            # 保存XML文件
            tree = ET.ElementTree(root)
            tree.write(output_path, encoding='utf-8', xml_declaration=True)
            
            self.logger.info(f"XML导出成功: {len(results)} 个结果导出到 {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"XML导出失败: {e}")
            return False
    
    def load_from_json(self, input_path: Union[str, Path]) -> List[DetectionResult]:
        """
        从JSON文件加载检测结果
        
        Args:
            input_path: 输入文件路径
            
        Returns:
            List[DetectionResult]: 检测结果列表
        """
        try:
            input_path = Path(input_path)
            if not input_path.exists():
                self.logger.error(f"文件不存在: {input_path}")
                return []
            
            with open(input_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            results = []
            for result_dict in data.get("results", []):
                result = DetectionResult.from_dict(result_dict)
                results.append(result)
            
            self.logger.info(f"JSON加载成功: 从 {input_path} 加载了 {len(results)} 个结果")
            return results
            
        except Exception as e:
            self.logger.error(f"JSON加载失败: {e}")
            return []
