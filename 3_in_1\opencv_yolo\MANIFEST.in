# MANIFEST.in - 包含额外文件的清单

# 包含文档文件
include README.md
include LICENSE
include CHANGELOG.md
include requirements.txt
include requirements-dev.txt

# 包含配置文件
recursive-include configs *.yaml *.yml *.json

# 包含GUI资源
recursive-include src/yolo_opencv_detector/gui/icons *.png *.ico *.svg
recursive-include src/yolo_opencv_detector/gui/styles *.qss *.css

# 包含测试文件
recursive-include tests *.py

# 包含脚本文件
recursive-include scripts *.py *.bat *.sh

# 包含示例文件
recursive-include examples *.py *.yaml *.md

# 包含文档
recursive-include docs *.md *.rst *.txt

# 排除不需要的文件
global-exclude *.pyc
global-exclude *.pyo
global-exclude *.pyd
global-exclude __pycache__
global-exclude .git*
global-exclude .pytest_cache
global-exclude *.log
global-exclude .coverage
global-exclude htmlcov
global-exclude build
global-exclude dist
global-exclude *.egg-info
