# 模板管理和检测可视化功能说明

## 📋 问题1: 模板图片设置位置

### 🎯 模板管理位置
模板图片的设置和管理在GUI界面的 **"📋 模板"** 标签页中进行：

1. **访问路径**: 主窗口 → 左侧标签页 → "📋 模板"
2. **管理面板**: `TemplatePanelV2` 类提供完整的模板管理功能

### 📁 模板文件存储位置
```
项目根目录/
├── templates/              # 模板图片存储目录
│   ├── test_template.png   # 测试模板
│   ├── w.png              # W字符模板
│   ├── region_*.png       # 区域模板
│   └── template_*.png     # 用户创建的模板
```

### 🔧 模板管理功能

#### 添加模板
- **方法1**: 点击 "📁 选择文件" 按钮，从本地选择图片文件
- **方法2**: 点击 "📸 截取模板" 按钮，从屏幕截取区域创建模板
- **支持格式**: PNG, JPG, JPEG, BMP

#### 删除模板
- 选中模板后点击 "🗑️ 删除" 按钮
- 确认删除后从列表和配置文件中移除

#### 修改模板
- 选中模板后点击 "✏️ 编辑" 按钮
- 可修改名称、类别、描述等信息

### ⚙️ 源代码中的模板设置

#### 在源代码编辑器中修改模板名称
在 **"🎯 简化检测器"** 标签页的代码中，找到配置区域：

```python
# ==================== 配置区域 ====================
# 在这里修改要检测的模板名称
TEMPLATE_NAME = "test_template.png"  # 修改这里来指定要检测的模板
CONFIDENCE_THRESHOLD = 0.3           # 检测置信度阈值
SAVE_RESULTS = True                  # 是否保存检测结果图像
SHOW_VISUALIZATION = True            # 是否显示可视化结果
# ================================================
```

#### 实时读取GUI设置
**目前源代码编辑器中的代码是独立的**，不会实时读取GUI的设置。如果需要实时读取，可以：

1. **手动同步**: 在GUI中选择模板后，手动复制模板名称到源代码中
2. **代码修改**: 在源代码中添加读取配置的功能（需要导入项目模块）

### 📝 配置文件存储
模板配置保存在：
```
configs/templates.json
```

## 🎨 问题2: 检测结果可视化

### ✨ 已实现的可视化功能

#### 1. 边界框绘制
- **功能**: 在检测到的对象周围绘制彩色边界框
- **颜色**: 根据目标类别自动选择不同颜色
- **厚度**: 2像素边框，清晰可见

#### 2. 类别名称和置信度显示
- **位置**: 边界框上方或下方
- **内容**: "类别名称: 置信度"
- **背景**: 彩色背景确保文字清晰可读

#### 3. 中心点标注
- **标记**: 实心圆点 + 空心圆圈
- **坐标**: 显示精确的中心点坐标 "(x, y)"
- **颜色**: 与边界框颜色一致

#### 4. 序号标注
- **功能**: 为每个检测目标添加序号
- **位置**: 边界框左上角
- **用途**: 便于识别和引用特定目标

#### 5. 总体信息显示
- **内容**: "检测到 X 个目标"
- **位置**: 图像左上角
- **颜色**: 绿色文字

### 🎨 颜色映射
```python
color_map = {
    'person': (0, 255, 0),      # 绿色
    'car': (255, 0, 0),         # 蓝色
    'truck': (0, 0, 255),       # 红色
    'bus': (255, 255, 0),       # 青色
    'bicycle': (255, 0, 255),   # 紫色
    'motorbike': (0, 255, 255), # 黄色
    'laptop': (128, 0, 128),    # 紫色
    'cell phone': (255, 165, 0), # 橙色
    'book': (0, 128, 255),      # 橙蓝色
    'chair': (128, 128, 0),     # 橄榄色
    'bottle': (0, 128, 128),    # 青绿色
}
```

### 🚀 使用方法

#### 在源代码编辑器中运行
1. 打开源代码编辑器（工具栏 "📄 源代码"）
2. 选择 "🎯 简化检测器" 标签页
3. 勾选 "📝 编辑模式"（如需修改配置）
4. 点击 "▶️ 运行代码"
5. 查看输出面板的执行结果

#### 可视化输出
- **控制台输出**: 详细的检测信息和坐标
- **图像窗口**: 带标注的检测结果图像
- **保存文件**: 自动保存到 `screenshots/` 目录

### 📊 可视化示例输出
```
🚀 开始检测和可视化...
置信度阈值: 0.3
保存结果: True
显示可视化: True
--------------------------------------------------
📸 开始屏幕检测...
✅ 截图成功: (1920, 1080, 3)
🔍 开始检测，图像尺寸: (1920, 1080, 3)
✅ 检测完成，发现 2 个目标
🎯 屏幕检测完成，发现 2 个目标
📋 检测结果: 2 个目标
------------------------------------------------------------
目标 1:
  类别: laptop
  置信度: 0.749
  中心点: (960, 540)
  边界框: {'x': 49, 'y': 0, 'width': 1822, 'height': 1080}

目标 2:
  类别: person
  置信度: 0.623
  中心点: (1200, 300)
  边界框: {'x': 1100, 'y': 200, 'width': 200, 'height': 200}

🎨 开始绘制 2 个检测结果...
💾 可视化结果已保存: screenshots/detection_result_1703123456.png
✅ 可视化完成
🖼️ 检测结果窗口已打开，按任意键关闭...
```

### 🔧 自定义配置
在源代码中可以修改以下配置：
```python
TEMPLATE_NAME = "your_template.png"  # 模板名称
CONFIDENCE_THRESHOLD = 0.3           # 置信度阈值
SAVE_RESULTS = True                  # 是否保存结果
SHOW_VISUALIZATION = True            # 是否显示可视化
```

### 💡 使用建议

#### 最佳实践
1. **置信度设置**: 建议从0.3开始，根据实际效果调整
2. **模板质量**: 使用清晰、特征明显的模板图片
3. **保存结果**: 启用保存功能便于后续分析
4. **多次测试**: 在不同场景下测试检测效果

#### 故障排除
1. **无检测结果**: 降低置信度阈值或更换模板
2. **图像窗口无法显示**: 检查是否安装了OpenCV的GUI支持
3. **保存失败**: 确保screenshots目录存在且有写入权限

## 🎯 总结

1. **模板管理**: 在GUI的"📋 模板"标签页中进行，支持添加、删除、修改
2. **模板文件**: 存储在`templates/`目录下
3. **源代码配置**: 修改`TEMPLATE_NAME`变量指定要检测的模板
4. **可视化功能**: 完整实现边界框、标签、坐标、序号等标注
5. **实时预览**: 支持图像窗口显示和文件保存
6. **颜色区分**: 不同类别使用不同颜色便于识别

这些功能为用户提供了完整的模板管理和检测可视化体验，便于测试和验证检测效果的准确性。
