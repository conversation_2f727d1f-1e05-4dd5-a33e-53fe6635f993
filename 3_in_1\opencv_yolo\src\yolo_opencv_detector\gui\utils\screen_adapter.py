#!/usr/bin/env python3
"""
屏幕适配工具
提供增强的屏幕分辨率和多显示器支持
"""

import logging
from typing import Tuple, Dict, List, Optional
from dataclasses import dataclass
from enum import Enum

from PyQt6.QtWidgets import QApplication, QWidget
from PyQt6.QtCore import QRect, QSize
from PyQt6.QtGui import QScreen

class ScreenCategory(Enum):
    """屏幕分类"""
    ULTRA_LARGE = "ultra_large"    # ≥3840px
    LARGE = "large"                # ≥1920px
    MEDIUM = "medium"              # ≥1366px
    SMALL = "small"                # ≥1024px
    ULTRA_SMALL = "ultra_small"    # <1024px

class AspectRatioCategory(Enum):
    """屏幕比例分类"""
    ULTRA_WIDE = "ultra_wide"      # ≥21:9
    WIDE = "wide"                  # 16:9, 16:10
    STANDARD = "standard"          # 4:3, 5:4
    PORTRAIT = "portrait"          # 高>宽

@dataclass
class ScreenInfo:
    """屏幕信息"""
    index: int
    geometry: QRect
    available_geometry: QRect
    dpi: float
    scale_factor: float
    is_primary: bool
    category: ScreenCategory
    aspect_ratio: float
    aspect_category: AspectRatioCategory

@dataclass
class WindowSizeConfig:
    """窗口尺寸配置"""
    width: int
    height: int
    min_width: int
    min_height: int
    max_width: int
    max_height: int
    utilization_ratio: float

class ScreenAdapter:
    """增强的屏幕适配器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.screens_info: List[ScreenInfo] = []
        self.primary_screen_info: Optional[ScreenInfo] = None
        self._update_screens_info()
    
    def _update_screens_info(self):
        """更新屏幕信息"""
        app = QApplication.instance()
        if not app:
            return
        
        screens = app.screens()
        primary_screen = app.primaryScreen()
        self.screens_info.clear()
        
        for i, screen in enumerate(screens):
            geometry = screen.geometry()
            available = screen.availableGeometry()
            dpi = screen.logicalDotsPerInch()
            scale_factor = screen.devicePixelRatio()
            is_primary = (screen == primary_screen)
            
            # 计算屏幕分类
            width = geometry.width()
            height = geometry.height()
            
            if width >= 3840:
                category = ScreenCategory.ULTRA_LARGE
            elif width >= 1920:
                category = ScreenCategory.LARGE
            elif width >= 1366:
                category = ScreenCategory.MEDIUM
            elif width >= 1024:
                category = ScreenCategory.SMALL
            else:
                category = ScreenCategory.ULTRA_SMALL
            
            # 计算宽高比
            aspect_ratio = width / height
            
            if aspect_ratio >= 2.3:  # 21:9 ≈ 2.33
                aspect_category = AspectRatioCategory.ULTRA_WIDE
            elif aspect_ratio >= 1.5:  # 16:9 ≈ 1.78, 16:10 = 1.6
                aspect_category = AspectRatioCategory.WIDE
            elif aspect_ratio >= 1.0:
                aspect_category = AspectRatioCategory.STANDARD
            else:
                aspect_category = AspectRatioCategory.PORTRAIT
            
            screen_info = ScreenInfo(
                index=i,
                geometry=geometry,
                available_geometry=available,
                dpi=dpi,
                scale_factor=scale_factor,
                is_primary=is_primary,
                category=category,
                aspect_ratio=aspect_ratio,
                aspect_category=aspect_category
            )
            
            self.screens_info.append(screen_info)
            
            if is_primary:
                self.primary_screen_info = screen_info
        
        self.logger.info(f"检测到 {len(self.screens_info)} 个显示器")
    
    def get_optimal_window_size(self, 
                               target_screen: Optional[int] = None,
                               prefer_width: Optional[int] = None,
                               prefer_height: Optional[int] = None) -> WindowSizeConfig:
        """获取最优窗口尺寸"""
        
        # 选择目标屏幕
        if target_screen is not None and 0 <= target_screen < len(self.screens_info):
            screen_info = self.screens_info[target_screen]
        else:
            screen_info = self.primary_screen_info or self.screens_info[0]
        
        available = screen_info.available_geometry
        screen_width = available.width()
        screen_height = available.height()
        
        # 基础尺寸计算
        base_config = self._calculate_base_size(screen_info)
        
        # 宽高比优化
        optimized_config = self._optimize_for_aspect_ratio(base_config, screen_info)
        
        # 高DPI适配
        final_config = self._adapt_for_high_dpi(optimized_config, screen_info)
        
        # 用户偏好调整
        if prefer_width or prefer_height:
            final_config = self._apply_user_preferences(
                final_config, screen_info, prefer_width, prefer_height
            )
        
        return final_config
    
    def _calculate_base_size(self, screen_info: ScreenInfo) -> WindowSizeConfig:
        """计算基础窗口尺寸"""
        available = screen_info.available_geometry
        screen_width = available.width()
        screen_height = available.height()
        
        # 根据屏幕分类确定基础策略
        if screen_info.category == ScreenCategory.ULTRA_LARGE:
            # 超大屏幕：固定尺寸，避免过大
            width = 1600
            height = 1000
            utilization = 0.7
        elif screen_info.category == ScreenCategory.LARGE:
            # 大屏幕：80%利用率
            width = min(1400, int(screen_width * 0.8))
            height = min(900, int(screen_height * 0.8))
            utilization = 0.8
        elif screen_info.category == ScreenCategory.MEDIUM:
            # 中等屏幕：85%利用率
            width = min(1200, int(screen_width * 0.85))
            height = min(800, int(screen_height * 0.85))
            utilization = 0.85
        elif screen_info.category == ScreenCategory.SMALL:
            # 小屏幕：90%利用率
            width = min(1000, int(screen_width * 0.9))
            height = min(700, int(screen_height * 0.9))
            utilization = 0.9
        else:
            # 超小屏幕：95%利用率，紧凑布局
            width = min(900, int(screen_width * 0.95))
            height = min(600, int(screen_height * 0.95))
            utilization = 0.95
        
        return WindowSizeConfig(
            width=width,
            height=height,
            min_width=800,  # 绝对最小宽度
            min_height=500,  # 绝对最小高度
            max_width=1800,  # 最大宽度限制
            max_height=1200,  # 最大高度限制
            utilization_ratio=utilization
        )
    
    def _optimize_for_aspect_ratio(self, config: WindowSizeConfig, 
                                  screen_info: ScreenInfo) -> WindowSizeConfig:
        """根据宽高比优化"""
        
        if screen_info.aspect_category == AspectRatioCategory.ULTRA_WIDE:
            # 超宽屏：增加宽度，保持高度
            config.width = min(config.width + 200, config.max_width)
        elif screen_info.aspect_category == AspectRatioCategory.PORTRAIT:
            # 竖屏：减少宽度，增加高度
            config.width = max(config.width - 100, config.min_width)
            config.height = min(config.height + 100, config.max_height)
        
        return config
    
    def _adapt_for_high_dpi(self, config: WindowSizeConfig, 
                           screen_info: ScreenInfo) -> WindowSizeConfig:
        """高DPI适配"""
        
        if screen_info.scale_factor > 1.5:
            # 高DPI屏幕：适当增加尺寸
            scale_adjustment = min(screen_info.scale_factor, 2.0)
            config.width = int(config.width * scale_adjustment)
            config.height = int(config.height * scale_adjustment)
            
            # 确保不超过屏幕限制
            available = screen_info.available_geometry
            config.width = min(config.width, int(available.width() * 0.9))
            config.height = min(config.height, int(available.height() * 0.9))
        
        return config
    
    def _apply_user_preferences(self, config: WindowSizeConfig, 
                               screen_info: ScreenInfo,
                               prefer_width: Optional[int],
                               prefer_height: Optional[int]) -> WindowSizeConfig:
        """应用用户偏好"""
        
        available = screen_info.available_geometry
        
        if prefer_width:
            config.width = max(config.min_width, 
                             min(prefer_width, available.width() - 100))
        
        if prefer_height:
            config.height = max(config.min_height,
                              min(prefer_height, available.height() - 100))
        
        return config
    
    def get_window_position(self, window_size: WindowSizeConfig,
                           target_screen: Optional[int] = None) -> Tuple[int, int]:
        """获取窗口位置（居中）"""
        
        # 选择目标屏幕
        if target_screen is not None and 0 <= target_screen < len(self.screens_info):
            screen_info = self.screens_info[target_screen]
        else:
            screen_info = self.primary_screen_info or self.screens_info[0]
        
        available = screen_info.available_geometry
        
        # 计算居中位置
        x = available.x() + (available.width() - window_size.width) // 2
        y = available.y() + (available.height() - window_size.height) // 2
        
        return (x, y)
    
    def apply_to_window(self, window: QWidget, 
                       target_screen: Optional[int] = None,
                       prefer_width: Optional[int] = None,
                       prefer_height: Optional[int] = None):
        """应用屏幕适配到窗口"""
        
        # 获取最优尺寸
        size_config = self.get_optimal_window_size(
            target_screen, prefer_width, prefer_height
        )
        
        # 获取位置
        x, y = self.get_window_position(size_config, target_screen)
        
        # 应用到窗口
        window.resize(size_config.width, size_config.height)
        window.move(x, y)
        window.setMinimumSize(size_config.min_width, size_config.min_height)
        
        self.logger.info(
            f"窗口适配完成: {size_config.width}×{size_config.height} "
            f"at ({x}, {y})"
        )
        
        return size_config
    
    def get_screens_summary(self) -> Dict[str, any]:
        """获取屏幕信息摘要"""
        if not self.screens_info:
            return {}
        
        summary = {
            "total_screens": len(self.screens_info),
            "primary_screen": None,
            "screens": []
        }
        
        for screen_info in self.screens_info:
            screen_data = {
                "index": screen_info.index,
                "resolution": f"{screen_info.geometry.width()}×{screen_info.geometry.height()}",
                "available": f"{screen_info.available_geometry.width()}×{screen_info.available_geometry.height()}",
                "dpi": screen_info.dpi,
                "scale_factor": screen_info.scale_factor,
                "is_primary": screen_info.is_primary,
                "category": screen_info.category.value,
                "aspect_ratio": round(screen_info.aspect_ratio, 2),
                "aspect_category": screen_info.aspect_category.value
            }
            
            summary["screens"].append(screen_data)
            
            if screen_info.is_primary:
                summary["primary_screen"] = screen_data
        
        return summary
    
    def get_robustness_score(self) -> Dict[str, any]:
        """获取鲁棒性评分"""
        if not self.primary_screen_info:
            return {"score": 0, "details": "无法检测屏幕信息"}
        
        screen = self.primary_screen_info
        scores = {}
        
        # 分辨率适配评分
        if screen.category == ScreenCategory.ULTRA_LARGE:
            scores["resolution"] = 95
        elif screen.category == ScreenCategory.LARGE:
            scores["resolution"] = 90
        elif screen.category == ScreenCategory.MEDIUM:
            scores["resolution"] = 85
        elif screen.category == ScreenCategory.SMALL:
            scores["resolution"] = 70
        else:
            scores["resolution"] = 50
        
        # 宽高比适配评分
        if screen.aspect_category in [AspectRatioCategory.WIDE, AspectRatioCategory.STANDARD]:
            scores["aspect_ratio"] = 90
        elif screen.aspect_category == AspectRatioCategory.ULTRA_WIDE:
            scores["aspect_ratio"] = 80
        else:
            scores["aspect_ratio"] = 70
        
        # 高DPI适配评分
        if screen.scale_factor <= 1.25:
            scores["high_dpi"] = 90
        elif screen.scale_factor <= 2.0:
            scores["high_dpi"] = 75
        else:
            scores["high_dpi"] = 60
        
        # 多显示器支持评分
        if len(self.screens_info) == 1:
            scores["multi_monitor"] = 80
        elif len(self.screens_info) <= 3:
            scores["multi_monitor"] = 70
        else:
            scores["multi_monitor"] = 60
        
        total_score = sum(scores.values()) / len(scores)
        
        return {
            "total_score": round(total_score, 1),
            "details": scores,
            "screen_info": {
                "category": screen.category.value,
                "resolution": f"{screen.geometry.width()}×{screen.geometry.height()}",
                "aspect_ratio": round(screen.aspect_ratio, 2),
                "scale_factor": screen.scale_factor
            }
        }
