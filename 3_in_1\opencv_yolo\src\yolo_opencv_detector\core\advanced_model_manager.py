# -*- coding: utf-8 -*-
"""
先进深度学习模型管理器

集成SAM、CLIP、DINO等先进模型，提供统一的模型管理和推理接口。

Created: 2025-07-13
Author: Augment Agent
"""

import torch
import numpy as np
from typing import Dict, List, Optional, Union, Tuple, Any
from pathlib import Path
from dataclasses import dataclass
import logging
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)


@dataclass
class ModelConfig:
    """模型配置"""
    name: str
    model_path: str
    device: str = "auto"
    precision: str = "fp16"  # fp16, fp32, int8
    batch_size: int = 1
    input_size: Tuple[int, int] = (640, 640)
    confidence_threshold: float = 0.5
    enabled: bool = True


class BaseAdvancedModel(ABC):
    """先进模型基类"""
    
    def __init__(self, config: ModelConfig):
        self.config = config
        self.model = None
        self.is_loaded = False
        self.device = self._resolve_device(config.device)
        
    def _resolve_device(self, device: str) -> str:
        """解析设备"""
        if device == "auto":
            return "cuda" if torch.cuda.is_available() else "cpu"
        return device
    
    @abstractmethod
    def load_model(self) -> bool:
        """加载模型"""
        pass
    
    @abstractmethod
    def predict(self, image: np.ndarray, **kwargs) -> Dict[str, Any]:
        """模型推理"""
        pass
    
    def unload_model(self):
        """卸载模型"""
        if self.model is not None:
            del self.model
            self.model = None
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        self.is_loaded = False


class SAMModel(BaseAdvancedModel):
    """Segment Anything Model集成"""
    
    def load_model(self) -> bool:
        """加载SAM模型"""
        try:
            # 尝试导入SAM
            try:
                from segment_anything import sam_model_registry, SamPredictor
                self.sam_model_registry = sam_model_registry
                self.SamPredictor = SamPredictor
            except ImportError:
                logger.error("segment-anything包未安装，请运行: pip install segment-anything")
                return False
            
            # 确定模型类型
            model_type = "vit_h"  # 默认使用最大模型
            if "vit_b" in self.config.model_path.lower():
                model_type = "vit_b"
            elif "vit_l" in self.config.model_path.lower():
                model_type = "vit_l"
            
            # 加载模型
            sam = self.sam_model_registry[model_type](checkpoint=self.config.model_path)
            sam.to(device=self.device)
            
            self.model = self.SamPredictor(sam)
            self.is_loaded = True
            
            logger.info(f"SAM模型加载成功: {model_type}, 设备: {self.device}")
            return True
            
        except Exception as e:
            logger.error(f"SAM模型加载失败: {e}")
            return False
    
    def predict(self, image: np.ndarray, 
                points: Optional[np.ndarray] = None,
                boxes: Optional[np.ndarray] = None,
                **kwargs) -> Dict[str, Any]:
        """SAM分割预测"""
        if not self.is_loaded:
            return {"masks": [], "scores": [], "logits": []}
        
        try:
            # 设置图像
            self.model.set_image(image)
            
            # 执行分割
            if boxes is not None:
                # 基于边界框的分割
                masks, scores, logits = self.model.predict(
                    box=boxes,
                    multimask_output=True
                )
            elif points is not None:
                # 基于点的分割
                masks, scores, logits = self.model.predict(
                    point_coords=points,
                    multimask_output=True
                )
            else:
                # 自动分割整个图像
                masks, scores, logits = self.model.predict(
                    multimask_output=True
                )
            
            return {
                "masks": masks,
                "scores": scores,
                "logits": logits
            }
            
        except Exception as e:
            logger.error(f"SAM预测失败: {e}")
            return {"masks": [], "scores": [], "logits": []}


class CLIPModel(BaseAdvancedModel):
    """CLIP模型集成"""
    
    def load_model(self) -> bool:
        """加载CLIP模型"""
        try:
            # 尝试导入CLIP
            try:
                import clip
                self.clip = clip
            except ImportError:
                logger.error("clip-by-openai包未安装，请运行: pip install git+https://github.com/openai/CLIP.git")
                return False
            
            # 加载模型
            model_name = "ViT-B/32"  # 默认模型
            if "ViT-L" in self.config.model_path:
                model_name = "ViT-L/14"
            elif "RN50" in self.config.model_path:
                model_name = "RN50"
            
            self.model, self.preprocess = clip.load(model_name, device=self.device)
            self.is_loaded = True
            
            logger.info(f"CLIP模型加载成功: {model_name}, 设备: {self.device}")
            return True
            
        except Exception as e:
            logger.error(f"CLIP模型加载失败: {e}")
            return False
    
    def predict(self, image: np.ndarray, 
                text_queries: List[str],
                **kwargs) -> Dict[str, Any]:
        """CLIP图文匹配预测"""
        if not self.is_loaded:
            return {"similarities": [], "predictions": []}
        
        try:
            # 预处理图像
            from PIL import Image
            if isinstance(image, np.ndarray):
                image = Image.fromarray(image)
            
            image_input = self.preprocess(image).unsqueeze(0).to(self.device)
            
            # 预处理文本
            text_inputs = self.clip.tokenize(text_queries).to(self.device)
            
            # 计算特征
            with torch.no_grad():
                image_features = self.model.encode_image(image_input)
                text_features = self.model.encode_text(text_inputs)
                
                # 计算相似度
                similarities = (100.0 * image_features @ text_features.T).softmax(dim=-1)
                
            return {
                "similarities": similarities.cpu().numpy(),
                "predictions": text_queries,
                "image_features": image_features.cpu().numpy(),
                "text_features": text_features.cpu().numpy()
            }
            
        except Exception as e:
            logger.error(f"CLIP预测失败: {e}")
            return {"similarities": [], "predictions": []}


class DINOModel(BaseAdvancedModel):
    """DINO模型集成"""
    
    def load_model(self) -> bool:
        """加载DINO模型"""
        try:
            # 尝试导入transformers
            try:
                from transformers import AutoImageProcessor, AutoModel
                self.AutoImageProcessor = AutoImageProcessor
                self.AutoModel = AutoModel
            except ImportError:
                logger.error("transformers包未安装，请运行: pip install transformers")
                return False
            
            # 加载模型
            model_name = "facebook/dino-vitb16"
            if "vits" in self.config.model_path.lower():
                model_name = "facebook/dino-vits16"
            elif "vitl" in self.config.model_path.lower():
                model_name = "facebook/dino-vitl16"
            
            self.processor = self.AutoImageProcessor.from_pretrained(model_name)
            self.model = self.AutoModel.from_pretrained(model_name)
            self.model.to(self.device)
            self.is_loaded = True
            
            logger.info(f"DINO模型加载成功: {model_name}, 设备: {self.device}")
            return True
            
        except Exception as e:
            logger.error(f"DINO模型加载失败: {e}")
            return False
    
    def predict(self, image: np.ndarray, **kwargs) -> Dict[str, Any]:
        """DINO特征提取"""
        if not self.is_loaded:
            return {"features": [], "attention": []}
        
        try:
            # 预处理图像
            from PIL import Image
            if isinstance(image, np.ndarray):
                image = Image.fromarray(image)
            
            inputs = self.processor(images=image, return_tensors="pt").to(self.device)
            
            # 提取特征
            with torch.no_grad():
                outputs = self.model(**inputs)
                features = outputs.last_hidden_state
                
                # 获取注意力权重（如果可用）
                attention_weights = None
                if hasattr(outputs, 'attentions') and outputs.attentions:
                    attention_weights = outputs.attentions[-1]  # 最后一层的注意力
            
            return {
                "features": features.cpu().numpy(),
                "attention": attention_weights.cpu().numpy() if attention_weights is not None else None,
                "pooled_features": features.mean(dim=1).cpu().numpy()  # 池化特征
            }
            
        except Exception as e:
            logger.error(f"DINO预测失败: {e}")
            return {"features": [], "attention": []}


class AdvancedModelManager:
    """先进模型管理器"""
    
    def __init__(self):
        self.models: Dict[str, BaseAdvancedModel] = {}
        self.model_configs: Dict[str, ModelConfig] = {}
        
    def register_model(self, name: str, model_class: type, config: ModelConfig):
        """注册模型"""
        try:
            model = model_class(config)
            self.models[name] = model
            self.model_configs[name] = config
            logger.info(f"模型已注册: {name}")
        except Exception as e:
            logger.error(f"模型注册失败 {name}: {e}")
    
    def load_model(self, name: str) -> bool:
        """加载指定模型"""
        if name not in self.models:
            logger.error(f"未找到模型: {name}")
            return False
        
        return self.models[name].load_model()
    
    def load_all_models(self) -> Dict[str, bool]:
        """加载所有启用的模型"""
        results = {}
        for name, model in self.models.items():
            if self.model_configs[name].enabled:
                results[name] = self.load_model(name)
        return results
    
    def predict(self, name: str, image: np.ndarray, **kwargs) -> Dict[str, Any]:
        """使用指定模型进行预测"""
        if name not in self.models:
            logger.error(f"未找到模型: {name}")
            return {}
        
        return self.models[name].predict(image, **kwargs)
    
    def unload_model(self, name: str):
        """卸载指定模型"""
        if name in self.models:
            self.models[name].unload_model()
    
    def unload_all_models(self):
        """卸载所有模型"""
        for model in self.models.values():
            model.unload_model()
    
    def get_model_status(self) -> Dict[str, Dict[str, Any]]:
        """获取所有模型状态"""
        status = {}
        for name, model in self.models.items():
            config = self.model_configs[name]
            status[name] = {
                "loaded": model.is_loaded,
                "enabled": config.enabled,
                "device": model.device,
                "model_path": config.model_path
            }
        return status
