#!/usr/bin/env python3
"""
检测结果可视化组件
在截图上显示检测框并支持交互选择
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QScrollArea, QFrame, QCheckBox, QSpinBox, QComboBox,
    QToolTip, QMenu, QMessageBox
)
from PyQt6.QtCore import Qt, pyqtSignal, QPoint, QRect, QTimer
from PyQt6.QtGui import (
    QPainter, QPen, QBrush, QColor, QFont, QPixmap, 
    QMouseEvent, QPaintEvent, QCursor
)
from typing import List, Dict, Any, Optional, Tuple
import logging

class DetectionBox:
    """检测框数据类"""
    def __init__(self, id: str, bbox: Tuple[int, int, int, int], 
                 label: str, confidence: float, selected: bool = False):
        self.id = id
        self.bbox = bbox  # (x, y, width, height)
        self.label = label
        self.confidence = confidence
        self.selected = selected
        self.hover = False
        self.operation_configured = False

class DetectionVisualizerWidget(QLabel):
    """检测结果可视化组件"""
    
    # 信号定义
    box_selected = pyqtSignal(str, bool)  # 检测框选择状态改变
    box_double_clicked = pyqtSignal(str)  # 检测框双击
    box_right_clicked = pyqtSignal(str, QPoint)  # 检测框右键点击
    selection_changed = pyqtSignal(list)  # 选择改变
    
    def __init__(self, parent=None):
        """初始化可视化组件"""
        super().__init__(parent)
        self.logger = logging.getLogger(__name__)
        
        # 数据存储
        self.detection_boxes: List[DetectionBox] = []
        self.original_pixmap: Optional[QPixmap] = None
        self.scale_factor = 1.0
        
        # 显示设置
        self.show_labels = True
        self.show_confidence = True
        self.show_coordinates = False
        self.min_confidence = 0.0
        
        # 颜色配置
        self.colors = {
            'normal': QColor(0, 255, 0, 180),      # 绿色
            'selected': QColor(255, 0, 0, 200),    # 红色
            'hover': QColor(255, 255, 0, 180),     # 黄色
            'configured': QColor(0, 0, 255, 180)   # 蓝色
        }
        
        # 字体配置
        self.label_font = QFont("Arial", 9, QFont.Weight.Bold)
        
        # 设置组件属性
        self.setMinimumSize(400, 300)
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.setStyleSheet("border: 1px solid #bdc3c7; background-color: #ecf0f1;")
        self.setMouseTracking(True)  # 启用鼠标跟踪
        
        # 工具提示定时器
        self.tooltip_timer = QTimer()
        self.tooltip_timer.setSingleShot(True)
        self.tooltip_timer.timeout.connect(self._show_tooltip)
        self.tooltip_position = QPoint()
        self.tooltip_box_id = ""
        
        self.logger.info("检测结果可视化组件初始化完成")
    
    def set_image(self, pixmap: QPixmap):
        """设置显示图像"""
        self.original_pixmap = pixmap
        self._update_display()
    
    def update_detections(self, detections: List[Dict[str, Any]]):
        """更新检测结果"""
        self.detection_boxes.clear()
        
        for i, detection in enumerate(detections):
            box = DetectionBox(
                id=f"detection_{i}",
                bbox=detection.get('bbox', (0, 0, 0, 0)),
                label=detection.get('label', 'unknown'),
                confidence=detection.get('confidence', 0.0)
            )
            
            # 过滤低置信度检测
            if box.confidence >= self.min_confidence:
                self.detection_boxes.append(box)
        
        self._update_display()
        self.logger.info(f"更新检测结果: {len(self.detection_boxes)} 个目标")
    
    def set_display_options(self, show_labels: bool = True, show_confidence: bool = True,
                          show_coordinates: bool = False, min_confidence: float = 0.0):
        """设置显示选项"""
        self.show_labels = show_labels
        self.show_confidence = show_confidence
        self.show_coordinates = show_coordinates
        self.min_confidence = min_confidence
        
        # 重新过滤检测结果
        filtered_boxes = [box for box in self.detection_boxes 
                         if box.confidence >= min_confidence]
        self.detection_boxes = filtered_boxes
        
        self._update_display()
    
    def get_selected_boxes(self) -> List[str]:
        """获取选中的检测框ID列表"""
        return [box.id for box in self.detection_boxes if box.selected]
    
    def select_all_boxes(self):
        """选择所有检测框"""
        for box in self.detection_boxes:
            if not box.selected:
                box.selected = True
                self.box_selected.emit(box.id, True)
        
        self._update_display()
        self._emit_selection_changed()
    
    def clear_selection(self):
        """清除所有选择"""
        for box in self.detection_boxes:
            if box.selected:
                box.selected = False
                self.box_selected.emit(box.id, False)
        
        self._update_display()
        self._emit_selection_changed()
    
    def set_box_configured(self, box_id: str, configured: bool):
        """设置检测框的配置状态"""
        for box in self.detection_boxes:
            if box.id == box_id:
                box.operation_configured = configured
                break
        
        self._update_display()
    
    def _update_display(self):
        """更新显示"""
        if not self.original_pixmap:
            self.setText("无图像")
            return
        
        # 创建绘制的图像副本
        display_pixmap = self.original_pixmap.copy()
        
        # 计算缩放因子
        widget_size = self.size()
        pixmap_size = display_pixmap.size()
        
        scale_x = widget_size.width() / pixmap_size.width()
        scale_y = widget_size.height() / pixmap_size.height()
        self.scale_factor = min(scale_x, scale_y, 1.0)  # 不放大，只缩小
        
        # 缩放图像
        if self.scale_factor < 1.0:
            scaled_size = pixmap_size * self.scale_factor
            display_pixmap = display_pixmap.scaled(
                scaled_size.toSize(),
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation
            )
        
        # 绘制检测框
        self._draw_detection_boxes(display_pixmap)
        
        # 设置显示
        self.setPixmap(display_pixmap)
    
    def _draw_detection_boxes(self, pixmap: QPixmap):
        """绘制检测框"""
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        for box in self.detection_boxes:
            self._draw_single_box(painter, box)
        
        painter.end()
    
    def _draw_single_box(self, painter: QPainter, box: DetectionBox):
        """绘制单个检测框"""
        # 计算缩放后的坐标
        x, y, w, h = box.bbox
        scaled_x = int(x * self.scale_factor)
        scaled_y = int(y * self.scale_factor)
        scaled_w = int(w * self.scale_factor)
        scaled_h = int(h * self.scale_factor)
        
        # 选择颜色
        if box.selected:
            color = self.colors['selected']
        elif box.operation_configured:
            color = self.colors['configured']
        elif box.hover:
            color = self.colors['hover']
        else:
            color = self.colors['normal']
        
        # 绘制检测框
        pen = QPen(color, 2)
        painter.setPen(pen)
        painter.drawRect(scaled_x, scaled_y, scaled_w, scaled_h)
        
        # 绘制填充（半透明）
        brush = QBrush(QColor(color.red(), color.green(), color.blue(), 30))
        painter.fillRect(scaled_x, scaled_y, scaled_w, scaled_h, brush)
        
        # 绘制标签
        if self.show_labels or self.show_confidence or self.show_coordinates:
            self._draw_box_label(painter, box, scaled_x, scaled_y, scaled_w, scaled_h)
    
    def _draw_box_label(self, painter: QPainter, box: DetectionBox, 
                       x: int, y: int, w: int, h: int):
        """绘制检测框标签"""
        # 构建标签文本
        label_parts = []
        
        if self.show_labels:
            label_parts.append(box.label)
        
        if self.show_confidence:
            label_parts.append(f"{box.confidence:.2f}")
        
        if self.show_coordinates:
            label_parts.append(f"({box.bbox[0]}, {box.bbox[1]})")
        
        if not label_parts:
            return
        
        label_text = " | ".join(label_parts)
        
        # 设置字体和颜色
        painter.setFont(self.label_font)
        painter.setPen(QPen(QColor(255, 255, 255), 1))
        
        # 计算文本尺寸
        font_metrics = painter.fontMetrics()
        text_rect = font_metrics.boundingRect(label_text)
        
        # 绘制背景
        bg_rect = QRect(x, y - text_rect.height() - 4, 
                       text_rect.width() + 8, text_rect.height() + 4)
        
        # 确保背景不超出图像边界
        if bg_rect.y() < 0:
            bg_rect.moveTop(y + h)
        
        painter.fillRect(bg_rect, QBrush(QColor(0, 0, 0, 180)))
        
        # 绘制文本
        text_pos = QPoint(bg_rect.x() + 4, bg_rect.y() + text_rect.height())
        painter.drawText(text_pos, label_text)
    
    def mousePressEvent(self, event: QMouseEvent):
        """鼠标按下事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            box_id = self._get_box_at_position(event.pos())
            if box_id:
                box = self._get_box_by_id(box_id)
                if box:
                    box.selected = not box.selected
                    self.box_selected.emit(box_id, box.selected)
                    self._update_display()
                    self._emit_selection_changed()
        
        elif event.button() == Qt.MouseButton.RightButton:
            box_id = self._get_box_at_position(event.pos())
            if box_id:
                self.box_right_clicked.emit(box_id, event.globalPosition().toPoint())
        
        super().mousePressEvent(event)
    
    def mouseDoubleClickEvent(self, event: QMouseEvent):
        """鼠标双击事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            box_id = self._get_box_at_position(event.pos())
            if box_id:
                self.box_double_clicked.emit(box_id)
        
        super().mouseDoubleClickEvent(event)
    
    def mouseMoveEvent(self, event: QMouseEvent):
        """鼠标移动事件"""
        # 更新悬停状态
        current_box_id = self._get_box_at_position(event.pos())
        
        hover_changed = False
        for box in self.detection_boxes:
            old_hover = box.hover
            box.hover = (box.id == current_box_id)
            if old_hover != box.hover:
                hover_changed = True
        
        if hover_changed:
            self._update_display()
        
        # 设置鼠标光标
        if current_box_id:
            self.setCursor(QCursor(Qt.CursorShape.PointingHandCursor))
            
            # 准备显示工具提示
            self.tooltip_position = event.pos()
            self.tooltip_box_id = current_box_id
            self.tooltip_timer.start(500)  # 500ms后显示
        else:
            self.setCursor(QCursor(Qt.CursorShape.ArrowCursor))
            self.tooltip_timer.stop()
            QToolTip.hideText()
        
        super().mouseMoveEvent(event)
    
    def leaveEvent(self, event):
        """鼠标离开事件"""
        # 清除悬停状态
        hover_changed = False
        for box in self.detection_boxes:
            if box.hover:
                box.hover = False
                hover_changed = True
        
        if hover_changed:
            self._update_display()
        
        self.setCursor(QCursor(Qt.CursorShape.ArrowCursor))
        self.tooltip_timer.stop()
        QToolTip.hideText()
        
        super().leaveEvent(event)
    
    def _get_box_at_position(self, pos: QPoint) -> Optional[str]:
        """获取指定位置的检测框ID"""
        # 转换为图像坐标
        pixmap = self.pixmap()
        if not pixmap:
            return None
        
        # 计算图像在组件中的偏移
        widget_rect = self.rect()
        pixmap_rect = pixmap.rect()
        
        # 居中显示的偏移
        offset_x = (widget_rect.width() - pixmap_rect.width()) // 2
        offset_y = (widget_rect.height() - pixmap_rect.height()) // 2
        
        # 转换坐标
        image_x = pos.x() - offset_x
        image_y = pos.y() - offset_y
        
        # 检查是否在图像范围内
        if (image_x < 0 or image_x >= pixmap_rect.width() or 
            image_y < 0 or image_y >= pixmap_rect.height()):
            return None
        
        # 转换为原始图像坐标
        original_x = image_x / self.scale_factor
        original_y = image_y / self.scale_factor
        
        # 查找包含该点的检测框
        for box in self.detection_boxes:
            x, y, w, h = box.bbox
            if (x <= original_x <= x + w and y <= original_y <= y + h):
                return box.id
        
        return None
    
    def _get_box_by_id(self, box_id: str) -> Optional[DetectionBox]:
        """根据ID获取检测框"""
        for box in self.detection_boxes:
            if box.id == box_id:
                return box
        return None
    
    def _show_tooltip(self):
        """显示工具提示"""
        if self.tooltip_box_id:
            box = self._get_box_by_id(self.tooltip_box_id)
            if box:
                tooltip_text = (
                    f"类型: {box.label}\n"
                    f"置信度: {box.confidence:.3f}\n"
                    f"位置: ({box.bbox[0]}, {box.bbox[1]})\n"
                    f"尺寸: {box.bbox[2]} × {box.bbox[3]}\n"
                    f"状态: {'已选择' if box.selected else '未选择'}\n"
                    f"操作: {'已配置' if box.operation_configured else '未配置'}"
                )
                
                global_pos = self.mapToGlobal(self.tooltip_position)
                QToolTip.showText(global_pos, tooltip_text)
    
    def _emit_selection_changed(self):
        """发送选择改变信号"""
        selected_ids = self.get_selected_boxes()
        self.selection_changed.emit(selected_ids)
    
    def resizeEvent(self, event):
        """窗口大小改变事件"""
        super().resizeEvent(event)
        self._update_display()
