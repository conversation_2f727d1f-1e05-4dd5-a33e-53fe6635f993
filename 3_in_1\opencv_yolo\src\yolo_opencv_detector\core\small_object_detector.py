# -*- coding: utf-8 -*-
"""
小目标检测优化模块

专门针对小目标检测场景的优化算法，包括多尺度检测、特征金字塔、
注意力机制等先进技术。

Created: 2025-07-13
Author: Augment Agent
"""

import cv2
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)


@dataclass
class SmallObjectConfig:
    """小目标检测配置"""
    min_object_size: int = 16  # 最小目标尺寸
    max_object_size: int = 64  # 最大小目标尺寸
    scale_factors: List[float] = None  # 缩放因子
    tile_size: int = 640  # 切片大小
    tile_overlap: float = 0.2  # 切片重叠率
    nms_threshold: float = 0.5  # NMS阈值
    confidence_threshold: float = 0.3  # 置信度阈值
    enable_tta: bool = True  # 测试时增强
    enable_multiscale: bool = True  # 多尺度检测
    
    def __post_init__(self):
        if self.scale_factors is None:
            self.scale_factors = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0]


class FeaturePyramidNetwork(nn.Module):
    """特征金字塔网络"""
    
    def __init__(self, in_channels_list: List[int], out_channels: int = 256):
        super().__init__()
        
        self.in_channels_list = in_channels_list
        self.out_channels = out_channels
        
        # 横向连接
        self.lateral_convs = nn.ModuleList()
        for in_channels in in_channels_list:
            self.lateral_convs.append(
                nn.Conv2d(in_channels, out_channels, 1)
            )
        
        # 输出卷积
        self.fpn_convs = nn.ModuleList()
        for _ in in_channels_list:
            self.fpn_convs.append(
                nn.Conv2d(out_channels, out_channels, 3, padding=1)
            )
    
    def forward(self, features: List[torch.Tensor]) -> List[torch.Tensor]:
        """
        前向传播
        
        Args:
            features: 多尺度特征列表，从低分辨率到高分辨率
            
        Returns:
            FPN特征列表
        """
        # 横向连接
        laterals = []
        for i, feature in enumerate(features):
            lateral = self.lateral_convs[i](feature)
            laterals.append(lateral)
        
        # 自顶向下路径
        fpn_features = []
        for i in range(len(laterals) - 1, -1, -1):
            if i == len(laterals) - 1:
                # 最高层特征
                fpn_feat = laterals[i]
            else:
                # 上采样并相加
                upsampled = F.interpolate(
                    fpn_features[0], 
                    size=laterals[i].shape[2:], 
                    mode='nearest'
                )
                fpn_feat = laterals[i] + upsampled
            
            # 输出卷积
            fpn_feat = self.fpn_convs[i](fpn_feat)
            fpn_features.insert(0, fpn_feat)
        
        return fpn_features


class SmallObjectAttention(nn.Module):
    """小目标注意力模块"""
    
    def __init__(self, in_channels: int, reduction: int = 16):
        super().__init__()
        
        self.in_channels = in_channels
        self.reduction = reduction
        
        # 通道注意力
        self.channel_attention = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(in_channels, in_channels // reduction, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels // reduction, in_channels, 1),
            nn.Sigmoid()
        )
        
        # 空间注意力
        self.spatial_attention = nn.Sequential(
            nn.Conv2d(2, 1, 7, padding=3),
            nn.Sigmoid()
        )
        
        # 小目标增强
        self.small_object_enhancer = nn.Sequential(
            nn.Conv2d(in_channels, in_channels, 3, padding=1),
            nn.BatchNorm2d(in_channels),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels, in_channels, 1),
            nn.Sigmoid()
        )
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # 通道注意力
        channel_att = self.channel_attention(x)
        x = x * channel_att
        
        # 空间注意力
        avg_pool = torch.mean(x, dim=1, keepdim=True)
        max_pool, _ = torch.max(x, dim=1, keepdim=True)
        spatial_input = torch.cat([avg_pool, max_pool], dim=1)
        spatial_att = self.spatial_attention(spatial_input)
        x = x * spatial_att
        
        # 小目标增强
        small_obj_att = self.small_object_enhancer(x)
        x = x * small_obj_att
        
        return x


class TileBasedDetector:
    """基于切片的检测器"""
    
    def __init__(self, config: SmallObjectConfig):
        self.config = config
        
    def create_tiles(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """创建图像切片"""
        h, w = image.shape[:2]
        tile_size = self.config.tile_size
        overlap = int(tile_size * self.config.tile_overlap)
        
        tiles = []
        
        # 计算切片位置
        y_positions = list(range(0, h - tile_size + 1, tile_size - overlap))
        if y_positions[-1] + tile_size < h:
            y_positions.append(h - tile_size)
        
        x_positions = list(range(0, w - tile_size + 1, tile_size - overlap))
        if x_positions[-1] + tile_size < w:
            x_positions.append(w - tile_size)
        
        # 创建切片
        for y in y_positions:
            for x in x_positions:
                tile_image = image[y:y+tile_size, x:x+tile_size]
                
                tiles.append({
                    'image': tile_image,
                    'x_offset': x,
                    'y_offset': y,
                    'original_size': (w, h)
                })
        
        return tiles
    
    def merge_tile_results(self, tile_results: List[List[Dict]], 
                          tiles_info: List[Dict]) -> List[Dict]:
        """合并切片检测结果"""
        merged_results = []
        
        for tile_result, tile_info in zip(tile_results, tiles_info):
            x_offset = tile_info['x_offset']
            y_offset = tile_info['y_offset']
            
            for detection in tile_result:
                # 调整坐标到原图
                bbox = detection['bbox'].copy()
                bbox[0] += x_offset  # x
                bbox[1] += y_offset  # y
                
                adjusted_detection = detection.copy()
                adjusted_detection['bbox'] = bbox
                merged_results.append(adjusted_detection)
        
        # 应用NMS去除重复检测
        if merged_results:
            merged_results = self._apply_nms(merged_results)
        
        return merged_results
    
    def _apply_nms(self, detections: List[Dict]) -> List[Dict]:
        """应用非极大值抑制"""
        if not detections:
            return []
        
        # 转换为numpy数组
        boxes = np.array([det['bbox'] for det in detections])
        scores = np.array([det['confidence'] for det in detections])
        
        # 转换为x1, y1, x2, y2格式
        x1 = boxes[:, 0]
        y1 = boxes[:, 1]
        x2 = boxes[:, 0] + boxes[:, 2]
        y2 = boxes[:, 1] + boxes[:, 3]
        
        # 计算面积
        areas = (x2 - x1) * (y2 - y1)
        
        # 按分数排序
        order = scores.argsort()[::-1]
        
        keep = []
        while order.size > 0:
            i = order[0]
            keep.append(i)
            
            # 计算IoU
            xx1 = np.maximum(x1[i], x1[order[1:]])
            yy1 = np.maximum(y1[i], y1[order[1:]])
            xx2 = np.minimum(x2[i], x2[order[1:]])
            yy2 = np.minimum(y2[i], y2[order[1:]])
            
            w = np.maximum(0, xx2 - xx1)
            h = np.maximum(0, yy2 - yy1)
            intersection = w * h
            
            iou = intersection / (areas[i] + areas[order[1:]] - intersection)
            
            # 保留IoU小于阈值的检测
            inds = np.where(iou <= self.config.nms_threshold)[0]
            order = order[inds + 1]
        
        return [detections[i] for i in keep]


class MultiScaleDetector:
    """多尺度检测器"""
    
    def __init__(self, config: SmallObjectConfig):
        self.config = config
        
    def detect_multiscale(self, image: np.ndarray, 
                         detector_func) -> List[Dict]:
        """多尺度检测"""
        all_detections = []
        original_h, original_w = image.shape[:2]
        
        for scale in self.config.scale_factors:
            # 缩放图像
            new_h, new_w = int(original_h * scale), int(original_w * scale)
            if new_h < 32 or new_w < 32:  # 跳过过小的尺度
                continue
                
            scaled_image = cv2.resize(image, (new_w, new_h))
            
            # 检测
            detections = detector_func(scaled_image)
            
            # 调整检测结果到原图尺度
            for detection in detections:
                bbox = detection['bbox']
                # 缩放回原图坐标
                bbox[0] = int(bbox[0] / scale)  # x
                bbox[1] = int(bbox[1] / scale)  # y
                bbox[2] = int(bbox[2] / scale)  # w
                bbox[3] = int(bbox[3] / scale)  # h
                
                detection['bbox'] = bbox
                detection['scale'] = scale
                all_detections.append(detection)
        
        # 应用NMS
        if all_detections:
            all_detections = self._weighted_nms(all_detections)
        
        return all_detections
    
    def _weighted_nms(self, detections: List[Dict]) -> List[Dict]:
        """加权NMS，考虑不同尺度的置信度"""
        if not detections:
            return []
        
        # 按置信度排序
        detections.sort(key=lambda x: x['confidence'], reverse=True)
        
        keep = []
        while detections:
            current = detections.pop(0)
            keep.append(current)
            
            # 移除重叠的检测
            remaining = []
            for det in detections:
                iou = self._calculate_iou(current['bbox'], det['bbox'])
                if iou <= self.config.nms_threshold:
                    remaining.append(det)
                else:
                    # 如果IoU高但来自不同尺度，可以考虑融合
                    if abs(current.get('scale', 1.0) - det.get('scale', 1.0)) > 0.3:
                        # 融合检测结果
                        fused_detection = self._fuse_detections(current, det)
                        if fused_detection['confidence'] > current['confidence']:
                            keep[-1] = fused_detection
            
            detections = remaining
        
        return keep
    
    def _calculate_iou(self, bbox1: List[int], bbox2: List[int]) -> float:
        """计算IoU"""
        x1, y1, w1, h1 = bbox1
        x2, y2, w2, h2 = bbox2
        
        # 转换为x1, y1, x2, y2格式
        x1_max, y1_max = x1 + w1, y1 + h1
        x2_max, y2_max = x2 + w2, y2 + h2
        
        # 计算交集
        inter_x1 = max(x1, x2)
        inter_y1 = max(y1, y2)
        inter_x2 = min(x1_max, x2_max)
        inter_y2 = min(y1_max, y2_max)
        
        if inter_x2 <= inter_x1 or inter_y2 <= inter_y1:
            return 0.0
        
        inter_area = (inter_x2 - inter_x1) * (inter_y2 - inter_y1)
        area1 = w1 * h1
        area2 = w2 * h2
        union_area = area1 + area2 - inter_area
        
        return inter_area / union_area if union_area > 0 else 0.0
    
    def _fuse_detections(self, det1: Dict, det2: Dict) -> Dict:
        """融合两个检测结果"""
        # 加权平均置信度
        w1 = det1['confidence']
        w2 = det2['confidence']
        total_weight = w1 + w2
        
        if total_weight == 0:
            return det1
        
        # 融合边界框
        bbox1 = det1['bbox']
        bbox2 = det2['bbox']
        
        fused_bbox = [
            int((bbox1[0] * w1 + bbox2[0] * w2) / total_weight),
            int((bbox1[1] * w1 + bbox2[1] * w2) / total_weight),
            int((bbox1[2] * w1 + bbox2[2] * w2) / total_weight),
            int((bbox1[3] * w1 + bbox2[3] * w2) / total_weight)
        ]
        
        # 融合置信度
        fused_confidence = (w1 + w2) / 2
        
        return {
            'bbox': fused_bbox,
            'confidence': fused_confidence,
            'class_name': det1.get('class_name', det2.get('class_name')),
            'class_id': det1.get('class_id', det2.get('class_id')),
            'scale': (det1.get('scale', 1.0) + det2.get('scale', 1.0)) / 2
        }


class TestTimeAugmentation:
    """测试时增强"""
    
    def __init__(self, config: SmallObjectConfig):
        self.config = config
        
    def augment_and_detect(self, image: np.ndarray, 
                          detector_func) -> List[Dict]:
        """测试时增强检测"""
        if not self.config.enable_tta:
            return detector_func(image)
        
        all_detections = []
        
        # 原图检测
        detections = detector_func(image)
        all_detections.extend(detections)
        
        # 水平翻转
        flipped_image = cv2.flip(image, 1)
        flipped_detections = detector_func(flipped_image)
        
        # 调整翻转后的检测结果
        w = image.shape[1]
        for det in flipped_detections:
            bbox = det['bbox']
            bbox[0] = w - bbox[0] - bbox[2]  # 调整x坐标
            det['bbox'] = bbox
        
        all_detections.extend(flipped_detections)
        
        # 旋转增强（小角度）
        for angle in [-5, 5]:
            rotated_image = self._rotate_image(image, angle)
            rotated_detections = detector_func(rotated_image)
            
            # 调整旋转后的检测结果（简化处理）
            all_detections.extend(rotated_detections)
        
        # 亮度调整
        for gamma in [0.8, 1.2]:
            adjusted_image = self._adjust_gamma(image, gamma)
            adjusted_detections = detector_func(adjusted_image)
            all_detections.extend(adjusted_detections)
        
        # 融合所有检测结果
        if all_detections:
            all_detections = self._ensemble_detections(all_detections)
        
        return all_detections
    
    def _rotate_image(self, image: np.ndarray, angle: float) -> np.ndarray:
        """旋转图像"""
        h, w = image.shape[:2]
        center = (w // 2, h // 2)
        
        rotation_matrix = cv2.getRotationMatrix2D(center, angle, 1.0)
        rotated_image = cv2.warpAffine(image, rotation_matrix, (w, h))
        
        return rotated_image
    
    def _adjust_gamma(self, image: np.ndarray, gamma: float) -> np.ndarray:
        """调整图像伽马值"""
        inv_gamma = 1.0 / gamma
        table = np.array([((i / 255.0) ** inv_gamma) * 255 
                         for i in np.arange(0, 256)]).astype("uint8")
        
        return cv2.LUT(image, table)
    
    def _ensemble_detections(self, detections: List[Dict]) -> List[Dict]:
        """集成多个检测结果"""
        # 简化的集成策略：基于置信度的加权平均
        detection_groups = {}
        
        for det in detections:
            key = (det.get('class_name', ''), 
                   det['bbox'][0] // 10,  # 粗略的位置分组
                   det['bbox'][1] // 10)
            
            if key not in detection_groups:
                detection_groups[key] = []
            detection_groups[key].append(det)
        
        ensemble_results = []
        for group in detection_groups.values():
            if len(group) >= 2:  # 至少有2个检测才考虑集成
                ensemble_det = self._average_detections(group)
                ensemble_results.append(ensemble_det)
            else:
                ensemble_results.extend(group)
        
        return ensemble_results
    
    def _average_detections(self, detections: List[Dict]) -> Dict:
        """平均多个检测结果"""
        if not detections:
            return {}
        
        # 计算平均边界框
        avg_bbox = [0, 0, 0, 0]
        avg_confidence = 0
        
        for det in detections:
            bbox = det['bbox']
            for i in range(4):
                avg_bbox[i] += bbox[i]
            avg_confidence += det['confidence']
        
        n = len(detections)
        avg_bbox = [int(x / n) for x in avg_bbox]
        avg_confidence /= n
        
        return {
            'bbox': avg_bbox,
            'confidence': avg_confidence,
            'class_name': detections[0].get('class_name'),
            'class_id': detections[0].get('class_id')
        }


class RobustTemplateMatching:
    """鲁棒模板匹配"""

    def __init__(self, config: SmallObjectConfig):
        self.config = config

    def match_with_deformation(self, image: np.ndarray, template: np.ndarray,
                              max_rotation: float = 15.0,
                              scale_range: Tuple[float, float] = (0.8, 1.2)) -> List[Dict]:
        """处理模板变形的匹配"""
        matches = []

        # 旋转变换
        for angle in np.linspace(-max_rotation, max_rotation, 7):
            rotated_template = self._rotate_template(template, angle)
            if rotated_template is not None:
                # 多尺度匹配
                for scale in np.linspace(scale_range[0], scale_range[1], 5):
                    scaled_template = cv2.resize(rotated_template, None, fx=scale, fy=scale)

                    # 执行匹配
                    result = cv2.matchTemplate(image, scaled_template, cv2.TM_CCOEFF_NORMED)
                    locations = np.where(result >= self.config.confidence_threshold)

                    h, w = scaled_template.shape[:2]
                    for pt in zip(*locations[::-1]):
                        matches.append({
                            'bbox': [pt[0], pt[1], w, h],
                            'confidence': float(result[pt[1], pt[0]]),
                            'rotation': angle,
                            'scale': scale,
                            'class_name': 'template_match'
                        })

        return self._filter_overlapping_matches(matches)

    def match_with_illumination_invariance(self, image: np.ndarray,
                                         template: np.ndarray) -> List[Dict]:
        """光照不变性匹配"""
        matches = []

        # 直方图均衡化
        if len(image.shape) == 3:
            image_eq = cv2.cvtColor(image, cv2.COLOR_BGR2YUV)
            image_eq[:,:,0] = cv2.equalizeHist(image_eq[:,:,0])
            image_eq = cv2.cvtColor(image_eq, cv2.COLOR_YUV2BGR)
        else:
            image_eq = cv2.equalizeHist(image)

        if len(template.shape) == 3:
            template_eq = cv2.cvtColor(template, cv2.COLOR_BGR2YUV)
            template_eq[:,:,0] = cv2.equalizeHist(template_eq[:,:,0])
            template_eq = cv2.cvtColor(template_eq, cv2.COLOR_YUV2BGR)
        else:
            template_eq = cv2.equalizeHist(template)

        # 多种匹配方法
        methods = [
            cv2.TM_CCOEFF_NORMED,
            cv2.TM_CCORR_NORMED,
            cv2.TM_SQDIFF_NORMED
        ]

        for method in methods:
            # 原图匹配
            result1 = cv2.matchTemplate(image, template, method)
            # 均衡化后匹配
            result2 = cv2.matchTemplate(image_eq, template_eq, method)

            # 融合结果
            if method == cv2.TM_SQDIFF_NORMED:
                combined_result = (2.0 - result1 - result2) / 2.0
                threshold = 1.0 - self.config.confidence_threshold
            else:
                combined_result = (result1 + result2) / 2.0
                threshold = self.config.confidence_threshold

            locations = np.where(combined_result >= threshold)
            h, w = template.shape[:2]

            for pt in zip(*locations[::-1]):
                confidence = float(combined_result[pt[1], pt[0]])
                if method == cv2.TM_SQDIFF_NORMED:
                    confidence = 1.0 - confidence

                matches.append({
                    'bbox': [pt[0], pt[1], w, h],
                    'confidence': confidence,
                    'method': method,
                    'class_name': 'template_match'
                })

        return self._filter_overlapping_matches(matches)

    def _rotate_template(self, template: np.ndarray, angle: float) -> Optional[np.ndarray]:
        """旋转模板"""
        try:
            h, w = template.shape[:2]
            center = (w // 2, h // 2)

            rotation_matrix = cv2.getRotationMatrix2D(center, angle, 1.0)
            rotated = cv2.warpAffine(template, rotation_matrix, (w, h))

            return rotated
        except Exception:
            return None

    def _filter_overlapping_matches(self, matches: List[Dict]) -> List[Dict]:
        """过滤重叠匹配"""
        if not matches:
            return []

        # 按置信度排序
        matches.sort(key=lambda x: x['confidence'], reverse=True)

        filtered = []
        for match in matches:
            is_overlapping = False

            for existing in filtered:
                iou = self._calculate_match_iou(match['bbox'], existing['bbox'])
                if iou > 0.5:  # 重叠阈值
                    is_overlapping = True
                    break

            if not is_overlapping:
                filtered.append(match)

        return filtered

    def _calculate_match_iou(self, bbox1: List[int], bbox2: List[int]) -> float:
        """计算匹配结果的IoU"""
        x1, y1, w1, h1 = bbox1
        x2, y2, w2, h2 = bbox2

        # 计算交集
        inter_x1 = max(x1, x2)
        inter_y1 = max(y1, y2)
        inter_x2 = min(x1 + w1, x2 + w2)
        inter_y2 = min(y1 + h1, y2 + h2)

        if inter_x2 <= inter_x1 or inter_y2 <= inter_y1:
            return 0.0

        inter_area = (inter_x2 - inter_x1) * (inter_y2 - inter_y1)
        area1 = w1 * h1
        area2 = w2 * h2
        union_area = area1 + area2 - inter_area

        return inter_area / union_area if union_area > 0 else 0.0


class SmallObjectDetectionPipeline:
    """小目标检测管道"""

    def __init__(self, config: SmallObjectConfig):
        self.config = config
        self.tile_detector = TileBasedDetector(config)
        self.multiscale_detector = MultiScaleDetector(config)
        self.tta = TestTimeAugmentation(config)
        self.robust_matcher = RobustTemplateMatching(config)

    def detect(self, image: np.ndarray, base_detector_func,
               template: Optional[np.ndarray] = None) -> List[Dict]:
        """执行小目标检测"""
        try:
            all_results = []

            # 1. 多尺度检测
            if self.config.enable_multiscale:
                multiscale_results = self.multiscale_detector.detect_multiscale(
                    image, base_detector_func
                )
                all_results.extend(multiscale_results)

            # 2. 切片检测（针对大图像）
            h, w = image.shape[:2]
            if h > self.config.tile_size or w > self.config.tile_size:
                tiles = self.tile_detector.create_tiles(image)
                tile_results = []

                for tile in tiles:
                    tile_detections = base_detector_func(tile['image'])
                    tile_results.append(tile_detections)

                tile_merged_results = self.tile_detector.merge_tile_results(
                    tile_results, tiles
                )
                all_results.extend(tile_merged_results)

            # 3. 测试时增强
            tta_results = self.tta.augment_and_detect(image, base_detector_func)
            all_results.extend(tta_results)

            # 4. 鲁棒模板匹配（如果提供了模板）
            if template is not None:
                robust_matches = self.robust_matcher.match_with_deformation(image, template)
                illumination_matches = self.robust_matcher.match_with_illumination_invariance(image, template)
                all_results.extend(robust_matches)
                all_results.extend(illumination_matches)

            # 5. 最终NMS和过滤
            final_results = self._final_postprocess(all_results)

            return final_results

        except Exception as e:
            logger.error(f"小目标检测失败: {e}")
            return []

    def _final_postprocess(self, detections: List[Dict]) -> List[Dict]:
        """最终后处理"""
        if not detections:
            return []

        # 过滤小目标尺寸范围
        filtered_detections = []
        for det in detections:
            bbox = det['bbox']
            area = bbox[2] * bbox[3]
            min_side = min(bbox[2], bbox[3])

            if (self.config.min_object_size <= min_side <= self.config.max_object_size and
                det['confidence'] >= self.config.confidence_threshold):
                filtered_detections.append(det)

        # 最终NMS
        if filtered_detections:
            filtered_detections = self.tile_detector._apply_nms(filtered_detections)

        return filtered_detections
