# -*- coding: utf-8 -*-
"""
系统集成测试
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

import pytest
import sys
import time
import numpy as np
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from yolo_opencv_detector.utils.config_manager import ConfigManager
from yolo_opencv_detector.utils.logger import Logger
from yolo_opencv_detector.core.detection_engine import DetectionEngine
from yolo_opencv_detector.core.yolo_detector import YOLODetector
from yolo_opencv_detector.core.template_matcher import TemplateMatcher
from yolo_opencv_detector.core.fusion_engine import FusionEngine
from yolo_opencv_detector.core.screen_capture import ScreenCaptureService
from yolo_opencv_detector.utils.data_structures import DetectionResult, BoundingBox, DetectionSource


class TestSystemIntegration:
    """系统集成测试类"""
    
    @pytest.fixture
    def config_manager(self):
        """创建配置管理器"""
        return ConfigManager()
    
    @pytest.fixture
    def logger(self):
        """创建日志记录器"""
        return Logger()
    
    @pytest.fixture
    def mock_screen_image(self):
        """创建模拟屏幕图像"""
        # 创建一个简单的测试图像
        image = np.zeros((600, 800, 3), dtype=np.uint8)
        # 添加一些简单的图形
        image[100:200, 100:200] = [255, 0, 0]  # 红色方块
        image[300:400, 300:400] = [0, 255, 0]  # 绿色方块
        image[450:550, 600:700] = [0, 0, 255]  # 蓝色方块
        return image
    
    @pytest.fixture
    def mock_yolo_results(self):
        """创建模拟YOLO检测结果"""
        return [
            DetectionResult(
                bbox=BoundingBox(100, 100, 100, 100),
                confidence=0.9,
                class_id=1,
                class_name="person",
                source=DetectionSource.YOLO
            ),
            DetectionResult(
                bbox=BoundingBox(300, 300, 100, 100),
                confidence=0.8,
                class_id=2,
                class_name="car",
                source=DetectionSource.YOLO
            )
        ]
    
    @pytest.fixture
    def mock_template_results(self):
        """创建模拟模板匹配结果"""
        return [
            DetectionResult(
                bbox=BoundingBox(105, 105, 90, 90),
                confidence=0.7,
                template_id="template_1",
                source=DetectionSource.TEMPLATE
            ),
            DetectionResult(
                bbox=BoundingBox(600, 450, 100, 100),
                confidence=0.6,
                template_id="template_2",
                source=DetectionSource.TEMPLATE
            )
        ]
    
    def test_config_manager_initialization(self, config_manager):
        """测试配置管理器初始化"""
        assert config_manager is not None
        assert hasattr(config_manager, 'app')
        assert hasattr(config_manager, 'detection')
        assert hasattr(config_manager, 'template')
        assert hasattr(config_manager, 'fusion')
        assert hasattr(config_manager, 'gui')
    
    def test_logger_initialization(self, logger):
        """测试日志系统初始化"""
        assert logger is not None
        
        # 测试获取日志记录器
        test_logger = logger.get_logger("test")
        assert test_logger is not None
        
        # 测试日志记录
        test_logger.info("测试日志消息")
        test_logger.warning("测试警告消息")
        test_logger.error("测试错误消息")
    
    @patch('yolo_opencv_detector.core.yolo_detector.YOLO')
    def test_yolo_detector_integration(self, mock_yolo_class, config_manager, mock_screen_image):
        """测试YOLO检测器集成"""
        # 模拟YOLO模型
        mock_model = Mock()
        mock_yolo_class.return_value = mock_model
        
        # 模拟预测结果
        mock_result = Mock()
        mock_result.boxes = Mock()
        mock_result.boxes.xyxy = np.array([[100, 100, 200, 200], [300, 300, 400, 400]])
        mock_result.boxes.conf = np.array([0.9, 0.8])
        mock_result.boxes.cls = np.array([1, 2])
        mock_model.return_value = [mock_result]
        
        # 创建YOLO检测器
        detector = YOLODetector(config_manager)
        
        # 执行检测
        results = detector.detect(mock_screen_image)
        
        # 验证结果
        assert len(results) == 2
        assert all(isinstance(r, DetectionResult) for r in results)
        assert all(r.source == DetectionSource.YOLO for r in results)
    
    def test_template_matcher_integration(self, config_manager, mock_screen_image):
        """测试模板匹配器集成"""
        # 创建模板匹配器
        matcher = TemplateMatcher(config_manager)
        
        # 创建简单的模板
        template = np.ones((50, 50, 3), dtype=np.uint8) * 255  # 白色模板
        template_id = "test_template"
        
        # 添加模板
        matcher.add_template(template_id, template)
        
        # 执行匹配
        results = matcher.match_all(mock_screen_image)
        
        # 验证结果
        assert isinstance(results, list)
        # 由于是简单的白色模板，可能不会有匹配结果，但不应该出错
    
    def test_fusion_engine_integration(self, mock_yolo_results, mock_template_results):
        """测试融合引擎集成"""
        # 创建融合引擎
        fusion_engine = FusionEngine()
        
        # 执行融合
        fused_results = fusion_engine.fuse_results(mock_yolo_results, mock_template_results)
        
        # 验证结果
        assert isinstance(fused_results, list)
        assert len(fused_results) > 0
        
        # 检查是否有融合结果
        fusion_found = any(r.source == DetectionSource.FUSION for r in fused_results)
        # 由于有重叠的边界框，应该有融合结果
        
        # 检查结果的有效性
        for result in fused_results:
            assert isinstance(result, DetectionResult)
            assert 0.0 <= result.confidence <= 1.0
            assert result.bbox.width > 0
            assert result.bbox.height > 0
    
    @patch('yolo_opencv_detector.core.screen_capture.mss')
    def test_screen_capture_integration(self, mock_mss, config_manager):
        """测试屏幕截取集成"""
        # 模拟MSS
        mock_sct = Mock()
        mock_mss.mss.return_value = mock_sct
        
        # 模拟截图数据
        mock_screenshot = {
            'width': 800,
            'height': 600,
            'rgb': np.random.randint(0, 255, (600, 800, 3), dtype=np.uint8).tobytes()
        }
        mock_sct.grab.return_value = mock_screenshot
        
        # 创建屏幕截取服务
        capture_service = ScreenCaptureService(config_manager)
        
        # 执行截图
        screenshot = capture_service.capture_screen()
        
        # 验证结果
        assert screenshot is not None
        assert isinstance(screenshot, np.ndarray)
        assert len(screenshot.shape) == 3
        assert screenshot.shape[2] == 3  # RGB通道
    
    @patch('yolo_opencv_detector.core.yolo_detector.YOLO')
    @patch('yolo_opencv_detector.core.screen_capture.mss')
    def test_detection_engine_integration(self, mock_mss, mock_yolo_class, config_manager):
        """测试检测引擎集成"""
        # 模拟屏幕截取
        mock_sct = Mock()
        mock_mss.mss.return_value = mock_sct
        mock_screenshot = {
            'width': 800,
            'height': 600,
            'rgb': np.random.randint(0, 255, (600, 800, 3), dtype=np.uint8).tobytes()
        }
        mock_sct.grab.return_value = mock_screenshot
        
        # 模拟YOLO模型
        mock_model = Mock()
        mock_yolo_class.return_value = mock_model
        mock_result = Mock()
        mock_result.boxes = Mock()
        mock_result.boxes.xyxy = np.array([[100, 100, 200, 200]])
        mock_result.boxes.conf = np.array([0.9])
        mock_result.boxes.cls = np.array([1])
        mock_model.return_value = [mock_result]
        
        # 创建检测引擎
        detection_engine = DetectionEngine(config_manager)
        
        # 执行单次检测
        results = detection_engine.detect_once()
        
        # 验证结果
        assert isinstance(results, list)
        # 由于模拟了YOLO结果，应该有检测结果
    
    def test_performance_monitoring(self, config_manager):
        """测试性能监控"""
        # 创建检测引擎
        detection_engine = DetectionEngine(config_manager)
        
        # 获取性能统计
        stats = detection_engine.get_performance_stats()
        
        # 验证统计信息结构
        assert isinstance(stats, dict)
        # 初始状态下可能没有统计数据，但不应该出错
    
    def test_error_handling(self, config_manager):
        """测试错误处理"""
        # 测试无效配置的处理
        try:
            # 创建一个无效的检测器配置
            invalid_config = ConfigManager()
            invalid_config.detection.yolo_model_path = "nonexistent_model.pt"
            
            # 尝试创建检测器（应该优雅地处理错误）
            detector = YOLODetector(invalid_config)
            
            # 即使模型不存在，也不应该在初始化时崩溃
            assert detector is not None
            
        except Exception as e:
            # 如果抛出异常，应该是可预期的异常
            assert isinstance(e, (FileNotFoundError, ValueError, RuntimeError))
    
    def test_memory_usage(self, config_manager, mock_screen_image):
        """测试内存使用"""
        import psutil
        import gc
        
        # 获取初始内存使用
        process = psutil.Process()
        initial_memory = process.memory_info().rss
        
        # 执行多次检测操作
        fusion_engine = FusionEngine()
        
        for i in range(10):
            # 创建模拟结果
            yolo_results = [
                DetectionResult(
                    bbox=BoundingBox(i*10, i*10, 50, 50),
                    confidence=0.8,
                    class_id=1,
                    source=DetectionSource.YOLO
                )
            ]
            
            template_results = [
                DetectionResult(
                    bbox=BoundingBox(i*10+5, i*10+5, 45, 45),
                    confidence=0.7,
                    template_id="test",
                    source=DetectionSource.TEMPLATE
                )
            ]
            
            # 执行融合
            results = fusion_engine.fuse_results(yolo_results, template_results)
            
            # 清理结果
            del results
        
        # 强制垃圾回收
        gc.collect()
        
        # 检查内存使用
        final_memory = process.memory_info().rss
        memory_increase = final_memory - initial_memory
        
        # 内存增长应该在合理范围内（小于100MB）
        assert memory_increase < 100 * 1024 * 1024
    
    def test_concurrent_operations(self, config_manager):
        """测试并发操作"""
        import threading
        import queue
        
        # 创建结果队列
        result_queue = queue.Queue()
        
        def worker():
            try:
                # 创建融合引擎
                fusion_engine = FusionEngine()
                
                # 执行融合操作
                yolo_results = [
                    DetectionResult(
                        bbox=BoundingBox(100, 100, 50, 50),
                        confidence=0.8,
                        class_id=1,
                        source=DetectionSource.YOLO
                    )
                ]
                
                template_results = [
                    DetectionResult(
                        bbox=BoundingBox(105, 105, 45, 45),
                        confidence=0.7,
                        template_id="test",
                        source=DetectionSource.TEMPLATE
                    )
                ]
                
                results = fusion_engine.fuse_results(yolo_results, template_results)
                result_queue.put(("success", len(results)))
                
            except Exception as e:
                result_queue.put(("error", str(e)))
        
        # 创建多个线程
        threads = []
        for i in range(5):
            thread = threading.Thread(target=worker)
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join(timeout=10)
        
        # 检查结果
        success_count = 0
        error_count = 0
        
        while not result_queue.empty():
            status, result = result_queue.get()
            if status == "success":
                success_count += 1
                assert isinstance(result, int)
                assert result >= 0
            else:
                error_count += 1
        
        # 大部分操作应该成功
        assert success_count >= 3
        assert error_count <= 2
    
    def test_configuration_changes(self, config_manager):
        """测试配置变更"""
        # 创建融合引擎
        fusion_engine = FusionEngine()
        
        # 获取初始配置
        initial_iou = fusion_engine.iou_threshold
        
        # 更新配置
        new_config = {
            "iou_threshold": 0.7,
            "confidence_weight": 0.8,
            "template_weight": 0.2
        }
        
        fusion_engine.update_config(**new_config)
        
        # 验证配置更新
        assert fusion_engine.iou_threshold == 0.7
        assert abs(fusion_engine.confidence_weight - 0.8) < 0.01
        assert abs(fusion_engine.template_weight - 0.2) < 0.01
        
        # 验证配置有效性
        assert fusion_engine.validate_config() == True
    
    def test_system_cleanup(self, config_manager):
        """测试系统清理"""
        # 创建各种组件
        detection_engine = DetectionEngine(config_manager)
        fusion_engine = FusionEngine()
        
        # 获取初始统计
        initial_stats = fusion_engine.get_performance_stats()
        
        # 执行一些操作
        yolo_results = [
            DetectionResult(
                bbox=BoundingBox(100, 100, 50, 50),
                confidence=0.8,
                class_id=1,
                source=DetectionSource.YOLO
            )
        ]
        
        template_results = [
            DetectionResult(
                bbox=BoundingBox(105, 105, 45, 45),
                confidence=0.7,
                template_id="test",
                source=DetectionSource.TEMPLATE
            )
        ]
        
        fusion_engine.fuse_results(yolo_results, template_results)
        
        # 重置统计
        fusion_engine.reset_stats()
        
        # 验证清理
        reset_stats = fusion_engine.get_performance_stats()
        assert reset_stats == {} or len(reset_stats) == 0
