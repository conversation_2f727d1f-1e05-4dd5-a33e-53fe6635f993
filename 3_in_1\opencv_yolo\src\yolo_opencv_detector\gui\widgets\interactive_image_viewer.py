# -*- coding: utf-8 -*-
"""
交互式图像查看器 - 支持缩放、平移、区域选择等功能
作者: Cursor AI
创建时间: 2025-07-05
编码标准: UTF-8无BOM
"""

import time
from typing import Optional, Tuple, List
import numpy as np

from PyQt6.QtWidgets import (
    QGraphicsView, QGraphicsScene, QGraphicsPixmapItem, QGraphicsRectItem,
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QToolBar,
    QApplication, QGraphicsItem
)
from PyQt6.QtCore import Qt, pyqtSignal, QRectF, QPointF, QTimer, QPropertyAnimation, QEasingCurve
from PyQt6.QtGui import (
    QPixmap, QPen, QBrush, QColor, QWheelEvent, QMouseEvent, 
    QKeyEvent, QPainter, QTransform, QCursor
)

from ...utils.logger import Logger


class ImageViewerToolbar(QWidget):
    """图像查看器工具栏"""

    # 信号定义
    fit_to_window_requested = pyqtSignal()
    actual_size_requested = pyqtSignal()
    zoom_in_requested = pyqtSignal()
    zoom_out_requested = pyqtSignal()
    reset_view_requested = pyqtSignal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = Logger()
        self._init_ui()

    def _init_ui(self):
        """初始化用户界面"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(8)

        # 缩放控制按钮
        self.zoom_out_btn = QPushButton("🔍-")
        self.zoom_out_btn.setToolTip("缩小 (Ctrl+-)")
        self.zoom_out_btn.setFixedSize(35, 30)
        self.zoom_out_btn.clicked.connect(self.zoom_out_requested.emit)
        layout.addWidget(self.zoom_out_btn)

        # 缩放比例显示
        self.zoom_label = QLabel("100%")
        self.zoom_label.setMinimumWidth(50)
        self.zoom_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.zoom_label.setStyleSheet("""
            QLabel {
                background-color: #f0f0f0;
                border: 1px solid #ccc;
                border-radius: 3px;
                padding: 2px 5px;
                font-weight: bold;
            }
        """)
        layout.addWidget(self.zoom_label)

        self.zoom_in_btn = QPushButton("🔍+")
        self.zoom_in_btn.setToolTip("放大 (Ctrl++)")
        self.zoom_in_btn.setFixedSize(35, 30)
        self.zoom_in_btn.clicked.connect(self.zoom_in_requested.emit)
        layout.addWidget(self.zoom_in_btn)

        # 分隔线
        layout.addWidget(self._create_separator())

        # 视图控制按钮
        self.fit_window_btn = QPushButton("📐 适应窗口")
        self.fit_window_btn.setToolTip("调整图像大小以适应窗口")
        self.fit_window_btn.clicked.connect(self.fit_to_window_requested.emit)
        layout.addWidget(self.fit_window_btn)

        self.actual_size_btn = QPushButton("📏 实际大小")
        self.actual_size_btn.setToolTip("显示图像原始尺寸 (Ctrl+0)")
        self.actual_size_btn.clicked.connect(self.actual_size_requested.emit)
        layout.addWidget(self.actual_size_btn)

        # 分隔线
        layout.addWidget(self._create_separator())

        # 图像信息显示
        self.info_label = QLabel("无图像")
        self.info_label.setStyleSheet("color: #666; font-size: 11px;")
        layout.addWidget(self.info_label)

        layout.addStretch()

        # 重置按钮
        self.reset_btn = QPushButton("🔄 重置")
        self.reset_btn.setToolTip("重置视图到初始状态")
        self.reset_btn.clicked.connect(self.reset_view_requested.emit)
        self.reset_btn.setVisible(False)  # 默认隐藏
        layout.addWidget(self.reset_btn)

        # 操作提示
        self.hint_label = QLabel("💡 Ctrl+拖拽平移 | 滚轮缩放 | 拖拽选择区域")
        self.hint_label.setStyleSheet("color: #888; font-size: 10px; font-style: italic;")
        layout.addWidget(self.hint_label)

    def _create_separator(self):
        """创建分隔线"""
        line = QLabel("|")
        line.setStyleSheet("color: #ccc; margin: 0 5px;")
        return line

    def update_zoom_info(self, zoom_factor: float):
        """更新缩放信息"""
        percentage = int(zoom_factor * 100)
        self.zoom_label.setText(f"{percentage}%")

        # 显示/隐藏重置按钮
        if abs(zoom_factor - 1.0) > 0.05:
            self.reset_btn.setVisible(True)
        else:
            self.reset_btn.setVisible(False)

    def update_image_info(self, width: int, height: int):
        """更新图像信息"""
        if width > 0 and height > 0:
            self.info_label.setText(f"📐 {width} × {height}")
        else:
            self.info_label.setText("无图像")


class InteractiveImageViewerWidget(QWidget):
    """完整的交互式图像查看器组件（包含工具栏）"""

    # 信号定义
    region_selected = pyqtSignal(tuple)  # (x, y, width, height)
    selection_changed = pyqtSignal(tuple)  # 实时选择变化

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = Logger()
        self._init_ui()
        self._init_connections()

    def _init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # 工具栏
        self.toolbar = ImageViewerToolbar(self)
        layout.addWidget(self.toolbar)

        # 图像查看器
        self.viewer = InteractiveImageViewer(self)
        layout.addWidget(self.viewer)

    def _init_connections(self):
        """初始化信号连接"""
        # 工具栏信号
        self.toolbar.fit_to_window_requested.connect(self.viewer.fit_to_window)
        self.toolbar.actual_size_requested.connect(self.viewer.actual_size)
        self.toolbar.zoom_in_requested.connect(self.viewer.zoom_in)
        self.toolbar.zoom_out_requested.connect(self.viewer.zoom_out)
        self.toolbar.reset_view_requested.connect(self._reset_view)

        # 查看器信号
        self.viewer.zoom_changed.connect(self.toolbar.update_zoom_info)
        self.viewer.region_selected.connect(self.region_selected.emit)
        self.viewer.selection_changed.connect(self.selection_changed.emit)

    def _reset_view(self):
        """重置视图"""
        self.viewer.actual_size()
        self.viewer.clear_selection()

    def set_image(self, pixmap: QPixmap):
        """设置图像"""
        self.viewer.set_image(pixmap)
        width, height = self.viewer.get_image_size()
        self.toolbar.update_image_info(width, height)
        self.toolbar.update_zoom_info(self.viewer.get_zoom_factor())

    def clear_selection(self):
        """清除选择"""
        self.viewer.clear_selection()

    def set_selection_enabled(self, enabled: bool):
        """设置是否启用区域选择"""
        self.viewer.set_selection_enabled(enabled)

    def get_selected_region(self) -> Optional[Tuple[int, int, int, int]]:
        """获取选择的区域"""
        if self.viewer.selection_rect:
            rect = self.viewer.selection_rect.rect()
            return (int(rect.x()), int(rect.y()), int(rect.width()), int(rect.height()))
        return None

    def update_zoom_info(self, zoom_factor: float):
        """更新缩放信息"""
        percentage = int(zoom_factor * 100)
        self.zoom_label.setText(f"{percentage}%")

        # 显示/隐藏重置按钮
        if abs(zoom_factor - 1.0) > 0.05:
            self.reset_btn.setVisible(True)
        else:
            self.reset_btn.setVisible(False)

    def update_image_info(self, width: int, height: int):
        """更新图像信息"""
        if width > 0 and height > 0:
            self.info_label.setText(f"📐 {width} × {height}")
        else:
            self.info_label.setText("无图像")


class InteractiveImageViewer(QGraphicsView):
    """交互式图像查看器"""
    
    # 信号定义
    region_selected = pyqtSignal(tuple)  # (x, y, width, height)
    selection_changed = pyqtSignal(tuple)  # 实时选择变化
    zoom_changed = pyqtSignal(float)  # 缩放比例变化
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = Logger()
        
        # 图像和场景
        self.scene = QGraphicsScene(self)
        self.setScene(self.scene)
        self.pixmap_item = None
        self.original_pixmap = None
        
        # 缩放控制
        self.zoom_factor = 1.0
        self.min_zoom = 0.1
        self.max_zoom = 5.0
        self.zoom_step = 0.1
        
        # 平移控制
        self.pan_start_point = None
        self.is_panning = False
        
        # 区域选择
        self.selection_rect = None
        self.selection_start = None
        self.is_selecting = False
        self.selection_enabled = True
        
        # 动画 - 暂时禁用，使用直接缩放
        # self.zoom_animation = QPropertyAnimation(self, b"zoom_factor")
        # self.zoom_animation.setDuration(200)
        # self.zoom_animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        
        self._init_view()
        self._init_shortcuts()
        
        self.logger.info("交互式图像查看器初始化完成")
    
    def _init_view(self):
        """初始化视图设置"""
        # 基本设置
        self.setDragMode(QGraphicsView.DragMode.NoDrag)
        self.setRenderHint(QPainter.RenderHint.Antialiasing, True)
        self.setRenderHint(QPainter.RenderHint.SmoothPixmapTransform, True)
        self.setOptimizationFlag(QGraphicsView.OptimizationFlag.DontAdjustForAntialiasing, True)
        self.setViewportUpdateMode(QGraphicsView.ViewportUpdateMode.SmartViewportUpdate)
        
        # 滚动条设置
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        
        # 背景设置
        self.setBackgroundBrush(QBrush(QColor(240, 240, 240)))
        
        # 鼠标跟踪
        self.setMouseTracking(True)
        
        # 焦点设置
        self.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
    
    def _init_shortcuts(self):
        """初始化快捷键"""
        # 这里可以添加快捷键处理
        pass
    
    def set_image(self, pixmap: QPixmap):
        """设置要显示的图像"""
        try:
            if pixmap is None or pixmap.isNull():
                return
            
            # 清除现有内容
            self.scene.clear()
            
            # 保存原始图像
            self.original_pixmap = pixmap
            
            # 创建图像项
            self.pixmap_item = QGraphicsPixmapItem(pixmap)
            self.pixmap_item.setTransformationMode(Qt.TransformationMode.SmoothTransformation)
            self.scene.addItem(self.pixmap_item)
            
            # 设置场景矩形
            self.scene.setSceneRect(QRectF(pixmap.rect()))
            
            # 重置缩放
            self.zoom_factor = 1.0
            self.resetTransform()
            
            # 适应窗口
            self.fit_to_window()
            
            self.logger.info(f"图像已设置: {pixmap.width()}×{pixmap.height()}")
            
        except Exception as e:
            self.logger.error(f"设置图像失败: {e}")
    
    def fit_to_window(self):
        """适应窗口大小"""
        try:
            if not self.pixmap_item:
                return
            
            # 获取视图和图像尺寸
            view_rect = self.viewport().rect()
            image_rect = self.pixmap_item.boundingRect()
            
            if view_rect.isEmpty() or image_rect.isEmpty():
                return
            
            # 计算缩放比例
            scale_x = view_rect.width() / image_rect.width()
            scale_y = view_rect.height() / image_rect.height()
            scale = min(scale_x, scale_y) * 0.95  # 留一点边距
            
            # 限制缩放范围
            scale = max(self.min_zoom, min(self.max_zoom, scale))
            
            # 应用缩放
            self.zoom_factor = scale
            self.setTransform(QTransform().scale(scale, scale))
            
            # 居中显示
            self.centerOn(self.pixmap_item)
            
            self.zoom_changed.emit(self.zoom_factor)
            
        except Exception as e:
            self.logger.error(f"适应窗口失败: {e}")
    
    def actual_size(self):
        """显示实际大小"""
        try:
            if not self.pixmap_item:
                return
            
            self.zoom_factor = 1.0
            self.setTransform(QTransform())
            self.centerOn(self.pixmap_item)
            
            self.zoom_changed.emit(self.zoom_factor)
            
        except Exception as e:
            self.logger.error(f"显示实际大小失败: {e}")
    
    def zoom_in(self):
        """放大"""
        self.zoom(1 + self.zoom_step)
    
    def zoom_out(self):
        """缩小"""
        self.zoom(1 - self.zoom_step)
    
    def zoom(self, factor: float):
        """缩放到指定倍数"""
        try:
            if not self.pixmap_item:
                return
            
            new_zoom = self.zoom_factor * factor
            new_zoom = max(self.min_zoom, min(self.max_zoom, new_zoom))
            
            if abs(new_zoom - self.zoom_factor) < 0.01:
                return
            
            # 获取鼠标位置作为缩放中心
            mouse_pos = self.mapToScene(self.mapFromGlobal(QCursor.pos()))
            
            # 应用缩放
            scale_factor = new_zoom / self.zoom_factor
            self.scale(scale_factor, scale_factor)
            self.zoom_factor = new_zoom
            
            # 调整视图位置，保持鼠标位置不变
            new_mouse_pos = self.mapToScene(self.mapFromGlobal(QCursor.pos()))
            delta = mouse_pos - new_mouse_pos
            self.translate(delta.x(), delta.y())
            
            self.zoom_changed.emit(self.zoom_factor)
            
        except Exception as e:
            self.logger.error(f"缩放失败: {e}")
    
    def wheelEvent(self, event: QWheelEvent):
        """鼠标滚轮事件 - 缩放"""
        try:
            if not self.pixmap_item:
                return
            
            # 检查是否按住Ctrl键
            modifiers = QApplication.keyboardModifiers()
            if modifiers == Qt.KeyboardModifier.ControlModifier:
                # Ctrl+滚轮：缩放
                delta = event.angleDelta().y()
                if delta > 0:
                    self.zoom_in()
                else:
                    self.zoom_out()
                event.accept()
            else:
                # 普通滚轮：滚动
                super().wheelEvent(event)
                
        except Exception as e:
            self.logger.error(f"滚轮事件处理失败: {e}")
    
    def mousePressEvent(self, event: QMouseEvent):
        """鼠标按下事件"""
        try:
            if event.button() == Qt.MouseButton.LeftButton:
                modifiers = QApplication.keyboardModifiers()
                if modifiers == Qt.KeyboardModifier.ControlModifier:
                    # Ctrl+左键：平移
                    self._start_panning(event.pos())
                elif modifiers == Qt.KeyboardModifier.NoModifier:
                    if self.selection_enabled:
                        # 普通左键：区域选择
                        self._start_selection(event.pos())
                    else:
                        # 如果区域选择被禁用，则平移
                        self._start_panning(event.pos())

            elif event.button() == Qt.MouseButton.MiddleButton:
                # 中键：平移
                self._start_panning(event.pos())

            super().mousePressEvent(event)

        except Exception as e:
            self.logger.error(f"鼠标按下事件处理失败: {e}")
    
    def mouseMoveEvent(self, event: QMouseEvent):
        """鼠标移动事件"""
        try:
            if self.is_panning:
                self._update_panning(event.pos())
            elif self.is_selecting:
                self._update_selection(event.pos())
                
            super().mouseMoveEvent(event)
            
        except Exception as e:
            self.logger.error(f"鼠标移动事件处理失败: {e}")
    
    def mouseReleaseEvent(self, event: QMouseEvent):
        """鼠标释放事件"""
        try:
            if event.button() == Qt.MouseButton.LeftButton:
                if self.is_panning:
                    self._end_panning()
                elif self.is_selecting:
                    self._end_selection()
                    
            elif event.button() == Qt.MouseButton.MiddleButton:
                if self.is_panning:
                    self._end_panning()
                    
            super().mouseReleaseEvent(event)
            
        except Exception as e:
            self.logger.error(f"鼠标释放事件处理失败: {e}")
    
    def mouseDoubleClickEvent(self, event: QMouseEvent):
        """鼠标双击事件 - 切换适应窗口/实际大小"""
        try:
            if event.button() == Qt.MouseButton.LeftButton:
                if abs(self.zoom_factor - 1.0) < 0.1:
                    self.fit_to_window()
                else:
                    self.actual_size()
                    
        except Exception as e:
            self.logger.error(f"双击事件处理失败: {e}")
    
    def keyPressEvent(self, event: QKeyEvent):
        """键盘事件"""
        try:
            modifiers = event.modifiers()
            key = event.key()
            
            if modifiers == Qt.KeyboardModifier.ControlModifier:
                if key == Qt.Key.Key_Plus or key == Qt.Key.Key_Equal:
                    self.zoom_in()
                    event.accept()
                    return
                elif key == Qt.Key.Key_Minus:
                    self.zoom_out()
                    event.accept()
                    return
                elif key == Qt.Key.Key_0:
                    self.actual_size()
                    event.accept()
                    return
                    
            super().keyPressEvent(event)
            
        except Exception as e:
            self.logger.error(f"键盘事件处理失败: {e}")
    
    def _start_panning(self, pos):
        """开始平移"""
        self.is_panning = True
        self.pan_start_point = pos
        self.setCursor(Qt.CursorShape.ClosedHandCursor)
    
    def _update_panning(self, pos):
        """更新平移"""
        if not self.is_panning or not self.pan_start_point:
            return
            
        delta = pos - self.pan_start_point
        self.horizontalScrollBar().setValue(
            self.horizontalScrollBar().value() - delta.x()
        )
        self.verticalScrollBar().setValue(
            self.verticalScrollBar().value() - delta.y()
        )
        self.pan_start_point = pos
    
    def _end_panning(self):
        """结束平移"""
        self.is_panning = False
        self.pan_start_point = None
        self.setCursor(Qt.CursorShape.ArrowCursor)
    
    def _start_selection(self, pos):
        """开始区域选择"""
        if not self.pixmap_item:
            return
            
        scene_pos = self.mapToScene(pos)
        image_rect = self.pixmap_item.boundingRect()
        
        if image_rect.contains(scene_pos):
            self.is_selecting = True
            self.selection_start = scene_pos
            
            # 清除现有选择
            if self.selection_rect:
                self.scene.removeItem(self.selection_rect)
                
            # 创建新的选择矩形
            self.selection_rect = QGraphicsRectItem()
            self.selection_rect.setPen(QPen(QColor(0, 120, 215), 2))
            self.selection_rect.setBrush(QBrush(QColor(0, 120, 215, 30)))
            self.scene.addItem(self.selection_rect)
    
    def _update_selection(self, pos):
        """更新区域选择"""
        if not self.is_selecting or not self.selection_start or not self.selection_rect:
            return
            
        scene_pos = self.mapToScene(pos)
        
        # 计算选择矩形
        rect = QRectF(self.selection_start, scene_pos).normalized()
        
        # 限制在图像范围内
        if self.pixmap_item:
            image_rect = self.pixmap_item.boundingRect()
            rect = rect.intersected(image_rect)
        
        self.selection_rect.setRect(rect)
        
        # 发送实时选择信号
        if rect.width() > 5 and rect.height() > 5:
            self.selection_changed.emit((
                int(rect.x()), int(rect.y()),
                int(rect.width()), int(rect.height())
            ))
    
    def _end_selection(self):
        """结束区域选择"""
        if not self.is_selecting:
            return
            
        self.is_selecting = False
        
        if self.selection_rect:
            rect = self.selection_rect.rect()
            if rect.width() > 5 and rect.height() > 5:
                self.region_selected.emit((
                    int(rect.x()), int(rect.y()),
                    int(rect.width()), int(rect.height())
                ))
    
    def clear_selection(self):
        """清除选择"""
        if self.selection_rect:
            self.scene.removeItem(self.selection_rect)
            self.selection_rect = None
        self.is_selecting = False
        self.selection_start = None
    
    def set_selection_enabled(self, enabled: bool):
        """设置是否启用区域选择"""
        self.selection_enabled = enabled
        if not enabled:
            self.clear_selection()
    
    def get_zoom_factor(self) -> float:
        """获取当前缩放比例"""
        return self.zoom_factor
    
    def get_image_size(self) -> Tuple[int, int]:
        """获取图像尺寸"""
        if self.original_pixmap:
            return self.original_pixmap.width(), self.original_pixmap.height()
        return 0, 0
