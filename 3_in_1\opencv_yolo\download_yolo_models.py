#!/usr/bin/env python3
"""
YOLO模型批量下载脚本
下载所有预设的YOLO模型到models目录
"""

import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

def main():
    """主函数"""
    print("🚀 YOLO模型批量下载工具")
    print("=" * 50)
    print("目标: 下载所有预设的YOLO模型")
    print("=" * 50)
    
    try:
        from yolo_opencv_detector.utils.model_downloader import ModelDownloader
        
        # 创建下载器
        downloader = ModelDownloader()
        
        print("\n📋 **可用模型列表**:")
        models_info = downloader.list_available_models()
        for i, (model_name, info) in enumerate(models_info.items(), 1):
            print(f"   {i}. {model_name}")
            print(f"      大小: {info.get('size', '未知')}")
            print(f"      类型: {info.get('type', '未知')}")
        
        print(f"\n🎯 总计: {len(models_info)} 个模型")
        
        # 询问用户是否继续
        response = input("\n是否开始下载所有模型？(y/n): ").lower().strip()
        if response not in ['y', 'yes', '是']:
            print("❌ 下载已取消")
            return
        
        print("\n🚀 开始下载模型...")
        print("=" * 50)
        
        # 创建models目录
        models_dir = Path("models")
        models_dir.mkdir(exist_ok=True)
        print(f"📁 模型保存目录: {models_dir.absolute()}")
        
        # 下载所有模型
        success_count = 0
        total_count = len(downloader.model_urls)
        
        for i, model_name in enumerate(downloader.model_urls.keys(), 1):
            print(f"\n📥 [{i}/{total_count}] 下载模型: {model_name}")
            
            # 检查是否已存在
            if downloader.check_model_exists(model_name):
                print(f"   ✅ 模型已存在，跳过下载")
                success_count += 1
                continue
            
            # 显示模型信息
            model_info = downloader.get_model_info(model_name)
            print(f"   📊 大小: {model_info.get('size', '未知')}")
            print(f"   🎯 类型: {model_info.get('type', '未知')}")
            
            # 下载模型
            if downloader.download_model(model_name):
                success_count += 1
                print(f"   ✅ 下载成功")
            else:
                print(f"   ❌ 下载失败")
        
        print("\n" + "=" * 50)
        print(f"🎯 下载完成: {success_count}/{total_count} 个模型")
        
        if success_count == total_count:
            print("✅ 所有模型下载成功！")
            print("\n💡 现在您可以在应用程序中使用这些预设模型:")
            print("   1. 启动YOLO OpenCV检测工具")
            print("   2. 点击'配置'标签页")
            print("   3. 在'预设模型'下拉框中选择模型")
            print("   4. 开始检测")
        else:
            failed_count = total_count - success_count
            print(f"⚠️  有 {failed_count} 个模型下载失败")
            print("\n🔧 可能的解决方案:")
            print("   1. 检查网络连接")
            print("   2. 检查防火墙设置")
            print("   3. 稍后重试下载")
            print("   4. 手动下载失败的模型")
        
        print(f"\n📁 模型文件位置: {models_dir.absolute()}")
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保项目依赖已正确安装")
    except Exception as e:
        print(f"❌ 下载失败: {e}")
        import traceback
        traceback.print_exc()

def download_single_model():
    """下载单个模型的交互式函数"""
    try:
        from yolo_opencv_detector.utils.model_downloader import ModelDownloader
        
        downloader = ModelDownloader()
        models = list(downloader.model_urls.keys())
        
        print("\n📋 **可用模型**:")
        for i, model_name in enumerate(models, 1):
            info = downloader.get_model_info(model_name)
            status = "✅ 已存在" if downloader.check_model_exists(model_name) else "📥 未下载"
            print(f"   {i}. {model_name} ({info.get('size', '未知')}) - {status}")
        
        while True:
            try:
                choice = input(f"\n请选择要下载的模型 (1-{len(models)}, 0退出): ").strip()
                if choice == '0':
                    break
                
                index = int(choice) - 1
                if 0 <= index < len(models):
                    model_name = models[index]
                    print(f"\n📥 下载模型: {model_name}")
                    
                    if downloader.download_model(model_name):
                        print(f"✅ {model_name} 下载成功")
                    else:
                        print(f"❌ {model_name} 下载失败")
                else:
                    print("❌ 无效选择")
            except ValueError:
                print("❌ 请输入有效数字")
            except KeyboardInterrupt:
                print("\n❌ 下载已取消")
                break
                
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--single":
        download_single_model()
    else:
        main()
