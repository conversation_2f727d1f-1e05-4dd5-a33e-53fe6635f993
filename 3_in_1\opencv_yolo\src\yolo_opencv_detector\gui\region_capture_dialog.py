# -*- coding: utf-8 -*-
"""
区域选择截图对话框
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
    QLabel, QLineEdit, QTextEdit, QGroupBox,
    QDoubleSpinBox, QMessageBox, QFileDialog, QApplication
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer, QRect
from PyQt6.QtGui import QPixmap, QImage, QPainter, QPen, QColor, QCursor

import cv2
import numpy as np
from pathlib import Path
import time

from ..utils.logger import Logger


class RegionSelectionWidget(QLabel):
    """区域选择组件"""
    
    region_selected = pyqtSignal(QRect)  # 选择区域信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMinimumSize(400, 300)
        self.setStyleSheet("border: 1px solid gray;")
        self.setCursor(QCursor(Qt.CursorShape.CrossCursor))
        
        # 选择状态
        self.selecting = False
        self.start_point = None
        self.end_point = None
        self.selection_rect = QRect()
        
        # 原始图像
        self.original_image = None
        self.scaled_pixmap = None
        self.scale_factor = 1.0
        
    def set_image(self, cv_image):
        """设置图像"""
        try:
            # 保存原始图像
            self.original_image = cv_image.copy()
            
            # 转换为QPixmap
            height, width = cv_image.shape[:2]
            bytes_per_line = 3 * width
            rgb_image = cv2.cvtColor(cv_image, cv2.COLOR_BGR2RGB)
            q_image = QImage(rgb_image.data, width, height, bytes_per_line, QImage.Format.Format_RGB888)
            pixmap = QPixmap.fromImage(q_image)
            
            # 缩放到组件大小
            self.scaled_pixmap = pixmap.scaled(
                self.size(),
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation
            )
            
            # 计算缩放比例
            self.scale_factor = min(
                self.width() / pixmap.width(),
                self.height() / pixmap.height()
            )
            
            self.setPixmap(self.scaled_pixmap)
            
        except Exception as e:
            print(f"设置图像失败: {e}")
    
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.MouseButton.LeftButton and self.scaled_pixmap:
            self.selecting = True
            self.start_point = event.position().toPoint()
            self.end_point = self.start_point
            self.selection_rect = QRect()
    
    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if self.selecting and self.scaled_pixmap:
            self.end_point = event.position().toPoint()
            self.update()
    
    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.MouseButton.LeftButton and self.selecting:
            self.selecting = False
            
            if self.start_point and self.end_point:
                # 计算选择区域
                x1 = min(self.start_point.x(), self.end_point.x())
                y1 = min(self.start_point.y(), self.end_point.y())
                x2 = max(self.start_point.x(), self.end_point.x())
                y2 = max(self.start_point.y(), self.end_point.y())
                
                # 确保在图像范围内
                pixmap_rect = self.scaled_pixmap.rect()
                x1 = max(0, min(x1, pixmap_rect.width()))
                y1 = max(0, min(y1, pixmap_rect.height()))
                x2 = max(0, min(x2, pixmap_rect.width()))
                y2 = max(0, min(y2, pixmap_rect.height()))
                
                if x2 > x1 and y2 > y1:
                    self.selection_rect = QRect(x1, y1, x2 - x1, y2 - y1)
                    self.region_selected.emit(self.selection_rect)
    
    def paintEvent(self, event):
        """绘制事件"""
        super().paintEvent(event)
        
        if self.selecting and self.start_point and self.end_point:
            painter = QPainter(self)
            painter.setPen(QPen(QColor(255, 0, 0), 2, Qt.PenStyle.DashLine))
            
            x1 = min(self.start_point.x(), self.end_point.x())
            y1 = min(self.start_point.y(), self.end_point.y())
            x2 = max(self.start_point.x(), self.end_point.x())
            y2 = max(self.start_point.y(), self.end_point.y())
            
            painter.drawRect(x1, y1, x2 - x1, y2 - y1)
    
    def get_selected_region(self):
        """获取选择的区域图像"""
        if not self.original_image is None and not self.selection_rect.isEmpty():
            try:
                # 将选择区域坐标转换回原始图像坐标
                orig_height, orig_width = self.original_image.shape[:2]
                
                # 计算实际坐标
                x = int(self.selection_rect.x() / self.scale_factor)
                y = int(self.selection_rect.y() / self.scale_factor)
                w = int(self.selection_rect.width() / self.scale_factor)
                h = int(self.selection_rect.height() / self.scale_factor)
                
                # 确保坐标在有效范围内
                x = max(0, min(x, orig_width - 1))
                y = max(0, min(y, orig_height - 1))
                w = min(w, orig_width - x)
                h = min(h, orig_height - y)
                
                if w > 0 and h > 0:
                    return self.original_image[y:y+h, x:x+w]
                    
            except Exception as e:
                print(f"获取选择区域失败: {e}")
        
        return None


class RegionCaptureDialog(QDialog):
    """区域选择截图对话框"""
    
    template_created = pyqtSignal(str, dict)  # template_id, template_data
    
    def __init__(self, parent=None):
        """
        初始化区域选择截图对话框
        
        Args:
            parent: 父窗口
        """
        super().__init__(parent)
        self.logger = Logger().get_logger(__name__)
        
        # 数据
        self.captured_image = None
        self.selected_region = None
        
        self._setup_ui()
        self._connect_signals()
        
        self.logger.info("区域选择截图对话框初始化完成")
    
    def _setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("创建模板 - 区域选择")
        self.setModal(True)
        self.resize(800, 700)
        
        layout = QVBoxLayout(self)
        
        # 截取控制组
        capture_group = QGroupBox("获取图像")
        capture_layout = QVBoxLayout(capture_group)
        
        # 说明文本
        info_label = QLabel(
            "📋 操作步骤：\n"
            "1️⃣ 点击下方按钮获取图像\n"
            "2️⃣ 在预览区域用鼠标拖拽选择需要的区域\n"
            "3️⃣ 输入模板信息并创建"
        )
        info_label.setWordWrap(True)
        info_label.setStyleSheet("QLabel { background-color: #f0f8ff; padding: 10px; border: 1px solid #ccc; border-radius: 5px; }")
        capture_layout.addWidget(info_label)
        
        # 截取按钮
        button_layout = QHBoxLayout()
        
        self.capture_btn = QPushButton("截取屏幕")
        self.capture_btn.setMinimumHeight(40)
        button_layout.addWidget(self.capture_btn)
        
        self.load_file_btn = QPushButton("从文件加载")
        button_layout.addWidget(self.load_file_btn)
        
        capture_layout.addLayout(button_layout)
        
        # 状态信息
        self.status_label = QLabel("请选择获取图像的方式")
        capture_layout.addWidget(self.status_label)
        
        layout.addWidget(capture_group)
        
        # 区域选择组
        selection_group = QGroupBox("🖼️ 图像预览与区域选择")
        selection_layout = QVBoxLayout(selection_group)

        # 选择提示
        self.selection_hint = QLabel("📌 请先获取图像，然后在下方预览区域拖拽选择需要的区域")
        self.selection_hint.setStyleSheet("QLabel { color: #666; font-style: italic; }")
        selection_layout.addWidget(self.selection_hint)

        self.region_widget = RegionSelectionWidget()
        self.region_widget.region_selected.connect(self._on_region_selected)
        selection_layout.addWidget(self.region_widget)
        
        layout.addWidget(selection_group)
        
        # 模板信息组
        info_group = QGroupBox("模板信息")
        info_layout = QVBoxLayout(info_group)
        
        # 模板名称
        name_layout = QHBoxLayout()
        name_layout.addWidget(QLabel("名称:"))
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("输入模板名称...")
        name_layout.addWidget(self.name_edit)
        info_layout.addLayout(name_layout)
        
        # 模板描述
        desc_layout = QVBoxLayout()
        desc_layout.addWidget(QLabel("描述:"))
        self.desc_edit = QTextEdit()
        self.desc_edit.setMaximumHeight(60)
        self.desc_edit.setPlaceholderText("输入模板描述...")
        desc_layout.addWidget(self.desc_edit)
        info_layout.addLayout(desc_layout)
        
        # 匹配阈值
        threshold_layout = QHBoxLayout()
        threshold_layout.addWidget(QLabel("匹配阈值:"))
        self.threshold_spin = QDoubleSpinBox()
        self.threshold_spin.setRange(0.1, 1.0)
        self.threshold_spin.setSingleStep(0.05)
        self.threshold_spin.setDecimals(2)
        self.threshold_spin.setValue(0.8)
        threshold_layout.addWidget(self.threshold_spin)
        info_layout.addLayout(threshold_layout)
        
        layout.addWidget(info_group)
        
        # 按钮
        button_layout = QHBoxLayout()
        
        self.cancel_btn = QPushButton("取消")
        button_layout.addWidget(self.cancel_btn)
        
        button_layout.addStretch()
        
        self.create_btn = QPushButton("创建模板")
        self.create_btn.setEnabled(False)
        button_layout.addWidget(self.create_btn)
        
        layout.addLayout(button_layout)
    
    def _connect_signals(self):
        """连接信号"""
        self.capture_btn.clicked.connect(self._start_screen_capture)
        self.load_file_btn.clicked.connect(self._load_from_file)
        self.create_btn.clicked.connect(self._create_template)
        self.cancel_btn.clicked.connect(self.reject)
        
        # 文本变化
        self.name_edit.textChanged.connect(self._check_can_create)
    
    def _start_screen_capture(self):
        """开始屏幕截取"""
        try:
            reply = QMessageBox.information(
                self,
                "屏幕截取",
                "点击确定后，程序将在3秒后截取整个屏幕。\n"
                "然后您可以在预览中选择需要的区域。",
                QMessageBox.StandardButton.Ok | QMessageBox.StandardButton.Cancel
            )
            
            if reply == QMessageBox.StandardButton.Ok:
                self.hide()
                QTimer.singleShot(3000, self._capture_screen)
            
        except Exception as e:
            self.logger.error(f"开始屏幕截取失败: {e}")
    
    def _capture_screen(self):
        """截取屏幕"""
        try:
            # 使用PIL截图（更稳定）
            from PIL import ImageGrab
            
            screenshot = ImageGrab.grab()
            img_array = np.array(screenshot)
            img_bgr = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
            
            self.captured_image = img_bgr
            self.region_widget.set_image(img_bgr)

            height, width = img_bgr.shape[:2]
            self.status_label.setText(f"📸 屏幕截图成功: {width}×{height}")
            self.selection_hint.setText("🖱️ 现在请在下方预览图中拖拽选择需要的区域")
            self.selection_hint.setStyleSheet("QLabel { color: #0066cc; font-weight: bold; }")
            
            if not self.name_edit.text():
                timestamp = int(time.time())
                self.name_edit.setText(f"screenshot_{timestamp}")
            
            self.logger.info(f"屏幕截图成功: {width}×{height}")
            
        except Exception as e:
            self.logger.error(f"屏幕截图失败: {e}")
            QMessageBox.critical(self, "错误", f"截图失败: {e}")
        
        finally:
            self.show()
    
    def _load_from_file(self):
        """从文件加载图像"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "选择模板图像",
                "",
                "图像文件 (*.png *.jpg *.jpeg *.bmp *.tiff)"
            )
            
            if file_path:
                self.captured_image = cv2.imread(file_path)
                
                if self.captured_image is not None:
                    self.region_widget.set_image(self.captured_image)
                    
                    height, width = self.captured_image.shape[:2]
                    self.status_label.setText(f"文件加载成功: {Path(file_path).name} ({width}×{height})")
                    
                    if not self.name_edit.text():
                        file_name = Path(file_path).stem
                        self.name_edit.setText(f"template_{file_name}")
                    
                    self.logger.info(f"从文件加载图像成功: {file_path}")
                else:
                    QMessageBox.critical(self, "错误", "无法加载图像文件")
                    
        except Exception as e:
            self.logger.error(f"从文件加载图像失败: {e}")
            QMessageBox.critical(self, "错误", f"加载图像失败: {e}")
    
    def _on_region_selected(self, rect):
        """区域选择回调"""
        self.selected_region = self.region_widget.get_selected_region()
        if self.selected_region is not None:
            h, w = self.selected_region.shape[:2]
            self.status_label.setText(f"✅ 已选择区域: {w}×{h} 像素")
            self.selection_hint.setText(f"✅ 区域已选择: {w}×{h} 像素，请输入模板信息")
            self.selection_hint.setStyleSheet("QLabel { color: #008000; font-weight: bold; }")
            self._check_can_create()
        else:
            self.selection_hint.setText("❌ 选择区域无效，请重新选择")
            self.selection_hint.setStyleSheet("QLabel { color: #ff0000; }")
    
    def _check_can_create(self):
        """检查是否可以创建模板"""
        can_create = (
            self.selected_region is not None and
            len(self.name_edit.text().strip()) > 0
        )
        self.create_btn.setEnabled(can_create)
    
    def _create_template(self):
        """创建模板"""
        try:
            if self.selected_region is None:
                QMessageBox.warning(self, "警告", "请先选择图像区域")
                return
            
            name = self.name_edit.text().strip()
            if not name:
                QMessageBox.warning(self, "警告", "请输入模板名称")
                return
            
            # 生成模板ID
            template_id = name.lower().replace(" ", "_").replace("-", "_")
            
            # 保存模板图像
            templates_dir = Path("templates")
            templates_dir.mkdir(exist_ok=True)
            
            template_file = templates_dir / f"{template_id}.png"
            cv2.imwrite(str(template_file), self.selected_region)
            
            # 创建模板数据
            template_data = {
                "name": name,
                "description": self.desc_edit.toPlainText(),
                "file_path": str(template_file),
                "threshold": self.threshold_spin.value(),
                "image_size": self.selected_region.shape[:2],
                "created_time": time.time()
            }
            
            # 发射信号
            self.template_created.emit(template_id, template_data)
            
            QMessageBox.information(self, "成功", f"模板 '{name}' 创建成功！")
            self.accept()
            
        except Exception as e:
            self.logger.error(f"创建模板失败: {e}")
            QMessageBox.critical(self, "错误", f"创建模板失败: {e}")
