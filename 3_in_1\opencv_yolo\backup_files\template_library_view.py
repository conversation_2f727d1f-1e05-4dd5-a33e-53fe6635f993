# -*- coding: utf-8 -*-
"""
模板库视图
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QListWidget,
    QListWidgetItem, QPushButton, QLabel, QGroupBox,
    QLineEdit, QTextEdit, QSplitter, QFileDialog,
    QMessageBox, QInputDialog
)
from PyQt6.QtCore import Qt, pyqtSignal, QSize
from PyQt6.QtGui import QPixmap, QIcon, QFont

from typing import Dict, List, Any
from pathlib import Path
from ..utils.logger import Logger


class TemplateLibraryView(QWidget):
    """模板库视图"""
    
    # 信号定义
    template_selected = pyqtSignal(str)  # template_id
    template_added = pyqtSignal(str, object)  # template_id, template_data
    template_removed = pyqtSignal(str)  # template_id
    template_updated = pyqtSignal(str, dict)  # template_id, metadata
    
    def __init__(self, parent=None):
        """
        初始化模板库视图
        
        Args:
            parent: 父窗口
        """
        super().__init__(parent)
        self.logger = Logger().get_logger(__name__)
        
        # 数据
        self.templates: Dict[str, Dict[str, Any]] = {}
        self.current_template_id: str = None
        
        self._setup_ui()
        self._connect_signals()
        
        self.logger.info("模板库视图初始化完成")
    
    def _setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        layout.addWidget(splitter)
        
        # 左侧：模板列表
        left_widget = self._create_template_list()
        splitter.addWidget(left_widget)
        
        # 右侧：模板详情
        right_widget = self._create_template_details()
        splitter.addWidget(right_widget)
        
        # 设置分割比例
        splitter.setStretchFactor(0, 1)
        splitter.setStretchFactor(1, 1)
    
    def _create_template_list(self) -> QWidget:
        """创建模板列表"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 标题和统计
        header_layout = QHBoxLayout()
        
        title_label = QLabel("模板库")
        title_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        self.count_label = QLabel("0 个模板")
        header_layout.addWidget(self.count_label)
        
        layout.addLayout(header_layout)
        
        # 搜索框
        search_layout = QHBoxLayout()
        search_layout.addWidget(QLabel("搜索:"))
        
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("输入模板名称...")
        search_layout.addWidget(self.search_edit)
        
        layout.addLayout(search_layout)
        
        # 模板列表
        self.template_list = QListWidget()
        self.template_list.setIconSize(QSize(64, 64))
        layout.addWidget(self.template_list)
        
        # 操作按钮
        button_layout = QVBoxLayout()
        
        self.add_btn = QPushButton("添加模板")
        button_layout.addWidget(self.add_btn)
        
        self.import_btn = QPushButton("导入模板")
        button_layout.addWidget(self.import_btn)
        
        self.export_btn = QPushButton("导出模板")
        button_layout.addWidget(self.export_btn)
        
        self.remove_btn = QPushButton("删除模板")
        self.remove_btn.setEnabled(False)
        button_layout.addWidget(self.remove_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        return widget
    
    def _create_template_details(self) -> QWidget:
        """创建模板详情"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 标题
        title_label = QLabel("模板详情")
        title_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        layout.addWidget(title_label)
        
        # 基本信息组
        basic_group = QGroupBox("基本信息")
        basic_layout = QVBoxLayout(basic_group)
        
        # 模板名称
        name_layout = QHBoxLayout()
        name_layout.addWidget(QLabel("名称:"))
        self.name_edit = QLineEdit()
        name_layout.addWidget(self.name_edit)
        basic_layout.addLayout(name_layout)
        
        # 模板描述
        desc_layout = QVBoxLayout()
        desc_layout.addWidget(QLabel("描述:"))
        self.desc_edit = QTextEdit()
        self.desc_edit.setMaximumHeight(80)
        desc_layout.addWidget(self.desc_edit)
        basic_layout.addLayout(desc_layout)
        
        layout.addWidget(basic_group)
        
        # 匹配设置组
        match_group = QGroupBox("匹配设置")
        match_layout = QVBoxLayout(match_group)
        
        # 阈值设置
        threshold_layout = QHBoxLayout()
        threshold_layout.addWidget(QLabel("匹配阈值:"))
        self.threshold_edit = QLineEdit("0.8")
        threshold_layout.addWidget(self.threshold_edit)
        match_layout.addLayout(threshold_layout)
        
        # 缩放范围
        scale_layout = QHBoxLayout()
        scale_layout.addWidget(QLabel("缩放范围:"))
        self.scale_min_edit = QLineEdit("0.8")
        scale_layout.addWidget(self.scale_min_edit)
        scale_layout.addWidget(QLabel("-"))
        self.scale_max_edit = QLineEdit("1.2")
        scale_layout.addWidget(self.scale_max_edit)
        match_layout.addLayout(scale_layout)
        
        layout.addWidget(match_group)
        
        # 预览组
        preview_group = QGroupBox("预览")
        preview_layout = QVBoxLayout(preview_group)
        
        self.preview_label = QLabel("无预览")
        self.preview_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.preview_label.setMinimumHeight(150)
        self.preview_label.setStyleSheet("border: 1px solid gray;")
        preview_layout.addWidget(self.preview_label)
        
        layout.addWidget(preview_group)
        
        # 操作按钮
        action_layout = QHBoxLayout()
        
        self.save_btn = QPushButton("保存")
        self.save_btn.setEnabled(False)
        action_layout.addWidget(self.save_btn)
        
        self.test_btn = QPushButton("测试匹配")
        self.test_btn.setEnabled(False)
        action_layout.addWidget(self.test_btn)
        
        layout.addLayout(action_layout)
        
        layout.addStretch()
        
        return widget
    
    def _connect_signals(self):
        """连接信号"""
        # 列表选择
        self.template_list.itemSelectionChanged.connect(self._on_selection_changed)
        
        # 搜索
        self.search_edit.textChanged.connect(self._on_search_changed)
        
        # 按钮
        self.add_btn.clicked.connect(self._on_add_template)
        self.import_btn.clicked.connect(self._on_import_template)
        self.export_btn.clicked.connect(self._on_export_template)
        self.remove_btn.clicked.connect(self._on_remove_template)
        self.save_btn.clicked.connect(self._on_save_template)
        self.test_btn.clicked.connect(self._on_test_template)
        
        # 编辑框变化
        self.name_edit.textChanged.connect(self._on_details_changed)
        self.desc_edit.textChanged.connect(self._on_details_changed)
        self.threshold_edit.textChanged.connect(self._on_details_changed)
        self.scale_min_edit.textChanged.connect(self._on_details_changed)
        self.scale_max_edit.textChanged.connect(self._on_details_changed)
    
    def add_template(self, template_id: str, template_data: Dict[str, Any]):
        """
        添加模板
        
        Args:
            template_id: 模板ID
            template_data: 模板数据
        """
        self.templates[template_id] = template_data
        self._update_template_list()
        self._update_count()
        
        self.logger.info(f"添加模板: {template_id}")
    
    def remove_template(self, template_id: str):
        """
        移除模板
        
        Args:
            template_id: 模板ID
        """
        if template_id in self.templates:
            del self.templates[template_id]
            self._update_template_list()
            self._update_count()
            
            if self.current_template_id == template_id:
                self.current_template_id = None
                self._clear_details()
            
            self.logger.info(f"移除模板: {template_id}")
    
    def _update_template_list(self):
        """更新模板列表"""
        self.template_list.clear()
        
        search_text = self.search_edit.text().lower()
        
        for template_id, template_data in self.templates.items():
            # 搜索过滤
            if search_text and search_text not in template_id.lower():
                name = template_data.get("name", "")
                if search_text not in name.lower():
                    continue
            
            # 创建列表项
            item = QListWidgetItem(template_id)
            
            # 设置图标（如果有预览图）
            if "preview" in template_data:
                # 这里应该设置预览图标
                pass
            
            # 设置工具提示
            name = template_data.get("name", template_id)
            desc = template_data.get("description", "")
            tooltip = f"名称: {name}\n描述: {desc}"
            item.setToolTip(tooltip)
            
            # 存储模板ID
            item.setData(Qt.ItemDataRole.UserRole, template_id)
            
            self.template_list.addItem(item)
    
    def _update_count(self):
        """更新模板计数"""
        count = len(self.templates)
        self.count_label.setText(f"{count} 个模板")
    
    def _on_selection_changed(self):
        """选择改变事件"""
        current_item = self.template_list.currentItem()
        if current_item:
            template_id = current_item.data(Qt.ItemDataRole.UserRole)
            self.current_template_id = template_id
            self._load_template_details(template_id)
            self.remove_btn.setEnabled(True)
            self.save_btn.setEnabled(True)
            self.test_btn.setEnabled(True)
            self.template_selected.emit(template_id)
        else:
            self.current_template_id = None
            self._clear_details()
            self.remove_btn.setEnabled(False)
            self.save_btn.setEnabled(False)
            self.test_btn.setEnabled(False)
    
    def _load_template_details(self, template_id: str):
        """加载模板详情"""
        if template_id not in self.templates:
            return
        
        template_data = self.templates[template_id]
        
        # 基本信息
        self.name_edit.setText(template_data.get("name", template_id))
        self.desc_edit.setPlainText(template_data.get("description", ""))
        
        # 匹配设置
        self.threshold_edit.setText(str(template_data.get("threshold", 0.8)))
        scale_range = template_data.get("scale_range", [0.8, 1.2])
        self.scale_min_edit.setText(str(scale_range[0]))
        self.scale_max_edit.setText(str(scale_range[1]))
        
        # 预览图
        if "preview" in template_data:
            # 这里应该显示预览图
            self.preview_label.setText("预览图加载中...")
        else:
            self.preview_label.setText("无预览图")
    
    def _clear_details(self):
        """清空详情"""
        self.name_edit.clear()
        self.desc_edit.clear()
        self.threshold_edit.setText("0.8")
        self.scale_min_edit.setText("0.8")
        self.scale_max_edit.setText("1.2")
        self.preview_label.setText("无预览")
    
    def _on_search_changed(self, text: str):
        """搜索文本改变"""
        self._update_template_list()
    
    def _on_add_template(self):
        """添加模板"""
        # 打开文件对话框选择图像
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择模板图像",
            "",
            "图像文件 (*.png *.jpg *.jpeg *.bmp)"
        )
        
        if file_path:
            # 获取模板名称
            name, ok = QInputDialog.getText(
                self,
                "模板名称",
                "请输入模板名称:"
            )
            
            if ok and name:
                template_id = name.replace(" ", "_").lower()
                
                # 创建模板数据
                template_data = {
                    "name": name,
                    "description": "",
                    "file_path": file_path,
                    "threshold": 0.8,
                    "scale_range": [0.8, 1.2]
                }
                
                self.add_template(template_id, template_data)
                self.template_added.emit(template_id, template_data)
    
    def _on_import_template(self):
        """导入模板"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "导入模板文件",
            "",
            "JSON文件 (*.json)"
        )
        
        if file_path:
            try:
                import json
                with open(file_path, 'r', encoding='utf-8') as f:
                    templates_data = json.load(f)
                
                for template_id, template_data in templates_data.items():
                    self.add_template(template_id, template_data)
                
                QMessageBox.information(self, "导入成功", f"成功导入 {len(templates_data)} 个模板")
                
            except Exception as e:
                QMessageBox.critical(self, "导入失败", f"导入模板失败: {e}")
    
    def _on_export_template(self):
        """导出模板"""
        if not self.templates:
            QMessageBox.information(self, "提示", "没有模板可导出")
            return
        
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "导出模板",
            "templates.json",
            "JSON文件 (*.json)"
        )
        
        if file_path:
            try:
                import json
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.templates, f, ensure_ascii=False, indent=2)
                
                QMessageBox.information(self, "导出成功", f"模板已导出到: {file_path}")
                
            except Exception as e:
                QMessageBox.critical(self, "导出失败", f"导出模板失败: {e}")
    
    def _on_remove_template(self):
        """删除模板"""
        if not self.current_template_id:
            return
        
        reply = QMessageBox.question(
            self,
            "确认删除",
            f"确定要删除模板 '{self.current_template_id}' 吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            self.remove_template(self.current_template_id)
            self.template_removed.emit(self.current_template_id)
    
    def _on_save_template(self):
        """保存模板"""
        if not self.current_template_id:
            return
        
        # 更新模板数据
        template_data = self.templates[self.current_template_id]
        template_data["name"] = self.name_edit.text()
        template_data["description"] = self.desc_edit.toPlainText()
        
        try:
            template_data["threshold"] = float(self.threshold_edit.text())
            template_data["scale_range"] = [
                float(self.scale_min_edit.text()),
                float(self.scale_max_edit.text())
            ]
        except ValueError:
            QMessageBox.warning(self, "输入错误", "请输入有效的数值")
            return
        
        self._update_template_list()
        self.template_updated.emit(self.current_template_id, template_data)
        
        QMessageBox.information(self, "保存成功", "模板信息已保存")
    
    def _on_test_template(self):
        """测试模板匹配"""
        if self.current_template_id:
            self.logger.info(f"测试模板匹配: {self.current_template_id}")
            # 这里应该实现模板匹配测试
    
    def _on_details_changed(self):
        """详情改变"""
        # 这里可以添加实时验证逻辑
        pass
    
    def get_template_data(self, template_id: str) -> Dict[str, Any]:
        """获取模板数据"""
        return self.templates.get(template_id, {})
    
    def get_all_templates(self) -> Dict[str, Dict[str, Any]]:
        """获取所有模板"""
        return self.templates.copy()
