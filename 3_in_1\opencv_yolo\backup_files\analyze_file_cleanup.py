# -*- coding: utf-8 -*-
"""
项目文件清理分析脚本
作者: Cursor AI
创建时间: 2025-07-05
编码标准: UTF-8无BOM
"""

import sys
import os
import re
from pathlib import Path
from typing import List, Dict, Set, Tuple

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

class FileCleanupAnalyzer:
    """文件清理分析器"""
    
    def __init__(self):
        self.project_root = project_root
        self.src_path = src_path
        self.gui_path = self.src_path / "yolo_opencv_detector" / "gui"
        self.widgets_path = self.gui_path / "widgets"
        self.core_path = self.src_path / "yolo_opencv_detector" / "core"
        
        # 存储分析结果
        self.all_files = []
        self.import_map = {}  # 文件 -> 导入的模块列表
        self.usage_map = {}   # 文件 -> 被哪些文件导入
        self.v2_files = []    # v2版本文件
        self.v1_files = []    # v1版本文件
        self.obsolete_files = []  # 废弃文件
        self.test_files = []  # 测试文件
        self.temp_files = []  # 临时文件
    
    def scan_project_files(self):
        """扫描项目文件"""
        print("🔍 扫描项目文件...")
        
        # 扫描所有Python文件
        for py_file in self.src_path.rglob("*.py"):
            if "__pycache__" not in str(py_file):
                self.all_files.append(py_file)
        
        # 分类文件
        for file_path in self.all_files:
            filename = file_path.name
            
            # 识别v2文件
            if "_v2.py" in filename:
                self.v2_files.append(file_path)
            
            # 识别可能的v1文件（有对应v2版本的）
            elif filename.replace(".py", "_v2.py") in [f.name for f in self.v2_files]:
                self.v1_files.append(file_path)
            
            # 识别测试文件
            elif filename.startswith("test_") or "test" in filename:
                self.test_files.append(file_path)
            
            # 识别临时文件
            elif any(temp in filename for temp in ["temp", "tmp", "backup", "old"]):
                self.temp_files.append(file_path)
        
        print(f"   📁 总计Python文件: {len(self.all_files)}")
        print(f"   📁 V2版本文件: {len(self.v2_files)}")
        print(f"   📁 V1版本文件: {len(self.v1_files)}")
        print(f"   📁 测试文件: {len(self.test_files)}")
        print(f"   📁 临时文件: {len(self.temp_files)}")
    
    def analyze_imports(self):
        """分析导入关系"""
        print("\n🔗 分析导入关系...")
        
        for file_path in self.all_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 提取import语句
                imports = self._extract_imports(content)
                self.import_map[file_path] = imports
                
                # 建立被导入关系
                for imported_module in imports:
                    if imported_module not in self.usage_map:
                        self.usage_map[imported_module] = []
                    self.usage_map[imported_module].append(file_path)
                
            except Exception as e:
                print(f"   ⚠️ 读取文件失败: {file_path} - {e}")
        
        print(f"   📊 分析了 {len(self.import_map)} 个文件的导入关系")
    
    def _extract_imports(self, content: str) -> List[str]:
        """提取文件中的导入语句"""
        imports = []
        
        # 匹配 from ... import ... 语句
        from_imports = re.findall(r'from\s+([^\s]+)\s+import', content)
        imports.extend(from_imports)
        
        # 匹配 import ... 语句
        direct_imports = re.findall(r'^import\s+([^\s]+)', content, re.MULTILINE)
        imports.extend(direct_imports)
        
        return imports
    
    def identify_obsolete_files(self):
        """识别废弃文件"""
        print("\n🗑️ 识别废弃文件...")
        
        # 检查v1文件是否还被使用
        for v1_file in self.v1_files:
            module_name = self._get_module_name(v1_file)
            is_used = False
            
            # 检查是否被其他文件导入
            for usage_module, using_files in self.usage_map.items():
                if module_name in usage_module:
                    # 排除自己导入自己的情况
                    external_usage = [f for f in using_files if f != v1_file]
                    if external_usage:
                        is_used = True
                        print(f"   📌 {v1_file.name} 仍被使用: {[f.name for f in external_usage]}")
                        break
            
            if not is_used:
                self.obsolete_files.append(v1_file)
                print(f"   🗑️ {v1_file.name} 可能已废弃")
        
        # 检查特定的废弃文件
        obsolete_patterns = [
            "screenshot_dialog.py",  # 旧的截图对话框
            "template_capture_dialog.py",  # 旧的模板截取对话框
            "screen_capture_overlay.py",  # 旧的屏幕覆盖层
            "main.py",  # 旧的主程序（如果有main_v2.py）
        ]
        
        for file_path in self.all_files:
            for pattern in obsolete_patterns:
                if file_path.name == pattern:
                    # 检查是否有对应的v2版本
                    v2_name = pattern.replace(".py", "_v2.py")
                    v2_exists = any(f.name == v2_name for f in self.all_files)
                    
                    if v2_exists:
                        self.obsolete_files.append(file_path)
                        print(f"   🗑️ {file_path.name} 已有v2版本，可能废弃")
    
    def _get_module_name(self, file_path: Path) -> str:
        """获取文件的模块名"""
        # 计算相对于src的路径
        try:
            rel_path = file_path.relative_to(self.src_path)
            module_parts = list(rel_path.parts[:-1]) + [rel_path.stem]
            return ".".join(module_parts)
        except ValueError:
            return file_path.stem
    
    def check_main_entry_points(self):
        """检查主入口点"""
        print("\n🚀 检查主入口点...")
        
        main_files = [f for f in self.all_files if f.name in ["main.py", "main_v2.py"]]
        
        for main_file in main_files:
            print(f"   📄 发现主文件: {main_file}")
            
            # 分析主文件的导入
            if main_file in self.import_map:
                imports = self.import_map[main_file]
                print(f"      导入模块: {len(imports)} 个")
                
                # 检查导入的模块是否存在
                for imported in imports:
                    if "main_window" in imported:
                        print(f"      🏠 主窗口: {imported}")
    
    def generate_cleanup_plan(self):
        """生成清理计划"""
        print("\n📋 生成清理计划...")
        
        cleanup_plan = {
            "safe_to_delete": [],      # 安全删除
            "move_to_backup": [],      # 移动到备份
            "needs_review": [],        # 需要审查
            "keep": []                 # 保留
        }
        
        # 分类废弃文件
        for file_path in self.obsolete_files:
            module_name = self._get_module_name(file_path)
            
            # 检查是否被导入
            is_imported = any(module_name in usage for usage in self.usage_map.keys())
            
            if is_imported:
                cleanup_plan["needs_review"].append(file_path)
            else:
                cleanup_plan["safe_to_delete"].append(file_path)
        
        # 测试文件和临时文件
        for file_path in self.test_files + self.temp_files:
            if "integration" in file_path.name or "simple" in file_path.name:
                cleanup_plan["move_to_backup"].append(file_path)
            else:
                cleanup_plan["needs_review"].append(file_path)
        
        # V2文件保留
        for file_path in self.v2_files:
            cleanup_plan["keep"].append(file_path)
        
        return cleanup_plan
    
    def print_cleanup_report(self, cleanup_plan: Dict):
        """打印清理报告"""
        print("\n" + "=" * 60)
        print("📊 文件清理分析报告")
        print("=" * 60)
        
        print(f"\n🗑️ 安全删除 ({len(cleanup_plan['safe_to_delete'])} 个文件):")
        for file_path in cleanup_plan['safe_to_delete']:
            print(f"   ❌ {file_path.relative_to(self.project_root)}")
        
        print(f"\n📦 移动到备份 ({len(cleanup_plan['move_to_backup'])} 个文件):")
        for file_path in cleanup_plan['move_to_backup']:
            print(f"   📦 {file_path.relative_to(self.project_root)}")
        
        print(f"\n🔍 需要审查 ({len(cleanup_plan['needs_review'])} 个文件):")
        for file_path in cleanup_plan['needs_review']:
            print(f"   ⚠️ {file_path.relative_to(self.project_root)}")
        
        print(f"\n✅ 保留 ({len(cleanup_plan['keep'])} 个文件):")
        for file_path in cleanup_plan['keep']:
            print(f"   ✅ {file_path.relative_to(self.project_root)}")
        
        # 统计信息
        total_files = sum(len(files) for files in cleanup_plan.values())
        print(f"\n📈 统计信息:")
        print(f"   总文件数: {total_files}")
        print(f"   可删除: {len(cleanup_plan['safe_to_delete'])}")
        print(f"   需备份: {len(cleanup_plan['move_to_backup'])}")
        print(f"   需审查: {len(cleanup_plan['needs_review'])}")
        print(f"   保留: {len(cleanup_plan['keep'])}")

def main():
    """主函数"""
    print("🧹 YOLO OpenCV检测器项目文件清理分析")
    print("=" * 60)
    
    analyzer = FileCleanupAnalyzer()
    
    # 执行分析
    analyzer.scan_project_files()
    analyzer.analyze_imports()
    analyzer.identify_obsolete_files()
    analyzer.check_main_entry_points()
    
    # 生成清理计划
    cleanup_plan = analyzer.generate_cleanup_plan()
    
    # 打印报告
    analyzer.print_cleanup_report(cleanup_plan)
    
    return cleanup_plan

if __name__ == "__main__":
    cleanup_plan = main()
