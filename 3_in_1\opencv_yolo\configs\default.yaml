# YOLO OpenCV检测器配置文件
app:
  name: "YOLO OpenCV Detector"
  version: "1.0.0"
  debug: false

detection:
  yolo_model_path: "models/yolov8n.pt"
  confidence_threshold: 0.5
  nms_threshold: 0.4
  max_detections: 100
  input_size: [640, 640]

template:
  threshold: 0.8
  scale_range: [0.8, 1.2]
  angle_range: 15
  max_templates: 50

fusion:
  enable: true
  iou_threshold: 0.5
  confidence_weight: 0.6
  template_weight: 0.4

gui:
  theme: "default"
  window_size: [1200, 800]
  auto_save: true
  language: "zh_CN"

performance:
  max_workers: 4
  batch_size: 1
  cache_size: 100
  gpu_enabled: true
