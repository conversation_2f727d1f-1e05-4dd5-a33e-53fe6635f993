# -*- coding: utf-8 -*-
"""
YOLO OpenCV 界面检测工具 - 重构版本
作者: Cursor AI
创建时间: 2025-07-05
编码标准: UTF-8无BOM
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

def setup_application_icon(app, project_root, logger):
    """设置应用程序图标"""
    try:
        from PyQt6.QtGui import QIcon

        # 图标文件路径（按优先级排序）
        icon_paths = [
            project_root / "icons" / "yolo_detector_taskbar.ico",  # 任务栏专用图标
            project_root / "icons" / "yolo_detector_app.ico",     # 应用程序图标
            project_root / "icons" / "app_icon_64.png",           # PNG备用图标
            project_root / "icons" / "app_icon_32.png",           # 小尺寸PNG图标
        ]

        selected_icon = None
        for icon_path in icon_paths:
            if icon_path.exists():
                selected_icon = icon_path
                logger.info(f"找到图标文件: {icon_path}")
                break

        if selected_icon:
            # 创建图标对象
            icon = QIcon(str(selected_icon))

            # 设置应用程序图标（全局）
            app.setWindowIcon(icon)

            # 设置应用程序图标（任务栏）
            if hasattr(app, 'setApplicationDisplayName'):
                app.setApplicationDisplayName("YOLO OpenCV检测器")

            # Windows特定的任务栏图标设置
            try:
                import ctypes
                from ctypes import wintypes

                # 获取应用程序句柄
                hwnd = None

                # 设置应用程序用户模型ID（Windows 7+）
                try:
                    ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID("YOLOOpenCVDetector.v2.0")
                    logger.info("✅ 设置应用程序用户模型ID成功")
                except Exception as e:
                    logger.warning(f"设置应用程序用户模型ID失败: {e}")

            except ImportError:
                logger.info("非Windows系统，跳过Windows特定图标设置")
            except Exception as e:
                logger.warning(f"Windows图标设置失败: {e}")

            logger.info(f"✅ 应用程序图标设置成功: {selected_icon.name}")
            return True
        else:
            logger.warning("⚠️ 未找到任何图标文件")
            return False

    except Exception as e:
        logger.error(f"❌ 设置应用程序图标失败: {e}")
        return False

def main():
    """主函数"""
    try:
        # 导入PyQt6
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import Qt
        from PyQt6.QtGui import QIcon
        
        # 导入项目模块
        from yolo_opencv_detector.utils.logger import Logger
        from yolo_opencv_detector.utils.config_manager import ConfigManager
        from yolo_opencv_detector.gui.main_window_v2 import MainWindowV2
        
        # 初始化日志
        logger = Logger()
        logger.info("重构版应用程序启动")
        
        # 创建应用程序
        app = QApplication(sys.argv)
        app.setApplicationName("YOLO OpenCV 界面检测工具 v2.0")
        app.setApplicationVersion("2.0.0")
        app.setOrganizationName("Cursor AI")
        
        # 设置应用程序属性（PyQt6中已默认启用高DPI）
        try:
            app.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps, True)
        except AttributeError:
            pass  # PyQt6中某些属性可能不存在
        
        # 设置应用程序图标
        setup_application_icon(app, project_root, logger)
        
        # 创建配置管理器
        config_manager = ConfigManager()
        
        # 创建主窗口
        main_window = MainWindowV2(config_manager)
        
        # 显示主窗口
        main_window.show()
        
        logger.info("重构版GUI应用程序启动成功")
        
        # 运行应用程序
        return app.exec()
        
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保已安装所有必需的依赖包:")
        print("pip install PyQt6 opencv-python numpy pillow psutil")
        return 1
        
    except Exception as e:
        print(f"应用程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
