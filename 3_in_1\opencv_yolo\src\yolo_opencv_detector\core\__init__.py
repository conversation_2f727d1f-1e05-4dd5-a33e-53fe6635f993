# -*- coding: utf-8 -*-
"""
核心模块包 - 重构版本
作者: Cursor AI
创建时间: 2025-07-05
编码标准: UTF-8无BOM
"""

# 导入重构版本的组件
from .yolo_detector_v2 import YOLODetectorV2
from .template_matcher_v2 import TemplateMatcherV2
from .screen_capture_v2 import ScreenCaptureServiceV2

# 保留兼容的旧版本组件（如果仍被使用）
from .fusion_engine import FusionEngine
from .coordinate_mapper import CoordinateMapper
from .script_generator import ScriptGenerator

__all__ = [
    # 重构版本组件
    "YOLODetectorV2",
    "TemplateMatcherV2",
    "ScreenCaptureServiceV2",

    # 兼容组件
    "FusionEngine",
    "CoordinateMapper",
    "ScriptGenerator",
]
