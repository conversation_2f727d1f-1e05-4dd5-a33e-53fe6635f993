# -*- coding: utf-8 -*-
"""
截图缓存机制
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

import time
import threading
import hashlib
from typing import Dict, Any, Optional, List, Tuple
import numpy as np
from collections import OrderedDict
import pickle
import gzip
from pathlib import Path

from ..utils.logger import Logger


class ScreenshotCacheEntry:
    """截图缓存条目"""
    
    def __init__(self, 
                 image: np.ndarray,
                 cache_key: str,
                 metadata: Optional[Dict[str, Any]] = None,
                 compress: bool = True):
        """
        初始化缓存条目
        
        Args:
            image: 图像数据
            cache_key: 缓存键
            metadata: 元数据
            compress: 是否压缩
        """
        self.cache_key = cache_key
        self.timestamp = time.time()
        self.access_count = 0
        self.last_access_time = self.timestamp
        self.metadata = metadata or {}
        self.compressed = compress
        
        # 存储图像数据
        if compress:
            self.image_data = self._compress_image(image)
            self.original_size = image.nbytes
        else:
            self.image_data = image.copy()
            self.original_size = image.nbytes
        
        # 计算数据大小
        self.data_size = len(self.image_data) if compress else self.original_size
    
    def _compress_image(self, image: np.ndarray) -> bytes:
        """压缩图像数据"""
        try:
            # 使用pickle序列化然后gzip压缩
            pickled_data = pickle.dumps(image)
            compressed_data = gzip.compress(pickled_data)
            return compressed_data
        except Exception:
            # 压缩失败，返回原始数据
            return image.tobytes()
    
    def _decompress_image(self, data: bytes) -> np.ndarray:
        """解压缩图像数据"""
        try:
            # 尝试gzip解压缩然后pickle反序列化
            decompressed_data = gzip.decompress(data)
            image = pickle.loads(decompressed_data)
            return image
        except Exception:
            # 解压缩失败，假设是原始字节数据
            # 这种情况下需要知道原始形状和数据类型
            raise ValueError("无法解压缩图像数据")
    
    def get_image(self) -> np.ndarray:
        """获取图像数据"""
        self.access_count += 1
        self.last_access_time = time.time()
        
        if self.compressed:
            return self._decompress_image(self.image_data)
        else:
            return self.image_data.copy()
    
    def get_info(self) -> Dict[str, Any]:
        """获取缓存条目信息"""
        return {
            "cache_key": self.cache_key,
            "timestamp": self.timestamp,
            "access_count": self.access_count,
            "last_access_time": self.last_access_time,
            "data_size": self.data_size,
            "original_size": self.original_size,
            "compressed": self.compressed,
            "compression_ratio": self.original_size / self.data_size if self.data_size > 0 else 1.0,
            "metadata": self.metadata
        }


class ScreenshotCache:
    """截图缓存管理器"""
    
    def __init__(self,
                 max_size: int = 100,
                 max_memory_mb: int = 500,
                 default_ttl: float = 300.0,
                 enable_compression: bool = True,
                 enable_persistence: bool = False,
                 cache_dir: Optional[Path] = None):
        """
        初始化截图缓存
        
        Args:
            max_size: 最大缓存条目数
            max_memory_mb: 最大内存使用量(MB)
            default_ttl: 默认生存时间(秒)
            enable_compression: 是否启用压缩
            enable_persistence: 是否启用持久化
            cache_dir: 缓存目录
        """
        self.logger = Logger().get_logger(__name__)
        
        # 配置参数
        self.max_size = max_size
        self.max_memory_bytes = max_memory_mb * 1024 * 1024
        self.default_ttl = default_ttl
        self.enable_compression = enable_compression
        self.enable_persistence = enable_persistence
        self.cache_dir = cache_dir
        
        # 缓存存储
        self.cache: OrderedDict[str, ScreenshotCacheEntry] = OrderedDict()
        self.cache_lock = threading.RLock()
        
        # 统计信息
        self.stats = {
            "hits": 0,
            "misses": 0,
            "evictions": 0,
            "total_size": 0,
            "memory_usage": 0
        }
        
        # 清理线程
        self.cleanup_thread = None
        self.cleanup_interval = 60.0  # 60秒清理间隔
        self.stop_cleanup = threading.Event()
        
        # 初始化
        if self.enable_persistence and self.cache_dir:
            self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        self._start_cleanup_thread()
        
        self.logger.info(f"截图缓存初始化完成 - 最大条目: {max_size}, 最大内存: {max_memory_mb}MB")
    
    def _generate_cache_key(self, 
                           x: int, y: int, width: int, height: int,
                           monitor_id: Optional[int] = None,
                           extra_data: Optional[str] = None) -> str:
        """生成缓存键"""
        key_data = f"{x}_{y}_{width}_{height}_{monitor_id}_{extra_data}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def put(self, 
            image: np.ndarray,
            x: int, y: int, width: int, height: int,
            monitor_id: Optional[int] = None,
            extra_data: Optional[str] = None,
            metadata: Optional[Dict[str, Any]] = None,
            ttl: Optional[float] = None) -> str:
        """
        添加截图到缓存
        
        Args:
            image: 图像数据
            x, y, width, height: 截图区域
            monitor_id: 显示器ID
            extra_data: 额外数据
            metadata: 元数据
            ttl: 生存时间
            
        Returns:
            str: 缓存键
        """
        try:
            cache_key = self._generate_cache_key(x, y, width, height, monitor_id, extra_data)
            
            with self.cache_lock:
                # 创建缓存条目
                entry = ScreenshotCacheEntry(
                    image=image,
                    cache_key=cache_key,
                    metadata=metadata,
                    compress=self.enable_compression
                )
                
                # 添加TTL信息
                if ttl is None:
                    ttl = self.default_ttl
                entry.metadata["ttl"] = ttl
                entry.metadata["expires_at"] = time.time() + ttl
                
                # 添加到缓存
                self.cache[cache_key] = entry
                self.cache.move_to_end(cache_key)  # 移到最后（最新）
                
                # 更新统计
                self.stats["total_size"] += 1
                self.stats["memory_usage"] += entry.data_size
                
                # 检查是否需要清理
                self._cleanup_if_needed()
                
                self.logger.debug(f"截图已缓存: {cache_key}")
                return cache_key
                
        except Exception as e:
            self.logger.error(f"添加缓存失败: {e}")
            return ""
    
    def get(self, 
            x: int, y: int, width: int, height: int,
            monitor_id: Optional[int] = None,
            extra_data: Optional[str] = None) -> Optional[np.ndarray]:
        """
        从缓存获取截图
        
        Args:
            x, y, width, height: 截图区域
            monitor_id: 显示器ID
            extra_data: 额外数据
            
        Returns:
            Optional[np.ndarray]: 缓存的图像，不存在或过期返回None
        """
        try:
            cache_key = self._generate_cache_key(x, y, width, height, monitor_id, extra_data)
            
            with self.cache_lock:
                entry = self.cache.get(cache_key)
                
                if entry is None:
                    self.stats["misses"] += 1
                    return None
                
                # 检查是否过期
                current_time = time.time()
                expires_at = entry.metadata.get("expires_at", current_time + 1)
                
                if current_time > expires_at:
                    # 过期，删除条目
                    self._remove_entry(cache_key)
                    self.stats["misses"] += 1
                    return None
                
                # 命中，移到最后
                self.cache.move_to_end(cache_key)
                self.stats["hits"] += 1
                
                return entry.get_image()
                
        except Exception as e:
            self.logger.error(f"获取缓存失败: {e}")
            self.stats["misses"] += 1
            return None
    
    def get_by_key(self, cache_key: str) -> Optional[np.ndarray]:
        """
        根据缓存键获取截图
        
        Args:
            cache_key: 缓存键
            
        Returns:
            Optional[np.ndarray]: 缓存的图像
        """
        try:
            with self.cache_lock:
                entry = self.cache.get(cache_key)
                
                if entry is None:
                    self.stats["misses"] += 1
                    return None
                
                # 检查是否过期
                current_time = time.time()
                expires_at = entry.metadata.get("expires_at", current_time + 1)
                
                if current_time > expires_at:
                    self._remove_entry(cache_key)
                    self.stats["misses"] += 1
                    return None
                
                self.cache.move_to_end(cache_key)
                self.stats["hits"] += 1
                
                return entry.get_image()
                
        except Exception as e:
            self.logger.error(f"根据键获取缓存失败: {e}")
            self.stats["misses"] += 1
            return None
    
    def remove(self, cache_key: str) -> bool:
        """
        删除缓存条目
        
        Args:
            cache_key: 缓存键
            
        Returns:
            bool: 删除是否成功
        """
        try:
            with self.cache_lock:
                return self._remove_entry(cache_key)
        except Exception as e:
            self.logger.error(f"删除缓存失败: {e}")
            return False
    
    def _remove_entry(self, cache_key: str) -> bool:
        """内部删除条目方法"""
        entry = self.cache.pop(cache_key, None)
        if entry:
            self.stats["total_size"] -= 1
            self.stats["memory_usage"] -= entry.data_size
            return True
        return False
    
    def clear(self) -> None:
        """清空所有缓存"""
        with self.cache_lock:
            self.cache.clear()
            self.stats["total_size"] = 0
            self.stats["memory_usage"] = 0
        
        self.logger.info("缓存已清空")
    
    def _cleanup_if_needed(self) -> None:
        """根据需要清理缓存"""
        # 检查大小限制
        while len(self.cache) > self.max_size:
            # 删除最旧的条目
            oldest_key = next(iter(self.cache))
            self._remove_entry(oldest_key)
            self.stats["evictions"] += 1
        
        # 检查内存限制
        while self.stats["memory_usage"] > self.max_memory_bytes and self.cache:
            oldest_key = next(iter(self.cache))
            self._remove_entry(oldest_key)
            self.stats["evictions"] += 1
    
    def _cleanup_expired(self) -> None:
        """清理过期条目"""
        current_time = time.time()
        expired_keys = []
        
        with self.cache_lock:
            for key, entry in self.cache.items():
                expires_at = entry.metadata.get("expires_at", current_time + 1)
                if current_time > expires_at:
                    expired_keys.append(key)
            
            for key in expired_keys:
                self._remove_entry(key)
        
        if expired_keys:
            self.logger.debug(f"清理了 {len(expired_keys)} 个过期缓存条目")
    
    def _start_cleanup_thread(self) -> None:
        """启动清理线程"""
        def cleanup_worker():
            while not self.stop_cleanup.wait(self.cleanup_interval):
                try:
                    self._cleanup_expired()
                except Exception as e:
                    self.logger.error(f"缓存清理失败: {e}")
        
        self.cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        self.cleanup_thread.start()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self.cache_lock:
            hit_rate = (self.stats["hits"] / (self.stats["hits"] + self.stats["misses"]) 
                       if (self.stats["hits"] + self.stats["misses"]) > 0 else 0.0)
            
            return {
                "total_entries": len(self.cache),
                "max_size": self.max_size,
                "memory_usage_mb": self.stats["memory_usage"] / (1024 * 1024),
                "max_memory_mb": self.max_memory_bytes / (1024 * 1024),
                "hits": self.stats["hits"],
                "misses": self.stats["misses"],
                "hit_rate": hit_rate,
                "evictions": self.stats["evictions"],
                "compression_enabled": self.enable_compression,
                "persistence_enabled": self.enable_persistence
            }
    
    def get_cache_info(self) -> List[Dict[str, Any]]:
        """获取所有缓存条目信息"""
        with self.cache_lock:
            return [entry.get_info() for entry in self.cache.values()]
    
    def resize(self, new_max_size: int, new_max_memory_mb: int) -> None:
        """
        调整缓存大小
        
        Args:
            new_max_size: 新的最大条目数
            new_max_memory_mb: 新的最大内存使用量(MB)
        """
        with self.cache_lock:
            self.max_size = new_max_size
            self.max_memory_bytes = new_max_memory_mb * 1024 * 1024
            self._cleanup_if_needed()
        
        self.logger.info(f"缓存大小已调整: 最大条目 {new_max_size}, 最大内存 {new_max_memory_mb}MB")
    
    def __del__(self):
        """析构函数"""
        if hasattr(self, 'stop_cleanup'):
            self.stop_cleanup.set()
        if hasattr(self, 'cleanup_thread') and self.cleanup_thread:
            self.cleanup_thread.join(timeout=1.0)
