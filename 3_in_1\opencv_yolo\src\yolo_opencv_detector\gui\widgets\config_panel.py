# -*- coding: utf-8 -*-
"""
配置面板组件
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

from typing import Dict, Any
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, QTabWidget,
    QLabel, QLineEdit, QSpinBox, QDoubleSpinBox, QCheckBox,
    QComboBox, QSlider, QPushButton, QFileDialog, QMessageBox
)
from PyQt6.QtCore import Qt, pyqtSignal

from ...utils.logger import Logger
from ...utils.config_manager import ConfigManager


class ConfigPanel(QWidget):
    """配置面板类"""
    
    # 信号定义
    config_changed = pyqtSignal(dict)  # 配置变更
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化配置面板
        
        Args:
            config_manager: 配置管理器
        """
        super().__init__()
        
        self.logger = Logger().get_logger(__name__)
        self.config_manager = config_manager
        
        # 组件初始化
        self.tab_widget = None
        
        # 检测配置组件
        self.yolo_model_edit = None
        self.confidence_threshold_slider = None
        self.nms_threshold_slider = None
        self.max_detections_spinbox = None
        
        # 模板配置组件
        self.template_threshold_slider = None
        self.scale_range_min_spinbox = None
        self.scale_range_max_spinbox = None
        self.angle_range_spinbox = None
        
        # 融合配置组件
        self.enable_fusion_checkbox = None
        self.fusion_iou_threshold_slider = None
        self.confidence_weight_slider = None
        self.template_weight_slider = None
        
        # 界面配置组件
        self.theme_combo = None
        self.auto_save_checkbox = None
        self.log_level_combo = None
        
        # 初始化界面
        self._init_ui()
        self._init_connections()
        self._load_config()
        
        self.logger.info("配置面板初始化完成")
    
    def _init_ui(self) -> None:
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)  # 设置组件间距
        layout.setContentsMargins(8, 10, 8, 8)  # 设置边距

        # 添加标题和操作指导
        header_widget = QWidget()
        header_layout = QVBoxLayout(header_widget)
        header_layout.setContentsMargins(0, 0, 0, 0)

        title_label = QLabel("⚙️ 参数配置")
        title_label.setStyleSheet(
            "QLabel { "
            "font-size: 14px; "
            "font-weight: bold; "
            "color: #2c3e50; "
            "padding: 5px 0px; "
            "}"
        )
        header_layout.addWidget(title_label)

        help_label = QLabel("💡 步骤3: 调整检测参数以获得最佳效果")
        help_label.setStyleSheet(
            "QLabel { "
            "font-size: 11px; "
            "color: #7f8c8d; "
            "font-style: italic; "
            "margin-bottom: 5px; "
            "}"
        )
        header_layout.addWidget(help_label)
        layout.addWidget(header_widget)

        # 创建标签页
        self.tab_widget = QTabWidget()
        self.tab_widget.setMinimumHeight(200)  # 大幅增加高度
        self.tab_widget.setMaximumHeight(250)  # 限制最大高度
        self.tab_widget.setStyleSheet(
            "QTabWidget::pane { "
            "border: 1px solid #bdc3c7; "
            "border-radius: 5px; "
            "} "
            "QTabBar::tab { "
            "background-color: #ecf0f1; "
            "padding: 8px 16px; "
            "margin-right: 2px; "
            "border-top-left-radius: 5px; "
            "border-top-right-radius: 5px; "
            "} "
            "QTabBar::tab:selected { "
            "background-color: #3498db; "
            "color: white; "
            "}"
        )
        layout.addWidget(self.tab_widget)
        
        # 检测配置标签页
        self._create_detection_tab()
        
        # 模板配置标签页
        self._create_template_tab()
        
        # 融合配置标签页
        self._create_fusion_tab()
        
        # 界面配置标签页
        self._create_ui_tab()
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        reset_button = QPushButton("重置")
        reset_button.setToolTip("重置为默认配置")
        reset_button.clicked.connect(self._reset_config)
        button_layout.addWidget(reset_button)
        
        apply_button = QPushButton("应用")
        apply_button.setToolTip("应用当前配置")
        apply_button.clicked.connect(self._apply_config)
        button_layout.addWidget(apply_button)
        
        button_layout.addStretch()
        
        layout.addLayout(button_layout)
    
    def _create_detection_tab(self) -> None:
        """创建检测配置标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # YOLO配置组
        yolo_group = QGroupBox("YOLO配置")
        yolo_group.setMinimumHeight(150)  # 进一步减少高度
        yolo_group.setMaximumHeight(180)  # 严格限制最大高度
        yolo_layout = QVBoxLayout(yolo_group)
        yolo_layout.setSpacing(20)  # 增加组件间距
        yolo_layout.setContentsMargins(15, 25, 15, 20)  # 增加边距
        
        # 模型路径
        model_layout = QHBoxLayout()
        model_layout.setSpacing(10)  # 设置水平间距
        model_label = QLabel("模型路径:")
        model_label.setMinimumWidth(80)  # 设置标签最小宽度
        model_layout.addWidget(model_label)

        self.yolo_model_edit = QLineEdit()
        self.yolo_model_edit.setMinimumHeight(35)  # 设置输入框高度
        model_layout.addWidget(self.yolo_model_edit)

        browse_button = QPushButton("浏览")
        browse_button.setMinimumHeight(35)  # 设置按钮高度
        browse_button.setMinimumWidth(60)   # 设置按钮宽度
        browse_button.clicked.connect(self._browse_yolo_model)
        model_layout.addWidget(browse_button)

        yolo_layout.addLayout(model_layout)
        
        # 置信度阈值
        conf_layout = QHBoxLayout()
        conf_layout.setSpacing(10)  # 设置水平间距
        conf_label = QLabel("置信度阈值:")
        conf_label.setMinimumWidth(80)  # 设置标签最小宽度
        conf_layout.addWidget(conf_label)

        self.confidence_threshold_slider = QSlider(Qt.Orientation.Horizontal)
        self.confidence_threshold_slider.setRange(1, 100)
        self.confidence_threshold_slider.setValue(50)
        self.confidence_threshold_slider.setMinimumHeight(45)  # 进一步增加滑块高度
        self.confidence_threshold_slider.setStyleSheet(
            "QSlider::groove:horizontal { "
            "border: 1px solid #bbb; "
            "background: white; "
            "height: 10px; "
            "border-radius: 4px; "
            "} "
            "QSlider::handle:horizontal { "
            "background: #3498db; "
            "border: 1px solid #2980b9; "
            "width: 18px; "
            "margin: -2px 0; "
            "border-radius: 3px; "
            "}"
        )
        conf_layout.addWidget(self.confidence_threshold_slider)

        self.conf_label = QLabel("0.50")
        self.conf_label.setMinimumWidth(70)    # 设置标签宽度
        self.conf_label.setMaximumHeight(35)   # 限制最大高度
        self.conf_label.setMinimumHeight(35)   # 设置最小高度
        self.conf_label.setStyleSheet(
            "QLabel { "
            "border: 1px solid #bdc3c7; "
            "padding: 6px 8px; "
            "background-color: #ecf0f1; "
            "border-radius: 4px; "
            "font-weight: bold; "
            "text-align: center; "
            "font-size: 12px; "
            "}"
        )
        conf_layout.addWidget(self.conf_label)
        yolo_layout.addLayout(conf_layout)
        
        # NMS阈值
        nms_layout = QHBoxLayout()
        nms_layout.setSpacing(10)  # 设置水平间距
        nms_label = QLabel("NMS阈值:")
        nms_label.setMinimumWidth(80)  # 设置标签最小宽度
        nms_layout.addWidget(nms_label)

        self.nms_threshold_slider = QSlider(Qt.Orientation.Horizontal)
        self.nms_threshold_slider.setRange(1, 100)
        self.nms_threshold_slider.setValue(50)
        self.nms_threshold_slider.setMinimumHeight(45)  # 进一步增加滑块高度
        self.nms_threshold_slider.setStyleSheet(
            "QSlider::groove:horizontal { "
            "border: 1px solid #bbb; "
            "background: white; "
            "height: 10px; "
            "border-radius: 4px; "
            "} "
            "QSlider::handle:horizontal { "
            "background: #e74c3c; "
            "border: 1px solid #c0392b; "
            "width: 18px; "
            "margin: -2px 0; "
            "border-radius: 3px; "
            "}"
        )
        nms_layout.addWidget(self.nms_threshold_slider)

        self.nms_label = QLabel("0.50")
        self.nms_label.setMinimumWidth(70)    # 设置标签宽度
        self.nms_label.setMaximumHeight(35)   # 限制最大高度
        self.nms_label.setMinimumHeight(35)   # 设置最小高度
        self.nms_label.setStyleSheet(
            "QLabel { "
            "border: 1px solid #bdc3c7; "
            "padding: 6px 8px; "
            "background-color: #ecf0f1; "
            "border-radius: 4px; "
            "font-weight: bold; "
            "text-align: center; "
            "font-size: 12px; "
            "}"
        )
        nms_layout.addWidget(self.nms_label)
        yolo_layout.addLayout(nms_layout)
        
        # 最大检测数
        max_det_layout = QHBoxLayout()
        max_det_layout.setSpacing(10)  # 设置水平间距
        max_det_label = QLabel("最大检测数:")
        max_det_label.setMinimumWidth(80)  # 设置标签最小宽度
        max_det_layout.addWidget(max_det_label)

        self.max_detections_spinbox = QSpinBox()
        self.max_detections_spinbox.setRange(1, 1000)
        self.max_detections_spinbox.setValue(100)
        self.max_detections_spinbox.setMinimumHeight(45)  # 进一步增加输入框高度
        self.max_detections_spinbox.setMinimumWidth(120)  # 增加输入框宽度
        self.max_detections_spinbox.setStyleSheet(
            "QSpinBox { "
            "border: 2px solid #bdc3c7; "
            "border-radius: 5px; "
            "padding: 8px; "
            "background-color: white; "
            "font-size: 12px; "
            "font-weight: bold; "
            "} "
            "QSpinBox:focus { "
            "border-color: #3498db; "
            "}"
        )
        max_det_layout.addWidget(self.max_detections_spinbox)
        max_det_layout.addStretch()  # 添加弹性空间
        yolo_layout.addLayout(max_det_layout)
        
        layout.addWidget(yolo_group)
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "检测配置")
    
    def _create_template_tab(self) -> None:
        """创建模板配置标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 模板匹配配置组
        template_group = QGroupBox("模板匹配配置")
        template_group.setMinimumHeight(400)  # 基于实际测量增加高度
        template_layout = QVBoxLayout(template_group)
        template_layout.setSpacing(20)  # 增加组件间距
        template_layout.setContentsMargins(15, 25, 15, 20)  # 增加边距
        
        # 匹配阈值
        threshold_layout = QHBoxLayout()
        threshold_layout.addWidget(QLabel("匹配阈值:"))
        self.template_threshold_slider = QSlider(Qt.Orientation.Horizontal)
        self.template_threshold_slider.setRange(1, 100)
        self.template_threshold_slider.setValue(80)
        threshold_layout.addWidget(self.template_threshold_slider)
        
        self.template_threshold_label = QLabel("0.80")
        threshold_layout.addWidget(self.template_threshold_label)
        template_layout.addLayout(threshold_layout)
        
        # 缩放范围
        scale_layout = QHBoxLayout()
        scale_layout.addWidget(QLabel("缩放范围:"))
        
        self.scale_range_min_spinbox = QDoubleSpinBox()
        self.scale_range_min_spinbox.setRange(0.1, 2.0)
        self.scale_range_min_spinbox.setValue(0.8)
        self.scale_range_min_spinbox.setSingleStep(0.1)
        scale_layout.addWidget(self.scale_range_min_spinbox)
        
        scale_layout.addWidget(QLabel("到"))
        
        self.scale_range_max_spinbox = QDoubleSpinBox()
        self.scale_range_max_spinbox.setRange(0.1, 2.0)
        self.scale_range_max_spinbox.setValue(1.2)
        self.scale_range_max_spinbox.setSingleStep(0.1)
        scale_layout.addWidget(self.scale_range_max_spinbox)
        
        template_layout.addLayout(scale_layout)
        
        # 角度范围
        angle_layout = QHBoxLayout()
        angle_layout.addWidget(QLabel("角度范围(±):"))
        self.angle_range_spinbox = QSpinBox()
        self.angle_range_spinbox.setRange(0, 180)
        self.angle_range_spinbox.setValue(15)
        self.angle_range_spinbox.setSuffix("°")
        angle_layout.addWidget(self.angle_range_spinbox)
        template_layout.addLayout(angle_layout)
        
        layout.addWidget(template_group)
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "模板配置")
    
    def _create_fusion_tab(self) -> None:
        """创建融合配置标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 融合配置组
        fusion_group = QGroupBox("结果融合配置")
        fusion_group.setMinimumHeight(380)  # 基于实际测量增加高度
        fusion_layout = QVBoxLayout(fusion_group)
        fusion_layout.setSpacing(20)  # 增加组件间距
        fusion_layout.setContentsMargins(15, 25, 15, 20)  # 增加边距
        
        # 启用融合
        self.enable_fusion_checkbox = QCheckBox("启用结果融合")
        self.enable_fusion_checkbox.setChecked(True)
        fusion_layout.addWidget(self.enable_fusion_checkbox)
        
        # IoU阈值
        iou_layout = QHBoxLayout()
        iou_layout.addWidget(QLabel("IoU阈值:"))
        self.fusion_iou_threshold_slider = QSlider(Qt.Orientation.Horizontal)
        self.fusion_iou_threshold_slider.setRange(1, 100)
        self.fusion_iou_threshold_slider.setValue(50)
        iou_layout.addWidget(self.fusion_iou_threshold_slider)
        
        self.fusion_iou_label = QLabel("0.50")
        iou_layout.addWidget(self.fusion_iou_label)
        fusion_layout.addLayout(iou_layout)
        
        # 置信度权重
        conf_weight_layout = QHBoxLayout()
        conf_weight_layout.addWidget(QLabel("YOLO权重:"))
        self.confidence_weight_slider = QSlider(Qt.Orientation.Horizontal)
        self.confidence_weight_slider.setRange(0, 100)
        self.confidence_weight_slider.setValue(60)
        conf_weight_layout.addWidget(self.confidence_weight_slider)
        
        self.conf_weight_label = QLabel("0.60")
        conf_weight_layout.addWidget(self.conf_weight_label)
        fusion_layout.addLayout(conf_weight_layout)
        
        # 模板权重
        template_weight_layout = QHBoxLayout()
        template_weight_layout.addWidget(QLabel("模板权重:"))
        self.template_weight_slider = QSlider(Qt.Orientation.Horizontal)
        self.template_weight_slider.setRange(0, 100)
        self.template_weight_slider.setValue(40)
        template_weight_layout.addWidget(self.template_weight_slider)
        
        self.template_weight_label = QLabel("0.40")
        template_weight_layout.addWidget(self.template_weight_label)
        fusion_layout.addLayout(template_weight_layout)
        
        layout.addWidget(fusion_group)
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "融合配置")
    
    def _create_ui_tab(self) -> None:
        """创建界面配置标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 界面配置组
        ui_group = QGroupBox("界面配置")
        ui_group.setMinimumHeight(150)  # 设置最小高度
        ui_layout = QVBoxLayout(ui_group)
        
        # 主题选择
        theme_layout = QHBoxLayout()
        theme_layout.addWidget(QLabel("主题:"))
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["默认", "深色", "浅色"])
        self.theme_combo.setMinimumHeight(30)  # 设置下拉框高度
        theme_layout.addWidget(self.theme_combo)
        ui_layout.addLayout(theme_layout)
        
        # 自动保存
        self.auto_save_checkbox = QCheckBox("自动保存配置")
        self.auto_save_checkbox.setChecked(True)
        ui_layout.addWidget(self.auto_save_checkbox)
        
        # 日志级别
        log_layout = QHBoxLayout()
        log_layout.addWidget(QLabel("日志级别:"))
        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(["DEBUG", "INFO", "WARNING", "ERROR"])
        self.log_level_combo.setCurrentText("INFO")
        self.log_level_combo.setMinimumHeight(30)  # 设置下拉框高度
        log_layout.addWidget(self.log_level_combo)
        ui_layout.addLayout(log_layout)
        
        layout.addWidget(ui_group)
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "界面配置")
    
    def _init_connections(self) -> None:
        """初始化信号连接"""
        # 滑块值变更连接
        self.confidence_threshold_slider.valueChanged.connect(
            lambda v: self.conf_label.setText(f"{v/100:.2f}")
        )
        self.nms_threshold_slider.valueChanged.connect(
            lambda v: self.nms_label.setText(f"{v/100:.2f}")
        )
        self.template_threshold_slider.valueChanged.connect(
            lambda v: self.template_threshold_label.setText(f"{v/100:.2f}")
        )
        self.fusion_iou_threshold_slider.valueChanged.connect(
            lambda v: self.fusion_iou_label.setText(f"{v/100:.2f}")
        )
        self.confidence_weight_slider.valueChanged.connect(
            lambda v: self.conf_weight_label.setText(f"{v/100:.2f}")
        )
        self.template_weight_slider.valueChanged.connect(
            lambda v: self.template_weight_label.setText(f"{v/100:.2f}")
        )
        
        # 权重联动
        self.confidence_weight_slider.valueChanged.connect(self._update_template_weight)
        self.template_weight_slider.valueChanged.connect(self._update_confidence_weight)
    
    def _update_template_weight(self, conf_weight: int) -> None:
        """更新模板权重（保持总和为100）"""
        template_weight = 100 - conf_weight
        self.template_weight_slider.blockSignals(True)
        self.template_weight_slider.setValue(template_weight)
        self.template_weight_label.setText(f"{template_weight/100:.2f}")
        self.template_weight_slider.blockSignals(False)
    
    def _update_confidence_weight(self, template_weight: int) -> None:
        """更新置信度权重（保持总和为100）"""
        conf_weight = 100 - template_weight
        self.confidence_weight_slider.blockSignals(True)
        self.confidence_weight_slider.setValue(conf_weight)
        self.conf_weight_label.setText(f"{conf_weight/100:.2f}")
        self.confidence_weight_slider.blockSignals(False)
    
    def _load_config(self) -> None:
        """加载配置"""
        try:
            # 加载检测配置
            detection_config = self.config_manager.detection
            if hasattr(detection_config, 'yolo_model_path'):
                self.yolo_model_edit.setText(str(detection_config.yolo_model_path))
            if hasattr(detection_config, 'confidence_threshold'):
                value = int(detection_config.confidence_threshold * 100)
                self.confidence_threshold_slider.setValue(value)
                self.conf_label.setText(f"{value/100:.2f}")
            
            # 加载其他配置...
            
        except Exception as e:
            self.logger.error(f"加载配置失败: {e}")
    
    def _browse_yolo_model(self) -> None:
        """浏览YOLO模型文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择YOLO模型文件", "",
            "模型文件 (*.pt *.onnx);;所有文件 (*)"
        )
        if file_path:
            self.yolo_model_edit.setText(file_path)
    
    def _reset_config(self) -> None:
        """重置配置"""
        try:
            reply = QMessageBox.question(
                self, "确认重置",
                "确定要重置为默认配置吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                # 重置为默认值
                self.confidence_threshold_slider.setValue(50)
                self.nms_threshold_slider.setValue(50)
                self.max_detections_spinbox.setValue(100)
                self.template_threshold_slider.setValue(80)
                # ... 重置其他控件
                
                self.logger.info("配置已重置为默认值")
                
        except Exception as e:
            self.logger.error(f"重置配置失败: {e}")
    
    def _apply_config(self) -> None:
        """应用配置"""
        try:
            # 收集配置
            config = self._collect_config()
            
            # 发送配置变更信号
            self.config_changed.emit(config)
            
            self.logger.info("配置已应用")
            
        except Exception as e:
            self.logger.error(f"应用配置失败: {e}")
            QMessageBox.warning(self, "错误", f"应用配置失败: {e}")
    
    def _collect_config(self) -> Dict[str, Any]:
        """收集当前配置"""
        return {
            "detection": {
                "yolo_model_path": self.yolo_model_edit.text(),
                "confidence_threshold": self.confidence_threshold_slider.value() / 100.0,
                "nms_threshold": self.nms_threshold_slider.value() / 100.0,
                "max_detections": self.max_detections_spinbox.value()
            },
            "template": {
                "threshold": self.template_threshold_slider.value() / 100.0,
                "scale_range": (
                    self.scale_range_min_spinbox.value(),
                    self.scale_range_max_spinbox.value()
                ),
                "angle_range": self.angle_range_spinbox.value()
            },
            "fusion": {
                "enable": self.enable_fusion_checkbox.isChecked(),
                "iou_threshold": self.fusion_iou_threshold_slider.value() / 100.0,
                "confidence_weight": self.confidence_weight_slider.value() / 100.0,
                "template_weight": self.template_weight_slider.value() / 100.0
            },
            "ui": {
                "theme": self.theme_combo.currentText(),
                "auto_save": self.auto_save_checkbox.isChecked(),
                "log_level": self.log_level_combo.currentText()
            }
        }
    
    def get_current_config(self) -> Dict[str, Any]:
        """获取当前配置"""
        return self._collect_config()
