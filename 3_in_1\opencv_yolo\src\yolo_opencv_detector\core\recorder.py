# -*- coding: utf-8 -*-
"""
录制回放功能模块
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

import json
import time
import threading
from pathlib import Path
from typing import List, Dict, Any, Optional, Callable
from dataclasses import dataclass, asdict
import numpy as np
import cv2

from ..utils.logger import Logger
from ..utils.config_manager import ConfigManager
from ..utils.data_structures import DetectionResult


@dataclass
class RecordFrame:
    """录制帧数据类"""
    timestamp: float
    frame_id: int
    screenshot_path: str
    detection_results: List[Dict[str, Any]]
    metadata: Dict[str, Any]


@dataclass
class RecordSession:
    """录制会话数据类"""
    session_id: str
    start_time: float
    end_time: Optional[float]
    total_frames: int
    fps: float
    resolution: tuple
    config: Dict[str, Any]
    frames: List[RecordFrame]


class DetectionRecorder:
    """检测录制器类"""
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化检测录制器
        
        Args:
            config_manager: 配置管理器
        """
        self.logger = Logger().get_logger(__name__)
        self.config_manager = config_manager
        
        # 录制状态
        self.is_recording = False
        self.current_session: Optional[RecordSession] = None
        self.record_thread: Optional[threading.Thread] = None
        
        # 录制配置
        self.output_dir = Path("recordings")
        self.max_duration = 3600  # 最大录制时长（秒）
        self.max_frames = 10000   # 最大帧数
        self.save_screenshots = True
        self.compress_screenshots = True
        self.screenshot_quality = 85
        
        # 回调函数
        self.frame_callback: Optional[Callable] = None
        self.detection_callback: Optional[Callable] = None
        
        # 性能统计
        self.total_recorded_sessions = 0
        self.total_recorded_frames = 0
        
        # 确保输出目录存在
        self.output_dir.mkdir(exist_ok=True)
        
        self.logger.info("检测录制器初始化完成")
    
    def start_recording(self, 
                       session_id: Optional[str] = None,
                       config: Optional[Dict[str, Any]] = None) -> bool:
        """
        开始录制
        
        Args:
            session_id: 会话ID
            config: 录制配置
            
        Returns:
            bool: 是否成功开始录制
        """
        try:
            if self.is_recording:
                self.logger.warning("录制已在进行中")
                return False
            
            # 生成会话ID
            if not session_id:
                session_id = f"session_{int(time.time())}"
            
            # 创建录制会话
            self.current_session = RecordSession(
                session_id=session_id,
                start_time=time.time(),
                end_time=None,
                total_frames=0,
                fps=0.0,
                resolution=(0, 0),
                config=config or {},
                frames=[]
            )
            
            # 创建会话目录
            session_dir = self.output_dir / session_id
            session_dir.mkdir(exist_ok=True)
            
            # 开始录制
            self.is_recording = True
            self.record_thread = threading.Thread(
                target=self._recording_loop,
                daemon=True
            )
            self.record_thread.start()
            
            self.logger.info(f"开始录制会话: {session_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"开始录制失败: {e}")
            return False
    
    def stop_recording(self) -> Optional[str]:
        """
        停止录制
        
        Returns:
            Optional[str]: 录制文件路径
        """
        try:
            if not self.is_recording:
                self.logger.warning("当前没有进行录制")
                return None
            
            # 停止录制
            self.is_recording = False
            
            # 等待录制线程结束
            if self.record_thread:
                self.record_thread.join(timeout=5.0)
            
            # 完成会话信息
            if self.current_session:
                self.current_session.end_time = time.time()
                duration = self.current_session.end_time - self.current_session.start_time
                self.current_session.fps = self.current_session.total_frames / duration if duration > 0 else 0
                
                # 保存会话数据
                session_file = self._save_session()
                
                # 更新统计
                self.total_recorded_sessions += 1
                self.total_recorded_frames += self.current_session.total_frames
                
                self.logger.info(f"录制完成: {self.current_session.session_id}, "
                               f"帧数: {self.current_session.total_frames}, "
                               f"时长: {duration:.2f}秒")
                
                return session_file
            
            return None
            
        except Exception as e:
            self.logger.error(f"停止录制失败: {e}")
            return None
    
    def _recording_loop(self) -> None:
        """录制循环"""
        frame_id = 0
        
        while self.is_recording and self.current_session:
            try:
                # 检查录制限制
                if (frame_id >= self.max_frames or 
                    (time.time() - self.current_session.start_time) >= self.max_duration):
                    self.logger.info("达到录制限制，自动停止录制")
                    break
                
                # 获取当前帧数据
                frame_data = self._capture_frame(frame_id)
                if frame_data:
                    self.current_session.frames.append(frame_data)
                    self.current_session.total_frames += 1
                    
                    # 调用帧回调
                    if self.frame_callback:
                        self.frame_callback(frame_data)
                
                frame_id += 1
                
                # 控制录制频率
                time.sleep(0.1)  # 10 FPS
                
            except Exception as e:
                self.logger.error(f"录制循环出错: {e}")
                break
    
    def _capture_frame(self, frame_id: int) -> Optional[RecordFrame]:
        """
        捕获单帧数据
        
        Args:
            frame_id: 帧ID
            
        Returns:
            Optional[RecordFrame]: 帧数据
        """
        try:
            timestamp = time.time()
            
            # 这里应该调用实际的检测函数获取结果
            # 暂时使用空结果
            detection_results = []
            
            # 保存截图
            screenshot_path = ""
            if self.save_screenshots:
                screenshot_path = self._save_screenshot(frame_id)
            
            # 创建帧数据
            frame_data = RecordFrame(
                timestamp=timestamp,
                frame_id=frame_id,
                screenshot_path=screenshot_path,
                detection_results=[asdict(result) for result in detection_results],
                metadata={
                    "session_id": self.current_session.session_id,
                    "relative_time": timestamp - self.current_session.start_time
                }
            )
            
            return frame_data
            
        except Exception as e:
            self.logger.error(f"捕获帧数据失败: {e}")
            return None
    
    def _save_screenshot(self, frame_id: int) -> str:
        """
        保存截图
        
        Args:
            frame_id: 帧ID
            
        Returns:
            str: 截图文件路径
        """
        try:
            session_dir = self.output_dir / self.current_session.session_id
            screenshot_dir = session_dir / "screenshots"
            screenshot_dir.mkdir(exist_ok=True)
            
            # 这里应该调用实际的截图函数
            # 暂时创建一个空图像
            screenshot = np.zeros((600, 800, 3), dtype=np.uint8)
            
            # 保存截图
            if self.compress_screenshots:
                filename = f"frame_{frame_id:06d}.jpg"
                filepath = screenshot_dir / filename
                cv2.imwrite(str(filepath), screenshot, 
                           [cv2.IMWRITE_JPEG_QUALITY, self.screenshot_quality])
            else:
                filename = f"frame_{frame_id:06d}.png"
                filepath = screenshot_dir / filename
                cv2.imwrite(str(filepath), screenshot)
            
            return str(filepath.relative_to(self.output_dir))
            
        except Exception as e:
            self.logger.error(f"保存截图失败: {e}")
            return ""
    
    def _save_session(self) -> str:
        """
        保存会话数据
        
        Returns:
            str: 会话文件路径
        """
        try:
            session_dir = self.output_dir / self.current_session.session_id
            session_file = session_dir / "session.json"
            
            # 转换为字典
            session_data = asdict(self.current_session)
            
            # 保存到文件
            with open(session_file, 'w', encoding='utf-8') as f:
                json.dump(session_data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"会话数据已保存: {session_file}")
            return str(session_file)
            
        except Exception as e:
            self.logger.error(f"保存会话数据失败: {e}")
            return ""
    
    def load_session(self, session_file: str) -> Optional[RecordSession]:
        """
        加载会话数据
        
        Args:
            session_file: 会话文件路径
            
        Returns:
            Optional[RecordSession]: 会话数据
        """
        try:
            with open(session_file, 'r', encoding='utf-8') as f:
                session_data = json.load(f)
            
            # 重建RecordFrame对象
            frames = []
            for frame_data in session_data.get('frames', []):
                frame = RecordFrame(**frame_data)
                frames.append(frame)
            
            # 重建RecordSession对象
            session_data['frames'] = frames
            session = RecordSession(**session_data)
            
            self.logger.info(f"会话数据已加载: {session.session_id}")
            return session
            
        except Exception as e:
            self.logger.error(f"加载会话数据失败: {e}")
            return None
    
    def replay_session(self, 
                      session: RecordSession,
                      speed_factor: float = 1.0,
                      frame_callback: Optional[Callable] = None) -> bool:
        """
        回放会话
        
        Args:
            session: 会话数据
            speed_factor: 播放速度倍数
            frame_callback: 帧回调函数
            
        Returns:
            bool: 是否成功回放
        """
        try:
            self.logger.info(f"开始回放会话: {session.session_id}")
            
            start_time = time.time()
            
            for i, frame in enumerate(session.frames):
                # 计算等待时间
                if i > 0:
                    time_diff = frame.timestamp - session.frames[i-1].timestamp
                    wait_time = time_diff / speed_factor
                    if wait_time > 0:
                        time.sleep(wait_time)
                
                # 加载截图
                screenshot = None
                if frame.screenshot_path:
                    screenshot_full_path = self.output_dir / frame.screenshot_path
                    if screenshot_full_path.exists():
                        screenshot = cv2.imread(str(screenshot_full_path))
                
                # 调用回调函数
                if frame_callback:
                    frame_callback(frame, screenshot)
                
                # 显示进度
                if i % 100 == 0:
                    progress = (i + 1) / len(session.frames) * 100
                    self.logger.info(f"回放进度: {progress:.1f}%")
            
            total_time = time.time() - start_time
            self.logger.info(f"回放完成，耗时: {total_time:.2f}秒")
            
            return True
            
        except Exception as e:
            self.logger.error(f"回放会话失败: {e}")
            return False
    
    def export_session(self, 
                      session: RecordSession,
                      export_format: str = "video",
                      output_path: Optional[str] = None) -> Optional[str]:
        """
        导出会话
        
        Args:
            session: 会话数据
            export_format: 导出格式 ("video", "gif", "images")
            output_path: 输出路径
            
        Returns:
            Optional[str]: 导出文件路径
        """
        try:
            if not output_path:
                output_path = str(self.output_dir / f"{session.session_id}_export")
            
            if export_format == "video":
                return self._export_to_video(session, output_path)
            elif export_format == "gif":
                return self._export_to_gif(session, output_path)
            elif export_format == "images":
                return self._export_to_images(session, output_path)
            else:
                self.logger.error(f"不支持的导出格式: {export_format}")
                return None
                
        except Exception as e:
            self.logger.error(f"导出会话失败: {e}")
            return None
    
    def _export_to_video(self, session: RecordSession, output_path: str) -> Optional[str]:
        """导出为视频"""
        try:
            video_path = f"{output_path}.mp4"
            
            # 获取第一帧确定视频参数
            if not session.frames:
                return None
            
            first_frame_path = self.output_dir / session.frames[0].screenshot_path
            if not first_frame_path.exists():
                return None
            
            first_image = cv2.imread(str(first_frame_path))
            height, width = first_image.shape[:2]
            
            # 创建视频写入器
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            fps = max(session.fps, 10)  # 最小10fps
            video_writer = cv2.VideoWriter(video_path, fourcc, fps, (width, height))
            
            # 写入帧
            for frame in session.frames:
                frame_path = self.output_dir / frame.screenshot_path
                if frame_path.exists():
                    image = cv2.imread(str(frame_path))
                    if image is not None:
                        video_writer.write(image)
            
            video_writer.release()
            
            self.logger.info(f"视频导出完成: {video_path}")
            return video_path
            
        except Exception as e:
            self.logger.error(f"导出视频失败: {e}")
            return None
    
    def _export_to_gif(self, session: RecordSession, output_path: str) -> Optional[str]:
        """导出为GIF"""
        try:
            from PIL import Image
            
            gif_path = f"{output_path}.gif"
            images = []
            
            # 加载所有图像
            for frame in session.frames[::5]:  # 每5帧取一帧以减小文件大小
                frame_path = self.output_dir / frame.screenshot_path
                if frame_path.exists():
                    image = Image.open(frame_path)
                    # 调整大小以减小文件大小
                    image = image.resize((image.width // 2, image.height // 2))
                    images.append(image)
            
            if images:
                # 保存为GIF
                images[0].save(
                    gif_path,
                    save_all=True,
                    append_images=images[1:],
                    duration=200,  # 每帧200ms
                    loop=0
                )
                
                self.logger.info(f"GIF导出完成: {gif_path}")
                return gif_path
            
            return None
            
        except ImportError:
            self.logger.error("PIL未安装，无法导出GIF")
            return None
        except Exception as e:
            self.logger.error(f"导出GIF失败: {e}")
            return None
    
    def _export_to_images(self, session: RecordSession, output_path: str) -> Optional[str]:
        """导出为图像序列"""
        try:
            images_dir = Path(f"{output_path}_images")
            images_dir.mkdir(exist_ok=True)
            
            # 复制所有截图
            for i, frame in enumerate(session.frames):
                frame_path = self.output_dir / frame.screenshot_path
                if frame_path.exists():
                    target_path = images_dir / f"frame_{i:06d}.jpg"
                    import shutil
                    shutil.copy2(frame_path, target_path)
            
            self.logger.info(f"图像序列导出完成: {images_dir}")
            return str(images_dir)
            
        except Exception as e:
            self.logger.error(f"导出图像序列失败: {e}")
            return None
    
    def get_session_list(self) -> List[Dict[str, Any]]:
        """获取会话列表"""
        try:
            sessions = []
            
            for session_dir in self.output_dir.iterdir():
                if session_dir.is_dir():
                    session_file = session_dir / "session.json"
                    if session_file.exists():
                        try:
                            with open(session_file, 'r', encoding='utf-8') as f:
                                session_data = json.load(f)
                            
                            sessions.append({
                                "session_id": session_data.get("session_id"),
                                "start_time": session_data.get("start_time"),
                                "end_time": session_data.get("end_time"),
                                "total_frames": session_data.get("total_frames"),
                                "fps": session_data.get("fps"),
                                "file_path": str(session_file)
                            })
                            
                        except Exception as e:
                            self.logger.error(f"读取会话文件失败: {session_file}, {e}")
            
            # 按时间排序
            sessions.sort(key=lambda x: x.get("start_time", 0), reverse=True)
            
            return sessions
            
        except Exception as e:
            self.logger.error(f"获取会话列表失败: {e}")
            return []
    
    def delete_session(self, session_id: str) -> bool:
        """
        删除会话
        
        Args:
            session_id: 会话ID
            
        Returns:
            bool: 是否成功删除
        """
        try:
            session_dir = self.output_dir / session_id
            if session_dir.exists():
                import shutil
                shutil.rmtree(session_dir)
                self.logger.info(f"会话已删除: {session_id}")
                return True
            else:
                self.logger.warning(f"会话不存在: {session_id}")
                return False
                
        except Exception as e:
            self.logger.error(f"删除会话失败: {e}")
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            "total_recorded_sessions": self.total_recorded_sessions,
            "total_recorded_frames": self.total_recorded_frames,
            "is_recording": self.is_recording,
            "current_session_id": self.current_session.session_id if self.current_session else None,
            "output_dir": str(self.output_dir),
            "available_sessions": len(self.get_session_list())
        }
