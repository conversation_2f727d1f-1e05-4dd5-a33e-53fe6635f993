from generator_ui import CodeGeneratorDialog

def _create_context_menu(self):
    """创建右键菜单"""
    self.context_menu = tk.Menu(self.tree, tearoff=0)
    self.context_menu.add_command(label="生成定位代码", command=self._generate_locator_code)
    self.context_menu.add_command(label="复制文本", command=self._copy_text)
    self.context_menu.add_command(label="复制类名", command=self._copy_class_name)
    self.context_menu.add_command(label="复制句柄", command=self._copy_handle)
    self.context_menu.add_command(label="复制位置", command=self._copy_position)
    self.context_menu.add_command(label="复制AutomationId", command=self._copy_automation_id)
    
    # 绑定右键菜单
    self.tree.bind("<Button-3>", self._show_context_menu)

def _show_context_menu(self, event):
    """显示右键菜单"""
    # 选中点击的项
    item = self.tree.identify_row(event.y)
    if item:
        self.tree.selection_set(item)
        self.context_menu.post(event.x_root, event.y_root)

def _generate_locator_code(self):
    """生成定位代码"""
    # 获取选中的控件
    selected_item = self.tree.selection()[0]
    control = self.item_control_map.get(selected_item)
    
    if control:
        # 获取控件所属的窗口
        window = control.top_level_parent()
        
        # 创建并显示代码生成器对话框
        dialog = CodeGeneratorDialog(self.tree, control=control, window=window)
        dialog.show()

def _copy_text(self):
    """复制控件文本"""
    selected_item = self.tree.selection()[0]
    control = self.item_control_map.get(selected_item)
    if control:
        text = control.window_text()
        if text:
            self.tree.clipboard_clear()
            self.tree.clipboard_append(text)

def _copy_class_name(self):
    """复制控件类名"""
    selected_item = self.tree.selection()[0]
    control = self.item_control_map.get(selected_item)
    if control:
        class_name = control.class_name()
        if class_name:
            self.tree.clipboard_clear()
            self.tree.clipboard_append(class_name)

def _copy_handle(self):
    """复制控件句柄"""
    selected_item = self.tree.selection()[0]
    control = self.item_control_map.get(selected_item)
    if control:
        handle = control.handle
        if handle:
            self.tree.clipboard_clear()
            self.tree.clipboard_append(str(handle))

def _copy_position(self):
    """复制控件位置"""
    selected_item = self.tree.selection()[0]
    control = self.item_control_map.get(selected_item)
    if control:
        rect = control.rectangle()
        if rect:
            pos = f"({rect.left}, {rect.top}, {rect.right}, {rect.bottom})"
            self.tree.clipboard_clear()
            self.tree.clipboard_append(pos)

def _copy_automation_id(self):
    """复制控件AutomationId"""
    selected_item = self.tree.selection()[0]
    control = self.item_control_map.get(selected_item)
    if control:
        auto_id = control.automation_id()
        if auto_id:
            self.tree.clipboard_clear()
            self.tree.clipboard_append(auto_id) 