# -*- coding: utf-8 -*-
"""
配置管理器
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

import yaml
import json
from pathlib import Path
from typing import Dict, Any, Optional, Union, List
from dataclasses import dataclass, asdict
import logging

from .constants import (
    CONFIG_DIR, DEFAULT_CONFIG_FILE, USER_CONFIG_FILE,
    ErrorCodes
)


@dataclass
class AppConfig:
    """应用程序配置"""
    name: str = "YOLO OpenCV 屏幕识别工具"
    version: str = "1.0.0"
    debug: bool = False
    log_level: str = "INFO"


@dataclass
class YoloConfig:
    """YOLO检测器配置"""
    model_path: str = "models/yolov8n.pt"
    confidence_threshold: float = 0.5
    nms_threshold: float = 0.4
    input_size: tuple = (640, 640)
    device: str = "auto"
    max_detections: int = 100


@dataclass
class TemplateMatchingConfig:
    """模板匹配配置"""
    method: str = "cv2.TM_CCOEFF_NORMED"
    threshold: float = 0.8
    scale_range: tuple = (0.5, 2.0)
    scale_steps: int = 10
    angle_range: tuple = (-15, 15)
    angle_steps: int = 7
    enable_preprocessing: bool = True


@dataclass
class ScreenCaptureConfig:
    """屏幕截图配置"""
    format: str = "RGB"
    quality: int = 95
    cache_size: int = 10
    enable_multi_monitor: bool = True
    capture_cursor: bool = False


@dataclass
class FusionConfig:
    """结果融合配置"""
    iou_threshold: float = 0.5
    confidence_weight: float = 0.7
    template_weight: float = 0.3
    enable_nms: bool = True
    max_results: int = 50


@dataclass
class GuiConfig:
    """GUI配置"""
    window_size: tuple = (1200, 800)
    theme: str = "default"
    language: str = "zh_CN"
    auto_save_settings: bool = True
    show_confidence: bool = True
    show_class_names: bool = True


@dataclass
class ScriptGenerationConfig:
    """脚本生成配置"""
    default_language: str = "python"
    include_comments: bool = True
    add_error_handling: bool = True
    click_delay: float = 0.1
    drag_duration: float = 0.5


@dataclass
class PerformanceConfig:
    """性能监控配置"""
    enable_monitoring: bool = True
    log_interval: float = 5.0
    memory_threshold: int = 200
    cpu_threshold: int = 80
    gpu_threshold: int = 90


@dataclass
class DatabaseConfig:
    """数据库配置"""
    path: str = "data/templates.db"
    backup_interval: int = 3600
    max_backup_files: int = 5


@dataclass
class LoggingConfig:
    """日志配置"""
    level: str = "INFO"
    format: str = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
    rotation: str = "10 MB"
    retention: str = "7 days"
    file_path: str = "logs/app.log"


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_dir: Optional[Path] = None):
        """
        初始化配置管理器
        
        Args:
            config_dir: 配置文件目录，默认使用常量中定义的目录
        """
        self.config_dir = config_dir or CONFIG_DIR
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        self.default_config_path = self.config_dir / DEFAULT_CONFIG_FILE
        self.user_config_path = self.config_dir / USER_CONFIG_FILE
        
        self.logger = logging.getLogger(__name__)
        
        # 配置对象
        self.app = AppConfig()
        self.yolo = YoloConfig()
        self.template_matching = TemplateMatchingConfig()
        self.screen_capture = ScreenCaptureConfig()
        self.fusion = FusionConfig()
        self.gui = GuiConfig()
        self.script_generation = ScriptGenerationConfig()
        self.performance = PerformanceConfig()
        self.database = DatabaseConfig()
        self.logging = LoggingConfig()

        # 兼容性别名
        self.detection = self.yolo  # 为了兼容现有代码
        self.template = self.template_matching  # 为了兼容现有代码

        # 加载配置
        self.load_config()
    
    def load_config(self) -> bool:
        """
        加载配置文件
        
        Returns:
            bool: 加载是否成功
        """
        try:
            # 首先加载默认配置
            if self.default_config_path.exists():
                default_config = self._load_yaml_file(self.default_config_path)
                if default_config:
                    self._update_config_from_dict(default_config)
            
            # 然后加载用户配置（覆盖默认配置）
            if self.user_config_path.exists():
                user_config = self._load_yaml_file(self.user_config_path)
                if user_config:
                    self._update_config_from_dict(user_config)
            
            self.logger.info("配置文件加载成功")
            return True
            
        except Exception as e:
            self.logger.error(f"配置文件加载失败: {e}")
            return False
    
    def save_config(self, save_to_user: bool = True) -> bool:
        """
        保存配置文件
        
        Args:
            save_to_user: 是否保存到用户配置文件
            
        Returns:
            bool: 保存是否成功
        """
        try:
            config_dict = self._config_to_dict()
            
            if save_to_user:
                target_path = self.user_config_path
            else:
                target_path = self.default_config_path
            
            self._save_yaml_file(config_dict, target_path)
            self.logger.info(f"配置文件保存成功: {target_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"配置文件保存失败: {e}")
            return False
    
    def reset_to_default(self) -> bool:
        """
        重置为默认配置
        
        Returns:
            bool: 重置是否成功
        """
        try:
            # 删除用户配置文件
            if self.user_config_path.exists():
                self.user_config_path.unlink()
            
            # 重新创建默认配置对象
            self.app = AppConfig()
            self.yolo = YoloConfig()
            self.template_matching = TemplateMatchingConfig()
            self.screen_capture = ScreenCaptureConfig()
            self.fusion = FusionConfig()
            self.gui = GuiConfig()
            self.script_generation = ScriptGenerationConfig()
            self.performance = PerformanceConfig()
            self.database = DatabaseConfig()
            self.logging = LoggingConfig()
            
            self.logger.info("配置已重置为默认值")
            return True
            
        except Exception as e:
            self.logger.error(f"配置重置失败: {e}")
            return False
    
    def get_config_value(self, section: str, key: str) -> Any:
        """
        获取配置值
        
        Args:
            section: 配置节名
            key: 配置键名
            
        Returns:
            Any: 配置值
        """
        try:
            config_obj = getattr(self, section)
            return getattr(config_obj, key)
        except AttributeError:
            self.logger.warning(f"配置项不存在: {section}.{key}")
            return None
    
    def set_config_value(self, section: str, key: str, value: Any) -> bool:
        """
        设置配置值

        Args:
            section: 配置节名
            key: 配置键名
            value: 配置值

        Returns:
            bool: 设置是否成功
        """
        try:
            config_obj = getattr(self, section)
            setattr(config_obj, key, value)
            return True
        except AttributeError:
            self.logger.warning(f"配置项不存在: {section}.{key}")
            return False

    def get_detection_config(self) -> YoloConfig:
        """
        获取检测配置

        Returns:
            YoloConfig: YOLO检测器配置对象
        """
        return self.yolo

    def get_yolo_config(self) -> YoloConfig:
        """
        获取YOLO配置（兼容性方法）

        Returns:
            YoloConfig: YOLO检测器配置对象
        """
        return self.yolo

    def get_templates(self) -> Dict[str, Any]:
        """
        获取模板配置（兼容性方法）

        Returns:
            Dict[str, Any]: 模板配置字典
        """
        return {
            'threshold': self.template_matching.threshold,
            'method': self.template_matching.method,
            'scale_range': self.template_matching.scale_range,
            'scale_steps': self.template_matching.scale_steps,
            'angle_range': self.template_matching.angle_range,
            'angle_steps': self.template_matching.angle_steps,
            'enable_preprocessing': self.template_matching.enable_preprocessing
        }
    
    def _load_yaml_file(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """加载YAML文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            self.logger.error(f"YAML文件加载失败 {file_path}: {e}")
            return None
    
    def _save_yaml_file(self, data: Dict[str, Any], file_path: Path) -> None:
        """保存YAML文件"""
        with open(file_path, 'w', encoding='utf-8') as f:
            yaml.dump(data, f, default_flow_style=False, allow_unicode=True)
    
    def _update_config_from_dict(self, config_dict: Dict[str, Any]) -> None:
        """从字典更新配置"""
        for section_name, section_data in config_dict.items():
            if hasattr(self, section_name) and isinstance(section_data, dict):
                config_obj = getattr(self, section_name)
                for key, value in section_data.items():
                    if hasattr(config_obj, key):
                        setattr(config_obj, key, value)
    
    def _config_to_dict(self) -> Dict[str, Any]:
        """将配置转换为字典"""
        return {
            'app': asdict(self.app),
            'yolo': asdict(self.yolo),
            'template_matching': asdict(self.template_matching),
            'screen_capture': asdict(self.screen_capture),
            'fusion': asdict(self.fusion),
            'gui': asdict(self.gui),
            'script_generation': asdict(self.script_generation),
            'performance': asdict(self.performance),
            'database': asdict(self.database),
            'logging': asdict(self.logging),
        }

    def save_templates(self, templates: List[Dict[str, Any]]) -> bool:
        """保存模板列表到配置文件

        Args:
            templates: 模板列表

        Returns:
            是否保存成功
        """
        try:
            # 创建模板配置文件路径
            templates_config_path = self.config_dir / "templates.json"

            # 保存模板数据
            import json
            with open(templates_config_path, 'w', encoding='utf-8') as f:
                json.dump(templates, f, indent=2, ensure_ascii=False)

            print(f"模板配置已保存: {templates_config_path}")
            return True

        except Exception as e:
            print(f"保存模板配置失败: {e}")
            return False

    def load_templates(self) -> List[Dict[str, Any]]:
        """从配置文件加载模板列表

        Returns:
            模板列表
        """
        try:
            templates_config_path = self.config_dir / "templates.json"

            if templates_config_path.exists():
                import json
                with open(templates_config_path, 'r', encoding='utf-8') as f:
                    templates = json.load(f)
                print(f"模板配置已加载: {len(templates)} 个模板")
                return templates
            else:
                print("模板配置文件不存在，返回空列表")
                return []

        except Exception as e:
            print(f"加载模板配置失败: {e}")
            return []
