# -*- coding: utf-8 -*-
"""
检测结果视图
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
    QTableWidgetItem, QHeaderView, QLabel, QPushButton,
    QGroupBox, QTextEdit, QSplitter, QFrame
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QPixmap, QFont

from typing import List, Dict, Any
from ..utils.logger import Logger
from ..utils.data_structures import DetectionResult


class ResultView(QWidget):
    """检测结果视图"""
    
    # 信号定义
    result_selected = pyqtSignal(object)  # DetectionResult
    result_double_clicked = pyqtSignal(object)  # DetectionResult
    
    def __init__(self, parent=None):
        """
        初始化结果视图
        
        Args:
            parent: 父窗口
        """
        super().__init__(parent)
        self.logger = Logger().get_logger(__name__)
        
        # 数据
        self.detection_results: List[DetectionResult] = []
        self.current_result: DetectionResult = None
        
        self._setup_ui()
        self._connect_signals()
        
        self.logger.info("检测结果视图初始化完成")
    
    def _setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        layout.addWidget(splitter)
        
        # 左侧：结果列表
        left_widget = self._create_results_list()
        splitter.addWidget(left_widget)
        
        # 右侧：结果详情
        right_widget = self._create_result_details()
        splitter.addWidget(right_widget)
        
        # 设置分割比例
        splitter.setStretchFactor(0, 2)
        splitter.setStretchFactor(1, 1)
    
    def _create_results_list(self) -> QWidget:
        """创建结果列表"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 标题
        title_label = QLabel("检测结果")
        title_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        layout.addWidget(title_label)
        
        # 统计信息
        self.stats_label = QLabel("总计: 0 个结果")
        layout.addWidget(self.stats_label)
        
        # 结果表格
        self.results_table = QTableWidget()
        self.results_table.setColumnCount(6)
        self.results_table.setHorizontalHeaderLabels([
            "类型", "置信度", "X", "Y", "宽度", "高度"
        ])
        
        # 设置表格属性
        self.results_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.results_table.setAlternatingRowColors(True)
        self.results_table.setSortingEnabled(True)
        
        # 调整列宽
        header = self.results_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)
        
        layout.addWidget(self.results_table)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        self.clear_btn = QPushButton("清空结果")
        button_layout.addWidget(self.clear_btn)
        
        self.export_btn = QPushButton("导出结果")
        button_layout.addWidget(self.export_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        return widget
    
    def _create_result_details(self) -> QWidget:
        """创建结果详情"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 标题
        title_label = QLabel("详细信息")
        title_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        layout.addWidget(title_label)
        
        # 基本信息组
        basic_group = QGroupBox("基本信息")
        basic_layout = QVBoxLayout(basic_group)
        
        self.class_label = QLabel("类别: -")
        basic_layout.addWidget(self.class_label)
        
        self.confidence_label = QLabel("置信度: -")
        basic_layout.addWidget(self.confidence_label)
        
        self.source_label = QLabel("来源: -")
        basic_layout.addWidget(self.source_label)
        
        layout.addWidget(basic_group)
        
        # 位置信息组
        position_group = QGroupBox("位置信息")
        position_layout = QVBoxLayout(position_group)
        
        self.position_label = QLabel("位置: -")
        position_layout.addWidget(self.position_label)
        
        self.size_label = QLabel("尺寸: -")
        position_layout.addWidget(self.size_label)
        
        self.center_label = QLabel("中心点: -")
        position_layout.addWidget(self.center_label)
        
        layout.addWidget(position_group)
        
        # 元数据组
        metadata_group = QGroupBox("元数据")
        metadata_layout = QVBoxLayout(metadata_group)
        
        self.metadata_text = QTextEdit()
        self.metadata_text.setMaximumHeight(100)
        self.metadata_text.setReadOnly(True)
        metadata_layout.addWidget(self.metadata_text)
        
        layout.addWidget(metadata_group)
        
        # 操作按钮
        action_layout = QHBoxLayout()
        
        self.copy_btn = QPushButton("复制坐标")
        action_layout.addWidget(self.copy_btn)
        
        self.locate_btn = QPushButton("定位")
        action_layout.addWidget(self.locate_btn)
        
        layout.addLayout(action_layout)
        
        layout.addStretch()
        
        return widget
    
    def _connect_signals(self):
        """连接信号"""
        # 表格选择
        self.results_table.itemSelectionChanged.connect(self._on_selection_changed)
        self.results_table.itemDoubleClicked.connect(self._on_item_double_clicked)
        
        # 按钮
        self.clear_btn.clicked.connect(self.clear_results)
        self.export_btn.clicked.connect(self._on_export_clicked)
        self.copy_btn.clicked.connect(self._on_copy_coordinates)
        self.locate_btn.clicked.connect(self._on_locate_clicked)
    
    def update_results(self, results: List[DetectionResult]):
        """
        更新检测结果
        
        Args:
            results: 检测结果列表
        """
        self.detection_results = results
        self._populate_table()
        self._update_stats()
    
    def _populate_table(self):
        """填充表格数据"""
        self.results_table.setRowCount(len(self.detection_results))
        
        for row, result in enumerate(self.detection_results):
            # 类型
            class_name = result.class_name or result.template_id or "Unknown"
            self.results_table.setItem(row, 0, QTableWidgetItem(class_name))
            
            # 置信度
            confidence_item = QTableWidgetItem(f"{result.confidence:.3f}")
            confidence_item.setData(Qt.ItemDataRole.UserRole, result.confidence)
            self.results_table.setItem(row, 1, confidence_item)
            
            # 坐标和尺寸
            self.results_table.setItem(row, 2, QTableWidgetItem(str(result.bbox.x)))
            self.results_table.setItem(row, 3, QTableWidgetItem(str(result.bbox.y)))
            self.results_table.setItem(row, 4, QTableWidgetItem(str(result.bbox.width)))
            self.results_table.setItem(row, 5, QTableWidgetItem(str(result.bbox.height)))
            
            # 存储完整的结果对象
            self.results_table.item(row, 0).setData(Qt.ItemDataRole.UserRole, result)
    
    def _update_stats(self):
        """更新统计信息"""
        total_count = len(self.detection_results)
        
        # 按来源分类统计
        source_counts = {}
        for result in self.detection_results:
            source = result.source.value
            source_counts[source] = source_counts.get(source, 0) + 1
        
        # 构建统计文本
        stats_text = f"总计: {total_count} 个结果"
        if source_counts:
            source_details = ", ".join([f"{k}: {v}" for k, v in source_counts.items()])
            stats_text += f" ({source_details})"
        
        self.stats_label.setText(stats_text)
    
    def _on_selection_changed(self):
        """选择改变事件"""
        current_row = self.results_table.currentRow()
        if current_row >= 0 and current_row < len(self.detection_results):
            result = self.detection_results[current_row]
            self.current_result = result
            self._update_details(result)
            self.result_selected.emit(result)
    
    def _on_item_double_clicked(self, item):
        """项目双击事件"""
        if self.current_result:
            self.result_double_clicked.emit(self.current_result)
    
    def _update_details(self, result: DetectionResult):
        """更新详情显示"""
        # 基本信息
        class_name = result.class_name or result.template_id or "Unknown"
        self.class_label.setText(f"类别: {class_name}")
        self.confidence_label.setText(f"置信度: {result.confidence:.3f}")
        self.source_label.setText(f"来源: {result.source.value}")
        
        # 位置信息
        self.position_label.setText(f"位置: ({result.bbox.x}, {result.bbox.y})")
        self.size_label.setText(f"尺寸: {result.bbox.width} × {result.bbox.height}")
        self.center_label.setText(f"中心点: ({result.bbox.center_x:.1f}, {result.bbox.center_y:.1f})")
        
        # 元数据
        if result.metadata:
            metadata_text = ""
            for key, value in result.metadata.items():
                metadata_text += f"{key}: {value}\n"
            self.metadata_text.setPlainText(metadata_text)
        else:
            self.metadata_text.setPlainText("无元数据")
    
    def _on_export_clicked(self):
        """导出按钮点击"""
        # 这里应该实现导出功能
        self.logger.info("导出功能待实现")
    
    def _on_copy_coordinates(self):
        """复制坐标"""
        if self.current_result:
            from PyQt6.QtWidgets import QApplication
            clipboard = QApplication.clipboard()
            coords = f"{self.current_result.bbox.center_x:.0f},{self.current_result.bbox.center_y:.0f}"
            clipboard.setText(coords)
            self.logger.info(f"坐标已复制: {coords}")
    
    def _on_locate_clicked(self):
        """定位按钮点击"""
        if self.current_result:
            # 这里应该实现定位功能
            self.logger.info("定位功能待实现")
    
    def clear_results(self):
        """清空结果"""
        self.detection_results.clear()
        self.current_result = None
        self.results_table.setRowCount(0)
        self._update_stats()
        self._clear_details()
    
    def _clear_details(self):
        """清空详情显示"""
        self.class_label.setText("类别: -")
        self.confidence_label.setText("置信度: -")
        self.source_label.setText("来源: -")
        self.position_label.setText("位置: -")
        self.size_label.setText("尺寸: -")
        self.center_label.setText("中心点: -")
        self.metadata_text.clear()
    
    def add_result(self, result: DetectionResult):
        """
        添加单个结果
        
        Args:
            result: 检测结果
        """
        self.detection_results.append(result)
        self._populate_table()
        self._update_stats()
    
    def get_selected_result(self) -> DetectionResult:
        """获取当前选中的结果"""
        return self.current_result
    
    def select_result_by_index(self, index: int):
        """
        按索引选择结果
        
        Args:
            index: 结果索引
        """
        if 0 <= index < len(self.detection_results):
            self.results_table.selectRow(index)
    
    def filter_results_by_confidence(self, min_confidence: float):
        """
        按置信度过滤结果
        
        Args:
            min_confidence: 最小置信度
        """
        filtered_results = [
            result for result in self.detection_results 
            if result.confidence >= min_confidence
        ]
        
        # 临时更新显示
        original_results = self.detection_results
        self.detection_results = filtered_results
        self._populate_table()
        self._update_stats()
        
        # 恢复原始数据
        self.detection_results = original_results
    
    def get_results_summary(self) -> Dict[str, Any]:
        """获取结果摘要"""
        if not self.detection_results:
            return {"total": 0}
        
        confidences = [r.confidence for r in self.detection_results]
        
        return {
            "total": len(self.detection_results),
            "avg_confidence": sum(confidences) / len(confidences),
            "max_confidence": max(confidences),
            "min_confidence": min(confidences),
            "sources": list(set(r.source.value for r in self.detection_results))
        }
