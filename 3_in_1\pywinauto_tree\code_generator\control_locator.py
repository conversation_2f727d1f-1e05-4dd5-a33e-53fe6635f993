#!/usr/bin/env python
# -*- coding:utf-8 -*-

from abc import ABC, abstractmethod
import logging
from typing import Optional, Dict, Any, List
from pywinauto import Application
from pywinauto.controls.uiawrapper import UIAWrapper

class ControlLocatorStrategy(ABC):
    """控件定位策略的基类"""
    
    @abstractmethod
    def generate_code(self, params: Dict[str, Any]) -> str:
        """生成定位代码"""
        pass
    
    @abstractmethod
    def test_location(self, window: UIAWrapper, params: Dict[str, Any]) -> Optional[UIAWrapper]:
        """测试定位是否成功"""
        pass
    
    @property
    @abstractmethod
    def description(self) -> str:
        """策略描述"""
        pass
    
    @property
    @abstractmethod
    def required_params(self) -> Dict[str, str]:
        """所需参数及其描述"""
        pass

class AutomationIdLocator(ControlLocatorStrategy):
    """通过AutomationId定位"""
    
    def generate_code(self, params: Dict[str, Any]) -> str:
        auto_id = params.get('auto_id', '')
        return f"""# 通过AutomationId定位控件
control = window.child_window(auto_id='{auto_id}')"""
    
    def test_location(self, window: UIAWrapper, params: Dict[str, Any]) -> Optional[UIAWrapper]:
        try:
            auto_id = params.get('auto_id', '')
            control = window.child_window(auto_id=auto_id)
            if control.exists():
                return control
        except Exception as e:
            logging.error(f"通过AutomationId定位控件失败: {e}")
        return None
    
    @property
    def description(self) -> str:
        return "通过AutomationId定位控件（最稳定的方式）"
    
    @property
    def required_params(self) -> Dict[str, str]:
        return {
            'auto_id': 'AutomationId值'
        }

class NameLocator(ControlLocatorStrategy):
    """通过Name定位"""
    
    def generate_code(self, params: Dict[str, Any]) -> str:
        name = params.get('name', '')
        return f"""# 通过Name定位控件
control = window.child_window(title='{name}')"""
    
    def test_location(self, window: UIAWrapper, params: Dict[str, Any]) -> Optional[UIAWrapper]:
        try:
            name = params.get('name', '')
            control = window.child_window(title=name)
            if control.exists():
                return control
        except Exception as e:
            logging.error(f"通过Name定位控件失败: {e}")
        return None
    
    @property
    def description(self) -> str:
        return "通过控件名称定位（显示文本）"
    
    @property
    def required_params(self) -> Dict[str, str]:
        return {
            'name': '控件名称'
        }

class ClassNameLocator(ControlLocatorStrategy):
    """通过ClassName定位"""
    
    def generate_code(self, params: Dict[str, Any]) -> str:
        class_name = params.get('class_name', '')
        return f"""# 通过ClassName定位控件
control = window.child_window(class_name='{class_name}')"""
    
    def test_location(self, window: UIAWrapper, params: Dict[str, Any]) -> Optional[UIAWrapper]:
        try:
            class_name = params.get('class_name', '')
            control = window.child_window(class_name=class_name)
            if control.exists():
                return control
        except Exception as e:
            logging.error(f"通过ClassName定位控件失败: {e}")
        return None
    
    @property
    def description(self) -> str:
        return "通过控件类名定位"
    
    @property
    def required_params(self) -> Dict[str, str]:
        return {
            'class_name': '控件类名'
        }

class ControlTypeLocator(ControlLocatorStrategy):
    """通过ControlType定位"""
    
    def generate_code(self, params: Dict[str, Any]) -> str:
        control_type = params.get('control_type', '')
        return f"""# 通过ControlType定位控件
control = window.child_window(control_type='{control_type}')"""
    
    def test_location(self, window: UIAWrapper, params: Dict[str, Any]) -> Optional[UIAWrapper]:
        try:
            control_type = params.get('control_type', '')
            control = window.child_window(control_type=control_type)
            if control.exists():
                return control
        except Exception as e:
            logging.error(f"通过ControlType定位控件失败: {e}")
        return None
    
    @property
    def description(self) -> str:
        return "通过控件类型定位"
    
    @property
    def required_params(self) -> Dict[str, str]:
        return {
            'control_type': '控件类型'
        }

class IndexLocator(ControlLocatorStrategy):
    """通过索引定位"""
    
    def generate_code(self, params: Dict[str, Any]) -> str:
        found_index = params.get('found_index', 0)
        return f"""# 通过索引定位控件
control = window.child_window(found_index={found_index})"""
    
    def test_location(self, window: UIAWrapper, params: Dict[str, Any]) -> Optional[UIAWrapper]:
        try:
            found_index = params.get('found_index', 0)
            control = window.child_window(found_index=found_index)
            if control.exists():
                return control
        except Exception as e:
            logging.error(f"通过索引定位控件失败: {e}")
        return None
    
    @property
    def description(self) -> str:
        return "通过控件索引定位（不稳定，仅用于测试）"
    
    @property
    def required_params(self) -> Dict[str, str]:
        return {
            'found_index': '控件索引（从0开始）'
        }

class CompositeLocator(ControlLocatorStrategy):
    """组合定位策略"""
    
    def generate_code(self, params: Dict[str, Any]) -> str:
        criteria = params.get('criteria', {})
        criteria_str = ', '.join([f"{k}='{v}'" for k, v in criteria.items()])
        return f"""# 通过组合条件定位控件
control = window.child_window({criteria_str})"""
    
    def test_location(self, window: UIAWrapper, params: Dict[str, Any]) -> Optional[UIAWrapper]:
        try:
            criteria = params.get('criteria', {})
            control = window.child_window(**criteria)
            if control.exists():
                return control
        except Exception as e:
            logging.error(f"通过组合条件定位控件失败: {e}")
        return None
    
    @property
    def description(self) -> str:
        return "通过多个条件组合定位控件"
    
    @property
    def required_params(self) -> Dict[str, str]:
        return {
            'criteria': '定位条件字典'
        }

class CustomControlLocator(ControlLocatorStrategy):
    """自定义控件定位策略"""
    
    def generate_code(self, params: Dict[str, Any]) -> str:
        code = params.get('code', '')
        return f"""# 自定义控件定位代码
{code}"""
    
    def test_location(self, window: UIAWrapper, params: Dict[str, Any]) -> Optional[UIAWrapper]:
        try:
            code = params.get('code', '')
            # 创建一个局部命名空间
            local_vars = {'window': window}
            # 执行代码
            exec(code, {}, local_vars)
            # 检查是否定义了control
            if 'control' in local_vars:
                control = local_vars['control']
                if control.exists():
                    return control
        except Exception as e:
            logging.error(f"自定义定位控件失败: {e}")
        return None
    
    @property
    def description(self) -> str:
        return "使用自定义代码定位控件（完全自定义）"
    
    @property
    def required_params(self) -> Dict[str, str]:
        return {
            'code': '自定义定位代码（必须定义control变量）'
        }

def get_available_strategies() -> Dict[str, ControlLocatorStrategy]:
    """获取所有可用的定位策略"""
    return {
        'auto_id': AutomationIdLocator(),
        'name': NameLocator(),
        'class_name': ClassNameLocator(),
        'control_type': ControlTypeLocator(),
        'index': IndexLocator(),
        'composite': CompositeLocator(),
        'custom': CustomControlLocator()
    }

def get_recommended_strategies(control_info: Dict[str, Any]) -> List[str]:
    """根据控件信息推荐最佳定位策略

    Args:
        control_info: 控件信息字典，包含auto_id, name, class_name等

    Returns:
        按优先级排序的策略列表
    """
    strategies = []

    # 1. 优先推荐AutomationId（最稳定）
    if control_info.get('auto_id'):
        strategies.append('auto_id')

    # 2. 组合定位（高精确度）
    available_attrs = sum([
        bool(control_info.get('auto_id')),
        bool(control_info.get('name')),
        bool(control_info.get('class_name'))
    ])
    if available_attrs >= 2:
        strategies.append('composite')

    # 3. 类名定位（中等稳定性）
    if control_info.get('class_name'):
        strategies.append('class_name')

    # 4. 名称定位（适用于静态文本）
    if control_info.get('name') and not _is_dynamic_text(control_info.get('name', '')):
        strategies.append('name')

    # 5. 控件类型定位（特定场景）
    if control_info.get('control_type'):
        strategies.append('control_type')

    # 6. 索引定位（最后选择）
    strategies.append('index')

    return strategies

def _is_dynamic_text(text: str) -> bool:
    """判断文本是否为动态内容"""
    dynamic_patterns = [
        r'\d{4}-\d{2}-\d{2}',  # 日期格式
        r'\d{2}:\d{2}:\d{2}',  # 时间格式
        r'\d+%',               # 百分比
        r'第\s*\d+\s*页',      # 页码
        r'\d+\s*条记录',       # 记录数
    ]

    import re
    for pattern in dynamic_patterns:
        if re.search(pattern, text):
            return True
    return False

def test_strategy(window: UIAWrapper):
    """测试各种定位策略"""
    strategies = get_available_strategies()
    
    # 获取窗口中第一个控件的信息作为测试数据
    try:
        first_control = window.children()[0]
        auto_id = first_control.automation_id()
        name = first_control.window_text()
        class_name = first_control.class_name()
        control_type = first_control.control_type()
        
        # 测试AutomationId定位
        if auto_id:
            print("\n测试AutomationId定位:")
            control = strategies['auto_id'].test_location(window, {'auto_id': auto_id})
            print("成功" if control else "失败")
        
        # 测试Name定位
        if name:
            print("\n测试Name定位:")
            control = strategies['name'].test_location(window, {'name': name})
            print("成功" if control else "失败")
        
        # 测试ClassName定位
        if class_name:
            print("\n测试ClassName定位:")
            control = strategies['class_name'].test_location(window, {'class_name': class_name})
            print("成功" if control else "失败")
        
        # 测试ControlType定位
        if control_type:
            print("\n测试ControlType定位:")
            control = strategies['control_type'].test_location(window, {'control_type': control_type})
            print("成功" if control else "失败")
        
        # 测试组合定位
        print("\n测试组合定位:")
        criteria = {}
        if auto_id:
            criteria['auto_id'] = auto_id
        if name:
            criteria['title'] = name
        if class_name:
            criteria['class_name'] = class_name
        if criteria:
            control = strategies['composite'].test_location(window, {'criteria': criteria})
            print("成功" if control else "失败")
        
        # 生成示例代码
        print("\n生成的代码示例:")
        if auto_id:
            print(strategies['auto_id'].generate_code({'auto_id': auto_id}))
            
    except Exception as e:
        logging.error(f"测试定位策略失败: {e}")

if __name__ == "__main__":
    # 此处需要一个有效的窗口对象来测试
    pass
