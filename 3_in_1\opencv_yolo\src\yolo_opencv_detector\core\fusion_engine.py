# -*- coding: utf-8 -*-
"""
结果融合引擎
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

import time
from typing import List, Dict, Any, Optional, Tuple
import numpy as np

from ..utils.logger import Logger, performance_timer
from ..utils.data_structures import DetectionResult, BoundingBox, DetectionSource
from ..utils.constants import DEFAULT_IOU_THRESHOLD, DEFAULT_CONFIDENCE_WEIGHT, DEFAULT_TEMPLATE_WEIGHT


class FusionEngine:
    """结果融合引擎类"""
    
    def __init__(self,
                 iou_threshold: float = DEFAULT_IOU_THRESHOLD,
                 confidence_weight: float = DEFAULT_CONFIDENCE_WEIGHT,
                 template_weight: float = DEFAULT_TEMPLATE_WEIGHT,
                 enable_nms: bool = True,
                 max_results: int = 50):
        """
        初始化融合引擎
        
        Args:
            iou_threshold: IoU阈值
            confidence_weight: YOLO检测置信度权重
            template_weight: 模板匹配置信度权重
            enable_nms: 是否启用非极大值抑制
            max_results: 最大结果数量
        """
        self.logger = Logger().get_logger(__name__)
        
        # 配置参数
        self.iou_threshold = iou_threshold
        self.confidence_weight = confidence_weight
        self.template_weight = template_weight
        self.enable_nms = enable_nms
        self.max_results = max_results
        
        # 性能统计
        self.fusion_times: List[float] = []
        self.total_fusions = 0
        self.fusion_stats = {
            "yolo_only": 0,
            "template_only": 0,
            "fused": 0,
            "discarded": 0
        }
        
        self.logger.info(f"结果融合引擎初始化完成 - IoU阈值: {iou_threshold}")
    
    @performance_timer("结果融合")
    def fuse_results(self, 
                    yolo_results: List[DetectionResult],
                    template_results: List[DetectionResult]) -> List[DetectionResult]:
        """
        融合YOLO和模板匹配结果
        
        Args:
            yolo_results: YOLO检测结果
            template_results: 模板匹配结果
            
        Returns:
            List[DetectionResult]: 融合后的结果
        """
        try:
            start_time = time.time()
            
            # 输入验证
            if not yolo_results and not template_results:
                return []
            
            # 如果只有一种类型的结果，直接返回
            if not yolo_results:
                self.fusion_stats["template_only"] += len(template_results)
                return self._apply_nms(template_results) if self.enable_nms else template_results
            
            if not template_results:
                self.fusion_stats["yolo_only"] += len(yolo_results)
                return self._apply_nms(yolo_results) if self.enable_nms else yolo_results
            
            # 执行融合
            fused_results = self._perform_fusion(yolo_results, template_results)
            
            # 应用NMS
            if self.enable_nms:
                fused_results = self._apply_nms(fused_results)
            
            # 限制结果数量
            if len(fused_results) > self.max_results:
                fused_results = sorted(fused_results, key=lambda x: x.confidence, reverse=True)
                fused_results = fused_results[:self.max_results]
            
            # 更新统计
            fusion_time = time.time() - start_time
            self.fusion_times.append(fusion_time)
            self.total_fusions += 1
            
            self.logger.debug(f"结果融合完成 - YOLO: {len(yolo_results)}, 模板: {len(template_results)}, "
                            f"融合后: {len(fused_results)}, 耗时: {fusion_time:.4f}s")
            
            return fused_results
            
        except Exception as e:
            self.logger.error(f"结果融合失败: {e}")
            # 发生错误时返回原始结果
            return yolo_results + template_results
    
    def _perform_fusion(self, 
                       yolo_results: List[DetectionResult],
                       template_results: List[DetectionResult]) -> List[DetectionResult]:
        """执行结果融合"""
        fused_results = []
        used_yolo_indices = set()
        used_template_indices = set()
        
        # 寻找匹配的检测结果进行融合
        for i, yolo_result in enumerate(yolo_results):
            best_match_idx = -1
            best_iou = 0.0
            
            # 寻找最佳匹配的模板结果
            for j, template_result in enumerate(template_results):
                if j in used_template_indices:
                    continue
                
                iou = self._calculate_iou(yolo_result.bbox, template_result.bbox)
                
                if iou > self.iou_threshold and iou > best_iou:
                    best_iou = iou
                    best_match_idx = j
            
            if best_match_idx >= 0:
                # 找到匹配，进行融合
                template_result = template_results[best_match_idx]
                fused_result = self._fuse_detection_pair(yolo_result, template_result, best_iou)
                fused_results.append(fused_result)
                
                used_yolo_indices.add(i)
                used_template_indices.add(best_match_idx)
                self.fusion_stats["fused"] += 1
        
        # 添加未匹配的YOLO结果
        for i, yolo_result in enumerate(yolo_results):
            if i not in used_yolo_indices:
                fused_results.append(yolo_result)
                self.fusion_stats["yolo_only"] += 1
        
        # 添加未匹配的模板结果
        for j, template_result in enumerate(template_results):
            if j not in used_template_indices:
                fused_results.append(template_result)
                self.fusion_stats["template_only"] += 1
        
        return fused_results
    
    def _fuse_detection_pair(self, 
                           yolo_result: DetectionResult,
                           template_result: DetectionResult,
                           iou: float) -> DetectionResult:
        """融合一对检测结果"""
        try:
            # 计算融合后的置信度
            fused_confidence = (
                yolo_result.confidence * self.confidence_weight +
                template_result.confidence * self.template_weight
            )
            
            # 融合边界框（加权平均）
            fused_bbox = self._fuse_bounding_boxes(
                yolo_result.bbox, template_result.bbox,
                yolo_result.confidence, template_result.confidence
            )
            
            # 选择类别信息（优先使用YOLO的类别信息）
            class_id = yolo_result.class_id
            class_name = yolo_result.class_name
            
            # 如果YOLO没有类别信息，使用模板信息
            if class_name is None and template_result.template_id:
                class_name = f"template_{template_result.template_id}"
            
            # 创建融合后的结果
            fused_result = DetectionResult(
                bbox=fused_bbox,
                confidence=min(fused_confidence, 1.0),  # 确保不超过1.0
                class_id=class_id,
                class_name=class_name,
                template_id=template_result.template_id,
                source=DetectionSource.FUSION,
                metadata={
                    "fusion_info": {
                        "yolo_confidence": yolo_result.confidence,
                        "template_confidence": template_result.confidence,
                        "iou": iou,
                        "confidence_weight": self.confidence_weight,
                        "template_weight": self.template_weight,
                        "yolo_bbox": yolo_result.bbox.to_tuple(),
                        "template_bbox": template_result.bbox.to_tuple()
                    },
                    "original_yolo_metadata": yolo_result.metadata,
                    "original_template_metadata": template_result.metadata
                }
            )
            
            return fused_result
            
        except Exception as e:
            self.logger.error(f"检测结果对融合失败: {e}")
            # 返回置信度更高的结果
            return yolo_result if yolo_result.confidence > template_result.confidence else template_result
    
    def _fuse_bounding_boxes(self, 
                           bbox1: BoundingBox, 
                           bbox2: BoundingBox,
                           weight1: float, 
                           weight2: float) -> BoundingBox:
        """融合两个边界框"""
        try:
            # 归一化权重
            total_weight = weight1 + weight2
            if total_weight == 0:
                w1, w2 = 0.5, 0.5
            else:
                w1 = weight1 / total_weight
                w2 = weight2 / total_weight
            
            # 加权平均坐标
            x1_1, y1_1, x2_1, y2_1 = bbox1.x, bbox1.y, bbox1.x2, bbox1.y2
            x1_2, y1_2, x2_2, y2_2 = bbox2.x, bbox2.y, bbox2.x2, bbox2.y2
            
            fused_x1 = int(x1_1 * w1 + x1_2 * w2)
            fused_y1 = int(y1_1 * w1 + y1_2 * w2)
            fused_x2 = int(x2_1 * w1 + x2_2 * w2)
            fused_y2 = int(y2_1 * w1 + y2_2 * w2)
            
            # 确保边界框有效
            fused_width = max(1, fused_x2 - fused_x1)
            fused_height = max(1, fused_y2 - fused_y1)
            
            return BoundingBox(
                x=fused_x1,
                y=fused_y1,
                width=fused_width,
                height=fused_height
            )
            
        except Exception as e:
            self.logger.error(f"边界框融合失败: {e}")
            # 返回面积更大的边界框
            return bbox1 if bbox1.area > bbox2.area else bbox2
    
    def _calculate_iou(self, bbox1: BoundingBox, bbox2: BoundingBox) -> float:
        """计算两个边界框的IoU"""
        try:
            return bbox1.iou(bbox2)
        except Exception as e:
            self.logger.error(f"IoU计算失败: {e}")
            return 0.0
    
    def _apply_nms(self, results: List[DetectionResult], iou_threshold: Optional[float] = None) -> List[DetectionResult]:
        """应用非极大值抑制"""
        if len(results) <= 1:
            return results
        
        try:
            if iou_threshold is None:
                iou_threshold = self.iou_threshold
            
            # 按置信度排序
            sorted_results = sorted(results, key=lambda x: x.confidence, reverse=True)
            
            keep_results = []
            
            for current_result in sorted_results:
                should_keep = True
                
                for kept_result in keep_results:
                    iou = self._calculate_iou(current_result.bbox, kept_result.bbox)
                    
                    if iou > iou_threshold:
                        # 如果IoU过高，检查是否应该保留
                        if self._should_suppress(current_result, kept_result):
                            should_keep = False
                            break
                
                if should_keep:
                    keep_results.append(current_result)
            
            suppressed_count = len(results) - len(keep_results)
            if suppressed_count > 0:
                self.fusion_stats["discarded"] += suppressed_count
                self.logger.debug(f"NMS抑制了 {suppressed_count} 个重叠检测")
            
            return keep_results
            
        except Exception as e:
            self.logger.error(f"NMS处理失败: {e}")
            return results
    
    def _should_suppress(self, current: DetectionResult, kept: DetectionResult) -> bool:
        """判断是否应该抑制当前检测结果"""
        # 如果置信度相同，保留面积更大的
        if abs(current.confidence - kept.confidence) < 0.01:
            return current.bbox.area < kept.bbox.area
        
        # 如果来源不同，需要特殊处理
        if current.source != kept.source:
            # 融合结果优先级最高
            if kept.source == DetectionSource.FUSION:
                return True
            if current.source == DetectionSource.FUSION:
                return False
            
            # YOLO和模板匹配结果之间，优先保留置信度高的
            return current.confidence < kept.confidence
        
        # 同类型结果，保留置信度高的
        return current.confidence < kept.confidence
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        if not self.fusion_times:
            return {}
        
        total_processed = sum(self.fusion_stats.values())
        
        return {
            "total_fusions": len(self.fusion_times),
            "avg_fusion_time": np.mean(self.fusion_times),
            "min_fusion_time": np.min(self.fusion_times),
            "max_fusion_time": np.max(self.fusion_times),
            "total_results_processed": total_processed,
            "fusion_distribution": self.fusion_stats.copy(),
            "fusion_rate": self.fusion_stats["fused"] / total_processed if total_processed > 0 else 0.0,
            "suppression_rate": self.fusion_stats["discarded"] / total_processed if total_processed > 0 else 0.0
        }
    
    def reset_stats(self) -> None:
        """重置性能统计"""
        self.fusion_times.clear()
        self.total_fusions = 0
        self.fusion_stats = {
            "yolo_only": 0,
            "template_only": 0,
            "fused": 0,
            "discarded": 0
        }
        self.logger.info("融合引擎性能统计已重置")
    
    def update_config(self, **kwargs) -> None:
        """
        更新配置参数
        
        Args:
            **kwargs: 配置参数
        """
        if "iou_threshold" in kwargs:
            self.iou_threshold = kwargs["iou_threshold"]
            
        if "confidence_weight" in kwargs:
            self.confidence_weight = kwargs["confidence_weight"]
            
        if "template_weight" in kwargs:
            self.template_weight = kwargs["template_weight"]
            
        if "enable_nms" in kwargs:
            self.enable_nms = kwargs["enable_nms"]
            
        if "max_results" in kwargs:
            self.max_results = kwargs["max_results"]
        
        # 确保权重归一化
        total_weight = self.confidence_weight + self.template_weight
        if total_weight > 0:
            self.confidence_weight /= total_weight
            self.template_weight /= total_weight
        
        self.logger.info(f"融合引擎配置已更新: {kwargs}")
    
    def validate_config(self) -> bool:
        """验证配置参数"""
        try:
            # 检查阈值范围
            if not (0.0 <= self.iou_threshold <= 1.0):
                self.logger.error(f"IoU阈值超出范围: {self.iou_threshold}")
                return False
            
            # 检查权重
            if self.confidence_weight < 0 or self.template_weight < 0:
                self.logger.error("权重不能为负数")
                return False
            
            if self.confidence_weight + self.template_weight == 0:
                self.logger.error("权重总和不能为零")
                return False
            
            # 检查最大结果数
            if self.max_results <= 0:
                self.logger.error(f"最大结果数必须大于0: {self.max_results}")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"配置验证失败: {e}")
            return False
