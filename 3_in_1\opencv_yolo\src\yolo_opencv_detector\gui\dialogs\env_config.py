#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
源代码对话框环境配置
自动生成的环境配置文件
"""

import sys
import os
from pathlib import Path

# 环境配置
PYTHON_EXECUTABLE = r"C:\ProgramData\anaconda3\python.exe"
PROJECT_ROOT = r"C:\Users\<USER>\Documents\【看见上海】\yolo_opencv_run"

def setup_environment():
    """设置正确的环境"""
    # 添加项目路径
    if str(PROJECT_ROOT) not in sys.path:
        sys.path.insert(0, str(PROJECT_ROOT))
    
    # 设置环境变量
    os.environ['PYTHONPATH'] = str(PROJECT_ROOT)
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    
    return True

# 自动设置环境
setup_environment()
