# -*- coding: utf-8 -*-
"""
GPU加速器模块
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

import os
import sys
import time
import numpy as np
from typing import Optional, Dict, Any, List, Tuple
from pathlib import Path

from ..utils.logger import Logger


class GPUAccelerator:
    """GPU加速器类"""
    
    def __init__(self):
        """初始化GPU加速器"""
        self.logger = Logger().get_logger(__name__)
        
        # GPU状态
        self.cuda_available = False
        self.gpu_count = 0
        self.gpu_info = {}
        self.current_device = None
        
        # 性能统计
        self.gpu_memory_usage = {}
        self.inference_times = []
        
        # 初始化GPU环境
        self._initialize_gpu()
        
        self.logger.info("GPU加速器初始化完成")
    
    def _initialize_gpu(self) -> None:
        """初始化GPU环境"""
        try:
            # 检查CUDA可用性
            self._check_cuda_availability()
            
            # 检测GPU设备
            if self.cuda_available:
                self._detect_gpu_devices()
                self._set_optimal_device()
            
        except Exception as e:
            self.logger.error(f"GPU环境初始化失败: {e}")
    
    def _check_cuda_availability(self) -> None:
        """检查CUDA可用性"""
        try:
            # 尝试导入torch
            try:
                import torch
                self.cuda_available = torch.cuda.is_available()
                if self.cuda_available:
                    self.gpu_count = torch.cuda.device_count()
                    self.logger.info(f"CUDA可用，检测到 {self.gpu_count} 个GPU设备")
                else:
                    self.logger.info("CUDA不可用，将使用CPU模式")
            except ImportError:
                self.logger.info("PyTorch未安装，无法使用CUDA加速")
                self.cuda_available = False
            
            # 检查OpenCV GPU支持
            try:
                import cv2
                if cv2.cuda.getCudaEnabledDeviceCount() > 0:
                    self.logger.info("OpenCV CUDA支持可用")
                else:
                    self.logger.info("OpenCV CUDA支持不可用")
            except:
                self.logger.info("OpenCV CUDA支持检查失败")
                
        except Exception as e:
            self.logger.error(f"CUDA可用性检查失败: {e}")
            self.cuda_available = False
    
    def _detect_gpu_devices(self) -> None:
        """检测GPU设备信息"""
        try:
            if not self.cuda_available:
                return
            
            import torch
            
            for i in range(self.gpu_count):
                device_props = torch.cuda.get_device_properties(i)
                
                self.gpu_info[i] = {
                    "name": device_props.name,
                    "total_memory": device_props.total_memory,
                    "major": device_props.major,
                    "minor": device_props.minor,
                    "multi_processor_count": device_props.multi_processor_count
                }
                
                self.logger.info(f"GPU {i}: {device_props.name} "
                               f"({device_props.total_memory / 1024**3:.1f}GB)")
                
        except Exception as e:
            self.logger.error(f"GPU设备检测失败: {e}")
    
    def _set_optimal_device(self) -> None:
        """设置最优GPU设备"""
        try:
            if not self.cuda_available or self.gpu_count == 0:
                return
            
            import torch
            
            # 选择显存最大的GPU
            best_device = 0
            max_memory = 0
            
            for i in range(self.gpu_count):
                memory = self.gpu_info[i]["total_memory"]
                if memory > max_memory:
                    max_memory = memory
                    best_device = i
            
            self.current_device = best_device
            torch.cuda.set_device(best_device)
            
            self.logger.info(f"选择GPU设备 {best_device}: {self.gpu_info[best_device]['name']}")
            
        except Exception as e:
            self.logger.error(f"设置最优GPU设备失败: {e}")
    
    def get_device_info(self) -> Dict[str, Any]:
        """获取设备信息"""
        return {
            "cuda_available": self.cuda_available,
            "gpu_count": self.gpu_count,
            "current_device": self.current_device,
            "gpu_info": self.gpu_info
        }
    
    def optimize_yolo_model(self, model_path: str) -> Optional[Any]:
        """优化YOLO模型以使用GPU"""
        try:
            if not self.cuda_available:
                self.logger.info("CUDA不可用，使用CPU模式")
                return None
            
            # 这里可以添加模型优化逻辑
            # 例如：TensorRT优化、模型量化等
            
            self.logger.info(f"YOLO模型GPU优化完成: {model_path}")
            return None
            
        except Exception as e:
            self.logger.error(f"YOLO模型GPU优化失败: {e}")
            return None
    
    def optimize_opencv_operations(self) -> bool:
        """优化OpenCV操作以使用GPU"""
        try:
            import cv2
            
            if cv2.cuda.getCudaEnabledDeviceCount() == 0:
                self.logger.info("OpenCV CUDA不可用")
                return False
            
            # 设置OpenCV使用GPU
            cv2.cuda.setDevice(self.current_device or 0)
            
            self.logger.info("OpenCV GPU优化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"OpenCV GPU优化失败: {e}")
            return False
    
    def monitor_gpu_usage(self) -> Dict[str, Any]:
        """监控GPU使用情况"""
        try:
            if not self.cuda_available:
                return {}
            
            import torch
            
            usage_info = {}
            
            for i in range(self.gpu_count):
                # 获取GPU内存使用情况
                torch.cuda.set_device(i)
                memory_allocated = torch.cuda.memory_allocated(i)
                memory_reserved = torch.cuda.memory_reserved(i)
                memory_total = self.gpu_info[i]["total_memory"]
                
                usage_info[f"gpu_{i}"] = {
                    "memory_allocated": memory_allocated,
                    "memory_reserved": memory_reserved,
                    "memory_total": memory_total,
                    "memory_usage_percent": (memory_allocated / memory_total) * 100,
                    "memory_allocated_mb": memory_allocated / 1024**2,
                    "memory_total_mb": memory_total / 1024**2
                }
            
            # 恢复原设备
            if self.current_device is not None:
                torch.cuda.set_device(self.current_device)
            
            return usage_info
            
        except Exception as e:
            self.logger.error(f"GPU使用情况监控失败: {e}")
            return {}
    
    def clear_gpu_cache(self) -> None:
        """清理GPU缓存"""
        try:
            if not self.cuda_available:
                return
            
            import torch
            torch.cuda.empty_cache()
            
            self.logger.info("GPU缓存已清理")
            
        except Exception as e:
            self.logger.error(f"GPU缓存清理失败: {e}")
    
    def benchmark_gpu_performance(self, test_iterations: int = 100) -> Dict[str, float]:
        """GPU性能基准测试"""
        try:
            if not self.cuda_available:
                return {"error": "CUDA不可用"}
            
            import torch
            
            # 创建测试数据
            device = torch.device(f"cuda:{self.current_device}")
            test_tensor = torch.randn(1000, 1000, device=device)
            
            # 矩阵乘法测试
            start_time = time.time()
            for _ in range(test_iterations):
                result = torch.mm(test_tensor, test_tensor)
                torch.cuda.synchronize()  # 等待GPU操作完成
            
            matrix_mult_time = (time.time() - start_time) / test_iterations
            
            # 内存带宽测试
            large_tensor = torch.randn(10000, 10000, device=device)
            start_time = time.time()
            for _ in range(10):
                copied_tensor = large_tensor.clone()
                torch.cuda.synchronize()
            
            memory_bandwidth_time = (time.time() - start_time) / 10
            
            benchmark_results = {
                "matrix_multiplication_avg_time": matrix_mult_time,
                "memory_bandwidth_avg_time": memory_bandwidth_time,
                "gpu_name": self.gpu_info[self.current_device]["name"],
                "test_iterations": test_iterations
            }
            
            self.logger.info(f"GPU性能基准测试完成: {benchmark_results}")
            return benchmark_results
            
        except Exception as e:
            self.logger.error(f"GPU性能基准测试失败: {e}")
            return {"error": str(e)}
    
    def get_optimal_batch_size(self, model_input_size: Tuple[int, int, int]) -> int:
        """获取最优批处理大小"""
        try:
            if not self.cuda_available:
                return 1
            
            import torch
            
            # 获取可用GPU内存
            device = torch.device(f"cuda:{self.current_device}")
            available_memory = torch.cuda.get_device_properties(device).total_memory
            allocated_memory = torch.cuda.memory_allocated(device)
            free_memory = available_memory - allocated_memory
            
            # 估算单个样本的内存需求
            c, h, w = model_input_size
            bytes_per_sample = c * h * w * 4  # 假设float32
            
            # 保留一些内存余量
            safety_factor = 0.8
            usable_memory = free_memory * safety_factor
            
            # 计算最优批大小
            optimal_batch_size = max(1, int(usable_memory // bytes_per_sample))
            
            # 限制在合理范围内
            optimal_batch_size = min(optimal_batch_size, 32)
            
            self.logger.info(f"推荐批处理大小: {optimal_batch_size}")
            return optimal_batch_size
            
        except Exception as e:
            self.logger.error(f"获取最优批处理大小失败: {e}")
            return 1
    
    def enable_mixed_precision(self) -> bool:
        """启用混合精度训练"""
        try:
            if not self.cuda_available:
                return False
            
            import torch
            
            # 检查是否支持混合精度
            if torch.cuda.is_available() and torch.cuda.get_device_capability()[0] >= 7:
                self.logger.info("启用混合精度模式")
                return True
            else:
                self.logger.info("GPU不支持混合精度模式")
                return False
                
        except Exception as e:
            self.logger.error(f"启用混合精度失败: {e}")
            return False
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        try:
            stats = {
                "gpu_info": self.get_device_info(),
                "gpu_usage": self.monitor_gpu_usage(),
                "inference_times": {
                    "count": len(self.inference_times),
                    "avg_time": np.mean(self.inference_times) if self.inference_times else 0,
                    "min_time": np.min(self.inference_times) if self.inference_times else 0,
                    "max_time": np.max(self.inference_times) if self.inference_times else 0
                }
            }
            
            return stats
            
        except Exception as e:
            self.logger.error(f"获取性能统计失败: {e}")
            return {}
    
    def record_inference_time(self, inference_time: float) -> None:
        """记录推理时间"""
        self.inference_times.append(inference_time)
        
        # 限制历史记录长度
        if len(self.inference_times) > 1000:
            self.inference_times = self.inference_times[-500:]
    
    def optimize_for_inference(self) -> Dict[str, Any]:
        """为推理优化GPU设置"""
        try:
            optimizations = {}
            
            if self.cuda_available:
                import torch
                
                # 设置为评估模式
                torch.backends.cudnn.benchmark = True
                optimizations["cudnn_benchmark"] = True
                
                # 禁用梯度计算
                torch.set_grad_enabled(False)
                optimizations["grad_enabled"] = False
                
                # 设置确定性算法
                torch.backends.cudnn.deterministic = False
                optimizations["cudnn_deterministic"] = False
                
                self.logger.info("GPU推理优化设置完成")
            
            return optimizations
            
        except Exception as e:
            self.logger.error(f"GPU推理优化失败: {e}")
            return {}
