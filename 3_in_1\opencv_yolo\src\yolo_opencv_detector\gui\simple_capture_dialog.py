# -*- coding: utf-8 -*-
"""
简化的模板截取对话框
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
    QLabel, QLineEdit, QTextEdit, QGroupBox,
    QDoubleSpinBox, QMessageBox, QFileDialog
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QPixmap, QImage

import cv2
import numpy as np
from pathlib import Path
import time

from ..utils.logger import Logger


class SimpleCaptureDialog(QDialog):
    """简化的模板截取对话框"""
    
    template_created = pyqtSignal(str, dict)  # template_id, template_data
    
    def __init__(self, parent=None):
        """
        初始化简化模板截取对话框
        
        Args:
            parent: 父窗口
        """
        super().__init__(parent)
        self.logger = Logger().get_logger(__name__)
        
        # 数据
        self.captured_image = None
        
        self._setup_ui()
        self._connect_signals()
        
        self.logger.info("简化模板截取对话框初始化完成")
    
    def _setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("创建模板")
        self.setModal(True)
        self.resize(600, 750)  # 增大窗口尺寸
        
        layout = QVBoxLayout(self)
        
        # 截取控制组
        capture_group = QGroupBox("获取图像")
        capture_layout = QVBoxLayout(capture_group)
        
        # 说明文本
        info_label = QLabel("选择获取模板图像的方式：")
        info_label.setWordWrap(True)
        capture_layout.addWidget(info_label)
        
        # 截取按钮
        button_layout = QHBoxLayout()
        
        self.capture_btn = QPushButton("截取屏幕")
        self.capture_btn.setMinimumHeight(40)
        self.capture_btn.setToolTip("截取整个屏幕作为模板")
        button_layout.addWidget(self.capture_btn)
        
        self.load_file_btn = QPushButton("从文件加载")
        self.load_file_btn.setToolTip("从图像文件加载模板")
        button_layout.addWidget(self.load_file_btn)
        
        capture_layout.addLayout(button_layout)
        
        # 状态信息
        self.status_label = QLabel("请选择获取图像的方式")
        capture_layout.addWidget(self.status_label)
        
        layout.addWidget(capture_group)
        
        # 预览组
        preview_group = QGroupBox("预览")
        preview_layout = QVBoxLayout(preview_group)
        
        self.preview_label = QLabel("无预览")
        self.preview_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.preview_label.setMinimumHeight(250)  # 增加预览区域高度
        self.preview_label.setStyleSheet("border: 1px solid gray; background-color: #f0f0f0;")
        preview_layout.addWidget(self.preview_label)
        
        layout.addWidget(preview_group)
        
        # 模板信息组
        info_group = QGroupBox("模板信息")
        info_layout = QVBoxLayout(info_group)
        
        # 模板名称
        name_layout = QHBoxLayout()
        name_layout.addWidget(QLabel("名称:"))
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("输入模板名称...")
        name_layout.addWidget(self.name_edit)
        info_layout.addLayout(name_layout)
        
        # 模板描述
        desc_layout = QVBoxLayout()
        desc_layout.addWidget(QLabel("描述:"))
        self.desc_edit = QTextEdit()
        self.desc_edit.setMaximumHeight(80)  # 增加描述框高度
        self.desc_edit.setPlaceholderText("输入模板描述...")
        desc_layout.addWidget(self.desc_edit)
        info_layout.addLayout(desc_layout)
        
        layout.addWidget(info_group)
        
        # 匹配设置组
        settings_group = QGroupBox("匹配设置")
        settings_layout = QVBoxLayout(settings_group)
        
        # 阈值
        threshold_layout = QHBoxLayout()
        threshold_layout.addWidget(QLabel("匹配阈值:"))
        self.threshold_spin = QDoubleSpinBox()
        self.threshold_spin.setRange(0.1, 1.0)
        self.threshold_spin.setSingleStep(0.05)
        self.threshold_spin.setDecimals(2)
        self.threshold_spin.setValue(0.8)
        threshold_layout.addWidget(self.threshold_spin)
        settings_layout.addLayout(threshold_layout)
        
        layout.addWidget(settings_group)
        
        # 按钮
        button_layout = QHBoxLayout()
        
        self.cancel_btn = QPushButton("取消")
        button_layout.addWidget(self.cancel_btn)
        
        button_layout.addStretch()
        
        self.create_btn = QPushButton("创建模板")
        self.create_btn.setEnabled(False)
        button_layout.addWidget(self.create_btn)
        
        layout.addLayout(button_layout)
    
    def _connect_signals(self):
        """连接信号"""
        self.capture_btn.clicked.connect(self._start_screen_capture)
        self.load_file_btn.clicked.connect(self._load_from_file)
        self.create_btn.clicked.connect(self._create_template)
        self.cancel_btn.clicked.connect(self.reject)
        
        # 文本变化
        self.name_edit.textChanged.connect(self._check_can_create)
    
    def _start_screen_capture(self):
        """开始屏幕截取"""
        try:
            # 提示用户
            reply = QMessageBox.information(
                self,
                "屏幕截取",
                "点击确定后，程序将在3秒后截取整个屏幕。\n"
                "请准备好要截取的内容。",
                QMessageBox.StandardButton.Ok | QMessageBox.StandardButton.Cancel
            )

            if reply == QMessageBox.StandardButton.Ok:
                self.logger.info("用户确认开始截图，3秒后执行...")
                self.status_label.setText("3秒后开始截图...")

                # 隐藏对话框
                self.hide()

                # 延迟3秒后截取
                QTimer.singleShot(3000, self._capture_screen)
            else:
                self.logger.info("用户取消截图")

        except Exception as e:
            self.logger.error(f"开始屏幕截取失败: {e}")
            QMessageBox.critical(self, "错误", f"开始截图失败: {e}")
    
    def _capture_screen(self):
        """截取屏幕"""
        try:
            self.logger.info("开始执行屏幕截图...")
            self.status_label.setText("正在截取屏幕...")

            # 方法1: 尝试mss
            try:
                import mss
                self.logger.info("使用mss方法截图...")

                with mss.mss() as sct:
                    # 获取主显示器
                    monitor = sct.monitors[1]
                    self.logger.info(f"显示器信息: {monitor}")

                    screenshot = sct.grab(monitor)
                    self.logger.info(f"截图完成，尺寸: {screenshot.size}")

                    # 转换为numpy数组
                    img_array = np.array(screenshot)
                    self.logger.info(f"转换为numpy数组: {img_array.shape}")

                    # 转换颜色格式 (BGRA -> BGR)
                    img_bgr = cv2.cvtColor(img_array, cv2.COLOR_BGRA2BGR)

                    self.captured_image = img_bgr
                    self.logger.info("mss截图成功")

            except Exception as e:
                self.logger.error(f"mss截图失败: {e}")

                # 方法2: 尝试PIL
                try:
                    from PIL import ImageGrab
                    self.logger.info("使用PIL方法截图...")

                    screenshot = ImageGrab.grab()
                    img_array = np.array(screenshot)
                    img_bgr = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)

                    self.captured_image = img_bgr
                    self.logger.info("PIL截图成功")

                except Exception as e2:
                    self.logger.error(f"PIL截图也失败: {e2}")
                    raise Exception(f"所有截图方法都失败了: mss({e}), PIL({e2})")

            if self.captured_image is not None:
                # 更新预览
                self._update_preview()

                # 更新状态
                height, width = self.captured_image.shape[:2]
                self.status_label.setText(f"屏幕截图成功: {width}×{height}")

                # 生成默认名称
                if not self.name_edit.text():
                    timestamp = int(time.time())
                    self.name_edit.setText(f"screenshot_{timestamp}")

                self.logger.info(f"屏幕截图成功: {width}×{height}")
                QMessageBox.information(self, "成功", "屏幕截图完成！")
            else:
                raise Exception("截图结果为空")

        except Exception as e:
            self.logger.error(f"屏幕截图失败: {e}")
            self.status_label.setText(f"截图失败: {e}")
            QMessageBox.critical(self, "错误", f"截图失败: {e}")

        finally:
            # 重新显示对话框
            self.show()
    
    def _load_from_file(self):
        """从文件加载图像"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "选择模板图像",
                "",
                "图像文件 (*.png *.jpg *.jpeg *.bmp *.tiff)"
            )
            
            if file_path:
                # 加载图像
                self.captured_image = cv2.imread(file_path)
                
                if self.captured_image is not None:
                    # 更新预览
                    self._update_preview()
                    
                    # 更新状态
                    height, width = self.captured_image.shape[:2]
                    self.status_label.setText(f"文件加载成功: {Path(file_path).name} ({width}×{height})")
                    
                    # 生成默认名称
                    if not self.name_edit.text():
                        file_name = Path(file_path).stem
                        self.name_edit.setText(f"template_{file_name}")
                    
                    self.logger.info(f"从文件加载图像成功: {file_path}")
                else:
                    QMessageBox.critical(self, "错误", "无法加载图像文件")
                    
        except Exception as e:
            self.logger.error(f"从文件加载图像失败: {e}")
            QMessageBox.critical(self, "错误", f"加载图像失败: {e}")
    
    def _update_preview(self):
        """更新预览图"""
        try:
            if self.captured_image is not None:
                # 转换为QPixmap
                height, width = self.captured_image.shape[:2]
                bytes_per_line = 3 * width

                # 转换颜色格式 (BGR -> RGB)
                rgb_image = cv2.cvtColor(self.captured_image, cv2.COLOR_BGR2RGB)

                q_image = QImage(rgb_image.data, width, height, bytes_per_line, QImage.Format.Format_RGB888)
                pixmap = QPixmap.fromImage(q_image)
                
                # 缩放到预览标签大小
                scaled_pixmap = pixmap.scaled(
                    self.preview_label.size(),
                    Qt.AspectRatioMode.KeepAspectRatio,
                    Qt.TransformationMode.SmoothTransformation
                )
                
                self.preview_label.setPixmap(scaled_pixmap)
                
        except Exception as e:
            self.logger.error(f"更新预览失败: {e}")
    
    def _check_can_create(self):
        """检查是否可以创建模板"""
        can_create = (
            self.captured_image is not None and
            len(self.name_edit.text().strip()) > 0
        )
        self.create_btn.setEnabled(can_create)
    
    def _create_template(self):
        """创建模板"""
        try:
            if self.captured_image is None:
                QMessageBox.warning(self, "警告", "请先截取或加载图像")
                return
            
            name = self.name_edit.text().strip()
            if not name:
                QMessageBox.warning(self, "警告", "请输入模板名称")
                return
            
            # 生成模板ID
            template_id = name.lower().replace(" ", "_").replace("-", "_")
            
            # 保存模板图像
            templates_dir = Path("templates")
            templates_dir.mkdir(exist_ok=True)
            
            template_file = templates_dir / f"{template_id}.png"
            cv2.imwrite(str(template_file), self.captured_image)
            
            # 创建模板数据
            template_data = {
                "name": name,
                "description": self.desc_edit.toPlainText(),
                "file_path": str(template_file),
                "threshold": self.threshold_spin.value(),
                "image_size": self.captured_image.shape[:2],
                "created_time": time.time()
            }
            
            # 发射信号
            self.template_created.emit(template_id, template_data)
            
            QMessageBox.information(self, "成功", f"模板 '{name}' 创建成功！")
            self.accept()
            
        except Exception as e:
            self.logger.error(f"创建模板失败: {e}")
            QMessageBox.critical(self, "错误", f"创建模板失败: {e}")
    
    def resizeEvent(self, event):
        """窗口大小改变事件"""
        super().resizeEvent(event)
        # 重新更新预览以适应新尺寸
        if self.captured_image is not None:
            QTimer.singleShot(100, self._update_preview)
