{"project_info": {"project_root": ".", "python_files_count": 106, "analysis_timestamp": "1751799505.752812"}, "requirements_analysis": {"total_declared": 35, "declared_packages": ["ultralytics", "torch", "torchvision", "opencv-python", "opencv-contrib-python", "Pillow", "PyQt6", "PyQt6-tools", "numpy", "scipy", "pandas", "scikit-image", "imageio", "PyAutoGUI", "pywin32", "pynput", "sqlite3  # Python内置，无需安装", "PyYAML", "configparser", "loguru", "psutil", "asyncio  # Python内置", "aiofiles", "pytest", "pytest-asyncio", "pytest-cov", "pytest-qt", "black", "flake8", "mypy", "ipython", "jup<PERSON><PERSON>", "pyinstaller", "setuptools", "wheel"]}, "code_analysis": {"total_imports": 76, "third_party_imports": ["tkinter", "psutil", "easyocr", "chardet", "screenshot_dialog_v2", "automation_complete", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "codecs", "screenshot_preview_dialog", "script_generator", "floating_detection_widget", "win32con", "loguru", "screen_overlay_widget", "win32ui", "fusion_engine", "screenshot_widget", "constants", "torch", "PyInstaller", "<PERSON><PERSON><PERSON><PERSON>", "screen_capture_overlay_v2", "PyQt6", "paddleocr", "yolo_opencv_detector", "PIL", "logger", "data_structures", "help_dialog", "unittest", "pytest", "result_view", "queue", "main_window_v2", "packaging", "ultralytics", "coordinate_mapper", "settings_dialog", "concurrent", "gui", "pytesseract", "mss", "config_manager", "cv2", "performance_monitor", "template_capture_dialog_v2", "gzip", "utils", "build", "multi_monitor", "numpy", "tarfile", "source_code_dialog", "win32api", "flake8", "requests", "zipfile", "core", "screen_capture_v2", "yolo_detector_v2", "locale", "ctypes", "win32gui", "setuptools", "template_matcher_v2", "enum", "dataclasses", "widgets", "mypy", "platform", "screenshot_service", "importlib", "yaml", "difflib", "dialogs", "automation_executor"], "import_usage": {"sys": ["src\\yolo_opencv_detector\\main_enhanced.py", "src\\yolo_opencv_detector\\main_v2.py", "src\\yolo_opencv_detector\\core\\gpu_accelerator.py", "src\\yolo_opencv_detector\\core\\memory_optimizer.py", "src\\yolo_opencv_detector\\core\\screen_capture_v2.py", "src\\yolo_opencv_detector\\gui\\main_window.py", "src\\yolo_opencv_detector\\gui\\main_window_v2.py", "src\\yolo_opencv_detector\\gui\\dialogs\\env_config.py", "src\\yolo_opencv_detector\\gui\\dialogs\\source_code_dialog.py", "src\\yolo_opencv_detector\\gui\\dialogs\\user_manual_dialog.py", "src\\yolo_opencv_detector\\gui\\widgets\\examples_panel.py", "src\\yolo_opencv_detector\\utils\\encoding_utils.py", "src\\yolo_opencv_detector\\utils\\logger.py", "src\\yolo_opencv_detector\\utils\\updater.py", "analyze_dependencies.py", "demo_automation.py", "deploy.py", "download_yolo_models.py", "integrate_app_icons.py", "run_tests.py", "test_app_icons.py", "test_taskbar_icon.py", "scripts\\build_executable.py", "scripts\\build_release.py", "scripts\\download_models.py", "scripts\\final_check.py", "scripts\\fix_imports.py", "scripts\\mvp_test.py", "scripts\\quick_test.py", "scripts\\verify_environment.py", "tests\\conftest.py", "tests\\test_integration.py", "tests\\test_performance.py", "examples\\game_automation.py", "examples\\ui_testing.py"], "os": ["src\\yolo_opencv_detector\\main_enhanced.py", "src\\yolo_opencv_detector\\main_v2.py", "src\\yolo_opencv_detector\\core\\gpu_accelerator.py", "src\\yolo_opencv_detector\\core\\smart_detection_manager.py", "src\\yolo_opencv_detector\\gui\\dialogs\\env_config.py", "src\\yolo_opencv_detector\\gui\\dialogs\\source_code_dialog.py", "src\\yolo_opencv_detector\\gui\\widgets\\detection_statistics_widget.py", "src\\yolo_opencv_detector\\gui\\widgets\\screenshot_widget.py", "src\\yolo_opencv_detector\\utils\\encoding_utils.py", "src\\yolo_opencv_detector\\utils\\model_downloader.py", "src\\yolo_opencv_detector\\utils\\updater.py", "analyze_dependencies.py", "create_app_icons.py", "deploy.py", "integrate_app_icons.py", "run_tests.py", "setup.py", "test_app_icons.py", "scripts\\build_executable.py", "scripts\\build_release.py", "scripts\\download_models.py", "tests\\conftest.py"], "pathlib": ["src\\yolo_opencv_detector\\main_enhanced.py", "src\\yolo_opencv_detector\\main_v2.py", "src\\yolo_opencv_detector\\automation\\template_manager.py", "src\\yolo_opencv_detector\\config\\floating_detection_config.py", "src\\yolo_opencv_detector\\core\\gpu_accelerator.py", "src\\yolo_opencv_detector\\core\\recorder.py", "src\\yolo_opencv_detector\\core\\result_processor.py", "src\\yolo_opencv_detector\\core\\screenshot_cache.py", "src\\yolo_opencv_detector\\core\\screen_capture.py", "src\\yolo_opencv_detector\\core\\screen_capture_v2.py", "src\\yolo_opencv_detector\\core\\script_generator.py", "src\\yolo_opencv_detector\\core\\smart_detection_manager.py", "src\\yolo_opencv_detector\\core\\template_matcher_v2.py", "src\\yolo_opencv_detector\\core\\yolo_detector.py", "src\\yolo_opencv_detector\\core\\yolo_detector_v2.py", "src\\yolo_opencv_detector\\gui\\main_window.py", "src\\yolo_opencv_detector\\gui\\main_window_v2.py", "src\\yolo_opencv_detector\\gui\\main_window_v2.py", "src\\yolo_opencv_detector\\gui\\main_window_v2.py", "src\\yolo_opencv_detector\\gui\\region_capture_dialog.py", "src\\yolo_opencv_detector\\gui\\screenshot_dialog_v2.py", "src\\yolo_opencv_detector\\gui\\screenshot_preview_dialog.py", "src\\yolo_opencv_detector\\gui\\simple_capture_dialog.py", "src\\yolo_opencv_detector\\gui\\template_capture_dialog_v2.py", "src\\yolo_opencv_detector\\gui\\dialogs\\env_config.py", "src\\yolo_opencv_detector\\gui\\dialogs\\source_code_dialog.py", "src\\yolo_opencv_detector\\gui\\dialogs\\user_manual_dialog.py", "src\\yolo_opencv_detector\\gui\\widgets\\automation_panel.py", "src\\yolo_opencv_detector\\gui\\widgets\\config_panel_v2.py", "src\\yolo_opencv_detector\\gui\\widgets\\detection_panel_v2.py", "src\\yolo_opencv_detector\\gui\\widgets\\examples_panel.py", "src\\yolo_opencv_detector\\gui\\widgets\\screenshot_widget.py", "src\\yolo_opencv_detector\\gui\\widgets\\screenshot_widget.py", "src\\yolo_opencv_detector\\gui\\widgets\\template_panel_v2.py", "src\\yolo_opencv_detector\\gui\\widgets\\template_panel_v2.py", "src\\yolo_opencv_detector\\gui\\windows\\automation_window.py", "src\\yolo_opencv_detector\\gui\\windows\\automation_window_optimized.py", "src\\yolo_opencv_detector\\utils\\config_manager.py", "src\\yolo_opencv_detector\\utils\\constants.py", "src\\yolo_opencv_detector\\utils\\data_structures.py", "src\\yolo_opencv_detector\\utils\\encoding_utils.py", "src\\yolo_opencv_detector\\utils\\feedback_collector.py", "src\\yolo_opencv_detector\\utils\\logger.py", "src\\yolo_opencv_detector\\utils\\model_downloader.py", "src\\yolo_opencv_detector\\utils\\screenshot_helper.py", "src\\yolo_opencv_detector\\utils\\updater.py", "analyze_dependencies.py", "demo_automation.py", "deploy.py", "download_yolo_models.py", "integrate_app_icons.py", "run_tests.py", "test_app_icons.py", "test_taskbar_icon.py", "scripts\\build_executable.py", "scripts\\build_release.py", "scripts\\download_models.py", "scripts\\final_check.py", "scripts\\fix_imports.py", "scripts\\mvp_test.py", "scripts\\quick_test.py", "scripts\\verify_environment.py", "tests\\conftest.py", "tests\\test_integration.py", "tests\\test_performance.py", "tests\\test_screen_capture.py", "tests\\test_template_manager.py", "tests\\test_template_matcher.py", "tests\\test_yolo_detector.py", "examples\\game_automation.py", "examples\\ui_testing.py"], "PyQt6": ["src\\yolo_opencv_detector\\main_enhanced.py", "src\\yolo_opencv_detector\\main_enhanced.py", "src\\yolo_opencv_detector\\main_enhanced.py", "src\\yolo_opencv_detector\\main_enhanced.py", "src\\yolo_opencv_detector\\main_v2.py", "src\\yolo_opencv_detector\\main_v2.py", "src\\yolo_opencv_detector\\main_v2.py", "src\\yolo_opencv_detector\\main_v2.py", "src\\yolo_opencv_detector\\gui\\capture_panel.py", "src\\yolo_opencv_detector\\gui\\capture_panel.py", "src\\yolo_opencv_detector\\gui\\capture_panel.py", "src\\yolo_opencv_detector\\gui\\help_dialog.py", "src\\yolo_opencv_detector\\gui\\help_dialog.py", "src\\yolo_opencv_detector\\gui\\help_dialog.py", "src\\yolo_opencv_detector\\gui\\main_window.py", "src\\yolo_opencv_detector\\gui\\main_window.py", "src\\yolo_opencv_detector\\gui\\main_window.py", "src\\yolo_opencv_detector\\gui\\main_window_v2.py", "src\\yolo_opencv_detector\\gui\\main_window_v2.py", "src\\yolo_opencv_detector\\gui\\main_window_v2.py", "src\\yolo_opencv_detector\\gui\\main_window_v2.py", "src\\yolo_opencv_detector\\gui\\main_window_v2.py", "src\\yolo_opencv_detector\\gui\\main_window_v2.py", "src\\yolo_opencv_detector\\gui\\region_capture_dialog.py", "src\\yolo_opencv_detector\\gui\\region_capture_dialog.py", "src\\yolo_opencv_detector\\gui\\region_capture_dialog.py", "src\\yolo_opencv_detector\\gui\\result_view.py", "src\\yolo_opencv_detector\\gui\\result_view.py", "src\\yolo_opencv_detector\\gui\\result_view.py", "src\\yolo_opencv_detector\\gui\\result_view.py", "src\\yolo_opencv_detector\\gui\\screenshot_dialog_v2.py", "src\\yolo_opencv_detector\\gui\\screenshot_dialog_v2.py", "src\\yolo_opencv_detector\\gui\\screenshot_dialog_v2.py", "src\\yolo_opencv_detector\\gui\\screenshot_dialog_v2.py", "src\\yolo_opencv_detector\\gui\\screenshot_preview_dialog.py", "src\\yolo_opencv_detector\\gui\\screenshot_preview_dialog.py", "src\\yolo_opencv_detector\\gui\\screenshot_preview_dialog.py", "src\\yolo_opencv_detector\\gui\\screenshot_preview_dialog.py", "src\\yolo_opencv_detector\\gui\\screenshot_preview_dialog.py", "src\\yolo_opencv_detector\\gui\\screen_capture_overlay_v2.py", "src\\yolo_opencv_detector\\gui\\screen_capture_overlay_v2.py", "src\\yolo_opencv_detector\\gui\\screen_capture_overlay_v2.py", "src\\yolo_opencv_detector\\gui\\settings_dialog.py", "src\\yolo_opencv_detector\\gui\\settings_dialog.py", "src\\yolo_opencv_detector\\gui\\settings_dialog.py", "src\\yolo_opencv_detector\\gui\\simple_capture_dialog.py", "src\\yolo_opencv_detector\\gui\\simple_capture_dialog.py", "src\\yolo_opencv_detector\\gui\\simple_capture_dialog.py", "src\\yolo_opencv_detector\\gui\\template_capture_dialog_v2.py", "src\\yolo_opencv_detector\\gui\\template_capture_dialog_v2.py", "src\\yolo_opencv_detector\\gui\\template_capture_dialog_v2.py", "src\\yolo_opencv_detector\\gui\\dialogs\\operation_preview_dialog.py", "src\\yolo_opencv_detector\\gui\\dialogs\\operation_preview_dialog.py", "src\\yolo_opencv_detector\\gui\\dialogs\\operation_preview_dialog.py", "src\\yolo_opencv_detector\\gui\\dialogs\\source_code_dialog.py", "src\\yolo_opencv_detector\\gui\\dialogs\\source_code_dialog.py", "src\\yolo_opencv_detector\\gui\\dialogs\\source_code_dialog.py", "src\\yolo_opencv_detector\\gui\\dialogs\\source_code_dialog.py", "src\\yolo_opencv_detector\\gui\\dialogs\\source_code_dialog.py", "src\\yolo_opencv_detector\\gui\\dialogs\\user_manual_dialog.py", "src\\yolo_opencv_detector\\gui\\dialogs\\user_manual_dialog.py", "src\\yolo_opencv_detector\\gui\\dialogs\\user_manual_dialog.py", "src\\yolo_opencv_detector\\gui\\utils\\screen_adapter.py", "src\\yolo_opencv_detector\\gui\\utils\\screen_adapter.py", "src\\yolo_opencv_detector\\gui\\utils\\screen_adapter.py", "src\\yolo_opencv_detector\\gui\\widgets\\automation_panel.py", "src\\yolo_opencv_detector\\gui\\widgets\\automation_panel.py", "src\\yolo_opencv_detector\\gui\\widgets\\automation_panel.py", "src\\yolo_opencv_detector\\gui\\widgets\\config_panel.py", "src\\yolo_opencv_detector\\gui\\widgets\\config_panel.py", "src\\yolo_opencv_detector\\gui\\widgets\\config_panel_v2.py", "src\\yolo_opencv_detector\\gui\\widgets\\config_panel_v2.py", "src\\yolo_opencv_detector\\gui\\widgets\\config_panel_v2.py", "src\\yolo_opencv_detector\\gui\\widgets\\detection_panel.py", "src\\yolo_opencv_detector\\gui\\widgets\\detection_panel.py", "src\\yolo_opencv_detector\\gui\\widgets\\detection_panel.py", "src\\yolo_opencv_detector\\gui\\widgets\\detection_panel_v2.py", "src\\yolo_opencv_detector\\gui\\widgets\\detection_panel_v2.py", "src\\yolo_opencv_detector\\gui\\widgets\\detection_panel_v2.py", "src\\yolo_opencv_detector\\gui\\widgets\\detection_panel_v2.py", "src\\yolo_opencv_detector\\gui\\widgets\\detection_statistics_widget.py", "src\\yolo_opencv_detector\\gui\\widgets\\detection_statistics_widget.py", "src\\yolo_opencv_detector\\gui\\widgets\\detection_statistics_widget.py", "src\\yolo_opencv_detector\\gui\\widgets\\detection_visualizer.py", "src\\yolo_opencv_detector\\gui\\widgets\\detection_visualizer.py", "src\\yolo_opencv_detector\\gui\\widgets\\detection_visualizer.py", "src\\yolo_opencv_detector\\gui\\widgets\\examples_panel.py", "src\\yolo_opencv_detector\\gui\\widgets\\examples_panel.py", "src\\yolo_opencv_detector\\gui\\widgets\\examples_panel.py", "src\\yolo_opencv_detector\\gui\\widgets\\floating_detection_widget.py", "src\\yolo_opencv_detector\\gui\\widgets\\floating_detection_widget.py", "src\\yolo_opencv_detector\\gui\\widgets\\floating_detection_widget.py", "src\\yolo_opencv_detector\\gui\\widgets\\floating_detection_widget.py", "src\\yolo_opencv_detector\\gui\\widgets\\interactive_image_viewer.py", "src\\yolo_opencv_detector\\gui\\widgets\\interactive_image_viewer.py", "src\\yolo_opencv_detector\\gui\\widgets\\interactive_image_viewer.py", "src\\yolo_opencv_detector\\gui\\widgets\\result_panel.py", "src\\yolo_opencv_detector\\gui\\widgets\\result_panel.py", "src\\yolo_opencv_detector\\gui\\widgets\\result_panel.py", "src\\yolo_opencv_detector\\gui\\widgets\\result_panel.py", "src\\yolo_opencv_detector\\gui\\widgets\\screenshot_service.py", "src\\yolo_opencv_detector\\gui\\widgets\\screenshot_service.py", "src\\yolo_opencv_detector\\gui\\widgets\\screenshot_widget.py", "src\\yolo_opencv_detector\\gui\\widgets\\screenshot_widget.py", "src\\yolo_opencv_detector\\gui\\widgets\\screenshot_widget.py", "src\\yolo_opencv_detector\\gui\\widgets\\screen_overlay_widget.py", "src\\yolo_opencv_detector\\gui\\widgets\\screen_overlay_widget.py", "src\\yolo_opencv_detector\\gui\\widgets\\screen_overlay_widget.py", "src\\yolo_opencv_detector\\gui\\widgets\\status_panel.py", "src\\yolo_opencv_detector\\gui\\widgets\\status_panel.py", "src\\yolo_opencv_detector\\gui\\widgets\\status_panel.py", "src\\yolo_opencv_detector\\gui\\widgets\\template_panel_v2.py", "src\\yolo_opencv_detector\\gui\\widgets\\template_panel_v2.py", "src\\yolo_opencv_detector\\gui\\widgets\\template_panel_v2.py", "src\\yolo_opencv_detector\\gui\\widgets\\template_panel_v2.py", "src\\yolo_opencv_detector\\gui\\windows\\automation_window.py", "src\\yolo_opencv_detector\\gui\\windows\\automation_window.py", "src\\yolo_opencv_detector\\gui\\windows\\automation_window.py", "src\\yolo_opencv_detector\\gui\\windows\\automation_window_optimized.py", "src\\yolo_opencv_detector\\gui\\windows\\automation_window_optimized.py", "src\\yolo_opencv_detector\\gui\\windows\\automation_window_optimized.py", "src\\yolo_opencv_detector\\utils\\screenshot_helper.py", "test_taskbar_icon.py", "test_taskbar_icon.py", "test_taskbar_icon.py", "scripts\\mvp_test.py", "scripts\\quick_test.py", "scripts\\quick_test.py"], "yolo_opencv_detector": ["src\\yolo_opencv_detector\\main_enhanced.py", "src\\yolo_opencv_detector\\main_enhanced.py", "src\\yolo_opencv_detector\\main_enhanced.py", "src\\yolo_opencv_detector\\main_v2.py", "src\\yolo_opencv_detector\\main_v2.py", "src\\yolo_opencv_detector\\main_v2.py", "download_yolo_models.py", "download_yolo_models.py", "scripts\\mvp_test.py", "scripts\\mvp_test.py", "scripts\\mvp_test.py", "scripts\\mvp_test.py", "scripts\\mvp_test.py", "scripts\\mvp_test.py", "scripts\\mvp_test.py", "scripts\\mvp_test.py", "scripts\\mvp_test.py", "scripts\\quick_test.py", "scripts\\quick_test.py", "scripts\\quick_test.py", "scripts\\quick_test.py", "scripts\\quick_test.py", "scripts\\quick_test.py", "scripts\\quick_test.py", "scripts\\quick_test.py", "scripts\\quick_test.py", "scripts\\quick_test.py", "scripts\\quick_test.py", "scripts\\quick_test.py", "scripts\\quick_test.py", "scripts\\quick_test.py", "scripts\\quick_test.py", "tests\\test_coordinate_mapper.py", "tests\\test_coordinate_mapper.py", "tests\\test_coordinate_mapper.py", "tests\\test_fusion_engine.py", "tests\\test_fusion_engine.py", "tests\\test_integration.py", "tests\\test_integration.py", "tests\\test_integration.py", "tests\\test_integration.py", "tests\\test_integration.py", "tests\\test_integration.py", "tests\\test_integration.py", "tests\\test_integration.py", "tests\\test_performance.py", "tests\\test_performance.py", "tests\\test_performance.py", "tests\\test_performance.py", "tests\\test_performance.py", "tests\\test_performance.py", "tests\\test_screen_capture.py", "tests\\test_screen_capture.py", "tests\\test_template_manager.py", "tests\\test_template_manager.py", "tests\\test_template_matcher.py", "tests\\test_template_matcher.py", "tests\\test_template_matcher.py", "tests\\test_yolo_detector.py", "tests\\test_yolo_detector.py", "examples\\game_automation.py", "examples\\game_automation.py", "examples\\game_automation.py", "examples\\ui_testing.py", "examples\\ui_testing.py", "examples\\ui_testing.py"], "ctypes": ["src\\yolo_opencv_detector\\main_enhanced.py", "src\\yolo_opencv_detector\\main_v2.py", "src\\yolo_opencv_detector\\main_v2.py", "test_taskbar_icon.py"], "traceback": ["src\\yolo_opencv_detector\\main_enhanced.py", "src\\yolo_opencv_detector\\main_v2.py", "create_app_icons.py", "demo_automation.py", "download_yolo_models.py", "integrate_app_icons.py", "test_taskbar_icon.py", "scripts\\mvp_test.py"], "core": ["src\\yolo_opencv_detector\\__init__.py", "src\\yolo_opencv_detector\\gui\\template_capture_dialog_v2.py", "src\\yolo_opencv_detector\\gui\\widgets\\detection_panel_v2.py", "src\\yolo_opencv_detector\\gui\\widgets\\detection_panel_v2.py", "src\\yolo_opencv_detector\\gui\\widgets\\detection_panel_v2.py", "src\\yolo_opencv_detector\\utils\\screenshot_helper.py"], "utils": ["src\\yolo_opencv_detector\\__init__.py", "src\\yolo_opencv_detector\\core\\confidence_fusion.py", "src\\yolo_opencv_detector\\core\\confidence_fusion.py", "src\\yolo_opencv_detector\\core\\coordinate_mapper.py", "src\\yolo_opencv_detector\\core\\coordinate_mapper.py", "src\\yolo_opencv_detector\\core\\fusion_engine.py", "src\\yolo_opencv_detector\\core\\fusion_engine.py", "src\\yolo_opencv_detector\\core\\fusion_engine.py", "src\\yolo_opencv_detector\\core\\gpu_accelerator.py", "src\\yolo_opencv_detector\\core\\memory_optimizer.py", "src\\yolo_opencv_detector\\core\\multi_monitor.py", "src\\yolo_opencv_detector\\core\\multi_monitor.py", "src\\yolo_opencv_detector\\core\\nms_algorithms.py", "src\\yolo_opencv_detector\\core\\nms_algorithms.py", "src\\yolo_opencv_detector\\core\\ocr_engine.py", "src\\yolo_opencv_detector\\core\\ocr_engine.py", "src\\yolo_opencv_detector\\core\\ocr_engine.py", "src\\yolo_opencv_detector\\core\\parallel_detector.py", "src\\yolo_opencv_detector\\core\\parallel_detector.py", "src\\yolo_opencv_detector\\core\\parallel_detector.py", "src\\yolo_opencv_detector\\core\\recorder.py", "src\\yolo_opencv_detector\\core\\recorder.py", "src\\yolo_opencv_detector\\core\\recorder.py", "src\\yolo_opencv_detector\\core\\result_processor.py", "src\\yolo_opencv_detector\\core\\result_processor.py", "src\\yolo_opencv_detector\\core\\screenshot_cache.py", "src\\yolo_opencv_detector\\core\\screen_capture.py", "src\\yolo_opencv_detector\\core\\screen_capture.py", "src\\yolo_opencv_detector\\core\\screen_capture.py", "src\\yolo_opencv_detector\\core\\screen_capture_v2.py", "src\\yolo_opencv_detector\\core\\script_generator.py", "src\\yolo_opencv_detector\\core\\script_generator.py", "src\\yolo_opencv_detector\\core\\smart_detection_manager.py", "src\\yolo_opencv_detector\\core\\template_matcher_v2.py", "src\\yolo_opencv_detector\\core\\template_matcher_v2.py", "src\\yolo_opencv_detector\\core\\yolo_detector.py", "src\\yolo_opencv_detector\\core\\yolo_detector.py", "src\\yolo_opencv_detector\\core\\yolo_detector.py", "src\\yolo_opencv_detector\\core\\yolo_detector_v2.py", "src\\yolo_opencv_detector\\core\\yolo_detector_v2.py", "src\\yolo_opencv_detector\\gui\\capture_panel.py", "src\\yolo_opencv_detector\\gui\\help_dialog.py", "src\\yolo_opencv_detector\\gui\\main_window.py", "src\\yolo_opencv_detector\\gui\\main_window.py", "src\\yolo_opencv_detector\\gui\\main_window.py", "src\\yolo_opencv_detector\\gui\\main_window_v2.py", "src\\yolo_opencv_detector\\gui\\main_window_v2.py", "src\\yolo_opencv_detector\\gui\\main_window_v2.py", "src\\yolo_opencv_detector\\gui\\main_window_v2.py", "src\\yolo_opencv_detector\\gui\\main_window_v2.py", "src\\yolo_opencv_detector\\gui\\main_window_v2.py", "src\\yolo_opencv_detector\\gui\\region_capture_dialog.py", "src\\yolo_opencv_detector\\gui\\result_view.py", "src\\yolo_opencv_detector\\gui\\result_view.py", "src\\yolo_opencv_detector\\gui\\screenshot_dialog_v2.py", "src\\yolo_opencv_detector\\gui\\screenshot_dialog_v2.py", "src\\yolo_opencv_detector\\gui\\screenshot_dialog_v2.py", "src\\yolo_opencv_detector\\gui\\screenshot_preview_dialog.py", "src\\yolo_opencv_detector\\gui\\screen_capture_overlay_v2.py", "src\\yolo_opencv_detector\\gui\\screen_capture_overlay_v2.py", "src\\yolo_opencv_detector\\gui\\settings_dialog.py", "src\\yolo_opencv_detector\\gui\\simple_capture_dialog.py", "src\\yolo_opencv_detector\\gui\\template_capture_dialog_v2.py", "src\\yolo_opencv_detector\\gui\\widgets\\config_panel.py", "src\\yolo_opencv_detector\\gui\\widgets\\config_panel.py", "src\\yolo_opencv_detector\\gui\\widgets\\config_panel_v2.py", "src\\yolo_opencv_detector\\gui\\widgets\\config_panel_v2.py", "src\\yolo_opencv_detector\\gui\\widgets\\detection_panel.py", "src\\yolo_opencv_detector\\gui\\widgets\\detection_panel.py", "src\\yolo_opencv_detector\\gui\\widgets\\detection_panel_v2.py", "src\\yolo_opencv_detector\\gui\\widgets\\detection_panel_v2.py", "src\\yolo_opencv_detector\\gui\\widgets\\detection_panel_v2.py", "src\\yolo_opencv_detector\\gui\\widgets\\detection_panel_v2.py", "src\\yolo_opencv_detector\\gui\\widgets\\detection_panel_v2.py", "src\\yolo_opencv_detector\\gui\\widgets\\detection_panel_v2.py", "src\\yolo_opencv_detector\\gui\\widgets\\detection_statistics_widget.py", "src\\yolo_opencv_detector\\gui\\widgets\\floating_detection_widget.py", "src\\yolo_opencv_detector\\gui\\widgets\\interactive_image_viewer.py", "src\\yolo_opencv_detector\\gui\\widgets\\result_panel.py", "src\\yolo_opencv_detector\\gui\\widgets\\result_panel.py", "src\\yolo_opencv_detector\\gui\\widgets\\screenshot_service.py", "src\\yolo_opencv_detector\\gui\\widgets\\screenshot_service.py", "src\\yolo_opencv_detector\\gui\\widgets\\screenshot_widget.py", "src\\yolo_opencv_detector\\gui\\widgets\\screen_overlay_widget.py", "src\\yolo_opencv_detector\\gui\\widgets\\status_panel.py", "src\\yolo_opencv_detector\\gui\\widgets\\status_panel.py", "src\\yolo_opencv_detector\\gui\\widgets\\template_panel_v2.py", "src\\yolo_opencv_detector\\gui\\widgets\\template_panel_v2.py", "src\\yolo_opencv_detector\\gui\\windows\\automation_window_optimized.py", "src\\yolo_opencv_detector\\gui\\windows\\automation_window_optimized.py"], "gui": ["src\\yolo_opencv_detector\\__init__.py"], "time": ["src\\yolo_opencv_detector\\automation\\automation_executor.py", "src\\yolo_opencv_detector\\automation\\template_manager.py", "src\\yolo_opencv_detector\\core\\confidence_fusion.py", "src\\yolo_opencv_detector\\core\\coordinate_mapper.py", "src\\yolo_opencv_detector\\core\\fusion_engine.py", "src\\yolo_opencv_detector\\core\\gpu_accelerator.py", "src\\yolo_opencv_detector\\core\\memory_optimizer.py", "src\\yolo_opencv_detector\\core\\multi_monitor.py", "src\\yolo_opencv_detector\\core\\ocr_engine.py", "src\\yolo_opencv_detector\\core\\parallel_detector.py", "src\\yolo_opencv_detector\\core\\recorder.py", "src\\yolo_opencv_detector\\core\\screenshot_cache.py", "src\\yolo_opencv_detector\\core\\screen_capture.py", "src\\yolo_opencv_detector\\core\\screen_capture_v2.py", "src\\yolo_opencv_detector\\core\\script_generator.py", "src\\yolo_opencv_detector\\core\\smart_detection_manager.py", "src\\yolo_opencv_detector\\core\\template_matcher_v2.py", "src\\yolo_opencv_detector\\core\\yolo_detector.py", "src\\yolo_opencv_detector\\core\\yolo_detector_v2.py", "src\\yolo_opencv_detector\\gui\\region_capture_dialog.py", "src\\yolo_opencv_detector\\gui\\screenshot_dialog_v2.py", "src\\yolo_opencv_detector\\gui\\screenshot_preview_dialog.py", "src\\yolo_opencv_detector\\gui\\simple_capture_dialog.py", "src\\yolo_opencv_detector\\gui\\template_capture_dialog_v2.py", "src\\yolo_opencv_detector\\gui\\dialogs\\source_code_dialog.py", "src\\yolo_opencv_detector\\gui\\widgets\\detection_panel_v2.py", "src\\yolo_opencv_detector\\gui\\widgets\\interactive_image_viewer.py", "src\\yolo_opencv_detector\\gui\\widgets\\status_panel.py", "src\\yolo_opencv_detector\\gui\\windows\\automation_window_optimized.py", "src\\yolo_opencv_detector\\utils\\data_structures.py", "src\\yolo_opencv_detector\\utils\\feedback_collector.py", "src\\yolo_opencv_detector\\utils\\logger.py", "src\\yolo_opencv_detector\\utils\\performance_monitor.py", "src\\yolo_opencv_detector\\utils\\screenshot_helper.py", "src\\yolo_opencv_detector\\utils\\updater.py", "demo_automation.py", "deploy.py", "run_tests.py", "scripts\\build_release.py", "scripts\\mvp_test.py", "scripts\\quick_test.py", "tests\\test_fusion_engine.py", "tests\\test_integration.py", "tests\\test_performance.py", "tests\\test_screen_capture.py", "examples\\game_automation.py", "examples\\ui_testing.py"], "pyautogui": ["src\\yolo_opencv_detector\\automation\\automation_executor.py", "src\\yolo_opencv_detector\\core\\screen_capture_v2.py", "src\\yolo_opencv_detector\\core\\screen_capture_v2.py", "src\\yolo_opencv_detector\\core\\screen_capture_v2.py", "examples\\game_automation.py"], "typing": ["src\\yolo_opencv_detector\\automation\\automation_executor.py", "src\\yolo_opencv_detector\\automation\\detection_processor.py", "src\\yolo_opencv_detector\\automation\\template_manager.py", "src\\yolo_opencv_detector\\config\\floating_detection_config.py", "src\\yolo_opencv_detector\\core\\confidence_fusion.py", "src\\yolo_opencv_detector\\core\\coordinate_mapper.py", "src\\yolo_opencv_detector\\core\\fusion_engine.py", "src\\yolo_opencv_detector\\core\\gpu_accelerator.py", "src\\yolo_opencv_detector\\core\\memory_optimizer.py", "src\\yolo_opencv_detector\\core\\multi_monitor.py", "src\\yolo_opencv_detector\\core\\nms_algorithms.py", "src\\yolo_opencv_detector\\core\\ocr_engine.py", "src\\yolo_opencv_detector\\core\\parallel_detector.py", "src\\yolo_opencv_detector\\core\\recorder.py", "src\\yolo_opencv_detector\\core\\result_processor.py", "src\\yolo_opencv_detector\\core\\screenshot_cache.py", "src\\yolo_opencv_detector\\core\\screen_capture.py", "src\\yolo_opencv_detector\\core\\screen_capture_v2.py", "src\\yolo_opencv_detector\\core\\script_generator.py", "src\\yolo_opencv_detector\\core\\smart_detection_manager.py", "src\\yolo_opencv_detector\\core\\template_matcher_v2.py", "src\\yolo_opencv_detector\\core\\yolo_detector.py", "src\\yolo_opencv_detector\\core\\yolo_detector_v2.py", "src\\yolo_opencv_detector\\gui\\main_window.py", "src\\yolo_opencv_detector\\gui\\main_window_v2.py", "src\\yolo_opencv_detector\\gui\\result_view.py", "src\\yolo_opencv_detector\\gui\\screenshot_dialog_v2.py", "src\\yolo_opencv_detector\\gui\\screenshot_preview_dialog.py", "src\\yolo_opencv_detector\\gui\\settings_dialog.py", "src\\yolo_opencv_detector\\gui\\template_capture_dialog_v2.py", "src\\yolo_opencv_detector\\gui\\dialogs\\operation_preview_dialog.py", "src\\yolo_opencv_detector\\gui\\utils\\screen_adapter.py", "src\\yolo_opencv_detector\\gui\\widgets\\automation_panel.py", "src\\yolo_opencv_detector\\gui\\widgets\\config_panel.py", "src\\yolo_opencv_detector\\gui\\widgets\\config_panel_v2.py", "src\\yolo_opencv_detector\\gui\\widgets\\detection_panel.py", "src\\yolo_opencv_detector\\gui\\widgets\\detection_panel_v2.py", "src\\yolo_opencv_detector\\gui\\widgets\\detection_statistics_widget.py", "src\\yolo_opencv_detector\\gui\\widgets\\detection_visualizer.py", "src\\yolo_opencv_detector\\gui\\widgets\\floating_detection_widget.py", "src\\yolo_opencv_detector\\gui\\widgets\\interactive_image_viewer.py", "src\\yolo_opencv_detector\\gui\\widgets\\result_panel.py", "src\\yolo_opencv_detector\\gui\\widgets\\screenshot_service.py", "src\\yolo_opencv_detector\\gui\\widgets\\screenshot_widget.py", "src\\yolo_opencv_detector\\gui\\widgets\\screen_overlay_widget.py", "src\\yolo_opencv_detector\\gui\\widgets\\status_panel.py", "src\\yolo_opencv_detector\\gui\\widgets\\template_panel_v2.py", "src\\yolo_opencv_detector\\gui\\windows\\automation_window.py", "src\\yolo_opencv_detector\\gui\\windows\\automation_window_optimized.py", "src\\yolo_opencv_detector\\utils\\config_manager.py", "src\\yolo_opencv_detector\\utils\\data_structures.py", "src\\yolo_opencv_detector\\utils\\encoding_utils.py", "src\\yolo_opencv_detector\\utils\\feedback_collector.py", "src\\yolo_opencv_detector\\utils\\logger.py", "src\\yolo_opencv_detector\\utils\\model_downloader.py", "src\\yolo_opencv_detector\\utils\\performance_monitor.py", "src\\yolo_opencv_detector\\utils\\screenshot_helper.py", "src\\yolo_opencv_detector\\utils\\updater.py", "analyze_dependencies.py", "scripts\\download_models.py", "examples\\ui_testing.py"], "dataclasses": ["src\\yolo_opencv_detector\\automation\\automation_executor.py", "src\\yolo_opencv_detector\\automation\\detection_processor.py", "src\\yolo_opencv_detector\\automation\\template_manager.py", "src\\yolo_opencv_detector\\config\\floating_detection_config.py", "src\\yolo_opencv_detector\\core\\recorder.py", "src\\yolo_opencv_detector\\core\\script_generator.py", "src\\yolo_opencv_detector\\core\\smart_detection_manager.py", "src\\yolo_opencv_detector\\gui\\utils\\screen_adapter.py", "src\\yolo_opencv_detector\\gui\\windows\\automation_window_optimized.py", "src\\yolo_opencv_detector\\utils\\config_manager.py", "src\\yolo_opencv_detector\\utils\\data_structures.py", "src\\yolo_opencv_detector\\utils\\feedback_collector.py", "src\\yolo_opencv_detector\\utils\\performance_monitor.py"], "enum": ["src\\yolo_opencv_detector\\automation\\automation_executor.py", "src\\yolo_opencv_detector\\automation\\detection_processor.py", "src\\yolo_opencv_detector\\core\\confidence_fusion.py", "src\\yolo_opencv_detector\\core\\nms_algorithms.py", "src\\yolo_opencv_detector\\gui\\utils\\screen_adapter.py", "src\\yolo_opencv_detector\\gui\\widgets\\screenshot_service.py", "src\\yolo_opencv_detector\\utils\\constants.py", "src\\yolo_opencv_detector\\utils\\data_structures.py"], "numpy": ["src\\yolo_opencv_detector\\automation\\detection_processor.py", "src\\yolo_opencv_detector\\core\\confidence_fusion.py", "src\\yolo_opencv_detector\\core\\coordinate_mapper.py", "src\\yolo_opencv_detector\\core\\fusion_engine.py", "src\\yolo_opencv_detector\\core\\gpu_accelerator.py", "src\\yolo_opencv_detector\\core\\memory_optimizer.py", "src\\yolo_opencv_detector\\core\\multi_monitor.py", "src\\yolo_opencv_detector\\core\\nms_algorithms.py", "src\\yolo_opencv_detector\\core\\ocr_engine.py", "src\\yolo_opencv_detector\\core\\parallel_detector.py", "src\\yolo_opencv_detector\\core\\recorder.py", "src\\yolo_opencv_detector\\core\\result_processor.py", "src\\yolo_opencv_detector\\core\\screenshot_cache.py", "src\\yolo_opencv_detector\\core\\screen_capture.py", "src\\yolo_opencv_detector\\core\\screen_capture_v2.py", "src\\yolo_opencv_detector\\core\\smart_detection_manager.py", "src\\yolo_opencv_detector\\core\\template_matcher_v2.py", "src\\yolo_opencv_detector\\core\\yolo_detector.py", "src\\yolo_opencv_detector\\core\\yolo_detector_v2.py", "src\\yolo_opencv_detector\\gui\\region_capture_dialog.py", "src\\yolo_opencv_detector\\gui\\screenshot_dialog_v2.py", "src\\yolo_opencv_detector\\gui\\screenshot_preview_dialog.py", "src\\yolo_opencv_detector\\gui\\screen_capture_overlay_v2.py", "src\\yolo_opencv_detector\\gui\\simple_capture_dialog.py", "src\\yolo_opencv_detector\\gui\\template_capture_dialog_v2.py", "src\\yolo_opencv_detector\\gui\\widgets\\interactive_image_viewer.py", "src\\yolo_opencv_detector\\gui\\widgets\\screenshot_service.py", "src\\yolo_opencv_detector\\utils\\screenshot_helper.py", "create_app_icons.py", "scripts\\mvp_test.py", "scripts\\quick_test.py", "tests\\test_integration.py", "tests\\test_performance.py", "tests\\test_screen_capture.py", "tests\\test_template_manager.py", "tests\\test_template_matcher.py", "tests\\test_yolo_detector.py"], "json": ["src\\yolo_opencv_detector\\automation\\template_manager.py", "src\\yolo_opencv_detector\\config\\floating_detection_config.py", "src\\yolo_opencv_detector\\config\\floating_detection_config.py", "src\\yolo_opencv_detector\\core\\recorder.py", "src\\yolo_opencv_detector\\core\\result_processor.py", "src\\yolo_opencv_detector\\core\\script_generator.py", "src\\yolo_opencv_detector\\gui\\widgets\\detection_statistics_widget.py", "src\\yolo_opencv_detector\\utils\\config_manager.py", "src\\yolo_opencv_detector\\utils\\config_manager.py", "src\\yolo_opencv_detector\\utils\\config_manager.py", "src\\yolo_opencv_detector\\utils\\data_structures.py", "src\\yolo_opencv_detector\\utils\\feedback_collector.py", "src\\yolo_opencv_detector\\utils\\performance_monitor.py", "src\\yolo_opencv_detector\\utils\\updater.py", "analyze_dependencies.py", "tests\\test_template_manager.py", "examples\\ui_testing.py"], "automation_executor": ["src\\yolo_opencv_detector\\automation\\template_manager.py"], "math": ["src\\yolo_opencv_detector\\core\\confidence_fusion.py"], "multi_monitor": ["src\\yolo_opencv_detector\\core\\coordinate_mapper.py"], "torch": ["src\\yolo_opencv_detector\\core\\gpu_accelerator.py", "src\\yolo_opencv_detector\\core\\gpu_accelerator.py", "src\\yolo_opencv_detector\\core\\gpu_accelerator.py", "src\\yolo_opencv_detector\\core\\gpu_accelerator.py", "src\\yolo_opencv_detector\\core\\gpu_accelerator.py", "src\\yolo_opencv_detector\\core\\gpu_accelerator.py", "src\\yolo_opencv_detector\\core\\gpu_accelerator.py", "src\\yolo_opencv_detector\\core\\gpu_accelerator.py", "src\\yolo_opencv_detector\\core\\gpu_accelerator.py", "src\\yolo_opencv_detector\\core\\yolo_detector.py", "src\\yolo_opencv_detector\\core\\yolo_detector_v2.py", "src\\yolo_opencv_detector\\core\\yolo_detector_v2.py", "scripts\\verify_environment.py"], "cv2": ["src\\yolo_opencv_detector\\core\\gpu_accelerator.py", "src\\yolo_opencv_detector\\core\\gpu_accelerator.py", "src\\yolo_opencv_detector\\core\\ocr_engine.py", "src\\yolo_opencv_detector\\core\\recorder.py", "src\\yolo_opencv_detector\\core\\screen_capture.py", "src\\yolo_opencv_detector\\core\\screen_capture_v2.py", "src\\yolo_opencv_detector\\core\\screen_capture_v2.py", "src\\yolo_opencv_detector\\core\\screen_capture_v2.py", "src\\yolo_opencv_detector\\core\\screen_capture_v2.py", "src\\yolo_opencv_detector\\core\\screen_capture_v2.py", "src\\yolo_opencv_detector\\core\\screen_capture_v2.py", "src\\yolo_opencv_detector\\core\\screen_capture_v2.py", "src\\yolo_opencv_detector\\core\\screen_capture_v2.py", "src\\yolo_opencv_detector\\core\\smart_detection_manager.py", "src\\yolo_opencv_detector\\core\\smart_detection_manager.py", "src\\yolo_opencv_detector\\core\\template_matcher_v2.py", "src\\yolo_opencv_detector\\core\\yolo_detector.py", "src\\yolo_opencv_detector\\gui\\region_capture_dialog.py", "src\\yolo_opencv_detector\\gui\\screenshot_dialog_v2.py", "src\\yolo_opencv_detector\\gui\\screenshot_dialog_v2.py", "src\\yolo_opencv_detector\\gui\\screenshot_preview_dialog.py", "src\\yolo_opencv_detector\\gui\\screenshot_preview_dialog.py", "src\\yolo_opencv_detector\\gui\\screen_capture_overlay_v2.py", "src\\yolo_opencv_detector\\gui\\simple_capture_dialog.py", "src\\yolo_opencv_detector\\gui\\template_capture_dialog_v2.py", "src\\yolo_opencv_detector\\gui\\template_capture_dialog_v2.py", "src\\yolo_opencv_detector\\gui\\widgets\\detection_panel_v2.py", "src\\yolo_opencv_detector\\gui\\widgets\\template_panel_v2.py", "src\\yolo_opencv_detector\\gui\\widgets\\template_panel_v2.py", "src\\yolo_opencv_detector\\gui\\widgets\\template_panel_v2.py", "src\\yolo_opencv_detector\\utils\\screenshot_helper.py", "scripts\\mvp_test.py", "scripts\\verify_environment.py", "tests\\test_template_manager.py", "tests\\test_template_matcher.py", "tests\\test_yolo_detector.py"], "gc": ["src\\yolo_opencv_detector\\core\\memory_optimizer.py", "tests\\test_integration.py"], "psutil": ["src\\yolo_opencv_detector\\core\\memory_optimizer.py", "src\\yolo_opencv_detector\\gui\\main_window_v2.py", "src\\yolo_opencv_detector\\gui\\widgets\\detection_statistics_widget.py", "src\\yolo_opencv_detector\\gui\\widgets\\status_panel.py", "src\\yolo_opencv_detector\\gui\\widgets\\status_panel.py", "src\\yolo_opencv_detector\\utils\\feedback_collector.py", "src\\yolo_opencv_detector\\utils\\performance_monitor.py", "tests\\test_integration.py", "tests\\test_performance.py"], "threading": ["src\\yolo_opencv_detector\\core\\memory_optimizer.py", "src\\yolo_opencv_detector\\core\\parallel_detector.py", "src\\yolo_opencv_detector\\core\\recorder.py", "src\\yolo_opencv_detector\\core\\screenshot_cache.py", "src\\yolo_opencv_detector\\core\\screen_capture.py", "src\\yolo_opencv_detector\\core\\yolo_detector_v2.py", "src\\yolo_opencv_detector\\utils\\feedback_collector.py", "src\\yolo_opencv_detector\\utils\\performance_monitor.py", "src\\yolo_opencv_detector\\utils\\updater.py", "tests\\test_integration.py", "tests\\test_performance.py", "tests\\test_performance.py", "tests\\test_template_manager.py"], "collections": ["src\\yolo_opencv_detector\\core\\memory_optimizer.py", "src\\yolo_opencv_detector\\core\\screenshot_cache.py", "src\\yolo_opencv_detector\\core\\screen_capture.py", "src\\yolo_opencv_detector\\utils\\performance_monitor.py", "analyze_dependencies.py"], "weakref": ["src\\yolo_opencv_detector\\core\\memory_optimizer.py"], "win32gui": ["src\\yolo_opencv_detector\\core\\multi_monitor.py", "src\\yolo_opencv_detector\\core\\screen_capture.py", "src\\yolo_opencv_detector\\core\\screen_capture.py"], "win32api": ["src\\yolo_opencv_detector\\core\\multi_monitor.py", "src\\yolo_opencv_detector\\core\\screen_capture.py"], "win32con": ["src\\yolo_opencv_detector\\core\\multi_monitor.py", "src\\yolo_opencv_detector\\core\\screen_capture.py"], "PIL": ["src\\yolo_opencv_detector\\core\\multi_monitor.py", "src\\yolo_opencv_detector\\core\\recorder.py", "src\\yolo_opencv_detector\\core\\screen_capture.py", "src\\yolo_opencv_detector\\core\\screen_capture_v2.py", "src\\yolo_opencv_detector\\core\\screen_capture_v2.py", "src\\yolo_opencv_detector\\core\\screen_capture_v2.py", "src\\yolo_opencv_detector\\core\\yolo_detector.py", "src\\yolo_opencv_detector\\gui\\region_capture_dialog.py", "src\\yolo_opencv_detector\\gui\\simple_capture_dialog.py", "create_app_icons.py", "test_app_icons.py", "tests\\test_screen_capture.py", "tests\\test_screen_capture.py", "tests\\test_template_manager.py", "tests\\test_template_matcher.py", "tests\\test_yolo_detector.py"], "re": ["src\\yolo_opencv_detector\\core\\ocr_engine.py", "src\\yolo_opencv_detector\\gui\\template_capture_dialog_v2.py", "src\\yolo_opencv_detector\\gui\\dialogs\\source_code_dialog.py", "src\\yolo_opencv_detector\\gui\\widgets\\examples_panel.py", "analyze_dependencies.py"], "easyocr": ["src\\yolo_opencv_detector\\core\\ocr_engine.py"], "paddleocr": ["src\\yolo_opencv_detector\\core\\ocr_engine.py"], "pytesseract": ["src\\yolo_opencv_detector\\core\\ocr_engine.py", "src\\yolo_opencv_detector\\core\\ocr_engine.py"], "difflib": ["src\\yolo_opencv_detector\\core\\ocr_engine.py"], "queue": ["src\\yolo_opencv_detector\\core\\parallel_detector.py", "tests\\test_integration.py", "tests\\test_performance.py"], "multiprocessing": ["src\\yolo_opencv_detector\\core\\parallel_detector.py"], "concurrent": ["src\\yolo_opencv_detector\\core\\parallel_detector.py"], "shutil": ["src\\yolo_opencv_detector\\core\\recorder.py", "src\\yolo_opencv_detector\\core\\recorder.py", "src\\yolo_opencv_detector\\utils\\updater.py", "src\\yolo_opencv_detector\\utils\\updater.py", "src\\yolo_opencv_detector\\utils\\updater.py", "src\\yolo_opencv_detector\\utils\\updater.py", "run_tests.py", "scripts\\build_executable.py", "scripts\\build_release.py"], "csv": ["src\\yolo_opencv_detector\\core\\result_processor.py"], "xml": ["src\\yolo_opencv_detector\\core\\result_processor.py"], "datetime": ["src\\yolo_opencv_detector\\core\\result_processor.py", "src\\yolo_opencv_detector\\core\\smart_detection_manager.py", "src\\yolo_opencv_detector\\gui\\widgets\\detection_panel.py", "src\\yolo_opencv_detector\\gui\\widgets\\detection_statistics_widget.py", "src\\yolo_opencv_detector\\gui\\widgets\\result_panel.py", "src\\yolo_opencv_detector\\gui\\widgets\\result_panel.py", "src\\yolo_opencv_detector\\gui\\widgets\\status_panel.py", "src\\yolo_opencv_detector\\gui\\widgets\\template_panel_v2.py", "src\\yolo_opencv_detector\\utils\\logger.py"], "hashlib": ["src\\yolo_opencv_detector\\core\\screenshot_cache.py", "src\\yolo_opencv_detector\\core\\smart_detection_manager.py", "src\\yolo_opencv_detector\\utils\\updater.py", "scripts\\download_models.py"], "pickle": ["src\\yolo_opencv_detector\\core\\screenshot_cache.py"], "gzip": ["src\\yolo_opencv_detector\\core\\screenshot_cache.py"], "mss": ["src\\yolo_opencv_detector\\core\\screen_capture.py", "src\\yolo_opencv_detector\\core\\screen_capture_v2.py", "src\\yolo_opencv_detector\\core\\screen_capture_v2.py", "src\\yolo_opencv_detector\\core\\screen_capture_v2.py", "src\\yolo_opencv_detector\\core\\screen_capture_v2.py", "src\\yolo_opencv_detector\\gui\\simple_capture_dialog.py"], "win32ui": ["src\\yolo_opencv_detector\\core\\screen_capture.py"], "tkinter": ["src\\yolo_opencv_detector\\core\\screen_capture_v2.py", "src\\yolo_opencv_detector\\core\\screen_capture_v2.py", "src\\yolo_opencv_detector\\utils\\screenshot_helper.py"], "ultralytics": ["src\\yolo_opencv_detector\\core\\yolo_detector.py", "src\\yolo_opencv_detector\\core\\yolo_detector_v2.py"], "yolo_detector_v2": ["src\\yolo_opencv_detector\\core\\__init__.py"], "template_matcher_v2": ["src\\yolo_opencv_detector\\core\\__init__.py"], "screen_capture_v2": ["src\\yolo_opencv_detector\\core\\__init__.py"], "fusion_engine": ["src\\yolo_opencv_detector\\core\\__init__.py"], "coordinate_mapper": ["src\\yolo_opencv_detector\\core\\__init__.py"], "script_generator": ["src\\yolo_opencv_detector\\core\\__init__.py"], "widgets": ["src\\yolo_opencv_detector\\gui\\main_window.py", "src\\yolo_opencv_detector\\gui\\main_window.py", "src\\yolo_opencv_detector\\gui\\main_window.py", "src\\yolo_opencv_detector\\gui\\main_window.py", "src\\yolo_opencv_detector\\gui\\main_window.py", "src\\yolo_opencv_detector\\gui\\main_window_v2.py", "src\\yolo_opencv_detector\\gui\\main_window_v2.py", "src\\yolo_opencv_detector\\gui\\main_window_v2.py", "src\\yolo_opencv_detector\\gui\\main_window_v2.py", "src\\yolo_opencv_detector\\gui\\main_window_v2.py", "src\\yolo_opencv_detector\\gui\\screenshot_preview_dialog.py", "src\\yolo_opencv_detector\\gui\\windows\\automation_window.py", "src\\yolo_opencv_detector\\gui\\windows\\automation_window.py"], "help_dialog": ["src\\yolo_opencv_detector\\gui\\main_window.py", "src\\yolo_opencv_detector\\gui\\main_window.py"], "dialogs": ["src\\yolo_opencv_detector\\gui\\main_window_v2.py", "src\\yolo_opencv_detector\\gui\\main_window_v2.py", "src\\yolo_opencv_detector\\gui\\windows\\automation_window.py"], "screenshot_preview_dialog": ["src\\yolo_opencv_detector\\gui\\main_window_v2.py", "src\\yolo_opencv_detector\\gui\\__init__.py", "src\\yolo_opencv_detector\\gui\\widgets\\screenshot_service.py"], "screen_capture_overlay_v2": ["src\\yolo_opencv_detector\\gui\\screenshot_dialog_v2.py", "src\\yolo_opencv_detector\\gui\\__init__.py"], "main_window_v2": ["src\\yolo_opencv_detector\\gui\\__init__.py"], "screenshot_dialog_v2": ["src\\yolo_opencv_detector\\gui\\__init__.py"], "template_capture_dialog_v2": ["src\\yolo_opencv_detector\\gui\\__init__.py"], "settings_dialog": ["src\\yolo_opencv_detector\\gui\\__init__.py"], "result_view": ["src\\yolo_opencv_detector\\gui\\__init__.py"], "logging": ["src\\yolo_opencv_detector\\gui\\dialogs\\operation_preview_dialog.py", "src\\yolo_opencv_detector\\gui\\utils\\screen_adapter.py", "src\\yolo_opencv_detector\\gui\\widgets\\automation_panel.py", "src\\yolo_opencv_detector\\gui\\widgets\\detection_visualizer.py", "src\\yolo_opencv_detector\\gui\\windows\\automation_window.py", "src\\yolo_opencv_detector\\gui\\windows\\automation_window_optimized.py", "src\\yolo_opencv_detector\\utils\\config_manager.py", "src\\yolo_opencv_detector\\utils\\encoding_utils.py", "src\\yolo_opencv_detector\\utils\\logger.py"], "subprocess": ["src\\yolo_opencv_detector\\gui\\dialogs\\source_code_dialog.py", "src\\yolo_opencv_detector\\gui\\widgets\\screenshot_widget.py", "src\\yolo_opencv_detector\\utils\\updater.py", "analyze_dependencies.py", "demo_automation.py", "deploy.py", "run_tests.py", "scripts\\build_executable.py", "scripts\\build_release.py", "scripts\\final_check.py", "scripts\\verify_environment.py"], "tempfile": ["src\\yolo_opencv_detector\\gui\\dialogs\\source_code_dialog.py", "src\\yolo_opencv_detector\\gui\\dialogs\\source_code_dialog.py", "tests\\test_screen_capture.py", "tests\\test_template_manager.py", "tests\\test_yolo_detector.py"], "source_code_dialog": ["src\\yolo_opencv_detector\\gui\\dialogs\\__init__.py"], "screenshot_widget": ["src\\yolo_opencv_detector\\gui\\widgets\\detection_panel.py"], "floating_detection_widget": ["src\\yolo_opencv_detector\\gui\\widgets\\detection_panel_v2.py"], "screen_overlay_widget": ["src\\yolo_opencv_detector\\gui\\widgets\\detection_panel_v2.py"], "screenshot_service": ["src\\yolo_opencv_detector\\gui\\widgets\\detection_panel_v2.py", "src\\yolo_opencv_detector\\gui\\widgets\\template_panel_v2.py"], "glob": ["src\\yolo_opencv_detector\\gui\\widgets\\screenshot_widget.py"], "platform": ["src\\yolo_opencv_detector\\gui\\widgets\\screenshot_widget.py", "src\\yolo_opencv_detector\\utils\\feedback_collector.py", "deploy.py"], "random": ["src\\yolo_opencv_detector\\gui\\widgets\\status_panel.py", "src\\yolo_opencv_detector\\gui\\widgets\\status_panel.py", "src\\yolo_opencv_detector\\gui\\windows\\automation_window_optimized.py"], "yaml": ["src\\yolo_opencv_detector\\utils\\config_manager.py"], "constants": ["src\\yolo_opencv_detector\\utils\\config_manager.py", "src\\yolo_opencv_detector\\utils\\logger.py", "src\\yolo_opencv_detector\\utils\\__init__.py"], "locale": ["src\\yolo_opencv_detector\\utils\\encoding_utils.py"], "codecs": ["src\\yolo_opencv_detector\\utils\\encoding_utils.py"], "chardet": ["src\\yolo_opencv_detector\\utils\\encoding_utils.py"], "importlib": ["src\\yolo_opencv_detector\\utils\\encoding_utils.py", "scripts\\verify_environment.py"], "uuid": ["src\\yolo_opencv_detector\\utils\\feedback_collector.py"], "requests": ["src\\yolo_opencv_detector\\utils\\feedback_collector.py", "src\\yolo_opencv_detector\\utils\\updater.py"], "logger": ["src\\yolo_opencv_detector\\utils\\feedback_collector.py", "src\\yolo_opencv_detector\\utils\\performance_monitor.py", "src\\yolo_opencv_detector\\utils\\screenshot_helper.py", "src\\yolo_opencv_detector\\utils\\updater.py", "src\\yolo_opencv_detector\\utils\\__init__.py"], "config_manager": ["src\\yolo_opencv_detector\\utils\\feedback_collector.py", "src\\yolo_opencv_detector\\utils\\updater.py", "src\\yolo_opencv_detector\\utils\\__init__.py"], "loguru": ["src\\yolo_opencv_detector\\utils\\logger.py"], "urllib": ["src\\yolo_opencv_detector\\utils\\model_downloader.py", "src\\yolo_opencv_detector\\utils\\model_downloader.py", "deploy.py", "scripts\\download_models.py"], "statistics": ["src\\yolo_opencv_detector\\utils\\performance_monitor.py"], "packaging": ["src\\yolo_opencv_detector\\utils\\updater.py"], "zipfile": ["src\\yolo_opencv_detector\\utils\\updater.py", "scripts\\build_executable.py"], "tarfile": ["src\\yolo_opencv_detector\\utils\\updater.py"], "performance_monitor": ["src\\yolo_opencv_detector\\utils\\__init__.py"], "data_structures": ["src\\yolo_opencv_detector\\utils\\__init__.py"], "ast": ["analyze_dependencies.py"], "automation_complete": ["demo_automation.py", "demo_automation.py"], "argparse": ["run_tests.py", "scripts\\build_executable.py", "scripts\\download_models.py"], "flake8": ["run_tests.py"], "mypy": ["run_tests.py"], "setuptools": ["setup.py"], "PyInstaller": ["scripts\\build_executable.py", "scripts\\build_release.py"], "build": ["scripts\\build_release.py"], "pytest": ["tests\\conftest.py", "tests\\test_coordinate_mapper.py", "tests\\test_fusion_engine.py", "tests\\test_integration.py", "tests\\test_performance.py", "tests\\test_screen_capture.py", "tests\\test_template_manager.py", "tests\\test_template_matcher.py", "tests\\test_yolo_detector.py"], "unittest": ["tests\\test_coordinate_mapper.py", "tests\\test_integration.py", "tests\\test_performance.py", "tests\\test_screen_capture.py"], "sqlite3": ["tests\\test_template_manager.py"]}}, "dependency_status": {"unused_dependencies": ["aiofiles", "asyncio  # Python内置", "pandas", "jup<PERSON><PERSON>", "sqlite3  # Python内置，无需安装", "wheel", "black", "scikit-image", "ipython", "configparser", "pynput", "scipy", "imageio"], "missing_dependencies": ["tkinter", "easyocr", "chardet", "screenshot_dialog_v2", "automation_complete", "codecs", "screenshot_preview_dialog", "script_generator", "floating_detection_widget", "screen_overlay_widget", "win32ui", "fusion_engine", "screenshot_widget", "constants", "<PERSON><PERSON><PERSON><PERSON>", "screen_capture_overlay_v2", "paddleocr", "yolo_opencv_detector", "logger", "help_dialog", "data_structures", "unittest", "result_view", "queue", "main_window_v2", "packaging", "settings_dialog", "coordinate_mapper", "concurrent", "pytesseract", "mss", "config_manager", "performance_monitor", "template_capture_dialog_v2", "gzip", "utils", "build", "multi_monitor", "tarfile", "source_code_dialog", "requests", "zipfile", "core", "screen_capture_v2", "yolo_detector_v2", "locale", "ctypes", "template_matcher_v2", "widgets", "enum", "dataclasses", "platform", "screenshot_service", "importlib", "difflib", "dialogs", "automation_executor"], "unused_count": 13, "missing_count": 57}}