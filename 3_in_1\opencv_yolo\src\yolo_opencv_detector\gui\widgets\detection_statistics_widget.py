#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检测统计信息显示组件
显示检测会话的详细统计信息
作者: Cursor AI
创建时间: 2025-07-05
编码标准: UTF-8无BOM
"""

from typing import Dict, Any, List
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, QLabel,
    QTableWidget, QTableWidgetItem, QProgressBar, QPushButton
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QPixmap, QPainter, QColor

from ...utils.logger import Logger


class DetectionStatisticsWidget(QWidget):
    """检测统计信息显示组件"""
    
    # 信号定义
    clear_statistics_requested = pyqtSignal()
    export_statistics_requested = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = Logger()
        
        # 统计数据
        self.session_data = {}
        self.target_history = []
        
        self._init_ui()
        
        # 更新定时器
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self._update_display)
        self.update_timer.start(1000)  # 每秒更新一次
        
        self.logger.info("检测统计组件初始化完成")
    
    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        
        # 标题
        title_label = QLabel("检测会话统计")
        title_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # 基本统计信息
        self._create_basic_stats_group(layout)
        
        # 目标类型统计
        self._create_target_stats_group(layout)
        
        # 性能统计
        self._create_performance_stats_group(layout)
        
        # 操作按钮
        self._create_action_buttons(layout)
    
    def _create_basic_stats_group(self, parent_layout):
        """创建基本统计信息组"""
        group = QGroupBox("基本信息")
        layout = QVBoxLayout(group)
        
        # 检测时长
        self.duration_label = QLabel("检测时长: 0秒")
        self.duration_label.setFont(QFont("Arial", 10))
        layout.addWidget(self.duration_label)
        
        # 总检测次数
        self.total_detections_label = QLabel("总检测次数: 0")
        self.total_detections_label.setFont(QFont("Arial", 10))
        layout.addWidget(self.total_detections_label)
        
        # 保存截图数
        self.saved_screenshots_label = QLabel("保存截图: 0张")
        self.saved_screenshots_label.setFont(QFont("Arial", 10))
        layout.addWidget(self.saved_screenshots_label)
        
        # 检测成功率
        self.success_rate_label = QLabel("检测成功率: 0%")
        self.success_rate_label.setFont(QFont("Arial", 10))
        layout.addWidget(self.success_rate_label)
        
        # 成功率进度条
        self.success_rate_bar = QProgressBar()
        self.success_rate_bar.setRange(0, 100)
        self.success_rate_bar.setValue(0)
        layout.addWidget(self.success_rate_bar)
        
        parent_layout.addWidget(group)
    
    def _create_target_stats_group(self, parent_layout):
        """创建目标类型统计组"""
        group = QGroupBox("目标类型统计")
        layout = QVBoxLayout(group)
        
        # 目标类型表格
        self.target_table = QTableWidget()
        self.target_table.setColumnCount(3)
        self.target_table.setHorizontalHeaderLabels(["目标类型", "检测次数", "占比"])
        self.target_table.setMaximumHeight(150)
        
        # 设置列宽
        header = self.target_table.horizontalHeader()
        header.setStretchLastSection(True)
        
        layout.addWidget(self.target_table)
        
        parent_layout.addWidget(group)
    
    def _create_performance_stats_group(self, parent_layout):
        """创建性能统计组"""
        group = QGroupBox("性能统计")
        layout = QVBoxLayout(group)
        
        # FPS统计
        self.fps_label = QLabel("平均FPS: 0.0")
        self.fps_label.setFont(QFont("Arial", 10))
        layout.addWidget(self.fps_label)
        
        # 检测频率
        self.detection_rate_label = QLabel("检测频率: 0.0次/秒")
        self.detection_rate_label.setFont(QFont("Arial", 10))
        layout.addWidget(self.detection_rate_label)
        
        # 内存使用（如果可获取）
        self.memory_label = QLabel("内存使用: N/A")
        self.memory_label.setFont(QFont("Arial", 10))
        layout.addWidget(self.memory_label)
        
        parent_layout.addWidget(group)
    
    def _create_action_buttons(self, parent_layout):
        """创建操作按钮"""
        button_layout = QHBoxLayout()
        
        # 清除统计按钮
        clear_button = QPushButton("清除统计")
        clear_button.clicked.connect(self.clear_statistics_requested.emit)
        button_layout.addWidget(clear_button)
        
        # 导出统计按钮
        export_button = QPushButton("导出统计")
        export_button.clicked.connect(self.export_statistics_requested.emit)
        button_layout.addWidget(export_button)
        
        parent_layout.addLayout(button_layout)
    
    def update_statistics(self, session_data: Dict[str, Any]):
        """更新统计数据"""
        self.session_data = session_data
        self._update_display()
    
    def _update_display(self):
        """更新显示"""
        if not self.session_data:
            return
        
        try:
            # 更新基本信息
            duration = self.session_data.get('duration', 0)
            total_detections = self.session_data.get('total_detections', 0)
            saved_screenshots = self.session_data.get('saved_screenshots', 0)
            
            self.duration_label.setText(f"检测时长: {duration}秒")
            self.total_detections_label.setText(f"总检测次数: {total_detections}")
            self.saved_screenshots_label.setText(f"保存截图: {saved_screenshots}张")
            
            # 计算成功率
            if total_detections > 0:
                success_rate = (saved_screenshots / total_detections) * 100
                self.success_rate_label.setText(f"检测成功率: {success_rate:.1f}%")
                self.success_rate_bar.setValue(int(success_rate))
            else:
                self.success_rate_label.setText("检测成功率: 0%")
                self.success_rate_bar.setValue(0)
            
            # 更新目标类型统计
            self._update_target_table()
            
            # 更新性能统计
            detection_rate = self.session_data.get('detection_rate', 0.0)
            self.detection_rate_label.setText(f"检测频率: {detection_rate:.1f}次/秒")
            
            # 更新内存使用（如果可获取）
            self._update_memory_usage()
            
        except Exception as e:
            self.logger.error(f"更新统计显示失败: {e}")
    
    def _update_target_table(self):
        """更新目标类型表格"""
        try:
            target_types = self.session_data.get('target_types', {})
            
            # 清空表格
            self.target_table.setRowCount(0)
            
            if not target_types:
                return
            
            # 计算总数
            total_count = sum(target_types.values())
            
            # 添加行
            self.target_table.setRowCount(len(target_types))
            
            for row, (target_type, count) in enumerate(target_types.items()):
                # 目标类型
                type_item = QTableWidgetItem(target_type)
                self.target_table.setItem(row, 0, type_item)
                
                # 检测次数
                count_item = QTableWidgetItem(str(count))
                self.target_table.setItem(row, 1, count_item)
                
                # 占比
                percentage = (count / total_count) * 100 if total_count > 0 else 0
                percentage_item = QTableWidgetItem(f"{percentage:.1f}%")
                self.target_table.setItem(row, 2, percentage_item)
            
        except Exception as e:
            self.logger.error(f"更新目标类型表格失败: {e}")
    
    def _update_memory_usage(self):
        """更新内存使用情况"""
        try:
            import psutil
            import os
            
            # 获取当前进程的内存使用
            process = psutil.Process(os.getpid())
            memory_info = process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024
            
            self.memory_label.setText(f"内存使用: {memory_mb:.1f} MB")
            
        except ImportError:
            self.memory_label.setText("内存使用: 需要psutil库")
        except Exception as e:
            self.memory_label.setText(f"内存使用: 获取失败")
            self.logger.debug(f"获取内存使用失败: {e}")
    
    def clear_statistics(self):
        """清除统计数据"""
        self.session_data = {}
        self.target_history = []
        
        # 重置显示
        self.duration_label.setText("检测时长: 0秒")
        self.total_detections_label.setText("总检测次数: 0")
        self.saved_screenshots_label.setText("保存截图: 0张")
        self.success_rate_label.setText("检测成功率: 0%")
        self.success_rate_bar.setValue(0)
        self.fps_label.setText("平均FPS: 0.0")
        self.detection_rate_label.setText("检测频率: 0.0次/秒")
        self.memory_label.setText("内存使用: N/A")
        
        # 清空表格
        self.target_table.setRowCount(0)
        
        self.logger.info("统计数据已清除")
    
    def export_statistics(self, file_path: str = None) -> bool:
        """导出统计数据"""
        try:
            if not file_path:
                from datetime import datetime
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                file_path = f"detection_statistics_{timestamp}.json"
            
            import json
            with open(file_path, 'w', encoding='utf-8') as f:
                export_data = {
                    'session_data': self.session_data,
                    'export_time': datetime.now().isoformat(),
                    'version': '1.0'
                }
                json.dump(export_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"统计数据已导出: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"导出统计数据失败: {e}")
            return False
