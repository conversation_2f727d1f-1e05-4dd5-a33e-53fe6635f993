#!/usr/bin/env python3
"""
自动化操作配置面板
提供检测结果的可视化选择和操作配置功能
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, QLabel, 
    QPushButton, QComboBox, QSpinBox, QLineEdit, QTextEdit,
    QListWidget, QListWidgetItem, QSplitter, QTabWidget,
    QCheckBox, QSlider, QProgressBar, QMessageBox, QTableWidget,
    QTableWidgetItem, QHeaderView, QFrame
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QPixmap, QPainter, QPen, QBrush, QColor
from typing import List, Dict, Any, Optional, Tuple
import logging
from pathlib import Path

class AutomationPanel(QWidget):
    """自动化操作配置面板"""
    
    # 信号定义
    operation_configured = pyqtSignal(dict)  # 操作配置完成
    operation_executed = pyqtSignal(str)     # 操作执行
    template_saved = pyqtSignal(str)         # 模板保存
    
    def __init__(self, parent=None):
        """初始化自动化面板"""
        super().__init__(parent)
        self.logger = logging.getLogger(__name__)
        
        # 数据存储
        self.detection_targets = []  # 检测目标列表
        self.current_operations = []  # 当前操作序列
        self.available_templates = []  # 可用模板列表
        
        # 初始化UI
        self._init_ui()
        self._setup_connections()
        
        self.logger.info("自动化操作面板初始化完成")
    
    def _init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # 创建标题
        title_label = QLabel("🤖 自动化操作配置")
        title_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        layout.addWidget(title_label)
        
        # 创建主分割器
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        layout.addWidget(main_splitter)
        
        # 左侧：检测目标选择
        left_panel = self._create_target_selection_panel()
        main_splitter.addWidget(left_panel)
        
        # 右侧：操作配置
        right_panel = self._create_operation_config_panel()
        main_splitter.addWidget(right_panel)
        
        # 设置分割器比例
        main_splitter.setSizes([400, 500])
        
        # 底部：操作控制
        control_panel = self._create_control_panel()
        layout.addWidget(control_panel)
    
    def _create_target_selection_panel(self) -> QWidget:
        """创建检测目标选择面板"""
        panel = QGroupBox("🎯 检测目标选择")
        layout = QVBoxLayout(panel)
        
        # 目标列表
        self.targets_list = QTableWidget()
        self.targets_list.setColumnCount(5)
        self.targets_list.setHorizontalHeaderLabels([
            "选择", "类型", "置信度", "位置", "操作"
        ])
        
        # 设置列宽
        header = self.targets_list.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Fixed)
        
        self.targets_list.setColumnWidth(0, 50)   # 选择列
        self.targets_list.setColumnWidth(2, 80)   # 置信度列
        self.targets_list.setColumnWidth(4, 100)  # 操作列
        
        self.targets_list.setToolTip("检测到的目标列表\n"
                                    "勾选要操作的目标\n"
                                    "点击'配置'按钮设置操作")
        layout.addWidget(self.targets_list)
        
        # 目标操作按钮
        target_buttons = QHBoxLayout()
        
        self.refresh_targets_btn = QPushButton("🔄 刷新目标")
        self.refresh_targets_btn.setToolTip("重新获取检测目标")
        target_buttons.addWidget(self.refresh_targets_btn)
        
        self.select_all_btn = QPushButton("✅ 全选")
        self.select_all_btn.setToolTip("选择所有检测目标")
        target_buttons.addWidget(self.select_all_btn)
        
        self.clear_selection_btn = QPushButton("❌ 清除")
        self.clear_selection_btn.setToolTip("清除所有选择")
        target_buttons.addWidget(self.clear_selection_btn)
        
        target_buttons.addStretch()
        layout.addLayout(target_buttons)
        
        # 目标统计信息
        self.targets_stats_label = QLabel("检测目标: 0 个")
        self.targets_stats_label.setStyleSheet("color: #7f8c8d; font-size: 11px;")
        layout.addWidget(self.targets_stats_label)
        
        return panel
    
    def _create_operation_config_panel(self) -> QWidget:
        """创建操作配置面板"""
        panel = QGroupBox("⚙️ 操作配置")
        layout = QVBoxLayout(panel)
        
        # 创建标签页
        tab_widget = QTabWidget()
        layout.addWidget(tab_widget)
        
        # 基础操作标签页
        basic_tab = self._create_basic_operations_tab()
        tab_widget.addTab(basic_tab, "🖱️ 基础操作")
        
        # 模板操作标签页
        template_tab = self._create_template_operations_tab()
        tab_widget.addTab(template_tab, "📋 操作模板")
        
        # 高级操作标签页
        advanced_tab = self._create_advanced_operations_tab()
        tab_widget.addTab(advanced_tab, "🔧 高级操作")
        
        return panel
    
    def _create_basic_operations_tab(self) -> QWidget:
        """创建基础操作标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 操作类型选择
        type_group = QGroupBox("操作类型")
        type_layout = QVBoxLayout(type_group)
        
        self.operation_type_combo = QComboBox()
        self.operation_type_combo.addItems([
            "🖱️ 鼠标点击",
            "⌨️ 键盘输入", 
            "🔗 组合操作",
            "⏱️ 延时等待"
        ])
        self.operation_type_combo.setToolTip("选择要执行的操作类型")
        type_layout.addWidget(self.operation_type_combo)
        layout.addWidget(type_group)
        
        # 操作参数配置
        params_group = QGroupBox("参数配置")
        params_layout = QVBoxLayout(params_group)
        
        # 鼠标操作参数
        mouse_frame = QFrame()
        mouse_layout = QVBoxLayout(mouse_frame)
        
        click_type_layout = QHBoxLayout()
        click_type_layout.addWidget(QLabel("点击类型:"))
        self.click_type_combo = QComboBox()
        self.click_type_combo.addItems(["左键单击", "右键单击", "双击", "拖拽"])
        click_type_layout.addWidget(self.click_type_combo)
        click_type_layout.addStretch()
        mouse_layout.addLayout(click_type_layout)
        
        offset_layout = QHBoxLayout()
        offset_layout.addWidget(QLabel("位置偏移:"))
        self.offset_x_spin = QSpinBox()
        self.offset_x_spin.setRange(-100, 100)
        self.offset_x_spin.setSuffix(" px")
        offset_layout.addWidget(QLabel("X:"))
        offset_layout.addWidget(self.offset_x_spin)
        self.offset_y_spin = QSpinBox()
        self.offset_y_spin.setRange(-100, 100)
        self.offset_y_spin.setSuffix(" px")
        offset_layout.addWidget(QLabel("Y:"))
        offset_layout.addWidget(self.offset_y_spin)
        offset_layout.addStretch()
        mouse_layout.addLayout(offset_layout)
        
        params_layout.addWidget(mouse_frame)
        
        # 键盘操作参数
        keyboard_frame = QFrame()
        keyboard_layout = QVBoxLayout(keyboard_frame)
        
        text_layout = QHBoxLayout()
        text_layout.addWidget(QLabel("输入文本:"))
        self.input_text_edit = QLineEdit()
        self.input_text_edit.setPlaceholderText("要输入的文本内容...")
        text_layout.addWidget(self.input_text_edit)
        keyboard_layout.addLayout(text_layout)
        
        hotkey_layout = QHBoxLayout()
        hotkey_layout.addWidget(QLabel("快捷键:"))
        self.hotkey_edit = QLineEdit()
        self.hotkey_edit.setPlaceholderText("如: ctrl+c, alt+tab")
        hotkey_layout.addWidget(self.hotkey_edit)
        keyboard_layout.addLayout(hotkey_layout)
        
        params_layout.addWidget(keyboard_frame)
        
        # 延时参数
        delay_layout = QHBoxLayout()
        delay_layout.addWidget(QLabel("延时:"))
        self.delay_spin = QSpinBox()
        self.delay_spin.setRange(0, 10000)
        self.delay_spin.setValue(100)
        self.delay_spin.setSuffix(" ms")
        delay_layout.addWidget(self.delay_spin)
        delay_layout.addStretch()
        params_layout.addLayout(delay_layout)
        
        layout.addWidget(params_group)
        
        # 操作预览
        preview_group = QGroupBox("操作预览")
        preview_layout = QVBoxLayout(preview_group)
        
        self.operation_preview = QTextEdit()
        self.operation_preview.setMaximumHeight(100)
        self.operation_preview.setReadOnly(True)
        self.operation_preview.setPlaceholderText("操作配置预览将显示在这里...")
        preview_layout.addWidget(self.operation_preview)
        
        layout.addWidget(preview_group)
        
        layout.addStretch()
        return widget
    
    def _create_template_operations_tab(self) -> QWidget:
        """创建模板操作标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 模板列表
        template_group = QGroupBox("可用模板")
        template_layout = QVBoxLayout(template_group)
        
        self.template_list = QListWidget()
        self.template_list.setToolTip("双击模板应用到当前目标")
        template_layout.addWidget(self.template_list)
        
        # 模板操作按钮
        template_buttons = QHBoxLayout()
        
        self.load_template_btn = QPushButton("📂 加载模板")
        template_buttons.addWidget(self.load_template_btn)
        
        self.save_template_btn = QPushButton("💾 保存模板")
        template_buttons.addWidget(self.save_template_btn)
        
        self.delete_template_btn = QPushButton("🗑️ 删除模板")
        template_buttons.addWidget(self.delete_template_btn)
        
        template_buttons.addStretch()
        template_layout.addLayout(template_buttons)
        
        layout.addWidget(template_group)
        
        # 模板详情
        details_group = QGroupBox("模板详情")
        details_layout = QVBoxLayout(details_group)
        
        self.template_details = QTextEdit()
        self.template_details.setMaximumHeight(150)
        self.template_details.setReadOnly(True)
        self.template_details.setPlaceholderText("选择模板查看详细信息...")
        details_layout.addWidget(self.template_details)
        
        layout.addWidget(details_group)
        
        layout.addStretch()
        return widget
    
    def _create_advanced_operations_tab(self) -> QWidget:
        """创建高级操作标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 批量操作
        batch_group = QGroupBox("批量操作")
        batch_layout = QVBoxLayout(batch_group)
        
        self.batch_operation_checkbox = QCheckBox("对所有选中目标执行相同操作")
        batch_layout.addWidget(self.batch_operation_checkbox)
        
        interval_layout = QHBoxLayout()
        interval_layout.addWidget(QLabel("操作间隔:"))
        self.batch_interval_spin = QSpinBox()
        self.batch_interval_spin.setRange(0, 5000)
        self.batch_interval_spin.setValue(500)
        self.batch_interval_spin.setSuffix(" ms")
        interval_layout.addWidget(self.batch_interval_spin)
        interval_layout.addStretch()
        batch_layout.addLayout(interval_layout)
        
        layout.addWidget(batch_group)
        
        # 条件执行
        condition_group = QGroupBox("条件执行")
        condition_layout = QVBoxLayout(condition_group)
        
        self.condition_checkbox = QCheckBox("启用条件执行")
        condition_layout.addWidget(self.condition_checkbox)
        
        condition_config_layout = QHBoxLayout()
        condition_config_layout.addWidget(QLabel("条件:"))
        self.condition_combo = QComboBox()
        self.condition_combo.addItems([
            "置信度 > 阈值",
            "目标数量 > 数值", 
            "位置在区域内",
            "自定义条件"
        ])
        condition_config_layout.addWidget(self.condition_combo)
        condition_layout.addLayout(condition_config_layout)
        
        layout.addWidget(condition_group)
        
        # 循环执行
        loop_group = QGroupBox("循环执行")
        loop_layout = QVBoxLayout(loop_group)
        
        self.loop_checkbox = QCheckBox("启用循环执行")
        loop_layout.addWidget(self.loop_checkbox)
        
        loop_config_layout = QHBoxLayout()
        loop_config_layout.addWidget(QLabel("循环次数:"))
        self.loop_count_spin = QSpinBox()
        self.loop_count_spin.setRange(1, 100)
        self.loop_count_spin.setValue(1)
        loop_config_layout.addWidget(self.loop_count_spin)
        loop_config_layout.addStretch()
        loop_layout.addLayout(loop_config_layout)
        
        layout.addWidget(loop_group)
        
        layout.addStretch()
        return widget
    
    def _create_control_panel(self) -> QWidget:
        """创建控制面板"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.Shape.StyledPanel)
        layout = QHBoxLayout(panel)
        
        # 操作序列显示
        sequence_label = QLabel("操作序列:")
        layout.addWidget(sequence_label)
        
        self.sequence_count_label = QLabel("0 个操作")
        self.sequence_count_label.setStyleSheet("font-weight: bold; color: #3498db;")
        layout.addWidget(self.sequence_count_label)
        
        layout.addStretch()
        
        # 控制按钮
        self.preview_btn = QPushButton("👁️ 预览")
        self.preview_btn.setToolTip("预览操作序列")
        layout.addWidget(self.preview_btn)
        
        self.test_btn = QPushButton("🧪 测试")
        self.test_btn.setToolTip("测试执行操作序列")
        layout.addWidget(self.test_btn)
        
        self.execute_btn = QPushButton("▶️ 执行")
        self.execute_btn.setToolTip("执行配置的操作序列")
        self.execute_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
        """)
        layout.addWidget(self.execute_btn)
        
        self.stop_btn = QPushButton("⏹️ 停止")
        self.stop_btn.setToolTip("停止执行")
        self.stop_btn.setEnabled(False)
        layout.addWidget(self.stop_btn)
        
        return panel
    
    def _setup_connections(self):
        """设置信号连接"""
        # 目标选择相关
        self.refresh_targets_btn.clicked.connect(self._refresh_targets)
        self.select_all_btn.clicked.connect(self._select_all_targets)
        self.clear_selection_btn.clicked.connect(self._clear_target_selection)
        
        # 操作配置相关
        self.operation_type_combo.currentTextChanged.connect(self._on_operation_type_changed)
        
        # 模板相关
        self.template_list.itemDoubleClicked.connect(self._apply_template)
        self.load_template_btn.clicked.connect(self._load_template)
        self.save_template_btn.clicked.connect(self._save_template)
        self.delete_template_btn.clicked.connect(self._delete_template)
        
        # 控制按钮
        self.preview_btn.clicked.connect(self._preview_operations)
        self.test_btn.clicked.connect(self._test_operations)
        self.execute_btn.clicked.connect(self._execute_operations)
        self.stop_btn.clicked.connect(self._stop_operations)
    
    def update_detection_targets(self, targets: List[Dict[str, Any]]):
        """更新检测目标列表"""
        self.detection_targets = targets
        self._refresh_targets_display()
        
    def _refresh_targets_display(self):
        """刷新目标显示"""
        self.targets_list.setRowCount(len(self.detection_targets))
        
        for i, target in enumerate(self.detection_targets):
            # 选择复选框
            checkbox = QCheckBox()
            self.targets_list.setCellWidget(i, 0, checkbox)
            
            # 目标类型
            type_item = QTableWidgetItem(target.get('label', 'unknown'))
            self.targets_list.setItem(i, 1, type_item)
            
            # 置信度
            confidence = target.get('confidence', 0.0)
            conf_item = QTableWidgetItem(f"{confidence:.2f}")
            self.targets_list.setItem(i, 2, conf_item)
            
            # 位置信息
            bbox = target.get('bbox', (0, 0, 0, 0))
            pos_text = f"({bbox[0]}, {bbox[1]})"
            pos_item = QTableWidgetItem(pos_text)
            self.targets_list.setItem(i, 3, pos_item)
            
            # 配置按钮
            config_btn = QPushButton("配置")
            config_btn.clicked.connect(lambda checked, idx=i: self._configure_target_operation(idx))
            self.targets_list.setCellWidget(i, 4, config_btn)
        
        # 更新统计信息
        self.targets_stats_label.setText(f"检测目标: {len(self.detection_targets)} 个")
    
    def _refresh_targets(self):
        """刷新检测目标"""
        # 这里应该从检测服务获取最新目标
        # 暂时使用模拟数据
        pass
    
    def _select_all_targets(self):
        """选择所有目标"""
        for i in range(self.targets_list.rowCount()):
            checkbox = self.targets_list.cellWidget(i, 0)
            if checkbox:
                checkbox.setChecked(True)
    
    def _clear_target_selection(self):
        """清除目标选择"""
        for i in range(self.targets_list.rowCount()):
            checkbox = self.targets_list.cellWidget(i, 0)
            if checkbox:
                checkbox.setChecked(False)
    
    def _on_operation_type_changed(self, operation_type: str):
        """操作类型改变处理"""
        # 根据操作类型显示/隐藏相应的参数配置
        pass
    
    def _configure_target_operation(self, target_index: int):
        """配置目标操作"""
        if target_index < len(self.detection_targets):
            target = self.detection_targets[target_index]
            # 打开操作配置对话框或切换到配置标签页
            pass
    
    def _apply_template(self, item: QListWidgetItem):
        """应用模板"""
        template_name = item.text()
        # 应用选中的模板到当前目标
        pass
    
    def _load_template(self):
        """加载模板"""
        pass
    
    def _save_template(self):
        """保存模板"""
        pass
    
    def _delete_template(self):
        """删除模板"""
        pass
    
    def _preview_operations(self):
        """预览操作"""
        pass
    
    def _test_operations(self):
        """测试操作"""
        pass
    
    def _execute_operations(self):
        """执行操作"""
        pass
    
    def _stop_operations(self):
        """停止操作"""
        pass
