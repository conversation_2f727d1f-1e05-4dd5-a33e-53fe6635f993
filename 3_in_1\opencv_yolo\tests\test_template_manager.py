# -*- coding: utf-8 -*-
"""
模板管理器测试
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

import pytest
import numpy as np
import cv2
from PIL import Image
from pathlib import Path
import tempfile
import json

from yolo_opencv_detector.core.template_manager import TemplateManager
from yolo_opencv_detector.utils.data_structures import TemplateInfo


class TestTemplateManager:
    """模板管理器测试类"""
    
    @pytest.fixture
    def temp_manager(self, tmp_path):
        """创建临时模板管理器"""
        db_path = tmp_path / "test_templates.db"
        templates_dir = tmp_path / "templates"
        return TemplateManager(db_path=db_path, templates_dir=templates_dir)
    
    @pytest.fixture
    def sample_template_image(self):
        """创建示例模板图像"""
        image = np.zeros((50, 50, 3), dtype=np.uint8)
        cv2.rectangle(image, (10, 10), (40, 40), (255, 255, 255), -1)
        cv2.circle(image, (25, 25), 8, (128, 128, 128), -1)
        return image
    
    @pytest.fixture
    def sample_template_file(self, sample_template_image, tmp_path):
        """创建示例模板文件"""
        template_path = tmp_path / "sample_template.png"
        cv2.imwrite(str(template_path), sample_template_image)
        return template_path
    
    def test_manager_initialization(self, temp_manager):
        """测试管理器初始化"""
        assert temp_manager is not None
        assert temp_manager.db_path.exists()
        assert temp_manager.templates_dir.exists()
    
    def test_database_initialization(self, temp_manager):
        """测试数据库初始化"""
        # 检查数据库文件是否创建
        assert temp_manager.db_path.exists()
        
        # 检查表是否创建
        import sqlite3
        with sqlite3.connect(str(temp_manager.db_path)) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='templates'")
            result = cursor.fetchone()
            assert result is not None
    
    def test_add_template_numpy(self, temp_manager, sample_template_image):
        """测试添加numpy数组模板"""
        template_id = temp_manager.add_template(
            name="测试模板",
            image=sample_template_image,
            description="用于测试的模板",
            category="test",
            tags=["test", "numpy"]
        )
        
        assert template_id is not None
        assert isinstance(template_id, str)
        
        # 验证模板是否保存
        template_info = temp_manager.get_template(template_id)
        assert template_info is not None
        assert template_info.name == "测试模板"
        assert template_info.category == "test"
        assert "test" in template_info.tags
    
    def test_add_template_file(self, temp_manager, sample_template_file):
        """测试添加文件模板"""
        template_id = temp_manager.add_template(
            name="文件模板",
            image=sample_template_file,
            description="从文件加载的模板"
        )
        
        assert template_id is not None
        
        # 验证文件是否复制
        template_info = temp_manager.get_template(template_id)
        assert template_info is not None
        assert template_info.file_path.exists()
    
    def test_add_template_pil(self, temp_manager, sample_template_image):
        """测试添加PIL图像模板"""
        pil_image = Image.fromarray(cv2.cvtColor(sample_template_image, cv2.COLOR_BGR2RGB))
        
        template_id = temp_manager.add_template(
            name="PIL模板",
            image=pil_image,
            description="PIL图像模板"
        )
        
        assert template_id is not None
        
        # 验证模板
        template_info = temp_manager.get_template(template_id)
        assert template_info is not None
        assert template_info.name == "PIL模板"
    
    def test_get_template(self, temp_manager, sample_template_image):
        """测试获取模板"""
        # 添加模板
        template_id = temp_manager.add_template(
            name="获取测试",
            image=sample_template_image
        )
        
        # 获取模板
        template_info = temp_manager.get_template(template_id)
        assert template_info is not None
        assert template_info.template_id == template_id
        assert template_info.name == "获取测试"
        
        # 获取不存在的模板
        non_existent = temp_manager.get_template("non_existent_id")
        assert non_existent is None
    
    def test_list_templates(self, temp_manager, sample_template_image):
        """测试列出模板"""
        # 添加多个模板
        template_ids = []
        for i in range(3):
            template_id = temp_manager.add_template(
                name=f"模板{i}",
                image=sample_template_image,
                category="test" if i < 2 else "other",
                tags=[f"tag{i}"]
            )
            template_ids.append(template_id)
        
        # 列出所有模板
        all_templates = temp_manager.list_templates()
        assert len(all_templates) == 3
        
        # 按类别过滤
        test_templates = temp_manager.list_templates(category="test")
        assert len(test_templates) == 2
        
        # 按标签过滤
        tag0_templates = temp_manager.list_templates(tags=["tag0"])
        assert len(tag0_templates) >= 1
        
        # 限制数量
        limited_templates = temp_manager.list_templates(limit=2)
        assert len(limited_templates) == 2
    
    def test_update_template(self, temp_manager, sample_template_image):
        """测试更新模板"""
        # 添加模板
        template_id = temp_manager.add_template(
            name="原始名称",
            image=sample_template_image,
            description="原始描述"
        )
        
        # 更新模板
        success = temp_manager.update_template(
            template_id,
            name="新名称",
            description="新描述",
            tags=["新标签"]
        )
        
        assert success
        
        # 验证更新
        template_info = temp_manager.get_template(template_id)
        assert template_info.name == "新名称"
        assert template_info.description == "新描述"
        assert "新标签" in template_info.tags
        
        # 更新不存在的模板
        success = temp_manager.update_template("non_existent", name="test")
        assert not success
    
    def test_remove_template(self, temp_manager, sample_template_image):
        """测试删除模板"""
        # 添加模板
        template_id = temp_manager.add_template(
            name="待删除",
            image=sample_template_image
        )
        
        # 获取文件路径
        template_info = temp_manager.get_template(template_id)
        file_path = template_info.file_path
        assert file_path.exists()
        
        # 删除模板
        success = temp_manager.remove_template(template_id, delete_file=True)
        assert success
        
        # 验证删除
        template_info = temp_manager.get_template(template_id)
        assert template_info is None
        assert not file_path.exists()
        
        # 删除不存在的模板
        success = temp_manager.remove_template("non_existent")
        assert not success
    
    def test_usage_stats(self, temp_manager, sample_template_image):
        """测试使用统计"""
        # 添加模板
        template_id = temp_manager.add_template(
            name="统计测试",
            image=sample_template_image
        )
        
        # 初始统计
        template_info = temp_manager.get_template(template_id)
        assert template_info.usage_count == 0
        assert template_info.success_rate == 0.0
        
        # 更新统计 - 成功
        temp_manager.update_usage_stats(template_id, success=True)
        template_info = temp_manager.get_template(template_id)
        assert template_info.usage_count == 1
        assert template_info.success_rate == 1.0
        
        # 更新统计 - 失败
        temp_manager.update_usage_stats(template_id, success=False)
        template_info = temp_manager.get_template(template_id)
        assert template_info.usage_count == 2
        assert template_info.success_rate == 0.5
    
    def test_get_categories(self, temp_manager, sample_template_image):
        """测试获取类别"""
        # 添加不同类别的模板
        temp_manager.add_template("模板1", sample_template_image, category="cat1")
        temp_manager.add_template("模板2", sample_template_image, category="cat2")
        temp_manager.add_template("模板3", sample_template_image, category="cat1")
        
        categories = temp_manager.get_categories()
        assert "cat1" in categories
        assert "cat2" in categories
        assert len(categories) >= 2
    
    def test_get_all_tags(self, temp_manager, sample_template_image):
        """测试获取所有标签"""
        # 添加带标签的模板
        temp_manager.add_template("模板1", sample_template_image, tags=["tag1", "tag2"])
        temp_manager.add_template("模板2", sample_template_image, tags=["tag2", "tag3"])
        
        all_tags = temp_manager.get_all_tags()
        assert "tag1" in all_tags
        assert "tag2" in all_tags
        assert "tag3" in all_tags
    
    def test_search_templates(self, temp_manager, sample_template_image):
        """测试搜索模板"""
        # 添加模板
        temp_manager.add_template(
            "按钮模板", 
            sample_template_image, 
            description="用于点击的按钮",
            tags=["button", "ui"]
        )
        temp_manager.add_template(
            "图标模板", 
            sample_template_image, 
            description="应用图标"
        )
        
        # 搜索
        results = temp_manager.search_templates("按钮")
        assert len(results) >= 1
        assert any("按钮" in r.name for r in results)
        
        results = temp_manager.search_templates("button")
        assert len(results) >= 1
    
    def test_export_import(self, temp_manager, sample_template_image, tmp_path):
        """测试导出导入"""
        # 添加模板
        template_ids = []
        for i in range(2):
            template_id = temp_manager.add_template(
                f"导出模板{i}",
                sample_template_image,
                description=f"描述{i}"
            )
            template_ids.append(template_id)
        
        # 导出
        export_path = tmp_path / "export"
        success = temp_manager.export_templates(export_path, template_ids)
        assert success
        assert (export_path / "templates_info.json").exists()
        
        # 创建新的管理器用于导入
        import_db = tmp_path / "import_templates.db"
        import_dir = tmp_path / "import_templates"
        import_manager = TemplateManager(db_path=import_db, templates_dir=import_dir)
        
        # 导入
        imported_count = import_manager.import_templates(export_path)
        assert imported_count == 2
        
        # 验证导入
        imported_templates = import_manager.list_templates()
        assert len(imported_templates) == 2
    
    def test_cleanup_orphaned_files(self, temp_manager, sample_template_image, tmp_path):
        """测试清理孤立文件"""
        # 添加模板
        template_id = temp_manager.add_template("清理测试", sample_template_image)
        
        # 创建孤立文件
        orphaned_file = temp_manager.templates_dir / "orphaned.png"
        cv2.imwrite(str(orphaned_file), sample_template_image)
        
        # 清理
        deleted_count = temp_manager.cleanup_orphaned_files()
        assert deleted_count >= 1
        assert not orphaned_file.exists()
    
    def test_get_stats(self, temp_manager, sample_template_image):
        """测试获取统计信息"""
        # 添加模板
        temp_manager.add_template("统计1", sample_template_image, category="cat1")
        temp_manager.add_template("统计2", sample_template_image, category="cat2")
        
        stats = temp_manager.get_stats()
        assert "total_templates" in stats
        assert "categories" in stats
        assert stats["total_templates"] >= 2
        assert "cat1" in stats["categories"]
        assert "cat2" in stats["categories"]
    
    def test_invalid_inputs(self, temp_manager):
        """测试无效输入处理"""
        # 测试添加无效图像
        template_id = temp_manager.add_template("无效", "invalid_image_data")
        assert template_id is None
        
        # 测试不存在的文件
        non_existent_file = Path("non_existent_image.png")
        template_id = temp_manager.add_template("不存在", non_existent_file)
        assert template_id is None
    
    def test_concurrent_access(self, temp_manager, sample_template_image):
        """测试并发访问"""
        # 这是一个简单的并发测试
        import threading
        
        results = []
        
        def add_template(index):
            template_id = temp_manager.add_template(
                f"并发模板{index}",
                sample_template_image
            )
            results.append(template_id)
        
        # 创建多个线程
        threads = []
        for i in range(3):
            thread = threading.Thread(target=add_template, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 验证结果
        assert len(results) == 3
        assert all(r is not None for r in results)
        assert len(set(results)) == 3  # 所有ID应该是唯一的
