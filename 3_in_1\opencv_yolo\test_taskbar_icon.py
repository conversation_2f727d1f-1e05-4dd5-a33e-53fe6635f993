#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图标显示测试工具
"""

import sys
from pathlib import Path

project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

def test_icon_display():
    """测试图标显示"""
    print("🎨 图标显示测试")
    print("=" * 40)
    
    try:
        from PyQt6.QtWidgets import QApplication, QMainWindow, QLabel
        from PyQt6.QtGui import QIcon
        from PyQt6.QtCore import Qt
        
        app = QApplication(sys.argv)
        
        # 设置应用程序属性
        app.setApplicationName("图标测试")
        app.setApplicationDisplayName("YOLO OpenCV图标测试")
        
        # 设置Windows应用程序ID
        try:
            import ctypes
            ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID(
                "YOLOOpenCVDetector.IconTest.v1.0"
            )
            print("✅ Windows应用程序ID设置成功")
        except Exception as e:
            print(f"⚠️ Windows应用程序ID设置失败: {e}")
        
        # 测试图标文件
        icon_path = project_root / "icons" / "yolo_detector_taskbar.ico"
        if icon_path.exists():
            icon = QIcon(str(icon_path))
            app.setWindowIcon(icon)
            print(f"✅ 图标文件加载成功: {icon_path}")
        else:
            print(f"❌ 图标文件不存在: {icon_path}")
            return False
        
        # 创建测试窗口
        window = QMainWindow()
        window.setWindowTitle("YOLO OpenCV图标测试")
        window.setWindowIcon(icon)
        window.resize(400, 300)
        
        label = QLabel("图标测试窗口\n请检查任务栏图标是否正确显示")
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        window.setCentralWidget(label)
        
        window.show()
        
        print("✅ 测试窗口已显示")
        print("💡 请检查任务栏图标是否正确显示")
        print("💡 按Ctrl+C或关闭窗口退出测试")
        
        return app.exec()
        
    except Exception as e:
        print(f"❌ 图标测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_icon_display()
