# -*- coding: utf-8 -*-
"""
屏幕截取面板
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGroupBox,
    QPushButton, QLabel, QSpinBox, QCheckBox,
    QComboBox, QSlider, QProgressBar
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QPixmap, QIcon

from ..utils.logger import Logger


class CapturePanel(QWidget):
    """屏幕截取控制面板"""
    
    # 信号定义
    capture_requested = pyqtSignal()
    capture_area_changed = pyqtSignal(tuple)  # (x, y, width, height)
    capture_settings_changed = pyqtSignal(dict)
    
    def __init__(self, parent=None):
        """
        初始化截取面板
        
        Args:
            parent: 父窗口
        """
        super().__init__(parent)
        self.logger = Logger().get_logger(__name__)
        
        # 截取设置
        self.capture_area = (0, 0, 1920, 1080)
        self.capture_interval = 1000  # 毫秒
        self.auto_capture = False
        
        # 定时器
        self.capture_timer = QTimer()
        self.capture_timer.timeout.connect(self.capture_requested.emit)
        
        self._setup_ui()
        self._connect_signals()
        
        self.logger.info("屏幕截取面板初始化完成")
    
    def _setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 截取控制组
        capture_group = self._create_capture_control_group()
        layout.addWidget(capture_group)
        
        # 区域设置组
        area_group = self._create_area_settings_group()
        layout.addWidget(area_group)
        
        # 高级设置组
        advanced_group = self._create_advanced_settings_group()
        layout.addWidget(advanced_group)
        
        layout.addStretch()
    
    def _create_capture_control_group(self) -> QGroupBox:
        """创建截取控制组"""
        group = QGroupBox("截取控制")
        layout = QVBoxLayout(group)
        
        # 手动截取按钮
        self.capture_btn = QPushButton("立即截取")
        self.capture_btn.setMinimumHeight(40)
        layout.addWidget(self.capture_btn)
        
        # 自动截取控制
        auto_layout = QHBoxLayout()
        
        self.auto_capture_cb = QCheckBox("自动截取")
        auto_layout.addWidget(self.auto_capture_cb)
        
        auto_layout.addWidget(QLabel("间隔:"))
        
        self.interval_spin = QSpinBox()
        self.interval_spin.setRange(100, 10000)
        self.interval_spin.setValue(1000)
        self.interval_spin.setSuffix(" ms")
        auto_layout.addWidget(self.interval_spin)
        
        layout.addLayout(auto_layout)
        
        # 截取状态
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("color: green;")
        layout.addWidget(self.status_label)
        
        return group
    
    def _create_area_settings_group(self) -> QGroupBox:
        """创建区域设置组"""
        group = QGroupBox("截取区域")
        layout = QVBoxLayout(group)
        
        # 预设区域
        preset_layout = QHBoxLayout()
        preset_layout.addWidget(QLabel("预设:"))
        
        self.preset_combo = QComboBox()
        self.preset_combo.addItems([
            "全屏",
            "主显示器",
            "当前窗口",
            "自定义区域"
        ])
        preset_layout.addWidget(self.preset_combo)
        
        layout.addLayout(preset_layout)
        
        # 自定义区域设置
        custom_layout = QVBoxLayout()
        
        # X坐标
        x_layout = QHBoxLayout()
        x_layout.addWidget(QLabel("X:"))
        self.x_spin = QSpinBox()
        self.x_spin.setRange(0, 9999)
        self.x_spin.setValue(0)
        x_layout.addWidget(self.x_spin)
        custom_layout.addLayout(x_layout)
        
        # Y坐标
        y_layout = QHBoxLayout()
        y_layout.addWidget(QLabel("Y:"))
        self.y_spin = QSpinBox()
        self.y_spin.setRange(0, 9999)
        self.y_spin.setValue(0)
        y_layout.addWidget(self.y_spin)
        custom_layout.addLayout(y_layout)
        
        # 宽度
        w_layout = QHBoxLayout()
        w_layout.addWidget(QLabel("宽度:"))
        self.width_spin = QSpinBox()
        self.width_spin.setRange(1, 9999)
        self.width_spin.setValue(1920)
        w_layout.addWidget(self.width_spin)
        custom_layout.addLayout(w_layout)
        
        # 高度
        h_layout = QHBoxLayout()
        h_layout.addWidget(QLabel("高度:"))
        self.height_spin = QSpinBox()
        self.height_spin.setRange(1, 9999)
        self.height_spin.setValue(1080)
        h_layout.addWidget(self.height_spin)
        custom_layout.addLayout(h_layout)
        
        layout.addLayout(custom_layout)
        
        # 区域选择按钮
        self.select_area_btn = QPushButton("选择区域")
        layout.addWidget(self.select_area_btn)
        
        return group
    
    def _create_advanced_settings_group(self) -> QGroupBox:
        """创建高级设置组"""
        group = QGroupBox("高级设置")
        layout = QVBoxLayout(group)
        
        # 图像质量
        quality_layout = QHBoxLayout()
        quality_layout.addWidget(QLabel("图像质量:"))
        
        self.quality_slider = QSlider(Qt.Orientation.Horizontal)
        self.quality_slider.setRange(1, 100)
        self.quality_slider.setValue(95)
        quality_layout.addWidget(self.quality_slider)
        
        self.quality_label = QLabel("95%")
        quality_layout.addWidget(self.quality_label)
        
        layout.addLayout(quality_layout)
        
        # 其他选项
        self.save_to_file_cb = QCheckBox("保存到文件")
        layout.addWidget(self.save_to_file_cb)
        
        self.show_cursor_cb = QCheckBox("包含鼠标光标")
        layout.addWidget(self.show_cursor_cb)
        
        self.multi_monitor_cb = QCheckBox("多显示器支持")
        layout.addWidget(self.multi_monitor_cb)
        
        return group
    
    def _connect_signals(self):
        """连接信号"""
        # 截取按钮
        self.capture_btn.clicked.connect(self.capture_requested.emit)
        
        # 自动截取
        self.auto_capture_cb.toggled.connect(self._on_auto_capture_toggled)
        self.interval_spin.valueChanged.connect(self._on_interval_changed)
        
        # 区域设置
        self.preset_combo.currentTextChanged.connect(self._on_preset_changed)
        self.x_spin.valueChanged.connect(self._on_area_changed)
        self.y_spin.valueChanged.connect(self._on_area_changed)
        self.width_spin.valueChanged.connect(self._on_area_changed)
        self.height_spin.valueChanged.connect(self._on_area_changed)
        
        # 区域选择
        self.select_area_btn.clicked.connect(self._on_select_area)
        
        # 高级设置
        self.quality_slider.valueChanged.connect(self._on_quality_changed)
        self.save_to_file_cb.toggled.connect(self._on_settings_changed)
        self.show_cursor_cb.toggled.connect(self._on_settings_changed)
        self.multi_monitor_cb.toggled.connect(self._on_settings_changed)
    
    def _on_auto_capture_toggled(self, checked: bool):
        """自动截取开关切换"""
        self.auto_capture = checked
        
        if checked:
            self.capture_timer.start(self.capture_interval)
            self.status_label.setText("自动截取中...")
            self.status_label.setStyleSheet("color: blue;")
        else:
            self.capture_timer.stop()
            self.status_label.setText("就绪")
            self.status_label.setStyleSheet("color: green;")
    
    def _on_interval_changed(self, value: int):
        """截取间隔改变"""
        self.capture_interval = value
        if self.auto_capture:
            self.capture_timer.setInterval(value)
    
    def _on_preset_changed(self, preset: str):
        """预设区域改变"""
        if preset == "全屏":
            # 这里应该获取实际的屏幕尺寸
            self._set_area(0, 0, 1920, 1080)
        elif preset == "主显示器":
            self._set_area(0, 0, 1920, 1080)
        elif preset == "自定义区域":
            # 启用自定义设置
            pass
    
    def _set_area(self, x: int, y: int, width: int, height: int):
        """设置截取区域"""
        self.x_spin.setValue(x)
        self.y_spin.setValue(y)
        self.width_spin.setValue(width)
        self.height_spin.setValue(height)
    
    def _on_area_changed(self):
        """截取区域改变"""
        self.capture_area = (
            self.x_spin.value(),
            self.y_spin.value(),
            self.width_spin.value(),
            self.height_spin.value()
        )
        self.capture_area_changed.emit(self.capture_area)
    
    def _on_select_area(self):
        """选择截取区域"""
        # 这里应该打开区域选择对话框
        self.logger.info("区域选择功能待实现")
    
    def _on_quality_changed(self, value: int):
        """图像质量改变"""
        self.quality_label.setText(f"{value}%")
        self._on_settings_changed()
    
    def _on_settings_changed(self):
        """设置改变"""
        settings = {
            "quality": self.quality_slider.value(),
            "save_to_file": self.save_to_file_cb.isChecked(),
            "show_cursor": self.show_cursor_cb.isChecked(),
            "multi_monitor": self.multi_monitor_cb.isChecked(),
            "capture_area": self.capture_area
        }
        self.capture_settings_changed.emit(settings)
    
    def get_capture_settings(self) -> dict:
        """获取截取设置"""
        return {
            "area": self.capture_area,
            "interval": self.capture_interval,
            "auto_capture": self.auto_capture,
            "quality": self.quality_slider.value(),
            "save_to_file": self.save_to_file_cb.isChecked(),
            "show_cursor": self.show_cursor_cb.isChecked(),
            "multi_monitor": self.multi_monitor_cb.isChecked()
        }
    
    def set_capture_settings(self, settings: dict):
        """设置截取参数"""
        if "area" in settings:
            area = settings["area"]
            self._set_area(area[0], area[1], area[2], area[3])
        
        if "interval" in settings:
            self.interval_spin.setValue(settings["interval"])
        
        if "auto_capture" in settings:
            self.auto_capture_cb.setChecked(settings["auto_capture"])
        
        if "quality" in settings:
            self.quality_slider.setValue(settings["quality"])
        
        if "save_to_file" in settings:
            self.save_to_file_cb.setChecked(settings["save_to_file"])
        
        if "show_cursor" in settings:
            self.show_cursor_cb.setChecked(settings["show_cursor"])
        
        if "multi_monitor" in settings:
            self.multi_monitor_cb.setChecked(settings["multi_monitor"])
    
    def start_capture(self):
        """开始截取"""
        if not self.auto_capture:
            self.auto_capture_cb.setChecked(True)
    
    def stop_capture(self):
        """停止截取"""
        if self.auto_capture:
            self.auto_capture_cb.setChecked(False)
    
    def update_status(self, status: str, color: str = "black"):
        """更新状态显示"""
        self.status_label.setText(status)
        self.status_label.setStyleSheet(f"color: {color};")
