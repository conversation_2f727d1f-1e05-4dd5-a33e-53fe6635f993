#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试代码导出功能
验证源代码面板的独立脚本导出能力
"""

import sys
import os
import shutil
from pathlib import Path
from datetime import datetime

def test_code_exporter_module():
    """测试代码导出器模块"""
    print("=" * 60)
    print("代码导出器模块测试")
    print("=" * 60)
    
    try:
        # 添加YOLO项目路径
        yolo_src_path = Path(__file__).parent / "opencv_yolo" / "src"
        sys.path.insert(0, str(yolo_src_path))
        
        from yolo_opencv_detector.utils.code_exporter import CodeExporter
        print("✅ 代码导出器模块导入成功")
        
        # 创建导出器实例
        exporter = CodeExporter()
        print("✅ 代码导出器实例创建成功")
        
        # 检查必要的方法
        required_methods = [
            'export_standalone_script',
            '_sanitize_filename',
            '_generate_main_script',
            '_generate_requirements',
            '_generate_run_script',
            '_generate_config_file',
            '_generate_readme',
            '_copy_utility_modules'
        ]
        
        missing_methods = []
        for method_name in required_methods:
            if not hasattr(exporter, method_name):
                missing_methods.append(method_name)
        
        if missing_methods:
            print(f"❌ 缺少方法: {missing_methods}")
            return False
        else:
            print("✅ 所有必需的方法都已实现")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_export_functionality():
    """测试导出功能"""
    print("\n" + "=" * 60)
    print("导出功能测试")
    print("=" * 60)
    
    try:
        # 添加YOLO项目路径
        yolo_src_path = Path(__file__).parent / "opencv_yolo" / "src"
        sys.path.insert(0, str(yolo_src_path))
        
        from yolo_opencv_detector.utils.code_exporter import CodeExporter
        
        # 创建测试代码
        test_code = '''
# 测试检测代码
import cv2
import numpy as np

def test_detection():
    """测试检测函数"""
    print("🔍 开始检测...")
    
    # 模拟检测逻辑
    image = np.zeros((480, 640, 3), dtype=np.uint8)
    
    # 绘制测试框
    cv2.rectangle(image, (100, 100), (200, 200), (0, 255, 0), 2)
    
    # 添加中文标签
    if CHINESE_RENDERER_AVAILABLE:
        image = self.render_text_on_image(image, "测试目标", (110, 90))
    else:
        cv2.putText(image, "Test Target", (110, 90), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
    
    # 保存结果
    cv2.imwrite("detection_result.png", image)
    print("✅ 检测完成，结果已保存")

# 运行检测
test_detection()
'''
        
        # 执行导出
        exporter = CodeExporter()
        template_name = "测试检测脚本"
        description = "用于测试代码导出功能的示例脚本"
        
        print(f"📝 模板名称: {template_name}")
        print(f"📋 描述: {description}")
        print("🚀 开始导出...")
        
        success, result = exporter.export_standalone_script(
            test_code, template_name, description
        )
        
        if success:
            print(f"✅ 导出成功！")
            print(f"📁 导出路径: {result}")
            
            # 验证导出的文件
            export_dir = Path(result)
            expected_files = [
                "main.py",
                "requirements.txt", 
                "run.bat",
                "README.md",
                "config/settings.json",
                "utils/__init__.py",
                "utils/chinese_text_renderer.py",
                "utils/detection_utils.py"
            ]
            
            print("\n📋 验证导出文件:")
            all_files_exist = True
            for file_path in expected_files:
                full_path = export_dir / file_path
                if full_path.exists():
                    print(f"  ✅ {file_path}")
                else:
                    print(f"  ❌ {file_path} (缺失)")
                    all_files_exist = False
            
            if all_files_exist:
                print("\n✅ 所有必需文件都已正确生成")
            else:
                print("\n❌ 部分文件缺失")
                return False
            
            # 检查文件内容
            print("\n📋 验证文件内容:")
            
            # 检查main.py
            main_py = export_dir / "main.py"
            with open(main_py, 'r', encoding='utf-8') as f:
                main_content = f.read()
                if "test_detection()" in main_content and "StandaloneDetector" in main_content:
                    print("  ✅ main.py 内容正确")
                else:
                    print("  ❌ main.py 内容不完整")
            
            # 检查requirements.txt
            req_file = export_dir / "requirements.txt"
            with open(req_file, 'r', encoding='utf-8') as f:
                req_content = f.read()
                if "opencv-python" in req_content and "numpy" in req_content:
                    print("  ✅ requirements.txt 内容正确")
                else:
                    print("  ❌ requirements.txt 内容不完整")
            
            # 检查README.md
            readme_file = export_dir / "README.md"
            with open(readme_file, 'r', encoding='utf-8') as f:
                readme_content = f.read()
                if template_name in readme_content and "快速开始" in readme_content:
                    print("  ✅ README.md 内容正确")
                else:
                    print("  ❌ README.md 内容不完整")
            
            return True
            
        else:
            print(f"❌ 导出失败: {result}")
            return False
            
    except Exception as e:
        print(f"❌ 导出功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_filename_sanitization():
    """测试文件名清理功能"""
    print("\n" + "=" * 60)
    print("文件名清理功能测试")
    print("=" * 60)
    
    try:
        # 添加YOLO项目路径
        yolo_src_path = Path(__file__).parent / "opencv_yolo" / "src"
        sys.path.insert(0, str(yolo_src_path))
        
        from yolo_opencv_detector.utils.code_exporter import CodeExporter
        
        exporter = CodeExporter()
        
        # 测试各种文件名
        test_cases = [
            ("正常文件名", "正常文件名"),
            ("包含空格 的文件名", "包含空格_的文件名"),
            ("包含<>:\"/\\|?*的文件名", "包含___________的文件名"),
            ("   前后有空格   ", "前后有空格"),
            ("超长文件名" * 10, "超长文件名超长文件名超长文件名超长文件名超长文件名超长文件名超长文件名超长文件名超长文件名超长"),
        ]
        
        print("🧪 测试文件名清理:")
        all_passed = True
        for original, expected in test_cases:
            result = exporter._sanitize_filename(original)
            if len(result) <= 50 and not any(c in result for c in '<>:"/\\|?*'):
                print(f"  ✅ '{original}' -> '{result}'")
            else:
                print(f"  ❌ '{original}' -> '{result}' (清理失败)")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 文件名清理测试失败: {e}")
        return False

def test_source_code_dialog_integration():
    """测试源代码对话框集成"""
    print("\n" + "=" * 60)
    print("源代码对话框集成测试")
    print("=" * 60)
    
    try:
        # 添加YOLO项目路径
        yolo_src_path = Path(__file__).parent / "opencv_yolo" / "src"
        sys.path.insert(0, str(yolo_src_path))
        
        from yolo_opencv_detector.gui.dialogs.source_code_dialog import SourceCodeDialog
        print("✅ 源代码对话框导入成功")
        
        # 检查新增的方法
        required_methods = [
            'export_as_standalone_script',
            'get_current_code'
        ]
        
        missing_methods = []
        for method_name in required_methods:
            if not hasattr(SourceCodeDialog, method_name):
                missing_methods.append(method_name)
        
        if missing_methods:
            print(f"❌ 缺少方法: {missing_methods}")
            return False
        else:
            print("✅ 所有必需的方法都已实现")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        return False

def create_export_demo():
    """创建导出功能演示"""
    print("\n" + "=" * 60)
    print("创建导出功能演示")
    print("=" * 60)
    
    try:
        import cv2
        import numpy as np
        
        # 添加YOLO项目路径
        yolo_src_path = Path(__file__).parent / "opencv_yolo" / "src"
        sys.path.insert(0, str(yolo_src_path))
        
        from yolo_opencv_detector.utils.chinese_text_renderer import put_chinese_text
        
        # 创建演示图像
        demo_image = np.zeros((700, 1000, 3), dtype=np.uint8)
        demo_image.fill(40)
        
        # 标题
        demo_image = put_chinese_text(demo_image, "代码导出功能演示", (50, 30), 
                                     font_size=28, color=(255, 255, 255), background=False)
        
        # 功能特性
        demo_image = put_chinese_text(demo_image, "功能特性:", (50, 100), 
                                     font_size=20, color=(100, 255, 100), background=False)
        
        features = [
            "• 一键导出完整可运行脚本包",
            "• 自动生成依赖列表和配置文件", 
            "• 包含详细的使用说明文档",
            "• 支持Windows批处理运行脚本",
            "• 兼容中文路径和文件名",
            "• 提供完整的工具模块"
        ]
        
        for i, feature in enumerate(features):
            demo_image = put_chinese_text(demo_image, feature, (70, 140 + i * 35), 
                                         font_size=16, color=(200, 255, 200), background=False)
        
        # 文件结构
        demo_image = put_chinese_text(demo_image, "导出文件结构:", (50, 380), 
                                     font_size=20, color=(255, 255, 100), background=False)
        
        file_structure = [
            "📁 [脚本名称]_[时间戳]/",
            "  ├── main.py              # 主要检测代码",
            "  ├── requirements.txt     # 依赖列表",
            "  ├── run.bat             # Windows运行脚本",
            "  ├── README.md           # 使用说明",
            "  ├── config/             # 配置文件目录",
            "  │   └── settings.json   # 检测参数配置",
            "  └── utils/              # 工具模块",
            "      ├── chinese_text_renderer.py",
            "      └── detection_utils.py"
        ]
        
        for i, line in enumerate(file_structure):
            color = (255, 200, 100) if line.startswith("📁") else (200, 200, 255)
            demo_image = put_chinese_text(demo_image, line, (70, 420 + i * 25), 
                                         font_size=14, color=color, background=False)
        
        # 保存演示
        output_path = Path("test_results") / "code_export_functionality_demo.png"
        output_path.parent.mkdir(exist_ok=True)
        cv2.imwrite(str(output_path), demo_image)
        
        print(f"✅ 导出功能演示已保存: {output_path}")
        return True
        
    except Exception as e:
        print(f"❌ 创建演示失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def cleanup_test_exports():
    """清理测试导出的文件"""
    try:
        export_base = Path(__file__).parent / "opencv_yolo" / "exported_scripts"
        if export_base.exists():
            # 只删除测试相关的导出
            for item in export_base.iterdir():
                if item.is_dir() and "测试" in item.name:
                    shutil.rmtree(item)
                    print(f"🗑️ 清理测试导出: {item.name}")
        
        print("✅ 测试文件清理完成")
        
    except Exception as e:
        print(f"⚠️ 清理测试文件失败: {e}")

if __name__ == "__main__":
    print("🚀 开始代码导出功能测试...")
    
    success_count = 0
    total_tests = 5
    
    # 代码导出器模块测试
    if test_code_exporter_module():
        success_count += 1
    
    # 导出功能测试
    if test_export_functionality():
        success_count += 1
    
    # 文件名清理测试
    if test_filename_sanitization():
        success_count += 1
    
    # 源代码对话框集成测试
    if test_source_code_dialog_integration():
        success_count += 1
    
    # 演示创建
    if create_export_demo():
        success_count += 1
    
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    if success_count == total_tests:
        print("🎉 所有测试通过！代码导出功能实现成功！")
        print("✅ 代码导出器模块正常工作")
        print("✅ 独立脚本导出功能完整")
        print("✅ 文件名清理机制有效")
        print("✅ 源代码对话框集成成功")
        print("✅ 支持中文路径和文件名")
    else:
        print(f"⚠️ {success_count}/{total_tests} 个测试通过")
        print("部分功能可能需要进一步调试")
    
    print("\n📁 测试结果文件保存在 test_results/ 目录中")
    print("📁 导出的测试脚本保存在 opencv_yolo/exported_scripts/ 目录中")
    
    # 询问是否清理测试文件
    try:
        response = input("\n是否清理测试导出的文件？(y/N): ").strip().lower()
        if response in ['y', 'yes']:
            cleanup_test_exports()
    except KeyboardInterrupt:
        print("\n测试完成")
