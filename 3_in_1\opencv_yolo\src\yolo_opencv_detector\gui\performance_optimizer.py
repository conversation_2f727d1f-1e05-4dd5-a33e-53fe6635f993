# -*- coding: utf-8 -*-
"""
PyQt6 GUI性能优化模块

提供GUI性能优化、内存管理、异步处理等功能，
确保实时检测的流畅性和稳定性。

Created: 2025-07-13
Author: Augment Agent
"""

import gc
import threading
import queue
import time
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, Future
import logging

from PyQt6.QtCore import QThread, QTimer, QObject, pyqtSignal, QMutex, QWaitCondition
from PyQt6.QtGui import QPixmap, QImage
from PyQt6.QtWidgets import QApplication
import numpy as np
import cv2

logger = logging.getLogger(__name__)


@dataclass
class PerformanceConfig:
    """性能配置"""
    max_fps: int = 30  # 最大帧率
    max_queue_size: int = 5  # 最大队列大小
    thread_pool_size: int = 4  # 线程池大小
    memory_check_interval: int = 5000  # 内存检查间隔(ms)
    max_memory_usage: float = 0.8  # 最大内存使用率
    enable_gpu_acceleration: bool = True  # 启用GPU加速
    image_cache_size: int = 50  # 图像缓存大小
    enable_frame_skip: bool = True  # 启用跳帧
    skip_threshold: float = 0.8  # 跳帧阈值


class MemoryManager:
    """内存管理器"""
    
    def __init__(self, config: PerformanceConfig):
        self.config = config
        self.image_cache: Dict[str, QPixmap] = {}
        self.cache_access_times: Dict[str, float] = {}
        
    def get_memory_usage(self) -> float:
        """获取内存使用率"""
        try:
            import psutil
            process = psutil.Process()
            memory_info = process.memory_info()
            system_memory = psutil.virtual_memory()
            
            usage_ratio = memory_info.rss / system_memory.total
            return usage_ratio
        except ImportError:
            logger.warning("psutil未安装，无法获取内存使用情况")
            return 0.0
    
    def cleanup_memory(self):
        """清理内存"""
        try:
            # 清理图像缓存
            self._cleanup_image_cache()
            
            # 强制垃圾回收
            gc.collect()
            
            # 清理Qt对象
            QApplication.processEvents()
            
            logger.debug("内存清理完成")
            
        except Exception as e:
            logger.error(f"内存清理失败: {e}")
    
    def _cleanup_image_cache(self):
        """清理图像缓存"""
        if len(self.image_cache) <= self.config.image_cache_size:
            return
        
        # 按访问时间排序，删除最旧的
        current_time = time.time()
        sorted_items = sorted(
            self.cache_access_times.items(),
            key=lambda x: x[1]
        )
        
        # 删除超出限制的缓存
        items_to_remove = len(self.image_cache) - self.config.image_cache_size
        for i in range(items_to_remove):
            key = sorted_items[i][0]
            if key in self.image_cache:
                del self.image_cache[key]
            if key in self.cache_access_times:
                del self.cache_access_times[key]
    
    def cache_image(self, key: str, pixmap: QPixmap):
        """缓存图像"""
        self.image_cache[key] = pixmap
        self.cache_access_times[key] = time.time()
        self._cleanup_image_cache()
    
    def get_cached_image(self, key: str) -> Optional[QPixmap]:
        """获取缓存图像"""
        if key in self.image_cache:
            self.cache_access_times[key] = time.time()
            return self.image_cache[key]
        return None


class AsyncDetectionWorker(QThread):
    """异步检测工作线程"""
    
    detection_finished = pyqtSignal(object)  # 检测完成信号
    error_occurred = pyqtSignal(str)  # 错误信号
    
    def __init__(self, config: PerformanceConfig):
        super().__init__()
        self.config = config
        self.detection_queue = queue.Queue(maxsize=config.max_queue_size)
        self.running = False
        self.detector_func = None
        self.mutex = QMutex()
        self.condition = QWaitCondition()
        
    def set_detector(self, detector_func: Callable):
        """设置检测函数"""
        self.detector_func = detector_func
    
    def add_detection_task(self, image: np.ndarray, task_id: str) -> bool:
        """添加检测任务"""
        try:
            if self.detection_queue.full():
                if self.config.enable_frame_skip:
                    # 跳帧：移除最旧的任务
                    try:
                        self.detection_queue.get_nowait()
                    except queue.Empty:
                        pass
                else:
                    return False
            
            self.detection_queue.put((image, task_id, time.time()))
            
            # 唤醒工作线程
            self.mutex.lock()
            self.condition.wakeOne()
            self.mutex.unlock()
            
            return True
            
        except Exception as e:
            logger.error(f"添加检测任务失败: {e}")
            return False
    
    def run(self):
        """工作线程主循环"""
        self.running = True
        
        while self.running:
            try:
                # 等待任务
                self.mutex.lock()
                if self.detection_queue.empty():
                    self.condition.wait(self.mutex, 100)  # 100ms超时
                self.mutex.unlock()
                
                if not self.running:
                    break
                
                # 获取任务
                try:
                    image, task_id, timestamp = self.detection_queue.get_nowait()
                except queue.Empty:
                    continue
                
                # 检查任务是否过期
                current_time = time.time()
                if current_time - timestamp > 1.0:  # 1秒超时
                    logger.debug(f"跳过过期任务: {task_id}")
                    continue
                
                # 执行检测
                if self.detector_func:
                    try:
                        start_time = time.time()
                        results = self.detector_func(image)
                        detection_time = time.time() - start_time
                        
                        # 发送结果
                        self.detection_finished.emit({
                            'task_id': task_id,
                            'results': results,
                            'detection_time': detection_time,
                            'timestamp': timestamp
                        })
                        
                    except Exception as e:
                        self.error_occurred.emit(f"检测失败: {e}")
                
            except Exception as e:
                logger.error(f"检测工作线程错误: {e}")
    
    def stop(self):
        """停止工作线程"""
        self.running = False
        self.mutex.lock()
        self.condition.wakeAll()
        self.mutex.unlock()
        self.wait()


class FrameRateController:
    """帧率控制器"""
    
    def __init__(self, config: PerformanceConfig):
        self.config = config
        self.last_frame_time = 0
        self.frame_interval = 1.0 / config.max_fps
        self.frame_times = []
        self.max_history = 30
        
    def should_process_frame(self) -> bool:
        """判断是否应该处理当前帧"""
        current_time = time.time()
        
        if current_time - self.last_frame_time < self.frame_interval:
            return False
        
        self.last_frame_time = current_time
        self.frame_times.append(current_time)
        
        # 保持历史记录长度
        if len(self.frame_times) > self.max_history:
            self.frame_times.pop(0)
        
        return True
    
    def get_current_fps(self) -> float:
        """获取当前帧率"""
        if len(self.frame_times) < 2:
            return 0.0
        
        time_span = self.frame_times[-1] - self.frame_times[0]
        if time_span == 0:
            return 0.0
        
        return (len(self.frame_times) - 1) / time_span
    
    def get_average_frame_time(self) -> float:
        """获取平均帧时间"""
        if len(self.frame_times) < 2:
            return 0.0
        
        intervals = []
        for i in range(1, len(self.frame_times)):
            intervals.append(self.frame_times[i] - self.frame_times[i-1])
        
        return sum(intervals) / len(intervals) if intervals else 0.0


class GPUAccelerator:
    """GPU加速器"""
    
    def __init__(self, config: PerformanceConfig):
        self.config = config
        self.gpu_available = self._check_gpu_availability()
        
    def _check_gpu_availability(self) -> bool:
        """检查GPU可用性"""
        try:
            import torch
            return torch.cuda.is_available()
        except ImportError:
            return False
    
    def optimize_image_processing(self, image: np.ndarray) -> np.ndarray:
        """GPU优化的图像处理"""
        if not self.gpu_available or not self.config.enable_gpu_acceleration:
            return image
        
        try:
            import torch
            
            # 转换为GPU张量
            device = torch.device('cuda')
            image_tensor = torch.from_numpy(image).float().to(device)
            
            # GPU上的图像处理操作
            # 这里可以添加各种GPU加速的图像处理
            
            # 转换回CPU numpy数组
            processed_image = image_tensor.cpu().numpy().astype(np.uint8)
            
            return processed_image
            
        except Exception as e:
            logger.warning(f"GPU加速失败，回退到CPU: {e}")
            return image
    
    def batch_process_images(self, images: List[np.ndarray]) -> List[np.ndarray]:
        """批量处理图像"""
        if not self.gpu_available or not self.config.enable_gpu_acceleration:
            return images
        
        try:
            import torch
            
            device = torch.device('cuda')
            
            # 批量转换
            batch_tensor = torch.stack([
                torch.from_numpy(img).float() for img in images
            ]).to(device)
            
            # 批量处理
            # 这里可以添加批量处理逻辑
            
            # 转换回列表
            processed_images = [
                tensor.cpu().numpy().astype(np.uint8) 
                for tensor in batch_tensor
            ]
            
            return processed_images
            
        except Exception as e:
            logger.warning(f"GPU批量处理失败: {e}")
            return images


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, config: PerformanceConfig):
        self.config = config
        self.metrics = {
            'fps': 0.0,
            'memory_usage': 0.0,
            'detection_time': 0.0,
            'queue_size': 0,
            'dropped_frames': 0,
            'total_frames': 0
        }
        self.start_time = time.time()
        
    def update_metrics(self, **kwargs):
        """更新性能指标"""
        for key, value in kwargs.items():
            if key in self.metrics:
                self.metrics[key] = value
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        runtime = time.time() - self.start_time
        
        return {
            'runtime': runtime,
            'metrics': self.metrics.copy(),
            'drop_rate': (self.metrics['dropped_frames'] / 
                         max(self.metrics['total_frames'], 1)) * 100,
            'avg_fps': self.metrics['total_frames'] / runtime if runtime > 0 else 0
        }


class PerformanceOptimizer:
    """性能优化器主类"""
    
    def __init__(self, config: PerformanceConfig):
        self.config = config
        self.memory_manager = MemoryManager(config)
        self.frame_controller = FrameRateController(config)
        self.gpu_accelerator = GPUAccelerator(config)
        self.performance_monitor = PerformanceMonitor(config)
        
        # 异步检测工作线程
        self.detection_worker = AsyncDetectionWorker(config)
        
        # 内存监控定时器
        self.memory_timer = QTimer()
        self.memory_timer.timeout.connect(self._check_memory)
        self.memory_timer.start(config.memory_check_interval)
        
        # 线程池
        self.thread_pool = ThreadPoolExecutor(max_workers=config.thread_pool_size)
        
    def start_detection_worker(self, detector_func: Callable):
        """启动检测工作线程"""
        self.detection_worker.set_detector(detector_func)
        self.detection_worker.start()
    
    def stop_detection_worker(self):
        """停止检测工作线程"""
        self.detection_worker.stop()
    
    def process_frame(self, image: np.ndarray, task_id: str) -> bool:
        """处理帧"""
        # 帧率控制
        if not self.frame_controller.should_process_frame():
            self.performance_monitor.update_metrics(dropped_frames=
                self.performance_monitor.metrics['dropped_frames'] + 1)
            return False
        
        # 更新总帧数
        self.performance_monitor.update_metrics(total_frames=
            self.performance_monitor.metrics['total_frames'] + 1)
        
        # GPU加速预处理
        if self.config.enable_gpu_acceleration:
            image = self.gpu_accelerator.optimize_image_processing(image)
        
        # 添加到检测队列
        success = self.detection_worker.add_detection_task(image, task_id)
        
        if not success:
            self.performance_monitor.update_metrics(dropped_frames=
                self.performance_monitor.metrics['dropped_frames'] + 1)
        
        # 更新性能指标
        self.performance_monitor.update_metrics(
            fps=self.frame_controller.get_current_fps(),
            queue_size=self.detection_worker.detection_queue.qsize()
        )
        
        return success
    
    def _check_memory(self):
        """检查内存使用情况"""
        memory_usage = self.memory_manager.get_memory_usage()
        self.performance_monitor.update_metrics(memory_usage=memory_usage)
        
        if memory_usage > self.config.max_memory_usage:
            logger.warning(f"内存使用率过高: {memory_usage:.2%}")
            self.memory_manager.cleanup_memory()
    
    def optimize_image_display(self, image: np.ndarray, cache_key: str = None) -> QPixmap:
        """优化图像显示"""
        # 检查缓存
        if cache_key:
            cached_pixmap = self.memory_manager.get_cached_image(cache_key)
            if cached_pixmap:
                return cached_pixmap
        
        # 转换为QPixmap
        if len(image.shape) == 3:
            height, width, channel = image.shape
            bytes_per_line = 3 * width
            q_image = QImage(image.data, width, height, bytes_per_line, 
                           QImage.Format.Format_RGB888).rgbSwapped()
        else:
            height, width = image.shape
            bytes_per_line = width
            q_image = QImage(image.data, width, height, bytes_per_line,
                           QImage.Format.Format_Grayscale8)
        
        pixmap = QPixmap.fromImage(q_image)
        
        # 缓存结果
        if cache_key:
            self.memory_manager.cache_image(cache_key, pixmap)
        
        return pixmap
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        return self.performance_monitor.get_performance_report()
    
    def cleanup(self):
        """清理资源"""
        try:
            # 停止定时器
            self.memory_timer.stop()
            
            # 停止检测工作线程
            self.stop_detection_worker()
            
            # 关闭线程池
            self.thread_pool.shutdown(wait=True)
            
            # 清理内存
            self.memory_manager.cleanup_memory()
            
            logger.info("性能优化器清理完成")
            
        except Exception as e:
            logger.error(f"性能优化器清理失败: {e}")


# 全局性能优化器实例
_global_optimizer: Optional[PerformanceOptimizer] = None


def get_performance_optimizer(config: PerformanceConfig = None) -> PerformanceOptimizer:
    """获取全局性能优化器实例"""
    global _global_optimizer
    
    if _global_optimizer is None:
        if config is None:
            config = PerformanceConfig()
        _global_optimizer = PerformanceOptimizer(config)
    
    return _global_optimizer


def cleanup_global_optimizer():
    """清理全局性能优化器"""
    global _global_optimizer
    
    if _global_optimizer is not None:
        _global_optimizer.cleanup()
        _global_optimizer = None
