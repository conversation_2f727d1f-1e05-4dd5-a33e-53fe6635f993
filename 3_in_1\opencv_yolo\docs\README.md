# 📚 YOLO OpenCV检测器文档索引

## 📋 文档结构

### 🏠 根目录
- [README.md](../README.md) - 项目主要说明文档

### 👥 用户文档 (docs/user/)
- [用户使用手册.md](./user/用户使用手册.md) - 完整的用户使用指南
- [模板制作指南.md](./user/模板制作指南.md) - 模板制作详细教程

### 🔧 开发文档 (docs/development/)
- [开发文档.md](./development/开发文档.md) - 技术设计和开发指南
- [YOLO OpenCV检测器功能特性和工作流程详细说明.md](./development/YOLO%20OpenCV检测器功能特性和工作流程详细说明.md) - 功能特性详细说明
- [源代码编辑器功能说明.md](./development/源代码编辑器功能说明.md) - 源代码编辑器使用说明
- [模板管理和检测可视化说明.md](./development/模板管理和检测可视化说明.md) - 模板管理功能说明
- [GUI检测方法完全复制版本.md](./development/GUI检测方法完全复制版本.md) - GUI检测方法说明
- [STARTUP_SCRIPTS_README.md](./development/STARTUP_SCRIPTS_README.md) - 启动脚本说明
- [TEMPLATE_SYNC_SUCCESS_REPORT.md](./development/TEMPLATE_SYNC_SUCCESS_REPORT.md) - 模板同步成功报告
- [FINAL_CONSISTENCY_ANALYSIS_REPORT.md](./development/FINAL_CONSISTENCY_ANALYSIS_REPORT.md) - 一致性分析报告
- [AI辅助守则——杜绝谎话连篇.md](./development/AI辅助守则——杜绝谎话连篇.md) - AI辅助开发守则

### 📊 报告文档 (docs/reports/)
- [项目清理和帮助系统完成报告.md](./reports/项目清理和帮助系统完成报告.md) - 项目清理报告
- [源代码对话框优化完成报告.md](./reports/源代码对话框优化完成报告.md) - 源代码对话框优化报告
- [代码执行错误和图标问题修复报告.md](./reports/代码执行错误和图标问题修复报告.md) - 代码执行修复报告
- [任务栏图标修复完成报告.md](./reports/任务栏图标修复完成报告.md) - 图标修复报告
- [源代码对话框环境问题解决方案.md](./reports/源代码对话框环境问题解决方案.md) - 环境问题解决方案
- [GUI与源代码检测差异问题解决方案.md](./reports/GUI与源代码检测差异问题解决方案.md) - 检测差异解决方案
- [GUI标签页删除完成报告.md](./reports/GUI标签页删除完成报告.md) - GUI标签页删除报告
- [源代码窗口使用示例更新完成报告.md](./reports/源代码窗口使用示例更新完成报告.md) - 使用示例更新报告
- [模板列表问题解决方案.md](./reports/模板列表问题解决方案.md) - 模板列表问题解决方案
- [标记显示问题解决方案.md](./reports/标记显示问题解决方案.md) - 标记显示问题解决方案

### 📦 归档文档 (docs/archive/)
- [UTF8_ENCODING_FIX_SUMMARY.md](./archive/UTF8_ENCODING_FIX_SUMMARY.md) - UTF8编码修复总结
- [MODULE_IMPORT_FIX_COMPLETE.md](./archive/MODULE_IMPORT_FIX_COMPLETE.md) - 模块导入修复完成
- [README_automation_complete.md](./archive/README_automation_complete.md) - 自动化完成说明
- [TEMPLATE_MATCHING_FIX_COMPLETE.md](./archive/TEMPLATE_MATCHING_FIX_COMPLETE.md) - 模板匹配修复完成
- [DETECTION_CONSISTENCY_FIX_COMPLETE.md](./archive/DETECTION_CONSISTENCY_FIX_COMPLETE.md) - 检测一致性修复完成
- [UTF8_FIX_COMPLETE.md](./archive/UTF8_FIX_COMPLETE.md) - UTF8修复完成

## 📝 文档说明

### 📖 如何使用文档
1. **新用户**: 从[用户使用手册.md](user/用户使用手册.md)开始
2. **开发者**: 查看[开发文档.md](development/开发文档.md)
3. **问题排查**: 参考reports/目录下的解决方案文档
4. **历史记录**: 查看archive/目录下的归档文档

### 🔄 文档维护
- 文档整理时间: 2025-07-06 18:27:53
- 整理工具: organize_documents.py
- 备份位置: docs_backup_[timestamp]/
- 移动日志: docs/move_log.json

### 📞 技术支持
如有文档相关问题，请参考:
- 项目主README.md
- 用户使用手册.md中的技术支持章节
- 开发文档.md中的联系方式
