#!/usr/bin/env python
# -*- coding:utf-8 -*-

from abc import ABC, abstractmethod
import logging
from typing import Optional, Dict, Any
from pywinauto import Application
import win32gui
import re

class WindowLocatorStrategy(ABC):
    """窗口定位策略的基类"""
    
    @abstractmethod
    def generate_code(self, params: Dict[str, Any]) -> str:
        """生成定位代码"""
        pass
    
    @abstractmethod
    def test_location(self, params: Dict[str, Any]) -> Optional[Application]:
        """测试定位是否成功"""
        pass
    
    @property
    @abstractmethod
    def description(self) -> str:
        """策略描述"""
        pass
    
    @property
    @abstractmethod
    def required_params(self) -> Dict[str, str]:
        """所需参数及其描述"""
        pass

class TitleLocator(WindowLocatorStrategy):
    """通过窗口标题定位"""
    
    def generate_code(self, params: Dict[str, Any]) -> str:
        title = params.get('title', '')
        return f"""from pywinauto.application import Application

# 通过窗口标题连接到应用程序
app = Application(backend='uia').connect(title='{title}')
window = app.window(title='{title}')"""
    
    def test_location(self, params: Dict[str, Any]) -> Optional[Application]:
        try:
            title = params.get('title', '')
            app = Application(backend='uia').connect(title=title)
            window = app.window(title=title)
            if window.exists():
                return app
        except Exception as e:
            logging.error(f"通过标题定位窗口失败: {e}")
        return None
    
    @property
    def description(self) -> str:
        return "通过窗口标题精确匹配定位窗口"
    
    @property
    def required_params(self) -> Dict[str, str]:
        return {
            'title': '窗口标题（完全匹配）'
        }

class HandleLocator(WindowLocatorStrategy):
    """通过窗口句柄定位"""
    
    def generate_code(self, params: Dict[str, Any]) -> str:
        handle = params.get('handle', 0)
        return f"""from pywinauto.application import Application

# 通过窗口句柄连接到应用程序
app = Application(backend='uia').connect(handle={handle})
window = app.window(handle={handle})"""
    
    def test_location(self, params: Dict[str, Any]) -> Optional[Application]:
        try:
            handle = params.get('handle', 0)
            app = Application(backend='uia').connect(handle=handle)
            window = app.window(handle=handle)
            if window.exists():
                return app
        except Exception as e:
            logging.error(f"通过句柄定位窗口失败: {e}")
        return None
    
    @property
    def description(self) -> str:
        return "通过窗口句柄定位窗口（最稳定的方式）"
    
    @property
    def required_params(self) -> Dict[str, str]:
        return {
            'handle': '窗口句柄（整数）'
        }

class RegexTitleLocator(WindowLocatorStrategy):
    """通过标题正则表达式定位"""
    
    def generate_code(self, params: Dict[str, Any]) -> str:
        title_pattern = params.get('title_pattern', '')
        return f"""from pywinauto.application import Application
import re

# 通过标题正则表达式连接到应用程序
app = Application(backend='uia').connect(title_re=r'{title_pattern}')
window = app.window(title_re=r'{title_pattern}')"""
    
    def test_location(self, params: Dict[str, Any]) -> Optional[Application]:
        try:
            title_pattern = params.get('title_pattern', '')
            app = Application(backend='uia').connect(title_re=title_pattern)
            window = app.window(title_re=title_pattern)
            if window.exists():
                return app
        except Exception as e:
            logging.error(f"通过标题正则定位窗口失败: {e}")
        return None
    
    @property
    def description(self) -> str:
        return "通过标题正则表达式匹配定位窗口（支持模糊匹配）"
    
    @property
    def required_params(self) -> Dict[str, str]:
        return {
            'title_pattern': '标题正则表达式'
        }

class ProcessLocator(WindowLocatorStrategy):
    """通过进程定位"""
    
    def generate_code(self, params: Dict[str, Any]) -> str:
        process = params.get('process', '')
        return f"""from pywinauto.application import Application

# 通过进程连接到应用程序
app = Application(backend='uia').connect(process={process})
window = app.top_window()"""
    
    def test_location(self, params: Dict[str, Any]) -> Optional[Application]:
        try:
            process = params.get('process', '')
            app = Application(backend='uia').connect(process=process)
            window = app.top_window()
            if window.exists():
                return app
        except Exception as e:
            logging.error(f"通过进程定位窗口失败: {e}")
        return None
    
    @property
    def description(self) -> str:
        return "通过进程ID或进程名定位窗口"
    
    @property
    def required_params(self) -> Dict[str, str]:
        return {
            'process': '进程ID或进程名'
        }

class PathLocator(WindowLocatorStrategy):
    """通过路径定位"""
    
    def generate_code(self, params: Dict[str, Any]) -> str:
        path = params.get('path', '')
        return f"""from pywinauto.application import Application

# 通过路径连接到应用程序
app = Application(backend='uia').start(r'{path}')
window = app.top_window()"""
    
    def test_location(self, params: Dict[str, Any]) -> Optional[Application]:
        try:
            path = params.get('path', '')
            app = Application(backend='uia').start(path)
            window = app.top_window()
            if window.exists():
                return app
        except Exception as e:
            logging.error(f"通过路径定位窗口失败: {e}")
        return None
    
    @property
    def description(self) -> str:
        return "通过应用程序路径启动并定位窗口"
    
    @property
    def required_params(self) -> Dict[str, str]:
        return {
            'path': '应用程序路径'
        }

class CustomLocator(WindowLocatorStrategy):
    """自定义定位策略"""
    
    def generate_code(self, params: Dict[str, Any]) -> str:
        code = params.get('code', '')
        return f"""from pywinauto.application import Application

# 自定义窗口定位代码
{code}"""
    
    def test_location(self, params: Dict[str, Any]) -> Optional[Application]:
        try:
            code = params.get('code', '')
            # 创建一个局部命名空间
            local_vars = {}
            # 执行代码
            exec(code, {'Application': Application}, local_vars)
            # 检查是否定义了 app 和 window
            if 'app' in local_vars and 'window' in local_vars:
                window = local_vars['window']
                if window.exists():
                    return local_vars['app']
        except Exception as e:
            logging.error(f"自定义定位窗口失败: {e}")
        return None
    
    @property
    def description(self) -> str:
        return "使用自定义代码定位窗口（完全自定义）"
    
    @property
    def required_params(self) -> Dict[str, str]:
        return {
            'code': '自定义定位代码（必须定义 app 和 window 变量）'
        }

def get_available_strategies() -> Dict[str, WindowLocatorStrategy]:
    """获取所有可用的定位策略"""
    return {
        'title': TitleLocator(),
        'handle': HandleLocator(),
        'regex': RegexTitleLocator(),
        'process': ProcessLocator(),
        'path': PathLocator(),
        'custom': CustomLocator()
    }

def test_strategy():
    """测试各种定位策略"""
    # 获取当前活动窗口作为测试目标
    hwnd = win32gui.GetForegroundWindow()
    title = win32gui.GetWindowText(hwnd)
    
    strategies = get_available_strategies()
    
    # 测试标题定位
    print("\n测试标题定位:")
    app = strategies['title'].test_location({'title': title})
    print("成功" if app else "失败")
    
    # 测试句柄定位
    print("\n测试句柄定位:")
    app = strategies['handle'].test_location({'handle': hwnd})
    print("成功" if app else "失败")
    
    # 测试正则定位
    print("\n测试正则定位:")
    pattern = re.escape(title)
    app = strategies['regex'].test_location({'title_pattern': pattern})
    print("成功" if app else "失败")
    
    # 生成示例代码
    print("\n生成的代码示例:")
    print(strategies['title'].generate_code({'title': title}))

if __name__ == "__main__":
    test_strategy()
