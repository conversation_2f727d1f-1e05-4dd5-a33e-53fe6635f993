@echo off
:: ============================================================================
:: YOLO OpenCV Detector - Quick Start
:: ============================================================================
:: Simple and fast startup script for experienced users
:: ============================================================================

title YOLO Detector - Quick Start

:: Set colors
color 0B

echo.
echo ========================================
echo    YOLO OpenCV Detector - Quick Start
echo ========================================
echo.

:: Check Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python not found!
    pause
    exit /b 1
)

:: Activate virtual environment
if exist "venv\Scripts\activate.bat" (
    echo Activating virtual environment...
    call venv\Scripts\activate.bat
) else (
    echo Creating virtual environment...
    python -m venv venv
    call venv\Scripts\activate.bat
    echo Installing dependencies...
    pip install -r requirements.txt --quiet
)

:: Create necessary directories
if not exist "models" mkdir models
if not exist "templates" mkdir templates
if not exist "screenshots" mkdir screenshots

:: Launch application
echo.
echo Starting YOLO OpenCV Detector...
echo.

if exist "src\yolo_opencv_detector\main_v2.py" (
    python src\yolo_opencv_detector\main_v2.py
) else if exist "main.py" (
    python main.py
) else (
    echo ERROR: Main application file not found!
    pause
    exit /b 1
)

:: Cleanup
echo.
echo Application closed.
deactivate 2>nul

pause
exit /b 0
