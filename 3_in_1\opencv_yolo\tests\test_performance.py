# -*- coding: utf-8 -*-
"""
性能测试
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

import pytest
import time
import numpy as np
import psutil
import threading
from pathlib import Path
import sys
from unittest.mock import Mock, patch

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from yolo_opencv_detector.utils.config_manager import ConfigManager
from yolo_opencv_detector.core.fusion_engine import FusionEngine
from yolo_opencv_detector.core.nms_algorithms import NMSProcessor, IOUCalculator
from yolo_opencv_detector.core.confidence_fusion import ConfidenceFusion
from yolo_opencv_detector.core.screen_capture import ScreenCaptureService
from yolo_opencv_detector.utils.data_structures import DetectionResult, BoundingBox, DetectionSource


class TestPerformance:
    """性能测试类"""
    
    @pytest.fixture
    def config_manager(self):
        """创建配置管理器"""
        return ConfigManager()
    
    @pytest.fixture
    def large_detection_results(self):
        """创建大量检测结果用于性能测试"""
        results = []
        for i in range(1000):
            # 创建随机分布的检测结果
            x = np.random.randint(0, 1920)
            y = np.random.randint(0, 1080)
            width = np.random.randint(20, 200)
            height = np.random.randint(20, 200)
            confidence = np.random.uniform(0.1, 1.0)
            
            result = DetectionResult(
                bbox=BoundingBox(x, y, width, height),
                confidence=confidence,
                class_id=np.random.randint(0, 80),
                class_name=f"class_{i % 10}",
                source=DetectionSource.YOLO if i % 2 == 0 else DetectionSource.TEMPLATE
            )
            results.append(result)
        
        return results
    
    @pytest.fixture
    def overlapping_results(self):
        """创建重叠的检测结果用于NMS测试"""
        results = []
        base_x, base_y = 100, 100
        
        for i in range(100):
            # 创建在相似位置的重叠检测结果
            x = base_x + np.random.randint(-20, 20)
            y = base_y + np.random.randint(-20, 20)
            width = 50 + np.random.randint(-10, 10)
            height = 50 + np.random.randint(-10, 10)
            confidence = np.random.uniform(0.5, 1.0)
            
            result = DetectionResult(
                bbox=BoundingBox(x, y, width, height),
                confidence=confidence,
                class_id=1,
                class_name="person",
                source=DetectionSource.YOLO
            )
            results.append(result)
        
        return results
    
    def test_fusion_engine_performance(self, large_detection_results):
        """测试融合引擎性能"""
        fusion_engine = FusionEngine()
        
        # 分割结果为YOLO和模板匹配
        yolo_results = [r for r in large_detection_results if r.source == DetectionSource.YOLO]
        template_results = [r for r in large_detection_results if r.source == DetectionSource.TEMPLATE]
        
        # 性能测试
        start_time = time.time()
        
        for _ in range(10):  # 执行10次融合
            fused_results = fusion_engine.fuse_results(yolo_results, template_results)
        
        end_time = time.time()
        total_time = end_time - start_time
        avg_time = total_time / 10
        
        print(f"融合引擎平均处理时间: {avg_time:.4f}秒")
        print(f"处理速度: {len(yolo_results + template_results) / avg_time:.1f} 结果/秒")
        
        # 性能要求：平均处理时间应小于1秒
        assert avg_time < 1.0
        
        # 获取性能统计
        stats = fusion_engine.get_performance_stats()
        assert "avg_fusion_time" in stats
        assert stats["avg_fusion_time"] < 1.0
    
    def test_nms_performance(self, overlapping_results):
        """测试NMS算法性能"""
        nms_processor = NMSProcessor()
        
        # 测试标准NMS
        start_time = time.time()
        
        for _ in range(100):  # 执行100次NMS
            nms_results = nms_processor.standard_nms(overlapping_results, iou_threshold=0.5)
        
        end_time = time.time()
        total_time = end_time - start_time
        avg_time = total_time / 100
        
        print(f"标准NMS平均处理时间: {avg_time:.4f}秒")
        print(f"处理速度: {len(overlapping_results) / avg_time:.1f} 结果/秒")
        
        # 性能要求：平均处理时间应小于0.1秒
        assert avg_time < 0.1
        
        # 测试Soft NMS
        start_time = time.time()
        
        for _ in range(50):  # Soft NMS较慢，执行50次
            soft_nms_results = nms_processor.soft_nms(overlapping_results, iou_threshold=0.5)
        
        end_time = time.time()
        soft_nms_time = (end_time - start_time) / 50
        
        print(f"Soft NMS平均处理时间: {soft_nms_time:.4f}秒")
        
        # Soft NMS通常比标准NMS慢，但应该在合理范围内
        assert soft_nms_time < 0.5
    
    def test_iou_calculation_performance(self):
        """测试IoU计算性能"""
        iou_calculator = IOUCalculator()
        
        # 创建大量边界框对
        bbox_pairs = []
        for _ in range(10000):
            bbox1 = BoundingBox(
                np.random.randint(0, 1000),
                np.random.randint(0, 1000),
                np.random.randint(20, 200),
                np.random.randint(20, 200)
            )
            bbox2 = BoundingBox(
                np.random.randint(0, 1000),
                np.random.randint(0, 1000),
                np.random.randint(20, 200),
                np.random.randint(20, 200)
            )
            bbox_pairs.append((bbox1, bbox2))
        
        # 测试标准IoU
        start_time = time.time()
        
        for bbox1, bbox2 in bbox_pairs:
            iou = iou_calculator.calculate_iou(bbox1, bbox2)
        
        end_time = time.time()
        iou_time = end_time - start_time
        
        print(f"IoU计算总时间: {iou_time:.4f}秒")
        print(f"IoU计算速度: {len(bbox_pairs) / iou_time:.1f} 计算/秒")
        
        # 性能要求：应该能够快速计算IoU
        assert len(bbox_pairs) / iou_time > 10000  # 至少10000计算/秒
        
        # 测试其他IoU变体
        start_time = time.time()
        
        for bbox1, bbox2 in bbox_pairs[:1000]:  # 测试较少数量
            giou = iou_calculator.calculate_giou(bbox1, bbox2)
            diou = iou_calculator.calculate_diou(bbox1, bbox2)
            ciou = iou_calculator.calculate_ciou(bbox1, bbox2)
        
        end_time = time.time()
        advanced_iou_time = end_time - start_time
        
        print(f"高级IoU计算时间: {advanced_iou_time:.4f}秒")
        
        # 高级IoU计算较慢，但应该在合理范围内
        assert advanced_iou_time < 1.0
    
    def test_confidence_fusion_performance(self):
        """测试置信度融合性能"""
        confidence_fusion = ConfidenceFusion()
        
        # 创建大量置信度数据
        test_cases = []
        for _ in range(1000):
            confidences = np.random.uniform(0.1, 1.0, np.random.randint(2, 10))
            weights = np.random.uniform(0.1, 1.0, len(confidences))
            test_cases.append((confidences.tolist(), weights.tolist()))
        
        # 测试加权平均融合
        start_time = time.time()
        
        for confidences, weights in test_cases:
            fused = confidence_fusion.fuse_confidences(confidences, weights)
        
        end_time = time.time()
        fusion_time = end_time - start_time
        
        print(f"置信度融合总时间: {fusion_time:.4f}秒")
        print(f"融合速度: {len(test_cases) / fusion_time:.1f} 融合/秒")
        
        # 性能要求：应该能够快速融合置信度
        assert len(test_cases) / fusion_time > 1000  # 至少1000融合/秒
    
    @patch('yolo_opencv_detector.core.screen_capture.mss')
    def test_screen_capture_performance(self, mock_mss, config_manager):
        """测试屏幕截取性能"""
        # 模拟MSS
        mock_sct = Mock()
        mock_mss.mss.return_value = mock_sct
        
        # 模拟截图数据
        mock_screenshot = {
            'width': 1920,
            'height': 1080,
            'rgb': np.random.randint(0, 255, (1080, 1920, 3), dtype=np.uint8).tobytes()
        }
        mock_sct.grab.return_value = mock_screenshot
        
        # 创建屏幕截取服务
        capture_service = ScreenCaptureService(config_manager)
        
        # 性能测试
        start_time = time.time()
        
        for _ in range(100):  # 执行100次截图
            screenshot = capture_service.capture_screen()
        
        end_time = time.time()
        capture_time = end_time - start_time
        avg_time = capture_time / 100
        
        print(f"屏幕截取平均时间: {avg_time:.4f}秒")
        print(f"截图FPS: {1 / avg_time:.1f}")
        
        # 性能要求：应该能够达到至少10FPS
        assert 1 / avg_time >= 10
    
    def test_memory_usage_under_load(self, large_detection_results):
        """测试负载下的内存使用"""
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        fusion_engine = FusionEngine()
        
        # 执行大量融合操作
        for i in range(100):
            # 分割结果
            yolo_results = large_detection_results[:500]
            template_results = large_detection_results[500:]
            
            # 执行融合
            fused_results = fusion_engine.fuse_results(yolo_results, template_results)
            
            # 每10次检查内存
            if i % 10 == 0:
                current_memory = process.memory_info().rss / 1024 / 1024  # MB
                memory_increase = current_memory - initial_memory
                print(f"迭代 {i}: 内存使用 {current_memory:.1f}MB (+{memory_increase:.1f}MB)")
                
                # 内存增长应该在合理范围内
                assert memory_increase < 500  # 不超过500MB
        
        # 最终内存检查
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        total_increase = final_memory - initial_memory
        
        print(f"总内存增长: {total_increase:.1f}MB")
        
        # 总内存增长应该在合理范围内
        assert total_increase < 200  # 不超过200MB
    
    def test_concurrent_performance(self, large_detection_results):
        """测试并发性能"""
        import queue
        import threading
        
        result_queue = queue.Queue()
        
        def worker(worker_id, results):
            start_time = time.time()
            
            fusion_engine = FusionEngine()
            
            # 分割结果
            yolo_results = [r for r in results if r.source == DetectionSource.YOLO]
            template_results = [r for r in results if r.source == DetectionSource.TEMPLATE]
            
            # 执行多次融合
            for _ in range(10):
                fused_results = fusion_engine.fuse_results(yolo_results, template_results)
            
            end_time = time.time()
            worker_time = end_time - start_time
            
            result_queue.put((worker_id, worker_time))
        
        # 创建多个工作线程
        threads = []
        num_workers = 4
        
        start_time = time.time()
        
        for i in range(num_workers):
            thread = threading.Thread(target=worker, args=(i, large_detection_results))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # 收集结果
        worker_times = []
        while not result_queue.empty():
            worker_id, worker_time = result_queue.get()
            worker_times.append(worker_time)
            print(f"工作线程 {worker_id} 完成时间: {worker_time:.4f}秒")
        
        print(f"并发执行总时间: {total_time:.4f}秒")
        print(f"平均工作线程时间: {np.mean(worker_times):.4f}秒")
        
        # 并发执行应该比串行执行快
        assert len(worker_times) == num_workers
        assert total_time < sum(worker_times)  # 并发应该比串行快
    
    def test_scalability(self):
        """测试可扩展性"""
        fusion_engine = FusionEngine()
        
        # 测试不同规模的数据
        scales = [10, 50, 100, 500, 1000]
        times = []
        
        for scale in scales:
            # 创建指定规模的数据
            yolo_results = []
            template_results = []
            
            for i in range(scale):
                yolo_result = DetectionResult(
                    bbox=BoundingBox(i*10, i*10, 50, 50),
                    confidence=0.8,
                    class_id=1,
                    source=DetectionSource.YOLO
                )
                yolo_results.append(yolo_result)
                
                template_result = DetectionResult(
                    bbox=BoundingBox(i*10+5, i*10+5, 45, 45),
                    confidence=0.7,
                    template_id="test",
                    source=DetectionSource.TEMPLATE
                )
                template_results.append(template_result)
            
            # 测试处理时间
            start_time = time.time()
            
            for _ in range(5):  # 执行5次取平均
                fused_results = fusion_engine.fuse_results(yolo_results, template_results)
            
            end_time = time.time()
            avg_time = (end_time - start_time) / 5
            times.append(avg_time)
            
            print(f"规模 {scale}: 平均处理时间 {avg_time:.4f}秒")
        
        # 分析时间复杂度
        # 理想情况下，时间应该与数据规模呈线性或接近线性关系
        for i in range(1, len(scales)):
            scale_ratio = scales[i] / scales[i-1]
            time_ratio = times[i] / times[i-1]
            
            print(f"规模比例 {scale_ratio:.1f}x, 时间比例 {time_ratio:.1f}x")
            
            # 时间增长不应该超过规模增长的平方
            assert time_ratio <= scale_ratio ** 2
    
    def test_fps_benchmark(self, config_manager):
        """测试FPS基准"""
        fusion_engine = FusionEngine()
        
        # 模拟实时检测场景
        frame_count = 100
        start_time = time.time()
        
        for frame in range(frame_count):
            # 模拟每帧的检测结果
            yolo_results = [
                DetectionResult(
                    bbox=BoundingBox(100 + frame, 100 + frame, 50, 50),
                    confidence=0.8,
                    class_id=1,
                    source=DetectionSource.YOLO
                )
            ]
            
            template_results = [
                DetectionResult(
                    bbox=BoundingBox(105 + frame, 105 + frame, 45, 45),
                    confidence=0.7,
                    template_id="test",
                    source=DetectionSource.TEMPLATE
                )
            ]
            
            # 执行融合
            fused_results = fusion_engine.fuse_results(yolo_results, template_results)
        
        end_time = time.time()
        total_time = end_time - start_time
        fps = frame_count / total_time
        
        print(f"处理 {frame_count} 帧，总时间: {total_time:.4f}秒")
        print(f"平均FPS: {fps:.1f}")
        
        # 性能要求：应该能够达到至少30FPS
        assert fps >= 30
