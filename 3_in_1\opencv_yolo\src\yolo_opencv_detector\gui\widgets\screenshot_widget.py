# -*- coding: utf-8 -*-
"""
截图显示组件
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

from typing import List, Dict, Any, Optional, Tuple
from PyQt6.QtWidgets import QWidget, QLabel, QVBoxLayout, QScrollArea, QHBoxLayout, QPushButton, QFileDialog
from PyQt6.QtCore import Qt, pyqtSignal, QRect, QPoint
from PyQt6.QtGui import (
    QPixmap, QPainter, QPen, QColor, QBrush, QFont,
    QMouseEvent, QPaintEvent, QWheelEvent
)
from pathlib import Path
import glob

from ...utils.logger import Logger


class ScreenshotWidget(QScrollArea):
    """截图显示组件类"""
    
    # 信号定义
    region_selected = pyqtSignal(dict)  # 区域选择信号
    point_clicked = pyqtSignal(dict)    # 点击信号
    
    def __init__(self):
        """初始化截图显示组件"""
        super().__init__()

        self.logger = Logger().get_logger(__name__)

        # 截图浏览相关
        self.screenshot_files = []
        self.current_index = -1
        self.screenshots_dir = Path("screenshots")

        # 创建主容器
        main_widget = QWidget()
        main_layout = QVBoxLayout(main_widget)

        # 添加浏览控制按钮
        self._create_browse_controls(main_layout)

        # 显示组件
        self.image_label = ScreenshotLabel()
        scroll_area = QScrollArea()
        scroll_area.setWidget(self.image_label)
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        main_layout.addWidget(scroll_area)

        # 设置主容器
        self.setWidget(main_widget)
        self.setWidgetResizable(True)

        # 连接信号
        self.image_label.region_selected.connect(self.region_selected)
        self.image_label.point_clicked.connect(self.point_clicked)
        self.image_label.zoom_changed.connect(self._on_zoom_changed)

        # 初始化截图列表
        self._refresh_screenshot_list()

        self.logger.info("截图显示组件初始化完成")

    def _create_browse_controls(self, parent_layout: QVBoxLayout) -> None:
        """创建浏览控制按钮"""
        controls_layout = QHBoxLayout()

        # 上一张按钮
        self.prev_button = QPushButton("◀ 上一张")
        self.prev_button.clicked.connect(self._show_previous)
        self.prev_button.setEnabled(False)
        self.prev_button.setToolTip("查看上一张截图\n"
                                   "按时间顺序浏览历史截图\n"
                                   "快捷键：左方向键")
        controls_layout.addWidget(self.prev_button)

        # 当前位置标签
        self.position_label = QLabel("无截图")
        self.position_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.position_label.setToolTip("显示当前截图位置\n"
                                      "格式：当前张数/总张数\n"
                                      "实时更新截图数量")
        controls_layout.addWidget(self.position_label)

        # 下一张按钮
        self.next_button = QPushButton("下一张 ▶")
        self.next_button.clicked.connect(self._show_next)
        self.next_button.setEnabled(False)
        self.next_button.setToolTip("查看下一张截图\n"
                                   "按时间顺序浏览截图\n"
                                   "快捷键：右方向键")
        controls_layout.addWidget(self.next_button)

        # 刷新按钮
        self.refresh_button = QPushButton("🔄 刷新")
        self.refresh_button.clicked.connect(self._refresh_screenshot_list)
        self.refresh_button.setToolTip("刷新截图列表\n"
                                      "重新扫描screenshots文件夹\n"
                                      "更新最新的截图文件")
        controls_layout.addWidget(self.refresh_button)

        # 打开文件夹按钮
        self.open_folder_button = QPushButton("📁 打开文件夹")
        self.open_folder_button.clicked.connect(self._open_screenshots_folder)
        self.open_folder_button.setToolTip("打开截图文件夹\n"
                                          "在文件管理器中查看所有截图\n"
                                          "可以手动管理截图文件")
        controls_layout.addWidget(self.open_folder_button)

        parent_layout.addLayout(controls_layout)

        # 添加缩放控制
        self._create_zoom_controls(parent_layout)

    def _create_zoom_controls(self, parent_layout: QVBoxLayout) -> None:
        """创建缩放控制按钮"""
        zoom_layout = QHBoxLayout()

        # 缩放标签
        zoom_label = QLabel("🔍 缩放控制:")
        zoom_layout.addWidget(zoom_label)

        # 放大按钮
        self.zoom_in_button = QPushButton("🔍+ 放大")
        self.zoom_in_button.clicked.connect(self.zoom_in)
        self.zoom_in_button.setToolTip("放大图像\n"
                                      "快捷键：Ctrl + +\n"
                                      "或鼠标滚轮向上")
        zoom_layout.addWidget(self.zoom_in_button)

        # 缩小按钮
        self.zoom_out_button = QPushButton("🔍- 缩小")
        self.zoom_out_button.clicked.connect(self.zoom_out)
        self.zoom_out_button.setToolTip("缩小图像\n"
                                       "快捷键：Ctrl + -\n"
                                       "或鼠标滚轮向下")
        zoom_layout.addWidget(self.zoom_out_button)

        # 实际大小按钮
        self.actual_size_button = QPushButton("📐 实际大小")
        self.actual_size_button.clicked.connect(self.actual_size)
        self.actual_size_button.setToolTip("显示图像实际大小\n"
                                          "快捷键：Ctrl + 0\n"
                                          "100%缩放比例")
        zoom_layout.addWidget(self.actual_size_button)

        # 适应窗口按钮
        self.fit_window_button = QPushButton("🖼️ 适应窗口")
        self.fit_window_button.clicked.connect(self.fit_to_window)
        self.fit_window_button.setToolTip("适应窗口大小\n"
                                         "快捷键：Ctrl + F\n"
                                         "自动调整到最佳显示")
        zoom_layout.addWidget(self.fit_window_button)

        # 缩放比例显示
        self.zoom_info_label = QLabel("100%")
        self.zoom_info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.zoom_info_label.setMinimumWidth(60)
        self.zoom_info_label.setStyleSheet("""
            QLabel {
                border: 1px solid #ccc;
                border-radius: 4px;
                padding: 4px;
                background-color: #f8f9fa;
                font-weight: bold;
            }
        """)
        self.zoom_info_label.setToolTip("当前缩放比例\n"
                                       "实时显示图像缩放状态")
        zoom_layout.addWidget(self.zoom_info_label)

        # 平移提示
        pan_hint_label = QLabel("💡 提示: Ctrl+左键拖拽平移")
        pan_hint_label.setStyleSheet("color: #666; font-size: 11px;")
        zoom_layout.addWidget(pan_hint_label)

        zoom_layout.addStretch()  # 添加弹性空间
        parent_layout.addLayout(zoom_layout)

    def _refresh_screenshot_list(self) -> None:
        """刷新截图列表"""
        try:
            if not self.screenshots_dir.exists():
                self.screenshots_dir.mkdir(exist_ok=True)

            # 获取所有截图文件，按修改时间排序（最新的在前）
            pattern = str(self.screenshots_dir / "*.png")
            files = glob.glob(pattern)
            files.sort(key=lambda x: Path(x).stat().st_mtime, reverse=True)

            self.screenshot_files = [Path(f) for f in files]

            # 更新界面
            self._update_controls()

            # 如果有截图且当前没有显示，显示最新的
            if self.screenshot_files and self.current_index == -1:
                self._show_latest()

            self.logger.debug(f"刷新截图列表: 找到 {len(self.screenshot_files)} 个文件")

        except Exception as e:
            self.logger.error(f"刷新截图列表失败: {e}")

    def _update_controls(self) -> None:
        """更新控制按钮状态"""
        total = len(self.screenshot_files)

        if total == 0:
            self.position_label.setText("无截图")
            self.prev_button.setEnabled(False)
            self.next_button.setEnabled(False)
        else:
            current = self.current_index + 1 if self.current_index >= 0 else 0
            self.position_label.setText(f"{current}/{total}")
            self.prev_button.setEnabled(self.current_index > 0)
            self.next_button.setEnabled(self.current_index < total - 1)

    def _show_previous(self) -> None:
        """显示上一张截图"""
        if self.current_index > 0:
            self.current_index -= 1
            self._load_current_screenshot()

    def _show_next(self) -> None:
        """显示下一张截图"""
        if self.current_index < len(self.screenshot_files) - 1:
            self.current_index += 1
            self._load_current_screenshot()

    def _show_latest(self) -> None:
        """显示最新的截图"""
        if self.screenshot_files:
            self.current_index = 0  # 最新的在索引0
            self._load_current_screenshot()

    def _load_current_screenshot(self) -> None:
        """加载当前索引的截图"""
        try:
            if 0 <= self.current_index < len(self.screenshot_files):
                file_path = self.screenshot_files[self.current_index]
                if file_path.exists():
                    pixmap = QPixmap(str(file_path))
                    if not pixmap.isNull():
                        self.image_label.set_screenshot(pixmap, str(file_path))
                        self._update_controls()
                        self.logger.debug(f"加载截图: {file_path.name}")
                    else:
                        self.logger.warning(f"无法加载截图: {file_path}")
                else:
                    self.logger.warning(f"截图文件不存在: {file_path}")
        except Exception as e:
            self.logger.error(f"加载截图失败: {e}")

    def _open_screenshots_folder(self) -> None:
        """打开截图文件夹"""
        try:
            import os
            import subprocess
            import platform

            folder_path = str(self.screenshots_dir.absolute())

            if platform.system() == "Windows":
                os.startfile(folder_path)
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", folder_path])
            else:  # Linux
                subprocess.run(["xdg-open", folder_path])

        except Exception as e:
            self.logger.error(f"打开文件夹失败: {e}")

    def set_screenshot(self, screenshot: QPixmap, filepath: str = None) -> None:
        """
        设置截图

        Args:
            screenshot: 截图像素图
            filepath: 截图文件路径
        """
        self.image_label.set_screenshot(screenshot, filepath)

        # 如果提供了文件路径，刷新列表并定位到该文件
        if filepath:
            self._refresh_screenshot_list()
            # 尝试找到该文件在列表中的位置
            file_path = Path(filepath)
            for i, screenshot_file in enumerate(self.screenshot_files):
                if screenshot_file.samefile(file_path):
                    self.current_index = i
                    self._update_controls()
                    break
    
    def set_detection_results(self, results: List[Dict[str, Any]]) -> None:
        """
        设置检测结果
        
        Args:
            results: 检测结果列表
        """
        self.image_label.set_detection_results(results)
    
    def clear_results(self) -> None:
        """清除检测结果"""
        self.image_label.clear_results()
    
    def zoom_in(self) -> None:
        """放大"""
        self.image_label.zoom_in()
    
    def zoom_out(self) -> None:
        """缩小"""
        self.image_label.zoom_out()
    
    def reset_zoom(self) -> None:
        """重置缩放"""
        self.image_label.reset_zoom()
    
    def fit_to_window(self) -> None:
        """适应窗口"""
        self.image_label.fit_to_window()

    def actual_size(self) -> None:
        """实际大小"""
        self.image_label.actual_size()

    def set_selection_enabled(self, enabled: bool) -> None:
        """设置区域选择是否启用"""
        self.image_label.set_selection_enabled(enabled)

    def _on_zoom_changed(self, zoom_factor: float) -> None:
        """处理缩放变化"""
        try:
            percentage = int(zoom_factor * 100)
            self.zoom_info_label.setText(f"{percentage}%")

            # 更新按钮状态
            self.zoom_in_button.setEnabled(zoom_factor < self.image_label.max_zoom)
            self.zoom_out_button.setEnabled(zoom_factor > self.image_label.min_zoom)

        except Exception as e:
            self.logger.error(f"处理缩放变化失败: {e}")


class ScreenshotLabel(QLabel):
    """截图标签类"""
    
    # 信号定义
    region_selected = pyqtSignal(dict)
    point_clicked = pyqtSignal(dict)
    zoom_changed = pyqtSignal(float)  # 缩放变化信号
    
    def __init__(self):
        """初始化截图标签"""
        super().__init__()
        
        self.logger = Logger().get_logger(__name__)
        
        # 图像相关
        self.original_pixmap: Optional[QPixmap] = None
        self.scaled_pixmap: Optional[QPixmap] = None
        self.scale_factor = 1.0
        self.screenshot_filepath = None

        # 缩放控制
        self.min_zoom = 0.1
        self.max_zoom = 5.0
        self.zoom_step = 0.25

        # 平移控制
        self.pan_start_point = None
        self.is_panning = False
        self.pan_offset_x = 0
        self.pan_offset_y = 0
        
        # 检测结果
        self.detection_results: List[Dict[str, Any]] = []
        
        # 区域选择
        self.selecting_region = False
        self.selection_start: Optional[QPoint] = None
        self.selection_end: Optional[QPoint] = None
        self.selection_rect: Optional[QRect] = None
        self.selection_enabled = True
        
        # 显示设置
        self.show_results = True
        self.show_confidence = True
        self.show_labels = True
        
        # 颜色设置
        self.bbox_colors = {
            "yolo": QColor(0, 255, 0),      # 绿色
            "template": QColor(255, 0, 0),   # 红色
            "fusion": QColor(0, 0, 255)     # 蓝色
        }
        
        # 设置标签属性
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.setMinimumSize(400, 300)
        self.setStyleSheet("QLabel { border: 1px solid gray; background-color: white; }")

        # 设置缩放模式 - 保持宽高比并适应容器
        self.setScaledContents(False)  # 不自动缩放内容，我们手动控制

        # 启用鼠标跟踪和键盘焦点
        self.setMouseTracking(True)
        self.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
    
    def set_screenshot(self, screenshot: QPixmap, filepath: str = None) -> None:
        """设置截图"""
        try:
            # 清除旧的显示内容
            self.clear()

            self.original_pixmap = screenshot
            self.screenshot_filepath = filepath

            # 计算合适的缩放因子
            self._calculate_scale_factor()
            self._update_display()

            # 记录截图信息
            if screenshot and not screenshot.isNull():
                size = screenshot.size()
                info_msg = f"截图已更新: {size.width()}×{size.height()}"
                if filepath:
                    from pathlib import Path
                    filename = Path(filepath).name
                    info_msg += f", 文件: {filename}"
                self.logger.info(info_msg)
            else:
                self.logger.warning("设置了无效的截图")

        except Exception as e:
            self.logger.error(f"设置截图失败: {e}")
    
    def set_detection_results(self, results: List[Any]) -> None:
        """设置检测结果"""
        try:
            # 确保所有结果都是字典格式
            normalized_results = []
            for i, result in enumerate(results):
                try:
                    if hasattr(result, 'to_dict') and callable(result.to_dict):
                        # DetectionResult对象，转换为字典
                        normalized_result = result.to_dict()
                        self.logger.debug(f"结果 {i}: DetectionResult对象已转换为字典")
                    elif isinstance(result, dict):
                        # 已经是字典，直接使用
                        normalized_result = result
                        self.logger.debug(f"结果 {i}: 字典格式，直接使用")
                    else:
                        # 未知类型，尝试转换或跳过
                        self.logger.warning(f"结果 {i}: 未知类型 {type(result)}，尝试转换")
                        if hasattr(result, '__dict__'):
                            # 如果有__dict__属性，尝试转换
                            normalized_result = result.__dict__
                        else:
                            # 无法转换，跳过此结果
                            self.logger.error(f"结果 {i}: 无法转换类型 {type(result)}，跳过")
                            continue

                    normalized_results.append(normalized_result)

                except Exception as e:
                    self.logger.error(f"处理结果 {i} 时出错: {e}")
                    continue

            self.detection_results = normalized_results
            self._update_display()
            self.logger.debug(f"检测结果已更新: {len(normalized_results)} 个结果 (原始: {len(results)})")

        except Exception as e:
            self.logger.error(f"设置检测结果失败: {e}")
            # 设置空结果以避免后续错误
            self.detection_results = []
            self._update_display()
    
    def clear_results(self) -> None:
        """清除检测结果"""
        self.detection_results.clear()
        self._update_display()
    
    def zoom_in(self) -> None:
        """放大"""
        new_scale = self.scale_factor * (1 + self.zoom_step)
        if new_scale <= self.max_zoom:
            self.scale_factor = new_scale
            self._update_display()
            self.zoom_changed.emit(self.scale_factor)

    def zoom_out(self) -> None:
        """缩小"""
        new_scale = self.scale_factor / (1 + self.zoom_step)
        if new_scale >= self.min_zoom:
            self.scale_factor = new_scale
            self._update_display()
            self.zoom_changed.emit(self.scale_factor)

    def reset_zoom(self) -> None:
        """重置缩放"""
        self.scale_factor = 1.0
        self.pan_offset_x = 0
        self.pan_offset_y = 0
        self._update_display()
        self.zoom_changed.emit(self.scale_factor)

    def fit_to_window(self) -> None:
        """适应窗口大小"""
        if not self.original_pixmap:
            return

        # 计算适应窗口的缩放因子
        widget_size = self.size()
        pixmap_size = self.original_pixmap.size()

        scale_x = widget_size.width() / pixmap_size.width()
        scale_y = widget_size.height() / pixmap_size.height()

        # 选择较小的缩放因子以确保图像完全显示
        self.scale_factor = min(scale_x, scale_y, 1.0)
        self.pan_offset_x = 0
        self.pan_offset_y = 0
        self._update_display()
        self.zoom_changed.emit(self.scale_factor)

    def actual_size(self) -> None:
        """实际大小"""
        self.scale_factor = 1.0
        self.pan_offset_x = 0
        self.pan_offset_y = 0
        self._update_display()
        self.zoom_changed.emit(self.scale_factor)

    def set_selection_enabled(self, enabled: bool) -> None:
        """设置区域选择是否启用"""
        self.selection_enabled = enabled

    def _start_panning(self, pos: QPoint) -> None:
        """开始平移"""
        self.is_panning = True
        self.pan_start_point = pos
        self.setCursor(Qt.CursorShape.ClosedHandCursor)

    def _update_panning(self, pos: QPoint) -> None:
        """更新平移"""
        if self.is_panning and self.pan_start_point:
            delta_x = pos.x() - self.pan_start_point.x()
            delta_y = pos.y() - self.pan_start_point.y()

            self.pan_offset_x += delta_x
            self.pan_offset_y += delta_y

            self.pan_start_point = pos
            self._update_display()

    def _stop_panning(self) -> None:
        """停止平移"""
        self.is_panning = False
        self.pan_start_point = None
        self.setCursor(Qt.CursorShape.ArrowCursor)
    
    def _calculate_scale_factor(self) -> None:
        """计算合适的缩放因子"""
        if not self.original_pixmap:
            self.scale_factor = 1.0
            return

        # 获取父容器大小
        parent_widget = self.parent()
        if parent_widget and hasattr(parent_widget, 'size'):
            container_size = parent_widget.size()
            # 留出边距
            available_width = max(container_size.width() - 40, 400)
            available_height = max(container_size.height() - 40, 300)
        else:
            # 默认大小
            available_width = 800
            available_height = 600

        pixmap_size = self.original_pixmap.size()

        scale_x = available_width / pixmap_size.width()
        scale_y = available_height / pixmap_size.height()
        self.scale_factor = min(scale_x, scale_y, 1.0)  # 不放大，只缩小

    def fit_to_window(self) -> None:
        """适应窗口"""
        self._calculate_scale_factor()
        self._update_display()
    
    def _update_display(self) -> None:
        """更新显示"""
        try:
            if not self.original_pixmap:
                self.setText("无截图")
                return

            # 缩放图像
            scaled_size = self.original_pixmap.size() * self.scale_factor
            self.scaled_pixmap = self.original_pixmap.scaled(
                scaled_size,
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation
            )

            # 绘制检测结果
            if self.detection_results and self.show_results:
                self._draw_detection_results()

            # 清除文本，使用paintEvent绘制
            self.setText("")

            # 触发重绘
            self.update()

        except Exception as e:
            self.logger.error(f"更新显示失败: {e}")
    
    def _draw_detection_results(self) -> None:
        """绘制检测结果"""
        try:
            if not self.scaled_pixmap or not self.detection_results:
                return
            
            painter = QPainter(self.scaled_pixmap)
            painter.setRenderHint(QPainter.RenderHint.Antialiasing)
            
            # 设置字体
            font = QFont()
            font.setPointSize(10)
            font.setBold(True)
            painter.setFont(font)
            
            for result in self.detection_results:
                self._draw_single_result(painter, result)
            
            painter.end()
            
        except Exception as e:
            self.logger.error(f"绘制检测结果失败: {e}")
    
    def _draw_single_result(self, painter: QPainter, result: Any) -> None:
        """绘制单个检测结果"""
        try:
            # 检查结果类型并适当处理
            if hasattr(result, 'to_dict') and callable(result.to_dict):
                # 如果是DetectionResult对象，转换为字典
                self.logger.debug("检测到DetectionResult对象，转换为字典")
                result_dict = result.to_dict()
            elif isinstance(result, dict):
                # 如果已经是字典，直接使用
                result_dict = result
            else:
                # 未知类型，记录错误并返回
                self.logger.error(f"未知的结果类型: {type(result)}")
                return

            # 获取边界框信息
            bbox = result_dict.get("bbox", {})

            # 处理不同格式的bbox
            if isinstance(bbox, (list, tuple)) and len(bbox) >= 4:
                # 如果是列表或元组格式 [x, y, width, height]
                x = bbox[0] * self.scale_factor
                y = bbox[1] * self.scale_factor
                width = bbox[2] * self.scale_factor
                height = bbox[3] * self.scale_factor
            elif isinstance(bbox, dict):
                # 如果是字典格式 {"x": x, "y": y, "width": width, "height": height}
                x = bbox.get("x", 0) * self.scale_factor
                y = bbox.get("y", 0) * self.scale_factor
                width = bbox.get("width", 0) * self.scale_factor
                height = bbox.get("height", 0) * self.scale_factor
            else:
                # 未知格式，记录错误并返回
                self.logger.error(f"未知的边界框格式: {type(bbox)}, 值: {bbox}")
                return

            # 获取结果信息
            confidence = result_dict.get("confidence", 0.0)
            class_name = result_dict.get("class_name", "")
            source = result_dict.get("source", "unknown")

            # 选择颜色
            color = self.bbox_colors.get(source.lower(), QColor(128, 128, 128))
            
            # 绘制边界框
            pen = QPen(color, 2)
            painter.setPen(pen)
            painter.drawRect(int(x), int(y), int(width), int(height))
            
            # 绘制标签背景
            if self.show_labels and (class_name or self.show_confidence):
                label_text = ""
                if class_name:
                    label_text += class_name
                if self.show_confidence:
                    if label_text:
                        label_text += f" ({confidence:.2f})"
                    else:
                        label_text = f"{confidence:.2f}"
                
                # 计算文本尺寸
                text_rect = painter.fontMetrics().boundingRect(label_text)
                label_rect = QRect(
                    int(x), int(y) - text_rect.height() - 4,
                    text_rect.width() + 8, text_rect.height() + 4
                )
                
                # 确保标签在图像内
                if label_rect.y() < 0:
                    label_rect.moveTop(int(y) + int(height))
                
                # 绘制标签背景
                brush = QBrush(color)
                painter.fillRect(label_rect, brush)
                
                # 绘制文本
                painter.setPen(QPen(QColor(255, 255, 255)))
                painter.drawText(
                    label_rect.x() + 4,
                    label_rect.y() + text_rect.height(),
                    label_text
                )

        except Exception as e:
            # 详细的错误日志，包含结果类型和内容信息
            result_type = type(result).__name__
            result_info = str(result)[:200] + "..." if len(str(result)) > 200 else str(result)
            self.logger.error(f"绘制单个结果失败: {e}")
            self.logger.error(f"结果类型: {result_type}")
            self.logger.error(f"结果内容: {result_info}")

            # 如果是AttributeError，提供更具体的诊断信息
            if isinstance(e, AttributeError):
                if "'list' object has no attribute 'get'" in str(e):
                    self.logger.error("检测到list对象被当作dict使用，这通常表示数据类型转换问题")
                elif "'DetectionResult' object has no attribute 'get'" in str(e):
                    self.logger.error("检测到DetectionResult对象未被正确转换为字典")

            # 记录调用栈以便调试
            import traceback
            self.logger.debug(f"错误调用栈: {traceback.format_exc()}")
    
    def mousePressEvent(self, event: QMouseEvent) -> None:
        """鼠标按下事件"""
        try:
            if event.button() == Qt.MouseButton.LeftButton:
                modifiers = event.modifiers()
                if modifiers == Qt.KeyboardModifier.ControlModifier:
                    # Ctrl+左键：平移
                    self._start_panning(event.pos())
                elif modifiers == Qt.KeyboardModifier.NoModifier:
                    if self.selection_enabled:
                        # 普通左键：区域选择
                        self.selecting_region = True
                        self.selection_start = event.pos()
                        self.selection_end = event.pos()
                        self.selection_rect = QRect()
                    else:
                        # 如果区域选择被禁用，则平移
                        self._start_panning(event.pos())

            elif event.button() == Qt.MouseButton.MiddleButton:
                # 中键：平移
                self._start_panning(event.pos())

            elif event.button() == Qt.MouseButton.RightButton:
                # 发送点击信号
                pos = self._widget_to_image_coords(event.pos())
                self.point_clicked.emit({
                    "x": pos.x(),
                    "y": pos.y(),
                    "button": "right"
                })

            super().mousePressEvent(event)

        except Exception as e:
            self.logger.error(f"鼠标按下事件处理失败: {e}")
    
    def mouseMoveEvent(self, event: QMouseEvent) -> None:
        """鼠标移动事件"""
        try:
            if self.is_panning:
                # 平移模式
                self._update_panning(event.pos())
            elif self.selecting_region and self.selection_start:
                # 区域选择模式
                self.selection_end = event.pos()
                self.selection_rect = QRect(self.selection_start, self.selection_end).normalized()
                self.update()  # 触发重绘

            super().mouseMoveEvent(event)

        except Exception as e:
            self.logger.error(f"鼠标移动事件处理失败: {e}")
    
    def mouseReleaseEvent(self, event: QMouseEvent) -> None:
        """鼠标释放事件"""
        try:
            if event.button() == Qt.MouseButton.LeftButton:
                if self.is_panning:
                    # 停止平移
                    self._stop_panning()
                elif self.selecting_region:
                    # 完成区域选择
                    self.selecting_region = False

                    if self.selection_rect and self.selection_rect.width() > 5 and self.selection_rect.height() > 5:
                        # 转换为图像坐标
                        image_rect = self._widget_to_image_rect(self.selection_rect)

                        # 发送区域选择信号
                        self.region_selected.emit({
                            "x": image_rect.x(),
                            "y": image_rect.y(),
                            "width": image_rect.width(),
                            "height": image_rect.height()
                        })

                    self.selection_rect = None
                    self.update()

            elif event.button() == Qt.MouseButton.MiddleButton:
                if self.is_panning:
                    self._stop_panning()

            super().mouseReleaseEvent(event)

        except Exception as e:
            self.logger.error(f"鼠标释放事件处理失败: {e}")
    
    def wheelEvent(self, event: QWheelEvent) -> None:
        """鼠标滚轮事件"""
        try:
            # 缩放（Ctrl+滚轮 或 单独滚轮）
            if event.modifiers() & Qt.KeyboardModifier.ControlModifier or not self.selection_enabled:
                delta = event.angleDelta().y()
                if delta > 0:
                    self.zoom_in()
                else:
                    self.zoom_out()
                event.accept()
            else:
                super().wheelEvent(event)

        except Exception as e:
            self.logger.error(f"滚轮事件处理失败: {e}")

    def keyPressEvent(self, event) -> None:
        """键盘事件"""
        try:
            modifiers = event.modifiers()
            key = event.key()

            if modifiers == Qt.KeyboardModifier.ControlModifier:
                if key == Qt.Key.Key_Plus or key == Qt.Key.Key_Equal:
                    self.zoom_in()
                    event.accept()
                    return
                elif key == Qt.Key.Key_Minus:
                    self.zoom_out()
                    event.accept()
                    return
                elif key == Qt.Key.Key_0:
                    self.actual_size()
                    event.accept()
                    return
                elif key == Qt.Key.Key_F:
                    self.fit_to_window()
                    event.accept()
                    return

            super().keyPressEvent(event)

        except Exception as e:
            self.logger.error(f"键盘事件处理失败: {e}")
    
    def paintEvent(self, event: QPaintEvent) -> None:
        """绘制事件 - 支持平移"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # 填充背景
        painter.fillRect(self.rect(), QColor(248, 249, 250))

        if self.scaled_pixmap and not self.scaled_pixmap.isNull():
            # 计算图像绘制位置（考虑平移偏移）
            widget_size = self.size()
            pixmap_size = self.scaled_pixmap.size()

            # 居中位置
            center_x = (widget_size.width() - pixmap_size.width()) // 2
            center_y = (widget_size.height() - pixmap_size.height()) // 2

            # 应用平移偏移
            draw_x = center_x + self.pan_offset_x
            draw_y = center_y + self.pan_offset_y

            # 绘制图像
            painter.drawPixmap(draw_x, draw_y, self.scaled_pixmap)

        elif not self.original_pixmap:
            # 绘制无截图提示
            painter.setPen(QColor(150, 150, 150))
            font = QFont()
            font.setPointSize(14)
            painter.setFont(font)
            painter.drawText(self.rect(), Qt.AlignmentFlag.AlignCenter, "无截图")

        # 绘制选择框
        if self.selecting_region and self.selection_rect:
            pen = QPen(QColor(255, 255, 0), 2, Qt.PenStyle.DashLine)
            painter.setPen(pen)
            painter.drawRect(self.selection_rect)
    
    def _widget_to_image_coords(self, widget_pos: QPoint) -> QPoint:
        """将组件坐标转换为图像坐标"""
        if not self.original_pixmap or self.scale_factor == 0:
            return widget_pos

        # 计算图像在组件中的位置
        widget_size = self.size()
        pixmap_size = self.scaled_pixmap.size() if self.scaled_pixmap else self.original_pixmap.size()

        # 居中位置
        center_x = (widget_size.width() - pixmap_size.width()) // 2
        center_y = (widget_size.height() - pixmap_size.height()) // 2

        # 应用平移偏移
        image_start_x = center_x + self.pan_offset_x
        image_start_y = center_y + self.pan_offset_y

        # 转换为图像坐标
        relative_x = widget_pos.x() - image_start_x
        relative_y = widget_pos.y() - image_start_y

        # 缩放到原始图像坐标
        image_x = int(relative_x / self.scale_factor)
        image_y = int(relative_y / self.scale_factor)

        # 限制在图像范围内
        image_x = max(0, min(image_x, self.original_pixmap.width() - 1))
        image_y = max(0, min(image_y, self.original_pixmap.height() - 1))

        return QPoint(image_x, image_y)
    
    def _widget_to_image_rect(self, widget_rect: QRect) -> QRect:
        """将组件矩形转换为图像矩形"""
        if not self.original_pixmap or self.scale_factor == 0:
            return widget_rect
        
        top_left = self._widget_to_image_coords(widget_rect.topLeft())
        bottom_right = self._widget_to_image_coords(widget_rect.bottomRight())
        
        return QRect(top_left, bottom_right)
    
    def toggle_results_display(self, show: bool) -> None:
        """切换检测结果显示"""
        self.show_results = show
        self._update_display()
    
    def toggle_confidence_display(self, show: bool) -> None:
        """切换置信度显示"""
        self.show_confidence = show
        self._update_display()
    
    def toggle_labels_display(self, show: bool) -> None:
        """切换标签显示"""
        self.show_labels = show
        self._update_display()
    
    def set_bbox_color(self, source: str, color: QColor) -> None:
        """设置边界框颜色"""
        self.bbox_colors[source.lower()] = color
        self._update_display()
