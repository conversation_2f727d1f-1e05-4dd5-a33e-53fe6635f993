# -*- coding: utf-8 -*-
"""
快速测试脚本
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

import sys
import time
import numpy as np
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

def test_config_manager():
    """测试配置管理器"""
    print("🔧 测试配置管理器...")
    try:
        from yolo_opencv_detector.utils.config_manager import ConfigManager
        
        config = ConfigManager()
        print(f"   ✅ 配置加载成功")
        print(f"   📊 检测配置: {config.detection.confidence_threshold}")
        return True
    except Exception as e:
        print(f"   ❌ 配置管理器测试失败: {e}")
        return False

def test_logger():
    """测试日志系统"""
    print("\n📝 测试日志系统...")
    try:
        from yolo_opencv_detector.utils.logger import Logger
        
        logger = Logger().get_logger("test")
        logger.info("测试日志消息")
        print("   ✅ 日志系统正常")
        return True
    except Exception as e:
        print(f"   ❌ 日志系统测试失败: {e}")
        return False

def test_data_structures():
    """测试数据结构"""
    print("\n📊 测试数据结构...")
    try:
        from yolo_opencv_detector.utils.data_structures import (
            DetectionResult, BoundingBox, DetectionSource
        )
        
        bbox = BoundingBox(10, 20, 100, 200)
        result = DetectionResult(
            bbox=bbox,
            confidence=0.9,
            class_id=1,
            class_name="test",
            source=DetectionSource.YOLO
        )
        
        print(f"   ✅ 数据结构创建成功")
        print(f"   📦 边界框中心: ({bbox.center_x}, {bbox.center_y})")
        return True
    except Exception as e:
        print(f"   ❌ 数据结构测试失败: {e}")
        return False

def test_screen_capture():
    """测试屏幕截取"""
    print("\n📸 测试屏幕截取...")
    try:
        from yolo_opencv_detector.core.screen_capture import ScreenCaptureService
        from yolo_opencv_detector.utils.config_manager import ConfigManager
        
        config = ConfigManager()
        capture_service = ScreenCaptureService(config)
        
        # 尝试截图
        screenshot = capture_service.capture_screen()
        if screenshot is not None:
            print(f"   ✅ 屏幕截取成功")
            print(f"   📐 截图尺寸: {screenshot.shape}")
            return True
        else:
            print("   ❌ 屏幕截取失败")
            return False
    except Exception as e:
        print(f"   ❌ 屏幕截取测试失败: {e}")
        return False

def test_yolo_detector():
    """测试YOLO检测器"""
    print("\n🎯 测试YOLO检测器...")
    try:
        from yolo_opencv_detector.core.yolo_detector import YOLODetector
        from yolo_opencv_detector.utils.config_manager import ConfigManager
        
        config = ConfigManager()
        
        # 检查是否有YOLO模型
        model_path = Path("models/yolov8n.pt")
        if not model_path.exists():
            print("   ⚠️  YOLO模型文件不存在，跳过检测器测试")
            print("   💡 请运行: python scripts/download_models.py")
            return True
        
        detector = YOLODetector(config)
        
        # 创建测试图像
        test_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
        
        # 执行检测
        results = detector.detect(test_image)
        print(f"   ✅ YOLO检测器创建成功")
        print(f"   🔍 检测结果数量: {len(results)}")
        return True
    except Exception as e:
        print(f"   ❌ YOLO检测器测试失败: {e}")
        return False

def test_template_matcher():
    """测试模板匹配器"""
    print("\n🔍 测试模板匹配器...")
    try:
        from yolo_opencv_detector.core.template_matcher import TemplateMatcher
        from yolo_opencv_detector.utils.config_manager import ConfigManager
        
        config = ConfigManager()
        matcher = TemplateMatcher(config)
        
        # 创建测试图像和模板
        test_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
        test_template = np.random.randint(0, 255, (50, 50, 3), dtype=np.uint8)
        
        # 添加模板
        matcher.add_template("test_template", test_template)
        
        # 执行匹配
        results = matcher.match_all(test_image)
        print(f"   ✅ 模板匹配器创建成功")
        print(f"   🎯 匹配结果数量: {len(results)}")
        return True
    except Exception as e:
        print(f"   ❌ 模板匹配器测试失败: {e}")
        return False

def test_fusion_engine():
    """测试融合引擎"""
    print("\n🧠 测试融合引擎...")
    try:
        from yolo_opencv_detector.core.fusion_engine import FusionEngine
        from yolo_opencv_detector.utils.data_structures import (
            DetectionResult, BoundingBox, DetectionSource
        )
        
        fusion_engine = FusionEngine()
        
        # 创建测试结果
        yolo_results = [
            DetectionResult(
                bbox=BoundingBox(100, 100, 50, 50),
                confidence=0.9,
                class_id=1,
                source=DetectionSource.YOLO
            )
        ]
        
        template_results = [
            DetectionResult(
                bbox=BoundingBox(105, 105, 45, 45),
                confidence=0.8,
                template_id="test",
                source=DetectionSource.TEMPLATE
            )
        ]
        
        # 执行融合
        fused_results = fusion_engine.fuse_results(yolo_results, template_results)
        print(f"   ✅ 融合引擎创建成功")
        print(f"   🔗 融合结果数量: {len(fused_results)}")
        return True
    except Exception as e:
        print(f"   ❌ 融合引擎测试失败: {e}")
        return False

def test_gui_imports():
    """测试GUI模块导入"""
    print("\n🖥️  测试GUI模块导入...")
    try:
        # 测试PyQt6导入
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import Qt
        
        # 测试GUI模块导入
        from yolo_opencv_detector.gui.main_window import MainWindow
        from yolo_opencv_detector.gui.widgets.detection_panel import DetectionPanel
        
        print("   ✅ GUI模块导入成功")
        return True
    except Exception as e:
        print(f"   ❌ GUI模块导入失败: {e}")
        return False

def performance_benchmark():
    """性能基准测试"""
    print("\n⚡ 性能基准测试...")
    try:
        from yolo_opencv_detector.core.fusion_engine import FusionEngine
        from yolo_opencv_detector.utils.data_structures import (
            DetectionResult, BoundingBox, DetectionSource
        )
        
        fusion_engine = FusionEngine()
        
        # 创建大量测试数据
        yolo_results = []
        template_results = []
        
        for i in range(100):
            yolo_results.append(DetectionResult(
                bbox=BoundingBox(i*5, i*5, 50, 50),
                confidence=0.8 + i*0.001,
                class_id=1,
                source=DetectionSource.YOLO
            ))
            
            template_results.append(DetectionResult(
                bbox=BoundingBox(i*5+2, i*5+2, 48, 48),
                confidence=0.7 + i*0.001,
                template_id="test",
                source=DetectionSource.TEMPLATE
            ))
        
        # 性能测试
        start_time = time.time()
        for _ in range(10):
            fused_results = fusion_engine.fuse_results(yolo_results, template_results)
        end_time = time.time()
        
        avg_time = (end_time - start_time) / 10
        print(f"   ✅ 融合性能测试完成")
        print(f"   ⏱️  平均处理时间: {avg_time:.4f}秒")
        print(f"   📊 处理速度: {len(yolo_results + template_results) / avg_time:.1f} 结果/秒")
        
        return avg_time < 1.0  # 要求1秒内完成
    except Exception as e:
        print(f"   ❌ 性能基准测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 YOLO OpenCV检测器快速测试")
    print("=" * 50)
    
    tests = [
        ("配置管理器", test_config_manager),
        ("日志系统", test_logger),
        ("数据结构", test_data_structures),
        ("屏幕截取", test_screen_capture),
        ("YOLO检测器", test_yolo_detector),
        ("模板匹配器", test_template_matcher),
        ("融合引擎", test_fusion_engine),
        ("GUI模块", test_gui_imports),
        ("性能基准", performance_benchmark)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"   ❌ {test_name}测试出现异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有核心功能测试通过！")
        return 0
    elif passed >= total * 0.8:
        print("⚠️  大部分功能正常，可以继续开发")
        return 0
    else:
        print("❌ 核心功能存在问题，需要修复")
        return 1

if __name__ == "__main__":
    sys.exit(main())
