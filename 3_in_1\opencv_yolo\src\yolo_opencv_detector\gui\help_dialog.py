# -*- coding: utf-8 -*-
"""
帮助对话框 - 提供操作指导
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
    QLabel, QTextEdit, QTabWidget, QWidget, QScrollArea
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont, QPixmap

from ..utils.logger import Logger


class HelpDialog(QDialog):
    """帮助对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = Logger().get_logger(__name__)
        self._setup_ui()
        
    def _setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("📖 使用帮助")
        self.setModal(True)
        self.resize(800, 600)
        
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("🎯 YOLO OpenCV 检测器 - 使用指南")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet(
            "QLabel { "
            "color: #2c3e50; "
            "padding: 15px; "
            "background-color: #ecf0f1; "
            "border-radius: 8px; "
            "margin-bottom: 10px; "
            "}"
        )
        layout.addWidget(title_label)
        
        # 创建标签页
        tab_widget = QTabWidget()
        tab_widget.setStyleSheet(
            "QTabWidget::pane { "
            "border: 1px solid #bdc3c7; "
            "border-radius: 5px; "
            "} "
            "QTabBar::tab { "
            "background-color: #ecf0f1; "
            "padding: 10px 20px; "
            "margin-right: 2px; "
            "border-top-left-radius: 5px; "
            "border-top-right-radius: 5px; "
            "} "
            "QTabBar::tab:selected { "
            "background-color: #3498db; "
            "color: white; "
            "}"
        )
        
        # 快速入门标签页
        tab_widget.addTab(self._create_quick_start_tab(), "🚀 快速入门")
        
        # 模板管理标签页
        tab_widget.addTab(self._create_template_tab(), "📋 模板管理")
        
        # 检测配置标签页
        tab_widget.addTab(self._create_detection_tab(), "⚙️ 检测配置")
        
        # 常见问题标签页
        tab_widget.addTab(self._create_faq_tab(), "❓ 常见问题")
        
        layout.addWidget(tab_widget)
        
        # 关闭按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        close_btn = QPushButton("✅ 关闭")
        close_btn.setMinimumHeight(40)
        close_btn.setMinimumWidth(100)
        close_btn.setStyleSheet(
            "QPushButton { "
            "background-color: #3498db; "
            "color: white; "
            "border: none; "
            "border-radius: 5px; "
            "font-size: 14px; "
            "font-weight: bold; "
            "} "
            "QPushButton:hover { background-color: #2980b9; }"
        )
        close_btn.clicked.connect(self.accept)
        button_layout.addWidget(close_btn)
        
        layout.addLayout(button_layout)
    
    def _create_quick_start_tab(self):
        """创建快速入门标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        content = QTextEdit()
        content.setReadOnly(True)
        content.setHtml("""
        <h2 style="color: #2c3e50;">🚀 快速入门指南</h2>
        
        <h3 style="color: #3498db;">📋 基本使用流程</h3>
        <ol style="line-height: 1.6;">
            <li><strong>步骤1: 创建检测模板</strong>
                <ul>
                    <li>点击模板面板中的 <span style="background-color: #e67e22; color: white; padding: 2px 6px; border-radius: 3px;">📷 截取模板</span> 按钮</li>
                    <li>选择 <strong>🖥️ 屏幕区域截取</strong> 或 <strong>📁 文件导入</strong></li>
                    <li>如选择屏幕截取，在屏幕上拖拽选择需要检测的区域</li>
                    <li>输入模板名称和描述，设置匹配阈值</li>
                    <li>点击 <strong>✅ 创建模板</strong> 完成</li>
                </ul>
            </li>
            
            <li><strong>步骤2: 配置检测参数</strong>
                <ul>
                    <li>在配置面板中调整 <strong>置信度阈值</strong> 和 <strong>NMS阈值</strong></li>
                    <li>设置 <strong>模板匹配阈值</strong> 以控制匹配精度</li>
                    <li>根据需要启用 <strong>结果融合</strong> 功能</li>
                </ul>
            </li>
            
            <li><strong>步骤3: 开始检测</strong>
                <ul>
                    <li>点击检测面板中的 <span style="background-color: #27ae60; color: white; padding: 2px 6px; border-radius: 3px;">▶️ 开始检测</span> 按钮</li>
                    <li>或者点击 <span style="background-color: #3498db; color: white; padding: 2px 6px; border-radius: 3px;">📷 立即截图</span> 进行单次检测</li>
                    <li>在结果面板中查看检测结果</li>
                </ul>
            </li>
        </ol>
        
        <h3 style="color: #e74c3c;">⚠️ 重要提示</h3>
        <ul style="line-height: 1.6;">
            <li>首次使用前请确保已安装所需的依赖包</li>
            <li>模板图像应选择具有明显特征的区域，避免纯色或重复图案</li>
            <li>建议模板尺寸在 50×50 到 300×300 像素之间</li>
            <li>检测精度与模板质量和参数设置密切相关</li>
        </ul>
        """)
        
        layout.addWidget(content)
        return widget
    
    def _create_template_tab(self):
        """创建模板管理标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        content = QTextEdit()
        content.setReadOnly(True)
        content.setHtml("""
        <h2 style="color: #2c3e50;">📋 模板管理详解</h2>
        
        <h3 style="color: #3498db;">🎯 什么是检测模板？</h3>
        <p style="line-height: 1.6;">
        检测模板是用于识别特定图像特征的参考图像。系统会在屏幕截图中寻找与模板相似的区域。
        </p>
        
        <h3 style="color: #3498db;">📷 创建模板的方法</h3>
        
        <h4 style="color: #e67e22;">方法1: 屏幕区域截取</h4>
        <ol style="line-height: 1.6;">
            <li>点击 <strong>📷 截取模板</strong> 按钮</li>
            <li>选择 <strong>🖥️ 屏幕区域截取</strong></li>
            <li>屏幕会显示半透明覆盖层</li>
            <li>用鼠标拖拽选择需要的区域</li>
            <li>释放鼠标完成选择</li>
            <li>在对话框中输入模板信息</li>
        </ol>
        
        <h4 style="color: #e67e22;">方法2: 文件导入</h4>
        <ol style="line-height: 1.6;">
            <li>点击 <strong>📷 截取模板</strong> 按钮</li>
            <li>选择 <strong>📁 文件导入</strong></li>
            <li>选择本地图像文件（支持 PNG、JPG、BMP 等格式）</li>
            <li>在预览中确认图像</li>
            <li>输入模板信息并创建</li>
        </ol>
        
        <h3 style="color: #3498db;">⚙️ 模板参数说明</h3>
        <ul style="line-height: 1.6;">
            <li><strong>模板名称</strong>: 用于识别和管理模板的唯一名称</li>
            <li><strong>描述</strong>: 模板的详细说明，便于后续管理</li>
            <li><strong>匹配阈值</strong>: 控制匹配的严格程度，值越高要求越严格</li>
        </ul>
        
        <h3 style="color: #3498db;">🎨 模板选择技巧</h3>
        <ul style="line-height: 1.6;">
            <li>选择具有独特特征的区域，如图标、按钮、文字等</li>
            <li>避免选择纯色区域或重复性图案</li>
            <li>确保模板区域在不同情况下保持相对稳定</li>
            <li>模板尺寸建议在 50×50 到 300×300 像素之间</li>
        </ul>
        """)
        
        layout.addWidget(content)
        return widget
    
    def _create_detection_tab(self):
        """创建检测配置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        content = QTextEdit()
        content.setReadOnly(True)
        content.setHtml("""
        <h2 style="color: #2c3e50;">⚙️ 检测配置说明</h2>
        
        <h3 style="color: #3498db;">🎯 YOLO 配置参数</h3>
        
        <h4 style="color: #e67e22;">置信度阈值 (Confidence Threshold)</h4>
        <ul style="line-height: 1.6;">
            <li><strong>作用</strong>: 控制YOLO检测的置信度要求</li>
            <li><strong>范围</strong>: 0.01 - 1.00</li>
            <li><strong>建议值</strong>: 0.3 - 0.7</li>
            <li><strong>调整原则</strong>: 值越高检测越严格，漏检可能增加；值越低误检可能增加</li>
        </ul>
        
        <h4 style="color: #e67e22;">NMS阈值 (Non-Maximum Suppression)</h4>
        <ul style="line-height: 1.6;">
            <li><strong>作用</strong>: 控制重叠检测框的过滤程度</li>
            <li><strong>范围</strong>: 0.01 - 1.00</li>
            <li><strong>建议值</strong>: 0.4 - 0.6</li>
            <li><strong>调整原则</strong>: 值越低过滤越严格，重叠框越少</li>
        </ul>
        
        <h4 style="color: #e67e22;">最大检测数</h4>
        <ul style="line-height: 1.6;">
            <li><strong>作用</strong>: 限制单次检测的最大目标数量</li>
            <li><strong>范围</strong>: 1 - 1000</li>
            <li><strong>建议值</strong>: 50 - 200</li>
        </ul>
        
        <h3 style="color: #3498db;">📋 模板匹配配置</h3>
        
        <h4 style="color: #e67e22;">匹配阈值</h4>
        <ul style="line-height: 1.6;">
            <li><strong>作用</strong>: 控制模板匹配的严格程度</li>
            <li><strong>范围</strong>: 0.1 - 1.0</li>
            <li><strong>建议值</strong>: 0.7 - 0.9</li>
            <li><strong>调整原则</strong>: 值越高匹配越严格</li>
        </ul>
        
        <h3 style="color: #3498db;">🔄 结果融合配置</h3>
        
        <h4 style="color: #e67e22;">IoU阈值</h4>
        <ul style="line-height: 1.6;">
            <li><strong>作用</strong>: 控制YOLO和模板匹配结果的融合条件</li>
            <li><strong>范围</strong>: 0.01 - 1.00</li>
            <li><strong>建议值</strong>: 0.3 - 0.7</li>
        </ul>
        
        <h4 style="color: #e67e22;">权重配置</h4>
        <ul style="line-height: 1.6;">
            <li><strong>YOLO权重</strong>: YOLO检测结果的权重比例</li>
            <li><strong>模板权重</strong>: 模板匹配结果的权重比例</li>
            <li><strong>调整原则</strong>: 根据实际效果调整两者的权重比例</li>
        </ul>
        """)
        
        layout.addWidget(content)
        return widget
    
    def _create_faq_tab(self):
        """创建常见问题标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        content = QTextEdit()
        content.setReadOnly(True)
        content.setHtml("""
        <h2 style="color: #2c3e50;">❓ 常见问题解答</h2>
        
        <h3 style="color: #e74c3c;">Q1: 检测效果不理想怎么办？</h3>
        <p style="line-height: 1.6;">
        <strong>A:</strong> 可以尝试以下方法：<br>
        • 调整置信度阈值和NMS阈值<br>
        • 重新创建质量更好的模板<br>
        • 调整模板匹配阈值<br>
        • 检查模板是否选择了具有独特特征的区域
        </p>
        
        <h3 style="color: #e74c3c;">Q2: 模板匹配总是失败？</h3>
        <p style="line-height: 1.6;">
        <strong>A:</strong> 请检查：<br>
        • 模板图像是否清晰且具有明显特征<br>
        • 匹配阈值是否设置过高<br>
        • 目标区域是否发生了显著变化<br>
        • 尝试降低匹配阈值到0.6-0.7
        </p>
        
        <h3 style="color: #e74c3c;">Q3: 检测速度太慢？</h3>
        <p style="line-height: 1.6;">
        <strong>A:</strong> 可以尝试：<br>
        • 减少最大检测数<br>
        • 提高置信度阈值以减少候选框<br>
        • 减少模板数量<br>
        • 关闭不必要的结果融合功能
        </p>
        
        <h3 style="color: #e74c3c;">Q4: 误检太多怎么办？</h3>
        <p style="line-height: 1.6;">
        <strong>A:</strong> 建议：<br>
        • 提高置信度阈值<br>
        • 提高模板匹配阈值<br>
        • 重新选择更具特征性的模板区域<br>
        • 调整NMS阈值以过滤重叠检测
        </p>
        
        <h3 style="color: #e74c3c;">Q5: 如何获得最佳检测效果？</h3>
        <p style="line-height: 1.6;">
        <strong>A:</strong> 最佳实践：<br>
        • 使用高质量、特征明显的模板<br>
        • 根据实际场景调整参数<br>
        • 启用结果融合以提高准确性<br>
        • 定期更新和优化模板<br>
        • 在不同场景下测试和调整参数
        </p>
        
        <h3 style="color: #e74c3c;">Q6: 程序崩溃或出错？</h3>
        <p style="line-height: 1.6;">
        <strong>A:</strong> 故障排除：<br>
        • 检查是否安装了所有必需的依赖包<br>
        • 确认YOLO模型文件路径正确<br>
        • 查看状态面板中的错误日志<br>
        • 重启程序并重新配置<br>
        • 检查系统资源是否充足
        </p>
        """)
        
        layout.addWidget(content)
        return widget
