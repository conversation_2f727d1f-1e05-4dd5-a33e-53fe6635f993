# -*- coding: utf-8 -*-
"""
模板匹配器模块
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

import cv2
import numpy as np
from pathlib import Path
from typing import List, Optional, Union, Tuple, Dict, Any
from PIL import Image
import time
import math

from ..utils.logger import Logger, performance_timer
from ..utils.data_structures import DetectionResult, BoundingBox, DetectionSource, TemplateInfo
from ..utils.constants import (
    DEFAULT_TEMPLATE_THRESHOLD, DEFAULT_SCALE_RANGE, DEFAULT_SCALE_STEPS,
    DEFAULT_ANGLE_RANGE, DEFAULT_ANGLE_STEPS, TemplateMatchMethods
)


class TemplateMatcher:
    """模板匹配器类"""
    
    def __init__(self,
                 threshold: float = DEFAULT_TEMPLATE_THRESHOLD,
                 method: str = TemplateMatchMethods.CCOEFF_NORMED.value,
                 scale_range: Tuple[float, float] = DEFAULT_SCALE_RANGE,
                 scale_steps: int = DEFAULT_SCALE_STEPS,
                 angle_range: Tuple[float, float] = DEFAULT_ANGLE_RANGE,
                 angle_steps: int = DEFAULT_ANGLE_STEPS,
                 enable_preprocessing: bool = True):
        """
        初始化模板匹配器
        
        Args:
            threshold: 匹配阈值
            method: 匹配方法
            scale_range: 缩放范围 (min_scale, max_scale)
            scale_steps: 缩放步数
            angle_range: 旋转角度范围 (min_angle, max_angle)
            angle_steps: 旋转步数
            enable_preprocessing: 是否启用预处理
        """
        self.logger = Logger().get_logger(__name__)
        
        # 参数设置
        self.threshold = threshold
        self.method = self._resolve_method(method)
        self.scale_range = scale_range
        self.scale_steps = scale_steps
        self.angle_range = angle_range
        self.angle_steps = angle_steps
        self.enable_preprocessing = enable_preprocessing
        
        # 性能统计
        self.match_times: List[float] = []
        self.total_matches = 0
        
        self.logger.info(f"模板匹配器初始化完成 - 方法: {method}, 阈值: {threshold}")
    
    def _resolve_method(self, method: str) -> int:
        """解析匹配方法"""
        method_map = {
            "cv2.TM_CCOEFF": cv2.TM_CCOEFF,
            "cv2.TM_CCOEFF_NORMED": cv2.TM_CCOEFF_NORMED,
            "cv2.TM_CCORR": cv2.TM_CCORR,
            "cv2.TM_CCORR_NORMED": cv2.TM_CCORR_NORMED,
            "cv2.TM_SQDIFF": cv2.TM_SQDIFF,
            "cv2.TM_SQDIFF_NORMED": cv2.TM_SQDIFF_NORMED
        }
        
        if method in method_map:
            return method_map[method]
        else:
            self.logger.warning(f"未知的匹配方法: {method}，使用默认方法")
            return cv2.TM_CCOEFF_NORMED
    
    @performance_timer("模板匹配")
    def match(self, 
              image: Union[np.ndarray, Image.Image, str, Path],
              template: Union[np.ndarray, Image.Image, str, Path, TemplateInfo],
              template_id: Optional[str] = None) -> List[DetectionResult]:
        """
        执行模板匹配
        
        Args:
            image: 输入图像
            template: 模板图像或模板信息
            template_id: 模板ID
            
        Returns:
            List[DetectionResult]: 匹配结果列表
        """
        try:
            start_time = time.time()
            
            # 预处理图像和模板
            processed_image = self._preprocess_image(image)
            processed_template, template_info = self._preprocess_template(template)
            
            if processed_image is None or processed_template is None:
                return []
            
            # 执行多尺度多角度匹配
            matches = self._multi_scale_match(processed_image, processed_template, template_info)
            
            # 记录性能统计
            match_time = time.time() - start_time
            self.match_times.append(match_time)
            self.total_matches += len(matches)
            
            self.logger.debug(f"模板匹配完成 - 找到 {len(matches)} 个匹配，耗时 {match_time:.4f}s")
            
            return matches
            
        except Exception as e:
            self.logger.error(f"模板匹配失败: {e}")
            return []
    
    def _preprocess_image(self, image: Union[np.ndarray, Image.Image, str, Path]) -> Optional[np.ndarray]:
        """预处理输入图像"""
        try:
            # 处理不同类型的输入
            if isinstance(image, (str, Path)):
                image_path = Path(image)
                if not image_path.exists():
                    self.logger.error(f"图像文件不存在: {image_path}")
                    return None
                image = cv2.imread(str(image_path))
                if image is None:
                    self.logger.error(f"无法读取图像文件: {image_path}")
                    return None
                    
            elif isinstance(image, Image.Image):
                image = np.array(image)
                if image.ndim == 3 and image.shape[2] == 3:
                    # RGB转BGR
                    image = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
                    
            elif isinstance(image, np.ndarray):
                if image.ndim == 3 and image.shape[2] == 4:
                    # RGBA转BGR
                    image = cv2.cvtColor(image, cv2.COLOR_RGBA2BGR)
                elif image.ndim == 3 and image.shape[2] == 3:
                    # 假设已经是BGR格式
                    pass
                elif image.ndim == 2:
                    # 灰度图转BGR
                    image = cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)
            
            # 预处理
            if self.enable_preprocessing:
                image = self._apply_preprocessing(image)
            
            return image
            
        except Exception as e:
            self.logger.error(f"图像预处理失败: {e}")
            return None
    
    def _preprocess_template(self, template: Union[np.ndarray, Image.Image, str, Path, TemplateInfo]) -> Tuple[Optional[np.ndarray], Optional[Dict[str, Any]]]:
        """预处理模板"""
        try:
            template_info = {}
            
            # 处理TemplateInfo对象
            if isinstance(template, TemplateInfo):
                template_info = template.to_dict()
                template = template.file_path
            
            # 处理不同类型的输入
            if isinstance(template, (str, Path)):
                template_path = Path(template)
                if not template_path.exists():
                    self.logger.error(f"模板文件不存在: {template_path}")
                    return None, None
                template = cv2.imread(str(template_path))
                if template is None:
                    self.logger.error(f"无法读取模板文件: {template_path}")
                    return None, None
                template_info["file_path"] = str(template_path)
                    
            elif isinstance(template, Image.Image):
                template = np.array(template)
                if template.ndim == 3 and template.shape[2] == 3:
                    template = cv2.cvtColor(template, cv2.COLOR_RGB2BGR)
                    
            elif isinstance(template, np.ndarray):
                if template.ndim == 3 and template.shape[2] == 4:
                    template = cv2.cvtColor(template, cv2.COLOR_RGBA2BGR)
                elif template.ndim == 2:
                    template = cv2.cvtColor(template, cv2.COLOR_GRAY2BGR)
            
            # 预处理
            if self.enable_preprocessing:
                template = self._apply_preprocessing(template)
            
            # 记录模板尺寸
            if template is not None:
                template_info["size"] = template.shape[:2]
            
            return template, template_info
            
        except Exception as e:
            self.logger.error(f"模板预处理失败: {e}")
            return None, None
    
    def _apply_preprocessing(self, image: np.ndarray) -> np.ndarray:
        """应用预处理"""
        try:
            # 转换为灰度图
            if image.ndim == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()
            
            # 直方图均衡化
            gray = cv2.equalizeHist(gray)
            
            # 高斯滤波降噪
            gray = cv2.GaussianBlur(gray, (3, 3), 0)
            
            return gray
            
        except Exception as e:
            self.logger.error(f"预处理失败: {e}")
            return image

    def _multi_scale_match(self, image: np.ndarray, template: np.ndarray,
                          template_info: Dict[str, Any]) -> List[DetectionResult]:
        """多尺度多角度匹配"""
        all_matches = []

        try:
            # 生成缩放比例
            scales = np.linspace(self.scale_range[0], self.scale_range[1], self.scale_steps)

            # 生成旋转角度
            if self.angle_steps > 1:
                angles = np.linspace(self.angle_range[0], self.angle_range[1], self.angle_steps)
            else:
                angles = [0]

            for scale in scales:
                for angle in angles:
                    # 变换模板
                    transformed_template = self._transform_template(template, scale, angle)
                    if transformed_template is None:
                        continue

                    # 检查模板尺寸
                    if (transformed_template.shape[0] > image.shape[0] or
                        transformed_template.shape[1] > image.shape[1]):
                        continue

                    # 执行匹配
                    matches = self._single_match(image, transformed_template, scale, angle, template_info)
                    all_matches.extend(matches)

            # 非极大值抑制
            filtered_matches = self._apply_nms(all_matches)

            return filtered_matches

        except Exception as e:
            self.logger.error(f"多尺度匹配失败: {e}")
            return []

    def _transform_template(self, template: np.ndarray, scale: float, angle: float) -> Optional[np.ndarray]:
        """变换模板（缩放和旋转）"""
        try:
            h, w = template.shape[:2]

            # 缩放
            new_w, new_h = int(w * scale), int(h * scale)
            if new_w < 5 or new_h < 5:  # 避免模板过小
                return None

            scaled_template = cv2.resize(template, (new_w, new_h))

            # 旋转
            if abs(angle) > 0.1:  # 只有角度足够大时才旋转
                center = (new_w // 2, new_h // 2)
                rotation_matrix = cv2.getRotationMatrix2D(center, angle, 1.0)

                # 计算旋转后的边界框
                cos_val = abs(rotation_matrix[0, 0])
                sin_val = abs(rotation_matrix[0, 1])
                new_w_rot = int((new_h * sin_val) + (new_w * cos_val))
                new_h_rot = int((new_h * cos_val) + (new_w * sin_val))

                # 调整旋转中心
                rotation_matrix[0, 2] += (new_w_rot / 2) - center[0]
                rotation_matrix[1, 2] += (new_h_rot / 2) - center[1]

                rotated_template = cv2.warpAffine(scaled_template, rotation_matrix, (new_w_rot, new_h_rot))
                return rotated_template
            else:
                return scaled_template

        except Exception as e:
            self.logger.error(f"模板变换失败: {e}")
            return None

    def _single_match(self, image: np.ndarray, template: np.ndarray,
                     scale: float, angle: float, template_info: Dict[str, Any]) -> List[DetectionResult]:
        """单次模板匹配"""
        matches = []

        try:
            # 执行模板匹配
            result = cv2.matchTemplate(image, template, self.method)

            # 根据匹配方法确定阈值比较方式
            if self.method in [cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED]:
                # 对于SQDIFF方法，值越小越好
                locations = np.where(result <= self.threshold)
                scores = 1.0 - result[locations]  # 转换为置信度
            else:
                # 对于其他方法，值越大越好
                locations = np.where(result >= self.threshold)
                scores = result[locations]

            # 创建检测结果
            template_h, template_w = template.shape[:2]

            for i, (y, x) in enumerate(zip(locations[0], locations[1])):
                confidence = float(scores[i])

                # 创建边界框
                bbox = BoundingBox(
                    x=int(x),
                    y=int(y),
                    width=template_w,
                    height=template_h
                )

                # 创建检测结果
                detection = DetectionResult(
                    bbox=bbox,
                    confidence=confidence,
                    template_id=template_info.get("template_id"),
                    source=DetectionSource.TEMPLATE,
                    metadata={
                        "scale": scale,
                        "angle": angle,
                        "method": self.method,
                        "template_info": template_info
                    }
                )

                matches.append(detection)

        except Exception as e:
            self.logger.error(f"单次匹配失败: {e}")

        return matches

    def _apply_nms(self, matches: List[DetectionResult], iou_threshold: float = 0.3) -> List[DetectionResult]:
        """应用非极大值抑制"""
        if len(matches) <= 1:
            return matches

        try:
            # 按置信度排序
            matches.sort(key=lambda x: x.confidence, reverse=True)

            filtered_matches = []

            for current_match in matches:
                # 检查是否与已选择的匹配重叠
                should_keep = True

                for kept_match in filtered_matches:
                    iou = current_match.bbox.iou(kept_match.bbox)
                    if iou > iou_threshold:
                        should_keep = False
                        break

                if should_keep:
                    filtered_matches.append(current_match)

            return filtered_matches

        except Exception as e:
            self.logger.error(f"NMS处理失败: {e}")
            return matches

    def batch_match(self, image: Union[np.ndarray, Image.Image, str, Path],
                   templates: List[Union[np.ndarray, Image.Image, str, Path, TemplateInfo]]) -> List[DetectionResult]:
        """
        批量模板匹配

        Args:
            image: 输入图像
            templates: 模板列表

        Returns:
            List[DetectionResult]: 所有匹配结果
        """
        all_matches = []

        for i, template in enumerate(templates):
            template_id = f"template_{i}"
            if isinstance(template, TemplateInfo):
                template_id = template.template_id

            matches = self.match(image, template, template_id)
            all_matches.extend(matches)

        # 对所有结果应用NMS
        return self._apply_nms(all_matches)

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        if not self.match_times:
            return {}

        return {
            "total_matches": len(self.match_times),
            "total_detections": self.total_matches,
            "avg_match_time": np.mean(self.match_times),
            "min_match_time": np.min(self.match_times),
            "max_match_time": np.max(self.match_times),
            "avg_fps": 1.0 / np.mean(self.match_times) if self.match_times else 0,
            "avg_detections_per_match": self.total_matches / len(self.match_times)
        }

    def reset_stats(self) -> None:
        """重置性能统计"""
        self.match_times.clear()
        self.total_matches = 0
        self.logger.info("模板匹配性能统计已重置")

    def update_config(self, **kwargs) -> None:
        """更新匹配器配置"""
        if "threshold" in kwargs:
            self.threshold = kwargs["threshold"]

        if "method" in kwargs:
            self.method = self._resolve_method(kwargs["method"])

        if "scale_range" in kwargs:
            self.scale_range = kwargs["scale_range"]

        if "scale_steps" in kwargs:
            self.scale_steps = kwargs["scale_steps"]

        if "angle_range" in kwargs:
            self.angle_range = kwargs["angle_range"]

        if "angle_steps" in kwargs:
            self.angle_steps = kwargs["angle_steps"]

        if "enable_preprocessing" in kwargs:
            self.enable_preprocessing = kwargs["enable_preprocessing"]

        self.logger.info(f"模板匹配器配置已更新: {kwargs}")
