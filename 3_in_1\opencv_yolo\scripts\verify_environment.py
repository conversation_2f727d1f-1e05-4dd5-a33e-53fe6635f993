# -*- coding: utf-8 -*-
"""
环境验证脚本
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

import sys
import subprocess
import importlib
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    print("🐍 检查Python版本...")
    version = sys.version_info
    print(f"   当前版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("   ❌ Python版本过低，需要3.8+")
        return False
    else:
        print("   ✅ Python版本符合要求")
        return True

def check_required_packages():
    """检查必需的包"""
    print("\n📦 检查必需的包...")
    
    required_packages = {
        'numpy': 'numpy',
        'opencv-python': 'cv2',
        'PyQt6': 'PyQt6',
        'ultralytics': 'ultralytics',
        'loguru': 'loguru',
        'PyYAML': 'yaml',
        'psutil': 'psutil',
        'mss': 'mss',
        'Pillow': 'PIL'
    }
    
    missing_packages = []
    
    for package_name, import_name in required_packages.items():
        try:
            importlib.import_module(import_name)
            print(f"   ✅ {package_name}")
        except ImportError:
            print(f"   ❌ {package_name} (缺失)")
            missing_packages.append(package_name)
    
    if missing_packages:
        print(f"\n⚠️  缺失的包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def check_optional_packages():
    """检查可选的包"""
    print("\n🔧 检查可选的包...")
    
    optional_packages = {
        'torch': 'torch',
        'easyocr': 'easyocr',
        'paddleocr': 'paddleocr',
        'pytest': 'pytest',
        'flake8': 'flake8'
    }
    
    for package_name, import_name in optional_packages.items():
        try:
            importlib.import_module(import_name)
            print(f"   ✅ {package_name}")
        except ImportError:
            print(f"   ⚪ {package_name} (可选)")

def check_project_structure():
    """检查项目结构"""
    print("\n📁 检查项目结构...")
    
    required_paths = [
        "src/yolo_opencv_detector",
        "src/yolo_opencv_detector/core",
        "src/yolo_opencv_detector/gui",
        "src/yolo_opencv_detector/utils",
        "tests",
        "configs",
        "requirements.txt"
    ]
    
    project_root = Path(__file__).parent.parent
    all_exist = True
    
    for path in required_paths:
        full_path = project_root / path
        if full_path.exists():
            print(f"   ✅ {path}")
        else:
            print(f"   ❌ {path} (缺失)")
            all_exist = False
    
    return all_exist

def check_gpu_support():
    """检查GPU支持"""
    print("\n🎮 检查GPU支持...")
    
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0)
            print(f"   ✅ CUDA可用，GPU数量: {gpu_count}")
            print(f"   📱 GPU型号: {gpu_name}")
        else:
            print("   ⚪ CUDA不可用，将使用CPU模式")
    except ImportError:
        print("   ⚪ PyTorch未安装，无法检查CUDA")
    
    try:
        import cv2
        if cv2.cuda.getCudaEnabledDeviceCount() > 0:
            print("   ✅ OpenCV CUDA支持可用")
        else:
            print("   ⚪ OpenCV CUDA支持不可用")
    except:
        print("   ⚪ 无法检查OpenCV CUDA支持")

def main():
    """主函数"""
    print("🔍 YOLO OpenCV检测器环境验证")
    print("=" * 50)
    
    checks = [
        check_python_version(),
        check_required_packages(),
        check_project_structure()
    ]
    
    check_optional_packages()
    check_gpu_support()
    
    print("\n" + "=" * 50)
    if all(checks):
        print("🎉 环境验证通过！可以开始使用项目。")
        return 0
    else:
        print("❌ 环境验证失败，请解决上述问题后重试。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
