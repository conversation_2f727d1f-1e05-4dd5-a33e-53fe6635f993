# -*- coding: utf-8 -*-
"""
截图功能整合验证测试
作者: Cursor AI
创建时间: 2025-07-05
编码标准: UTF-8无BOM
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

def test_screenshot_helper():
    """测试截图辅助工具"""
    print("=== 测试截图辅助工具 ===")
    
    try:
        from yolo_opencv_detector.utils.screenshot_helper import get_screenshot_helper
        
        helper = get_screenshot_helper()
        print(f"✅ 截图服务可用: {helper.is_available()}")
        print(f"✅ 可用方法: {helper.get_available_methods()}")
        
        if helper.is_available():
            # 测试基本截图
            image, filepath = helper.take_screenshot(save_to_file=True)
            if image is not None:
                print(f"✅ 基本截图成功: {image.shape}, 文件: {filepath}")
                
                # 测试带QPixmap的截图
                image2, pixmap, filepath2 = helper.take_screenshot_with_pixmap(save_to_file=True)
                if pixmap and not pixmap.isNull():
                    print(f"✅ QPixmap截图成功: {pixmap.size()}")
                    return True
                else:
                    print("❌ QPixmap截图失败")
                    return False
            else:
                print("❌ 基本截图失败")
                return False
        else:
            print("❌ 截图服务不可用")
            return False
            
    except Exception as e:
        print(f"❌ 截图辅助工具测试失败: {e}")
        return False

def test_screenshot_preview_dialog():
    """测试截图预览对话框"""
    print("\n=== 测试截图预览对话框 ===")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from yolo_opencv_detector.utils.screenshot_helper import get_screenshot_helper
        from yolo_opencv_detector.gui.screenshot_preview_dialog import ScreenshotPreviewDialog
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 获取截图
        helper = get_screenshot_helper()
        if helper.is_available():
            image, filepath = helper.take_screenshot(save_to_file=True)
            
            if image is not None:
                print(f"✅ 截图成功: {image.shape}")
                
                # 创建预览对话框
                dialog = ScreenshotPreviewDialog(image, filepath)
                print("✅ 截图预览对话框创建成功")
                
                # 测试信号连接
                signal_count = 0
                
                def on_screenshot_saved(filepath):
                    nonlocal signal_count
                    signal_count += 1
                    print(f"✅ 截图保存信号正常: {filepath}")
                
                def on_template_created(template_data):
                    nonlocal signal_count
                    signal_count += 1
                    print(f"✅ 模板创建信号正常: {template_data.get('name', 'Unknown')}")
                
                def on_region_extracted(image, region):
                    nonlocal signal_count
                    signal_count += 1
                    print(f"✅ 区域提取信号正常: {region}")
                
                dialog.screenshot_saved.connect(on_screenshot_saved)
                dialog.template_created.connect(on_template_created)
                dialog.region_extracted.connect(on_region_extracted)
                
                print("✅ 信号连接成功")
                return True
                
            else:
                print("❌ 截图失败")
                return False
        else:
            print("❌ 截图服务不可用")
            return False
            
    except Exception as e:
        print(f"❌ 截图预览对话框测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_detection_panel_integration():
    """测试检测面板集成"""
    print("\n=== 测试检测面板集成 ===")
    
    try:
        from yolo_opencv_detector.gui.widgets.detection_panel_v2 import DetectionPanelV2
        from yolo_opencv_detector.utils.config_manager import ConfigManager
        
        # 创建配置管理器
        config_manager = ConfigManager()
        
        # 创建检测面板
        panel = DetectionPanelV2(config_manager)
        print("✅ 检测面板创建成功")
        
        # 检查截图按钮
        if hasattr(panel, 'screenshot_button'):
            button = panel.screenshot_button
            print(f"✅ 截图按钮存在: {button.text()}")
            print(f"✅ 工具提示: {button.toolTip()}")
            
            # 检查方法是否存在
            if hasattr(panel, '_take_detection_screenshot'):
                print("✅ 检测截图方法存在")
                return True
            else:
                print("❌ 检测截图方法不存在")
                return False
        else:
            print("❌ 截图按钮不存在")
            return False
            
    except Exception as e:
        print(f"❌ 检测面板集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_template_panel_integration():
    """测试模板面板集成"""
    print("\n=== 测试模板面板集成 ===")
    
    try:
        from yolo_opencv_detector.gui.widgets.template_panel_v2 import TemplatePanelV2
        from yolo_opencv_detector.utils.config_manager import ConfigManager
        
        # 创建配置管理器
        config_manager = ConfigManager()
        
        # 创建模板面板
        panel = TemplatePanelV2(config_manager)
        print("✅ 模板面板创建成功")
        
        # 检查截取模板按钮
        if hasattr(panel, 'capture_button'):
            button = panel.capture_button
            print(f"✅ 截取模板按钮存在: {button.text()}")
            print(f"✅ 工具提示: {button.toolTip()}")
            
            # 检查方法是否存在
            if hasattr(panel, '_capture_template'):
                print("✅ 截取模板方法存在")
                return True
            else:
                print("❌ 截取模板方法不存在")
                return False
        else:
            print("❌ 截取模板按钮不存在")
            return False
            
    except Exception as e:
        print(f"❌ 模板面板集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_window_integration():
    """测试主窗口集成"""
    print("\n=== 测试主窗口集成 ===")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from yolo_opencv_detector.gui.main_window_v2 import MainWindowV2
        from yolo_opencv_detector.utils.config_manager import ConfigManager
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建配置管理器
        config_manager = ConfigManager()
        
        # 创建主窗口
        window = MainWindowV2(config_manager)
        print("✅ 主窗口创建成功")
        
        # 检查截图动作
        if hasattr(window, 'screenshot_action'):
            action = window.screenshot_action
            print(f"✅ 截图动作存在: {action.text()}")
            print(f"✅ 工具提示: {action.toolTip()}")
            print(f"✅ 快捷键: {action.shortcut().toString()}")
            
            # 检查方法是否存在
            if hasattr(window, '_open_screenshot_tool'):
                print("✅ 截图工具方法存在")
                return True
            else:
                print("❌ 截图工具方法不存在")
                return False
        else:
            print("❌ 截图动作不存在")
            return False
            
    except Exception as e:
        print(f"❌ 主窗口集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🔍 YOLO OpenCV检测器截图功能整合验证测试")
    print("=" * 60)
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("截图辅助工具", test_screenshot_helper()))
    test_results.append(("截图预览对话框", test_screenshot_preview_dialog()))
    test_results.append(("检测面板集成", test_detection_panel_integration()))
    test_results.append(("模板面板集成", test_template_panel_integration()))
    test_results.append(("主窗口集成", test_main_window_integration()))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！截图功能整合成功！")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
