# -*- coding: utf-8 -*-
"""
统一的截图服务组件 - 消除重复代码，提供上下文相关的截图功能
作者: Cursor AI
创建时间: 2025-07-05
编码标准: UTF-8无BOM
"""

from typing import Optional, Dict, Any, Callable
from enum import Enum
import numpy as np

from PyQt6.QtWidgets import QWidget, QMessageBox
from PyQt6.QtCore import QObject, pyqtSignal

from ...utils.logger import Logger


class ScreenshotContext(Enum):
    """截图上下文类型"""
    DETECTION = "detection"      # 用于目标检测
    TEMPLATE = "template"        # 用于模板创建
    GENERAL = "general"          # 通用截图


class ScreenshotService(QObject):
    """统一的截图服务类"""
    
    # 信号定义
    screenshot_completed = pyqtSignal(dict)  # 截图完成信号
    screenshot_failed = pyqtSignal(str)      # 截图失败信号
    
    def __init__(self):
        super().__init__()
        self.logger = Logger()
        
        # 上下文配置
        self.context_configs = {
            ScreenshotContext.DETECTION: {
                'title': '📷 检测截图',
                'description': '截取屏幕进行目标检测分析',
                'button_text': '📷 检测截图',
                'processing_text': '📷 检测截图中...',
                'success_text': '📸 检测截图完成',
                'enable_template_creation': True,
                'enable_region_extraction': True,
                'enable_detection_analysis': True,
                'default_action': 'detection_analysis'
            },
            ScreenshotContext.TEMPLATE: {
                'title': '📷 截取模板',
                'description': '截取屏幕区域创建新的匹配模板',
                'button_text': '📷 截取模板',
                'processing_text': '📷 模板截取中...',
                'success_text': '📸 模板截取完成',
                'enable_template_creation': True,
                'enable_region_extraction': False,
                'enable_detection_analysis': False,
                'default_action': 'template_creation'
            },
            ScreenshotContext.GENERAL: {
                'title': '📷 通用截图',
                'description': '通用截图工具，支持多种操作',
                'button_text': '📷 通用截图',
                'processing_text': '📷 截图中...',
                'success_text': '📸 截图完成',
                'enable_template_creation': True,
                'enable_region_extraction': True,
                'enable_detection_analysis': True,
                'default_action': 'preview'
            }
        }
    
    def take_screenshot(self, 
                       context: ScreenshotContext,
                       parent_widget: QWidget,
                       status_callback: Optional[Callable[[str], None]] = None,
                       button_callback: Optional[Callable[[bool, str], None]] = None) -> bool:
        """
        执行截图操作
        
        Args:
            context: 截图上下文
            parent_widget: 父窗口组件
            status_callback: 状态更新回调函数
            button_callback: 按钮状态更新回调函数 (enabled, text)
            
        Returns:
            bool: 是否成功启动截图
        """
        try:
            config = self.context_configs[context]
            
            # 更新状态
            if status_callback:
                status_callback(config['processing_text'])
            if button_callback:
                button_callback(False, config['processing_text'])
            
            # 获取截图
            from ...utils.screenshot_helper import get_screenshot_helper
            screenshot_helper = get_screenshot_helper()
            
            if not screenshot_helper.is_available():
                error_msg = "截图服务不可用"
                self._handle_error(error_msg, status_callback, button_callback, config)
                return False
            
            # 截取屏幕
            image, pixmap, filepath = screenshot_helper.take_screenshot_with_pixmap(save_to_file=True)
            
            if image is None or pixmap is None or pixmap.isNull():
                error_msg = "截图失败"
                self._handle_error(error_msg, status_callback, button_callback, config)
                return False
            
            # 打开上下文相关的预览对话框
            success = self._open_preview_dialog(
                image, pixmap, filepath, context, config, parent_widget
            )
            
            if success:
                # 更新成功状态
                if status_callback:
                    status_callback(config['success_text'])
                
                # 发送完成信号
                self.screenshot_completed.emit({
                    'context': context,
                    'image': image,
                    'pixmap': pixmap,
                    'filepath': filepath,
                    'config': config
                })
            
            return success
            
        except Exception as e:
            error_msg = f"截图操作失败: {e}"
            self._handle_error(error_msg, status_callback, button_callback, 
                             self.context_configs.get(context, {}))
            self.logger.error(error_msg)
            return False
        
        finally:
            # 恢复按钮状态
            if button_callback:
                config = self.context_configs[context]
                button_callback(True, config['button_text'])
    
    def _open_preview_dialog(self, 
                           image: np.ndarray, 
                           pixmap, 
                           filepath: str,
                           context: ScreenshotContext,
                           config: Dict[str, Any],
                           parent_widget: QWidget) -> bool:
        """打开上下文相关的预览对话框"""
        try:
            from ..screenshot_preview_dialog import ScreenshotPreviewDialog
            
            # 创建对话框
            dialog = ScreenshotPreviewDialog(image, filepath, parent_widget)
            
            # 设置对话框标题和提示
            dialog.setWindowTitle(config['title'])
            
            # 根据上下文配置对话框功能
            self._configure_dialog_for_context(dialog, context, config)
            
            # 连接信号 - 根据上下文连接不同的信号
            dialog.screenshot_saved.connect(
                lambda path: self._handle_screenshot_saved(path, context)
            )

            # 模板创建信号需要特殊处理，直接转发给父组件
            if context == ScreenshotContext.TEMPLATE:
                # 对于模板上下文，需要将信号转发给模板面板
                if hasattr(parent_widget, '_on_template_captured'):
                    dialog.template_created.connect(parent_widget._on_template_captured)
                    self.logger.info("模板创建信号已连接到模板面板")
                else:
                    dialog.template_created.connect(
                        lambda data: self._handle_template_created(data, context)
                    )
                    self.logger.warning("父组件没有_on_template_captured方法，使用默认处理")
            else:
                dialog.template_created.connect(
                    lambda data: self._handle_template_created(data, context)
                )

            if context == ScreenshotContext.DETECTION:
                # 对于检测上下文，需要将区域提取信号转发给检测面板
                if hasattr(parent_widget, '_on_region_extracted'):
                    dialog.region_extracted.connect(parent_widget._on_region_extracted)
                else:
                    dialog.region_extracted.connect(
                        lambda img, region: self._handle_region_extracted(img, region, context)
                    )
            else:
                dialog.region_extracted.connect(
                    lambda img, region: self._handle_region_extracted(img, region, context)
                )
            
            # 显示对话框
            result = dialog.exec()
            
            if result == dialog.DialogCode.Accepted:
                self.logger.info(f"{config['title']}对话框已确认")
                return True
            else:
                self.logger.info(f"{config['title']}对话框已取消")
                return False
                
        except Exception as e:
            self.logger.error(f"打开预览对话框失败: {e}")
            QMessageBox.warning(parent_widget, "错误", f"打开预览对话框失败: {e}")
            return False
    
    def _configure_dialog_for_context(self, 
                                    dialog, 
                                    context: ScreenshotContext, 
                                    config: Dict[str, Any]):
        """根据上下文配置对话框"""
        # 这里可以根据上下文隐藏或显示特定功能
        # 例如：模板上下文可能隐藏检测相关功能
        pass
    
    def _handle_screenshot_saved(self, filepath: str, context: ScreenshotContext):
        """处理截图保存事件"""
        self.logger.info(f"[{context.value}] 截图已保存: {filepath}")
    
    def _handle_template_created(self, template_data: Dict[str, Any], context: ScreenshotContext):
        """处理模板创建事件"""
        template_name = template_data.get('name', 'Unknown')
        self.logger.info(f"[{context.value}] 模板已创建: {template_name}")
    
    def _handle_region_extracted(self, image: np.ndarray, region: tuple, context: ScreenshotContext):
        """处理区域提取事件"""
        x, y, width, height = region
        self.logger.info(f"[{context.value}] 区域已提取: ({x}, {y}, {width}, {height})")
    
    def _handle_error(self, 
                     error_msg: str, 
                     status_callback: Optional[Callable[[str], None]],
                     button_callback: Optional[Callable[[bool, str], None]],
                     config: Dict[str, Any]):
        """处理错误"""
        if status_callback:
            status_callback(f"❌ {error_msg}")
        
        if button_callback and config:
            button_callback(True, config.get('button_text', '📷 截图'))
        
        self.screenshot_failed.emit(error_msg)
    
    def get_context_config(self, context: ScreenshotContext) -> Dict[str, Any]:
        """获取上下文配置"""
        return self.context_configs.get(context, {})


# 全局截图服务实例
_screenshot_service = None

def get_screenshot_service() -> ScreenshotService:
    """获取全局截图服务实例"""
    global _screenshot_service
    if _screenshot_service is None:
        _screenshot_service = ScreenshotService()
    return _screenshot_service
