# -*- coding: utf-8 -*-
"""
OCR文字识别引擎
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

import cv2
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
import time
import re

from ..utils.logger import Logger
from ..utils.config_manager import ConfigManager
from ..utils.data_structures import DetectionResult, BoundingBox, DetectionSource


class OCREngine:
    """OCR文字识别引擎类"""
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化OCR引擎
        
        Args:
            config_manager: 配置管理器
        """
        self.logger = Logger().get_logger(__name__)
        self.config_manager = config_manager
        
        # OCR引擎
        self.ocr_engine = None
        self.engine_type = "easyocr"  # 默认使用EasyOCR
        
        # 配置参数
        self.languages = ['ch_sim', 'en']  # 支持中英文
        self.confidence_threshold = 0.5
        self.text_threshold = 0.7
        self.link_threshold = 0.4
        
        # 预处理参数
        self.enable_preprocessing = True
        self.denoise_enabled = True
        self.contrast_enhancement = True
        self.binarization_enabled = True
        
        # 后处理参数
        self.enable_postprocessing = True
        self.filter_short_text = True
        self.min_text_length = 2
        self.filter_confidence = True
        
        # 性能统计
        self.recognition_count = 0
        self.total_recognition_time = 0.0
        self.avg_confidence = 0.0
        
        # 初始化OCR引擎
        self._initialize_ocr_engine()
        
        self.logger.info("OCR文字识别引擎初始化完成")
    
    def _initialize_ocr_engine(self) -> None:
        """初始化OCR引擎"""
        try:
            if self.engine_type == "easyocr":
                self._init_easyocr()
            elif self.engine_type == "paddleocr":
                self._init_paddleocr()
            elif self.engine_type == "tesseract":
                self._init_tesseract()
            else:
                self.logger.error(f"不支持的OCR引擎类型: {self.engine_type}")
                
        except Exception as e:
            self.logger.error(f"OCR引擎初始化失败: {e}")
    
    def _init_easyocr(self) -> None:
        """初始化EasyOCR"""
        try:
            import easyocr
            
            self.ocr_engine = easyocr.Reader(
                self.languages,
                gpu=True,  # 尝试使用GPU
                verbose=False
            )
            
            self.logger.info("EasyOCR引擎初始化成功")
            
        except ImportError:
            self.logger.error("EasyOCR未安装，请运行: pip install easyocr")
        except Exception as e:
            self.logger.error(f"EasyOCR初始化失败: {e}")
    
    def _init_paddleocr(self) -> None:
        """初始化PaddleOCR"""
        try:
            from paddleocr import PaddleOCR
            
            self.ocr_engine = PaddleOCR(
                use_angle_cls=True,
                lang='ch',
                use_gpu=True,
                show_log=False
            )
            
            self.logger.info("PaddleOCR引擎初始化成功")
            
        except ImportError:
            self.logger.error("PaddleOCR未安装，请运行: pip install paddlepaddle paddleocr")
        except Exception as e:
            self.logger.error(f"PaddleOCR初始化失败: {e}")
    
    def _init_tesseract(self) -> None:
        """初始化Tesseract"""
        try:
            import pytesseract
            
            # 检查Tesseract是否可用
            version = pytesseract.get_tesseract_version()
            self.logger.info(f"Tesseract版本: {version}")
            
            self.ocr_engine = pytesseract
            
        except ImportError:
            self.logger.error("pytesseract未安装，请运行: pip install pytesseract")
        except Exception as e:
            self.logger.error(f"Tesseract初始化失败: {e}")
    
    def preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """
        图像预处理
        
        Args:
            image: 输入图像
            
        Returns:
            np.ndarray: 预处理后的图像
        """
        try:
            if not self.enable_preprocessing:
                return image
            
            processed_image = image.copy()
            
            # 转换为灰度图
            if len(processed_image.shape) == 3:
                processed_image = cv2.cvtColor(processed_image, cv2.COLOR_BGR2GRAY)
            
            # 去噪
            if self.denoise_enabled:
                processed_image = cv2.medianBlur(processed_image, 3)
            
            # 对比度增强
            if self.contrast_enhancement:
                clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
                processed_image = clahe.apply(processed_image)
            
            # 二值化
            if self.binarization_enabled:
                _, processed_image = cv2.threshold(
                    processed_image, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU
                )
            
            return processed_image
            
        except Exception as e:
            self.logger.error(f"图像预处理失败: {e}")
            return image
    
    def recognize_text(self, image: np.ndarray) -> List[DetectionResult]:
        """
        识别图像中的文字
        
        Args:
            image: 输入图像
            
        Returns:
            List[DetectionResult]: 文字识别结果
        """
        try:
            if self.ocr_engine is None:
                self.logger.warning("OCR引擎未初始化")
                return []
            
            start_time = time.time()
            
            # 图像预处理
            processed_image = self.preprocess_image(image)
            
            # 执行OCR识别
            if self.engine_type == "easyocr":
                results = self._recognize_with_easyocr(processed_image)
            elif self.engine_type == "paddleocr":
                results = self._recognize_with_paddleocr(processed_image)
            elif self.engine_type == "tesseract":
                results = self._recognize_with_tesseract(processed_image)
            else:
                results = []
            
            # 后处理
            if self.enable_postprocessing:
                results = self._postprocess_results(results)
            
            # 更新统计
            recognition_time = time.time() - start_time
            self.recognition_count += 1
            self.total_recognition_time += recognition_time
            
            if results:
                avg_conf = sum(r.confidence for r in results) / len(results)
                self.avg_confidence = (self.avg_confidence * (self.recognition_count - 1) + avg_conf) / self.recognition_count
            
            self.logger.debug(f"OCR识别完成，识别到 {len(results)} 个文本，耗时 {recognition_time:.3f}秒")
            
            return results
            
        except Exception as e:
            self.logger.error(f"文字识别失败: {e}")
            return []
    
    def _recognize_with_easyocr(self, image: np.ndarray) -> List[DetectionResult]:
        """使用EasyOCR进行识别"""
        try:
            # EasyOCR识别
            ocr_results = self.ocr_engine.readtext(
                image,
                detail=1,
                paragraph=False,
                width_ths=0.7,
                height_ths=0.7
            )
            
            results = []
            for bbox_coords, text, confidence in ocr_results:
                if confidence < self.confidence_threshold:
                    continue
                
                # 转换边界框格式
                bbox_coords = np.array(bbox_coords)
                x_min = int(np.min(bbox_coords[:, 0]))
                y_min = int(np.min(bbox_coords[:, 1]))
                x_max = int(np.max(bbox_coords[:, 0]))
                y_max = int(np.max(bbox_coords[:, 1]))
                
                bbox = BoundingBox(
                    x=x_min,
                    y=y_min,
                    width=x_max - x_min,
                    height=y_max - y_min
                )
                
                result = DetectionResult(
                    bbox=bbox,
                    confidence=confidence,
                    class_name="text",
                    source=DetectionSource.OCR,
                    metadata={
                        "text": text,
                        "engine": "easyocr",
                        "bbox_coords": bbox_coords.tolist()
                    }
                )
                
                results.append(result)
            
            return results
            
        except Exception as e:
            self.logger.error(f"EasyOCR识别失败: {e}")
            return []
    
    def _recognize_with_paddleocr(self, image: np.ndarray) -> List[DetectionResult]:
        """使用PaddleOCR进行识别"""
        try:
            # PaddleOCR识别
            ocr_results = self.ocr_engine.ocr(image, cls=True)
            
            results = []
            if ocr_results and ocr_results[0]:
                for line in ocr_results[0]:
                    bbox_coords, (text, confidence) = line
                    
                    if confidence < self.confidence_threshold:
                        continue
                    
                    # 转换边界框格式
                    bbox_coords = np.array(bbox_coords)
                    x_min = int(np.min(bbox_coords[:, 0]))
                    y_min = int(np.min(bbox_coords[:, 1]))
                    x_max = int(np.max(bbox_coords[:, 0]))
                    y_max = int(np.max(bbox_coords[:, 1]))
                    
                    bbox = BoundingBox(
                        x=x_min,
                        y=y_min,
                        width=x_max - x_min,
                        height=y_max - y_min
                    )
                    
                    result = DetectionResult(
                        bbox=bbox,
                        confidence=confidence,
                        class_name="text",
                        source=DetectionSource.OCR,
                        metadata={
                            "text": text,
                            "engine": "paddleocr",
                            "bbox_coords": bbox_coords.tolist()
                        }
                    )
                    
                    results.append(result)
            
            return results
            
        except Exception as e:
            self.logger.error(f"PaddleOCR识别失败: {e}")
            return []
    
    def _recognize_with_tesseract(self, image: np.ndarray) -> List[DetectionResult]:
        """使用Tesseract进行识别"""
        try:
            import pytesseract
            
            # 配置Tesseract参数
            config = '--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz中文'
            
            # 获取详细信息
            data = pytesseract.image_to_data(
                image,
                config=config,
                output_type=pytesseract.Output.DICT,
                lang='chi_sim+eng'
            )
            
            results = []
            n_boxes = len(data['level'])
            
            for i in range(n_boxes):
                confidence = float(data['conf'][i])
                text = data['text'][i].strip()
                
                if confidence < self.confidence_threshold * 100 or not text:
                    continue
                
                x = data['left'][i]
                y = data['top'][i]
                width = data['width'][i]
                height = data['height'][i]
                
                bbox = BoundingBox(x=x, y=y, width=width, height=height)
                
                result = DetectionResult(
                    bbox=bbox,
                    confidence=confidence / 100.0,  # 转换为0-1范围
                    class_name="text",
                    source=DetectionSource.OCR,
                    metadata={
                        "text": text,
                        "engine": "tesseract"
                    }
                )
                
                results.append(result)
            
            return results
            
        except Exception as e:
            self.logger.error(f"Tesseract识别失败: {e}")
            return []
    
    def _postprocess_results(self, results: List[DetectionResult]) -> List[DetectionResult]:
        """后处理识别结果"""
        try:
            processed_results = []
            
            for result in results:
                text = result.metadata.get("text", "")
                
                # 过滤短文本
                if self.filter_short_text and len(text) < self.min_text_length:
                    continue
                
                # 过滤低置信度
                if self.filter_confidence and result.confidence < self.confidence_threshold:
                    continue
                
                # 文本清理
                cleaned_text = self._clean_text(text)
                if not cleaned_text:
                    continue
                
                # 更新文本
                result.metadata["text"] = cleaned_text
                result.metadata["original_text"] = text
                
                processed_results.append(result)
            
            return processed_results
            
        except Exception as e:
            self.logger.error(f"结果后处理失败: {e}")
            return results
    
    def _clean_text(self, text: str) -> str:
        """清理文本"""
        try:
            # 移除多余的空白字符
            cleaned = re.sub(r'\s+', ' ', text.strip())
            
            # 移除特殊字符（可选）
            # cleaned = re.sub(r'[^\w\s\u4e00-\u9fff]', '', cleaned)
            
            return cleaned
            
        except Exception as e:
            self.logger.error(f"文本清理失败: {e}")
            return text
    
    def search_text(self, image: np.ndarray, target_text: str, fuzzy: bool = False) -> List[DetectionResult]:
        """
        在图像中搜索特定文本
        
        Args:
            image: 输入图像
            target_text: 目标文本
            fuzzy: 是否启用模糊匹配
            
        Returns:
            List[DetectionResult]: 匹配的文本结果
        """
        try:
            # 识别所有文本
            all_results = self.recognize_text(image)
            
            matched_results = []
            
            for result in all_results:
                text = result.metadata.get("text", "")
                
                if fuzzy:
                    # 模糊匹配
                    if self._fuzzy_match(text, target_text):
                        result.metadata["match_type"] = "fuzzy"
                        result.metadata["target_text"] = target_text
                        matched_results.append(result)
                else:
                    # 精确匹配
                    if target_text.lower() in text.lower():
                        result.metadata["match_type"] = "exact"
                        result.metadata["target_text"] = target_text
                        matched_results.append(result)
            
            return matched_results
            
        except Exception as e:
            self.logger.error(f"文本搜索失败: {e}")
            return []
    
    def _fuzzy_match(self, text1: str, text2: str, threshold: float = 0.8) -> bool:
        """模糊匹配"""
        try:
            from difflib import SequenceMatcher
            
            similarity = SequenceMatcher(None, text1.lower(), text2.lower()).ratio()
            return similarity >= threshold
            
        except ImportError:
            # 简单的包含匹配
            return text2.lower() in text1.lower()
        except Exception as e:
            self.logger.error(f"模糊匹配失败: {e}")
            return False
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        avg_time = (self.total_recognition_time / self.recognition_count 
                   if self.recognition_count > 0 else 0)
        
        return {
            "engine_type": self.engine_type,
            "recognition_count": self.recognition_count,
            "total_recognition_time": self.total_recognition_time,
            "avg_recognition_time": avg_time,
            "avg_confidence": self.avg_confidence,
            "languages": self.languages,
            "confidence_threshold": self.confidence_threshold
        }
    
    def configure_engine(self, 
                        engine_type: str = None,
                        languages: List[str] = None,
                        confidence_threshold: float = None) -> None:
        """
        配置OCR引擎
        
        Args:
            engine_type: 引擎类型
            languages: 支持的语言
            confidence_threshold: 置信度阈值
        """
        try:
            if engine_type and engine_type != self.engine_type:
                self.engine_type = engine_type
                self._initialize_ocr_engine()
            
            if languages:
                self.languages = languages
            
            if confidence_threshold is not None:
                self.confidence_threshold = confidence_threshold
            
            self.logger.info(f"OCR引擎配置已更新: {self.engine_type}")
            
        except Exception as e:
            self.logger.error(f"配置OCR引擎失败: {e}")
    
    def reset_stats(self) -> None:
        """重置统计信息"""
        self.recognition_count = 0
        self.total_recognition_time = 0.0
        self.avg_confidence = 0.0
        
        self.logger.info("OCR统计信息已重置")
