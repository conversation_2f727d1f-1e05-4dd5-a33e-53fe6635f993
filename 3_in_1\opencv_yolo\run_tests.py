# -*- coding: utf-8 -*-
"""
测试运行脚本
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

import sys
import os
import subprocess
import argparse
from pathlib import Path
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))


def run_command(command, description=""):
    """
    运行命令并返回结果
    
    Args:
        command: 要运行的命令
        description: 命令描述
        
    Returns:
        tuple: (返回码, 标准输出, 标准错误)
    """
    print(f"\n{'='*60}")
    print(f"运行: {description or command}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            cwd=project_root
        )
        
        if result.stdout:
            print("标准输出:")
            print(result.stdout)
        
        if result.stderr:
            print("标准错误:")
            print(result.stderr)
        
        print(f"返回码: {result.returncode}")
        return result.returncode, result.stdout, result.stderr
        
    except Exception as e:
        print(f"命令执行失败: {e}")
        return 1, "", str(e)


def check_dependencies():
    """检查依赖项"""
    print("检查依赖项...")
    
    required_packages = [
        "pytest",
        "numpy",
        "opencv-python",
        "PyQt6",
        "psutil",
        "loguru"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
            print(f"✓ {package}")
        except ImportError:
            print(f"✗ {package} (缺失)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n缺失的依赖项: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("所有依赖项已安装")
    return True


def run_unit_tests(verbose=False, coverage=False):
    """运行单元测试"""
    command = "python -m pytest tests/ -v"
    
    if verbose:
        command += " -s"
    
    if coverage:
        command += " --cov=src/yolo_opencv_detector --cov-report=html --cov-report=term"
    
    # 排除集成测试和性能测试
    command += " --ignore=tests/test_integration.py --ignore=tests/test_performance.py"
    
    return run_command(command, "单元测试")


def run_integration_tests(verbose=False):
    """运行集成测试"""
    command = "python -m pytest tests/test_integration.py -v"
    
    if verbose:
        command += " -s"
    
    return run_command(command, "集成测试")


def run_performance_tests(verbose=False):
    """运行性能测试"""
    command = "python -m pytest tests/test_performance.py -v"
    
    if verbose:
        command += " -s"
    
    return run_command(command, "性能测试")


def run_linting():
    """运行代码检查"""
    print("运行代码检查...")
    
    # 检查是否安装了flake8
    try:
        import flake8
        command = "python -m flake8 src/ tests/ --max-line-length=100 --ignore=E203,W503"
        return run_command(command, "代码风格检查 (flake8)")
    except ImportError:
        print("flake8 未安装，跳过代码风格检查")
        return 0, "", ""


def run_type_checking():
    """运行类型检查"""
    print("运行类型检查...")
    
    # 检查是否安装了mypy
    try:
        import mypy
        command = "python -m mypy src/yolo_opencv_detector --ignore-missing-imports"
        return run_command(command, "类型检查 (mypy)")
    except ImportError:
        print("mypy 未安装，跳过类型检查")
        return 0, "", ""


def generate_test_report():
    """生成测试报告"""
    print("生成测试报告...")
    
    command = "python -m pytest tests/ --html=reports/test_report.html --self-contained-html"
    
    # 创建报告目录
    reports_dir = project_root / "reports"
    reports_dir.mkdir(exist_ok=True)
    
    return run_command(command, "生成测试报告")


def clean_test_artifacts():
    """清理测试产物"""
    print("清理测试产物...")
    
    artifacts = [
        ".pytest_cache",
        "__pycache__",
        "*.pyc",
        ".coverage",
        "htmlcov",
        "reports"
    ]
    
    for artifact in artifacts:
        if artifact.startswith("*."):
            # 使用find命令删除特定扩展名的文件
            command = f"find . -name '{artifact}' -delete"
        else:
            # 删除目录
            artifact_path = project_root / artifact
            if artifact_path.exists():
                if artifact_path.is_dir():
                    import shutil
                    shutil.rmtree(artifact_path)
                    print(f"删除目录: {artifact}")
                else:
                    artifact_path.unlink()
                    print(f"删除文件: {artifact}")
    
    print("清理完成")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="YOLO OpenCV 检测器测试运行脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python run_tests.py                    # 运行所有测试
  python run_tests.py --unit             # 只运行单元测试
  python run_tests.py --integration      # 只运行集成测试
  python run_tests.py --performance      # 只运行性能测试
  python run_tests.py --coverage         # 运行测试并生成覆盖率报告
  python run_tests.py --lint             # 运行代码检查
  python run_tests.py --clean            # 清理测试产物
        """
    )
    
    parser.add_argument(
        "--unit", "-u",
        action="store_true",
        help="只运行单元测试"
    )
    
    parser.add_argument(
        "--integration", "-i",
        action="store_true",
        help="只运行集成测试"
    )
    
    parser.add_argument(
        "--performance", "-p",
        action="store_true",
        help="只运行性能测试"
    )
    
    parser.add_argument(
        "--coverage", "-c",
        action="store_true",
        help="生成覆盖率报告"
    )
    
    parser.add_argument(
        "--lint", "-l",
        action="store_true",
        help="运行代码检查"
    )
    
    parser.add_argument(
        "--type-check", "-t",
        action="store_true",
        help="运行类型检查"
    )
    
    parser.add_argument(
        "--report", "-r",
        action="store_true",
        help="生成HTML测试报告"
    )
    
    parser.add_argument(
        "--clean",
        action="store_true",
        help="清理测试产物"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="详细输出"
    )
    
    parser.add_argument(
        "--fast",
        action="store_true",
        help="快速模式（跳过性能测试）"
    )
    
    args = parser.parse_args()
    
    # 如果指定了清理，执行清理并退出
    if args.clean:
        clean_test_artifacts()
        return 0
    
    # 检查依赖项
    if not check_dependencies():
        return 1
    
    start_time = time.time()
    failed_tests = []
    
    try:
        # 根据参数决定运行哪些测试
        if args.unit or (not any([args.integration, args.performance, args.lint, args.type_check])):
            print("\n开始运行单元测试...")
            returncode, _, _ = run_unit_tests(args.verbose, args.coverage)
            if returncode != 0:
                failed_tests.append("单元测试")
        
        if args.integration or (not any([args.unit, args.performance, args.lint, args.type_check])):
            print("\n开始运行集成测试...")
            returncode, _, _ = run_integration_tests(args.verbose)
            if returncode != 0:
                failed_tests.append("集成测试")
        
        if (args.performance or (not any([args.unit, args.integration, args.lint, args.type_check]))) and not args.fast:
            print("\n开始运行性能测试...")
            returncode, _, _ = run_performance_tests(args.verbose)
            if returncode != 0:
                failed_tests.append("性能测试")
        
        if args.lint:
            print("\n开始运行代码检查...")
            returncode, _, _ = run_linting()
            if returncode != 0:
                failed_tests.append("代码检查")
        
        if args.type_check:
            print("\n开始运行类型检查...")
            returncode, _, _ = run_type_checking()
            if returncode != 0:
                failed_tests.append("类型检查")
        
        if args.report:
            print("\n生成测试报告...")
            generate_test_report()
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        return 130
    
    # 输出总结
    end_time = time.time()
    total_time = end_time - start_time
    
    print(f"\n{'='*60}")
    print("测试总结")
    print(f"{'='*60}")
    print(f"总耗时: {total_time:.2f}秒")
    
    if failed_tests:
        print(f"失败的测试: {', '.join(failed_tests)}")
        return 1
    else:
        print("所有测试通过！")
        return 0


if __name__ == "__main__":
    sys.exit(main())
