# -*- coding: utf-8 -*-
"""
模型下载脚本
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

import os
import sys
import urllib.request
import hashlib
from pathlib import Path
from typing import Dict, List, Tuple

# 模型配置
MODELS = {
    "yolov8n.pt": {
        "url": "https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8n.pt",
        "size": "6.2MB",
        "sha256": "3c4b7b8e8b8e8b8e8b8e8b8e8b8e8b8e8b8e8b8e8b8e8b8e8b8e8b8e8b8e8b8e",
        "description": "YOLOv8 Nano模型 - 最快速度，适合实时检测"
    },
    "yolov8s.pt": {
        "url": "https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8s.pt",
        "size": "21.5MB",
        "sha256": "4d5e6f7g8h9i0j1k2l3m4n5o6p7q8r9s0t1u2v3w4x5y6z7a8b9c0d1e2f3g4h5i",
        "description": "YOLOv8 Small模型 - 平衡速度和精度"
    },
    "yolov8m.pt": {
        "url": "https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8m.pt",
        "size": "49.7MB",
        "sha256": "5j6k7l8m9n0o1p2q3r4s5t6u7v8w9x0y1z2a3b4c5d6e7f8g9h0i1j2k3l4m5n6o",
        "description": "YOLOv8 Medium模型 - 更高精度"
    }
}

def calculate_sha256(file_path: Path) -> str:
    """计算文件SHA256哈希值"""
    sha256_hash = hashlib.sha256()
    with open(file_path, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            sha256_hash.update(chunk)
    return sha256_hash.hexdigest()

def download_file(url: str, file_path: Path, description: str = "") -> bool:
    """下载文件并显示进度"""
    try:
        print(f"正在下载: {description or file_path.name}")
        print(f"URL: {url}")
        
        def progress_hook(block_num, block_size, total_size):
            if total_size > 0:
                percent = min(100, (block_num * block_size * 100) // total_size)
                downloaded = min(total_size, block_num * block_size)
                print(f"\r进度: {percent:3d}% ({downloaded:,}/{total_size:,} bytes)", end="")
        
        urllib.request.urlretrieve(url, file_path, progress_hook)
        print()  # 换行
        return True
        
    except Exception as e:
        print(f"\n下载失败: {e}")
        return False

def verify_file(file_path: Path, expected_sha256: str) -> bool:
    """验证文件完整性"""
    if not file_path.exists():
        return False
    
    print(f"验证文件: {file_path.name}")
    actual_sha256 = calculate_sha256(file_path)
    
    if actual_sha256 == expected_sha256:
        print("✓ 文件验证通过")
        return True
    else:
        print("✗ 文件验证失败")
        print(f"期望: {expected_sha256}")
        print(f"实际: {actual_sha256}")
        return False

def download_models(models_to_download: List[str] = None, force: bool = False) -> bool:
    """下载模型文件"""
    # 确定项目根目录和模型目录
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    models_dir = project_root / "models"
    
    # 创建模型目录
    models_dir.mkdir(exist_ok=True)
    
    # 如果未指定模型，默认下载nano模型
    if models_to_download is None:
        models_to_download = ["yolov8n.pt"]
    
    success_count = 0
    total_count = len(models_to_download)
    
    for model_name in models_to_download:
        if model_name not in MODELS:
            print(f"警告: 未知模型 {model_name}")
            continue
        
        model_info = MODELS[model_name]
        model_path = models_dir / model_name
        
        print(f"\n{'='*60}")
        print(f"处理模型: {model_name}")
        print(f"描述: {model_info['description']}")
        print(f"大小: {model_info['size']}")
        print(f"{'='*60}")
        
        # 检查文件是否已存在
        if model_path.exists() and not force:
            print(f"文件已存在: {model_path}")
            
            # 验证现有文件
            if verify_file(model_path, model_info["sha256"]):
                print("跳过下载（文件已存在且验证通过）")
                success_count += 1
                continue
            else:
                print("现有文件损坏，重新下载...")
                model_path.unlink()
        
        # 下载文件
        if download_file(model_info["url"], model_path, model_info["description"]):
            # 验证下载的文件
            if verify_file(model_path, model_info["sha256"]):
                print(f"✓ {model_name} 下载完成")
                success_count += 1
            else:
                print(f"✗ {model_name} 下载失败（验证不通过）")
                model_path.unlink()  # 删除损坏的文件
        else:
            print(f"✗ {model_name} 下载失败")
    
    print(f"\n{'='*60}")
    print(f"下载完成: {success_count}/{total_count} 个模型")
    print(f"{'='*60}")
    
    return success_count == total_count

def list_available_models():
    """列出可用的模型"""
    print("可用的模型:")
    print(f"{'模型名称':<15} {'大小':<10} {'描述'}")
    print("-" * 60)
    
    for model_name, info in MODELS.items():
        print(f"{model_name:<15} {info['size']:<10} {info['description']}")

def check_existing_models():
    """检查已存在的模型"""
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    models_dir = project_root / "models"
    
    if not models_dir.exists():
        print("模型目录不存在")
        return
    
    print("已存在的模型:")
    print(f"{'模型名称':<15} {'状态':<10} {'大小'}")
    print("-" * 40)
    
    for model_name in MODELS.keys():
        model_path = models_dir / model_name
        if model_path.exists():
            size = model_path.stat().st_size
            size_mb = size / (1024 * 1024)
            
            # 验证文件
            if verify_file(model_path, MODELS[model_name]["sha256"]):
                status = "✓ 正常"
            else:
                status = "✗ 损坏"
            
            print(f"{model_name:<15} {status:<10} {size_mb:.1f}MB")
        else:
            print(f"{model_name:<15} {'未下载':<10} -")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(
        description="YOLO模型下载工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python download_models.py                    # 下载默认模型(nano)
  python download_models.py --all             # 下载所有模型
  python download_models.py --models yolov8s.pt yolov8m.pt  # 下载指定模型
  python download_models.py --list            # 列出可用模型
  python download_models.py --check           # 检查已存在的模型
  python download_models.py --force           # 强制重新下载
        """
    )
    
    parser.add_argument(
        "--models", "-m",
        nargs="+",
        help="要下载的模型名称"
    )
    
    parser.add_argument(
        "--all", "-a",
        action="store_true",
        help="下载所有可用模型"
    )
    
    parser.add_argument(
        "--list", "-l",
        action="store_true",
        help="列出可用的模型"
    )
    
    parser.add_argument(
        "--check", "-c",
        action="store_true",
        help="检查已存在的模型"
    )
    
    parser.add_argument(
        "--force", "-f",
        action="store_true",
        help="强制重新下载（即使文件已存在）"
    )
    
    args = parser.parse_args()
    
    # 处理命令行参数
    if args.list:
        list_available_models()
        return 0
    
    if args.check:
        check_existing_models()
        return 0
    
    # 确定要下载的模型
    if args.all:
        models_to_download = list(MODELS.keys())
    elif args.models:
        models_to_download = args.models
    else:
        models_to_download = ["yolov8n.pt"]  # 默认下载nano模型
    
    # 下载模型
    success = download_models(models_to_download, args.force)
    
    if success:
        print("\n所有模型下载完成！")
        return 0
    else:
        print("\n部分模型下载失败！")
        return 1

if __name__ == "__main__":
    sys.exit(main())
