# 源代码编辑器功能说明

## 🎉 功能概述

GUI界面已成功添加了完整的源代码编辑器功能，支持在线编辑、直接运行、复位功能，并明确指出了运行环境、模板图片、模型文件的路径以及脚本文件与这些文件的相对路径关系。

## 🚀 访问方式

在主窗口工具栏中点击 **"📄 源代码"** 按钮即可打开源代码编辑器。

## ✨ 核心功能

### 1. 📝 代码编辑功能
- **编辑模式切换**: 通过复选框控制只读/编辑模式
- **语法高亮**: 编辑模式下使用蓝色背景和黄色文字
- **等宽字体**: 使用 Consolas/Courier New 字体显示代码
- **实时编辑**: 支持在线修改代码内容

### 2. ▶️ 代码直接运行
- **实时执行**: 在独立线程中运行代码，不阻塞界面
- **实时输出**: 显示执行过程中的所有输出信息
- **执行控制**: 提供运行/停止按钮控制执行过程
- **返回码显示**: 显示执行成功/失败状态
- **编码处理**: 自动处理中文路径和中文输出编码问题

### 3. 🔄 代码复位功能
- **原始代码存储**: 自动保存每个标签页的原始代码
- **一键复位**: 将修改的代码恢复到原始状态
- **复位确认**: 显示复位完成提示信息

### 4. 🌍 环境信息显示
- **运行环境**: 显示Python版本、解释器路径、虚拟环境信息
- **文件结构**: 详细的项目目录结构说明
- **相对路径**: 脚本与模型、模板、截图文件的关系
- **依赖检查**: 自动检测所需库的安装状态

## 📂 文件路径关系

### 项目目录结构
```
项目根目录 (yolo_opencv_run/)
├── 🤖 YOLO模型文件:
│   ├── models/yolov8n.pt (轻量级模型, 6MB)
│   ├── models/yolov8s.pt (小型模型, 22MB)
│   ├── models/yolov8m.pt (中型模型, 52MB)
│   └── models/yolov8l.pt (大型模型, 140MB)
│
├── 🖼️ 模板图片:
│   ├── templates/test_template.png
│   ├── templates/w.png
│   └── templates/region_*.png
│
├── 📸 截图保存:
│   └── screenshots/detection_*.png
│
├── 📋 配置文件:
│   ├── configs/default.yaml
│   └── configs/user_config.yaml
│
├── 🐍 虚拟环境:
│   └── venv/ (Python虚拟环境)
│
└── 📄 脚本文件:
    ├── standalone_detector_example.py
    └── simple_detection.py
```

### 🔗 相对路径关系
- **脚本运行目录**: 项目根目录
- **模型文件**: `./models/yolov8n.pt`
- **模板文件**: `./templates/`
- **截图目录**: `./screenshots/`
- **虚拟环境**: `./venv/`

## 📚 代码标签页

### 🧪 编码测试
- **功能**: 测试中文输出和编码处理
- **用途**: 验证运行环境是否正常
- **特点**: 包含emoji、中文字符、路径测试

### 🎯 简化检测器
- **功能**: 完整的检测器类
- **包含**: 截图、YOLO检测、坐标提取功能
- **特点**: 独立运行，无需依赖项目内部模块

### 📖 使用示例
- **功能**: 基本使用方法演示
- **包含**: 检测、坐标获取、结果处理
- **特点**: 多种使用场景的示例代码

### 🤖 自动化示例
- **功能**: 自动化操作演示
- **包含**: 鼠标点击、键盘输入、拖拽操作
- **特点**: 结合检测结果进行自动化操作

## ⚠️ 运行要求

### 环境准备
1. **激活虚拟环境**: `venv/Scripts/activate`
2. **安装依赖**: `pip install -r requirements.txt`
3. **下载模型**: `python download_yolo_models.py`
4. **确保模板文件存在**: 在 `templates/` 目录中

### 依赖库
- `ultralytics` - YOLO模型
- `opencv-python` - 图像处理
- `mss` - 屏幕截图
- `pillow` - 图像处理
- `pyautogui` - 自动化操作

## 🎯 使用流程

1. **打开编辑器**: 点击工具栏的 "📄 源代码" 按钮
2. **查看环境信息**: 右侧面板自动显示运行环境和路径信息
3. **选择代码标签**: 选择要编辑或运行的代码标签页
4. **编辑代码**: 勾选 "📝 编辑模式" 复选框进行编辑
5. **运行代码**: 点击 "▶️ 运行代码" 按钮执行
6. **查看输出**: 在右侧输出面板查看实时执行结果
7. **复位代码**: 点击 "🔄 复位代码" 恢复原始代码
8. **保存代码**: 点击 "💾 保存文件" 保存到本地

## 🔧 编码问题解决

### 问题描述
在Windows系统上运行包含中文路径或中文注释的Python代码时可能出现编码错误：
```
'gbk' codec can't decode byte 0x85 in position 2: illegal multibyte sequence
```

### 解决方案
1. **自动添加编码声明**: `# -*- coding: utf-8 -*-`
2. **设置环境变量**: `PYTHONIOENCODING=utf-8`, `PYTHONUTF8=1`
3. **使用UTF-8编码**: 所有文件操作使用UTF-8编码
4. **错误处理**: 遇到编码错误时替换而不是崩溃

## 💡 特色功能

### 智能编码处理
- 自动检测Windows系统并设置UTF-8编码
- 处理中文路径和中文输出
- 支持emoji和特殊字符显示

### 实时执行反馈
- 独立线程执行，不阻塞界面
- 实时显示执行输出
- 支持停止正在执行的代码

### 完整的开发环境
- 代码编辑、运行、调试一体化
- 详细的环境信息和路径说明
- 完整的依赖检查和错误提示

## 🎉 总结

源代码编辑器为用户提供了完整的代码开发和测试环境，用户可以：
- 直接在GUI中编辑和运行检测代码
- 清楚了解所有文件的路径关系和运行环境要求
- 快速测试和验证代码功能
- 复制代码到自己的项目中使用

这个功能大大提升了用户体验，使得从学习到实际应用的过程更加顺畅。
