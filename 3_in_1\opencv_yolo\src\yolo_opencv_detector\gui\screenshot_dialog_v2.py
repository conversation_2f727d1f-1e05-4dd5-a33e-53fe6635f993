# -*- coding: utf-8 -*-
"""
重构的截图对话框 - 支持全屏截图和区域选择
作者: Cursor AI
创建时间: 2025-07-05
编码标准: UTF-8无BOM
"""

import time
from typing import Optional, Dict, Any
import numpy as np
from pathlib import Path

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QGroupBox, QMessageBox, QScrollArea, QFrame
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QPixmap, QFont

from ..utils.logger import Logger
from .screen_capture_overlay_v2 import start_screen_capture_v2


class ScreenshotPreviewWidget(QLabel):
    """截图预览控件"""
    
    def __init__(self):
        super().__init__()
        self.setMinimumSize(400, 300)
        self.setMaximumSize(800, 600)
        self.setStyleSheet("""
            QLabel {
                border: 2px solid #bdc3c7;
                background-color: #f8f9fa;
                border-radius: 8px;
            }
        """)
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.setScaledContents(True)
        self.setText("📸 点击按钮开始截图")
        
        # 设置字体
        font = QFont()
        font.setPointSize(14)
        font.setBold(True)
        self.setFont(font)
    
    def set_screenshot(self, pixmap: QPixmap):
        """设置截图"""
        if pixmap and not pixmap.isNull():
            # 缩放图像以适应控件
            scaled_pixmap = pixmap.scaled(
                self.size(), 
                Qt.AspectRatioMode.KeepAspectRatio, 
                Qt.TransformationMode.SmoothTransformation
            )
            self.setPixmap(scaled_pixmap)
        else:
            self.setText("❌ 截图失败")


class ScreenshotDialogV2(QDialog):
    """重构的截图对话框"""
    
    screenshot_taken = pyqtSignal(object, str)  # (image, filepath)
    region_selected = pyqtSignal(object, tuple)  # (image, (x, y, width, height))
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = Logger()
        
        self.current_screenshot = None
        self.current_filepath = None
        self.capture_overlay = None
        
        self._init_ui()
        self._init_connections()
        
        self.logger.info("截图对话框初始化完成")
    
    def _init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("📷 屏幕截图工具")
        self.setModal(True)
        self.resize(900, 700)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        title_label = QLabel("📷 屏幕截图工具")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 10px;
                text-align: center;
                qproperty-alignment: AlignCenter;
            }
        """)
        layout.addWidget(title_label)
        
        # 操作按钮组
        self._create_action_group(layout)
        
        # 预览组
        self._create_preview_group(layout)
        
        # 状态信息组
        self._create_status_group(layout)
        
        # 底部按钮组
        self._create_button_group(layout)
    
    def _create_action_group(self, parent_layout):
        """创建操作按钮组"""
        group = QGroupBox("🎯 截图操作")
        group.setMaximumHeight(120)
        layout = QVBoxLayout(group)
        layout.setSpacing(10)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # 按钮布局
        button_layout = QHBoxLayout()
        button_layout.setSpacing(15)
        
        # 全屏截图按钮
        self.fullscreen_button = QPushButton("🖥️ 全屏截图")
        self.fullscreen_button.setMinimumHeight(45)
        self.fullscreen_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        button_layout.addWidget(self.fullscreen_button)
        
        # 区域截图按钮
        self.region_button = QPushButton("🎯 区域截图")
        self.region_button.setMinimumHeight(45)
        self.region_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        button_layout.addWidget(self.region_button)
        
        layout.addLayout(button_layout)
        
        # 提示标签
        hint_label = QLabel("💡 全屏截图：截取整个屏幕 | 区域截图：拖拽选择特定区域")
        hint_label.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                font-style: italic;
                font-size: 12px;
                padding: 5px;
                text-align: center;
                qproperty-alignment: AlignCenter;
            }
        """)
        layout.addWidget(hint_label)
        
        parent_layout.addWidget(group)
    
    def _create_preview_group(self, parent_layout):
        """创建预览组"""
        group = QGroupBox("🖼️ 截图预览")
        layout = QVBoxLayout(group)
        layout.setSpacing(10)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setMinimumHeight(350)
        
        # 预览控件
        self.preview_widget = ScreenshotPreviewWidget()
        scroll_area.setWidget(self.preview_widget)
        
        layout.addWidget(scroll_area)
        parent_layout.addWidget(group)
    
    def _create_status_group(self, parent_layout):
        """创建状态信息组"""
        group = QGroupBox("ℹ️ 状态信息")
        group.setMaximumHeight(100)
        layout = QVBoxLayout(group)
        layout.setSpacing(8)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # 状态标签
        self.status_label = QLabel("🟢 就绪 - 请选择截图方式")
        self.status_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #27ae60;
                padding: 10px;
                background-color: #d5f4e6;
                border-radius: 6px;
                border: 2px solid #27ae60;
                text-align: center;
                qproperty-alignment: AlignCenter;
            }
        """)
        layout.addWidget(self.status_label)
        
        # 文件路径标签
        self.filepath_label = QLabel("📁 文件路径: 未保存")
        self.filepath_label.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                font-size: 11px;
                padding: 5px;
                background-color: #f8f9fa;
                border-radius: 4px;
            }
        """)
        layout.addWidget(self.filepath_label)
        
        parent_layout.addWidget(group)
    
    def _create_button_group(self, parent_layout):
        """创建底部按钮组"""
        layout = QHBoxLayout()
        layout.setSpacing(15)
        
        # 关闭按钮
        close_button = QPushButton("❌ 关闭")
        close_button.setMinimumHeight(40)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 13px;
                padding: 8px 20px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        close_button.clicked.connect(self.reject)
        layout.addWidget(close_button)
        
        layout.addStretch()
        
        # 保存按钮
        self.save_button = QPushButton("💾 另存为...")
        self.save_button.setMinimumHeight(40)
        self.save_button.setEnabled(False)
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 13px;
                padding: 8px 20px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        layout.addWidget(self.save_button)
        
        # 确定按钮
        self.ok_button = QPushButton("✅ 确定")
        self.ok_button.setMinimumHeight(40)
        self.ok_button.setEnabled(False)
        self.ok_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 13px;
                padding: 8px 20px;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        layout.addWidget(self.ok_button)
        
        parent_layout.addLayout(layout)
    
    def _init_connections(self):
        """初始化信号连接"""
        self.fullscreen_button.clicked.connect(self._take_fullscreen)
        self.region_button.clicked.connect(self._take_region)
        self.save_button.clicked.connect(self._save_as)
        self.ok_button.clicked.connect(self._confirm_screenshot)
    
    def _take_fullscreen(self):
        """全屏截图"""
        try:
            self.status_label.setText("📸 正在截取全屏...")
            self.status_label.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    font-weight: bold;
                    color: #e74c3c;
                    padding: 10px;
                    background-color: #fadbd8;
                    border-radius: 6px;
                    border: 2px solid #e74c3c;
                    text-align: center;
                    qproperty-alignment: AlignCenter;
                }
            """)
            
            # 隐藏对话框
            self.hide()
            
            # 延迟截图
            QTimer.singleShot(500, self._do_fullscreen_capture)
            
        except Exception as e:
            self.logger.error(f"全屏截图失败: {e}")
            self._show_error(f"全屏截图失败: {e}")
    
    def _do_fullscreen_capture(self):
        """执行全屏截图"""
        try:
            from ..utils.screenshot_helper import get_screenshot_helper
            screenshot_helper = get_screenshot_helper()
            
            if screenshot_helper.is_available():
                image, pixmap, filepath = screenshot_helper.take_screenshot_with_pixmap(save_to_file=True)
                
                if image is not None and pixmap is not None:
                    self.current_screenshot = image
                    self.current_filepath = filepath
                    
                    # 更新预览
                    self.preview_widget.set_screenshot(pixmap)
                    
                    # 更新状态
                    self.status_label.setText("✅ 全屏截图完成")
                    self.status_label.setStyleSheet("""
                        QLabel {
                            font-size: 14px;
                            font-weight: bold;
                            color: #27ae60;
                            padding: 10px;
                            background-color: #d5f4e6;
                            border-radius: 6px;
                            border: 2px solid #27ae60;
                            text-align: center;
                            qproperty-alignment: AlignCenter;
                        }
                    """)
                    
                    self.filepath_label.setText(f"📁 文件路径: {filepath}")
                    
                    # 启用按钮
                    self.save_button.setEnabled(True)
                    self.ok_button.setEnabled(True)
                    
                    self.logger.info(f"全屏截图完成: {filepath}")
                else:
                    self._show_error("全屏截图失败")
            else:
                self._show_error("截图服务不可用")
            
            # 显示对话框
            self.show()
            
        except Exception as e:
            self.logger.error(f"执行全屏截图失败: {e}")
            self._show_error(f"执行全屏截图失败: {e}")
            self.show()
    
    def _take_region(self):
        """区域截图"""
        try:
            self.status_label.setText("🎯 请在屏幕上拖拽选择区域...")
            self.status_label.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    font-weight: bold;
                    color: #f39c12;
                    padding: 10px;
                    background-color: #fef9e7;
                    border-radius: 6px;
                    border: 2px solid #f39c12;
                    text-align: center;
                    qproperty-alignment: AlignCenter;
                }
            """)
            
            # 隐藏对话框
            self.hide()
            
            # 启动区域选择覆盖层
            self.capture_overlay = start_screen_capture_v2()
            self.capture_overlay.region_captured.connect(self._on_region_captured)
            self.capture_overlay.capture_cancelled.connect(self._on_capture_cancelled)
            self.capture_overlay.fullscreen_captured.connect(self._on_fullscreen_captured)
            
        except Exception as e:
            self.logger.error(f"区域截图失败: {e}")
            self._show_error(f"区域截图失败: {e}")
    
    def _on_region_captured(self, x, y, width, height, image):
        """区域截取完成"""
        try:
            # 保存区域图像
            timestamp = int(time.time())
            filename = f"region_{timestamp}_{x}_{y}_{width}x{height}.png"
            screenshots_dir = Path("screenshots")
            screenshots_dir.mkdir(exist_ok=True)
            filepath = screenshots_dir / filename
            
            # 保存图像
            import cv2
            if cv2.imwrite(str(filepath), image):
                self.current_screenshot = image
                self.current_filepath = str(filepath)
                
                # 转换为QPixmap
                from ..utils.screenshot_helper import get_screenshot_helper
                screenshot_helper = get_screenshot_helper()
                pixmap = screenshot_helper.numpy_to_qpixmap(image)
                
                if pixmap:
                    self.preview_widget.set_screenshot(pixmap)
                
                # 更新状态
                self.status_label.setText(f"✅ 区域截图完成: {width}×{height}")
                self.status_label.setStyleSheet("""
                    QLabel {
                        font-size: 14px;
                        font-weight: bold;
                        color: #27ae60;
                        padding: 10px;
                        background-color: #d5f4e6;
                        border-radius: 6px;
                        border: 2px solid #27ae60;
                        text-align: center;
                        qproperty-alignment: AlignCenter;
                    }
                """)
                
                self.filepath_label.setText(f"📁 文件路径: {filepath}")
                
                # 启用按钮
                self.save_button.setEnabled(True)
                self.ok_button.setEnabled(True)
                
                # 发送区域选择信号
                self.region_selected.emit(image, (x, y, width, height))
                
                self.logger.info(f"区域截图完成: {width}×{height}, 保存到: {filepath}")
            else:
                self._show_error("保存区域截图失败")
            
            # 显示对话框
            self.show()
            
        except Exception as e:
            self.logger.error(f"处理区域截图失败: {e}")
            self._show_error(f"处理区域截图失败: {e}")
            self.show()
    
    def _on_fullscreen_captured(self, image, filepath):
        """全屏截图完成（来自覆盖层）"""
        # 这个信号用于在区域选择时获取全屏截图
        pass
    
    def _on_capture_cancelled(self):
        """截图取消"""
        self.status_label.setText("❌ 截图已取消")
        self.status_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #e74c3c;
                padding: 10px;
                background-color: #fadbd8;
                border-radius: 6px;
                border: 2px solid #e74c3c;
                text-align: center;
                qproperty-alignment: AlignCenter;
            }
        """)
        self.show()
    
    def _save_as(self):
        """另存为"""
        if self.current_screenshot is None:
            return
        
        try:
            from PyQt6.QtWidgets import QFileDialog
            
            filename, _ = QFileDialog.getSaveFileName(
                self,
                "保存截图",
                f"screenshot_{int(time.time())}.png",
                "PNG文件 (*.png);;JPEG文件 (*.jpg);;所有文件 (*)"
            )
            
            if filename:
                import cv2
                if cv2.imwrite(filename, self.current_screenshot):
                    self.filepath_label.setText(f"📁 文件路径: {filename}")
                    QMessageBox.information(self, "成功", f"截图已保存到: {filename}")
                    self.logger.info(f"截图另存为: {filename}")
                else:
                    QMessageBox.warning(self, "错误", "保存截图失败")
                    
        except Exception as e:
            self.logger.error(f"另存为失败: {e}")
            QMessageBox.warning(self, "错误", f"另存为失败: {e}")
    
    def _confirm_screenshot(self):
        """确认截图"""
        if self.current_screenshot is not None and self.current_filepath:
            self.screenshot_taken.emit(self.current_screenshot, self.current_filepath)
            self.accept()
    
    def _show_error(self, message: str):
        """显示错误"""
        self.status_label.setText(f"❌ {message}")
        self.status_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #e74c3c;
                padding: 10px;
                background-color: #fadbd8;
                border-radius: 6px;
                border: 2px solid #e74c3c;
                text-align: center;
                qproperty-alignment: AlignCenter;
            }
        """)
        self.logger.error(message)
