# YOLO OpenCV 屏幕识别工具

基于YOLO深度学习检测结合OpenCV模板匹配的Windows屏幕识别工具

> 本文件由AI辅助生成 - Generated with AI Assistance  
> 生成时间: 2025-01-27 CST  
> AI模型: Claude-4-Sonnet  
> 审核状态: 待人工安全审查  
> 编码标准: UTF-8无BOM

## 项目简介

本项目是一个智能屏幕识别工具，结合了YOLO深度学习目标检测和OpenCV传统模板匹配技术，能够准确识别Windows屏幕上的各种UI元素，并生成相应的自动化操作脚本。

## 核心功能

- 🎯 **YOLO深度学习检测**：使用YOLOv8模型进行智能目标检测
- 🔍 **OpenCV模板匹配**：传统计算机视觉方法的精确匹配
- 🔄 **结果融合算法**：智能融合两种检测结果，提高准确率
- 🖥️ **多显示器支持**：完整的多屏幕坐标映射和管理
- 🎨 **PyQt6 GUI界面**：现代化的用户交互界面
- 📝 **脚本自动生成**：支持Python和C#自动化脚本生成
- ⚡ **GPU加速支持**：CUDA/cuDNN加速的深度学习推理

## 技术架构

```
├── 截图层：屏幕截取、坐标映射、多显示器支持
├── 检测层：YOLO检测服务、模板匹配服务
├── 融合层：结果融合与后处理
├── 交互层：PyQt6 GUI、自动化操作脚本生成
└── 管理层：配置管理、日志与监控
```

## 项目结构

```
yolo_opencv_run/
├── src/                          # 源代码目录
│   └── yolo_opencv_detector/      # 主包
│       ├── core/                  # 核心模块
│       ├── utils/                 # 工具模块
│       └── gui/                   # GUI模块
├── tests/                         # 测试代码
├── configs/                       # 配置文件
├── docs/                          # 文档目录
├── models/                        # 模型文件
├── templates/                     # 模板文件
├── logs/                          # 日志文件
├── data/                          # 数据文件
├── requirements.txt               # 依赖列表
├── setup.py                       # 安装脚本
└── README.md                      # 项目说明
```

## 快速开始

### 环境要求

- Python 3.9+
- Windows 10/11
- 8GB+ RAM
- NVIDIA GPU (可选，用于加速)

### 安装步骤

1. 克隆项目
```bash
git clone <repository-url>
cd yolo_opencv_run
```

2. 创建虚拟环境
```bash
python -m venv venv
venv\Scripts\activate
```

3. 安装依赖
```bash
pip install -r requirements.txt
```

4. 运行应用
```bash
python -m yolo_opencv_detector.main
```

## 开发指南

### 代码规范

- 使用UTF-8无BOM编码
- 遵循PEP 8代码风格
- 添加完整的类型注解
- 编写详细的文档字符串

### 测试

```bash
# 运行所有测试
pytest

# 运行测试并生成覆盖率报告
pytest --cov=src/yolo_opencv_detector --cov-report=html
```

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！
