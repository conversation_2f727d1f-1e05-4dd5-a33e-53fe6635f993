# -*- coding: utf-8 -*-
"""
重构的模板截取对话框 - 支持区域选择和模板创建
作者: Cursor AI
创建时间: 2025-07-05
编码标准: UTF-8无BOM
"""

import time
from typing import Optional, Dict, Any, Tuple
import numpy as np
import cv2
from pathlib import Path

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QLineEdit, QTextEdit, QComboBox, QGroupBox, QMessageBox,
    QProgressBar, QFrame, QSizePolicy
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QPixmap, QImage, QPainter, QPen, QColor

from ..utils.logger import Logger
from ..core.screen_capture_v2 import ScreenCaptureServiceV2


class RegionSelectorWidget(QLabel):
    """区域选择控件"""
    
    region_selected = pyqtSignal(tuple)  # (x, y, width, height)
    
    def __init__(self):
        super().__init__()
        self.setMinimumSize(400, 300)
        self.setStyleSheet("border: 2px solid #bdc3c7; background-color: #f8f9fa;")
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        self.image = None
        self.scale_factor = 1.0
        self.start_point = None
        self.end_point = None
        self.selecting = False
        
        self.setText("点击'截取屏幕'按钮开始")
    
    def set_image(self, image: np.ndarray):
        """设置显示的图像"""
        try:
            self.image = image.copy()
            self._update_display()
        except Exception as e:
            print(f"设置图像失败: {e}")
    
    def _update_display(self):
        """更新显示"""
        if self.image is None:
            return
        
        try:
            # 转换为QImage
            height, width, channel = self.image.shape
            bytes_per_line = 3 * width
            q_image = QImage(self.image.data, width, height, bytes_per_line, QImage.Format.Format_RGB888).rgbSwapped()
            
            # 计算缩放比例
            widget_size = self.size()
            image_size = q_image.size()
            
            scale_x = widget_size.width() / image_size.width()
            scale_y = widget_size.height() / image_size.height()
            self.scale_factor = min(scale_x, scale_y, 1.0)  # 不放大
            
            # 缩放图像
            scaled_size = image_size * self.scale_factor
            scaled_image = q_image.scaled(scaled_size, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
            
            # 创建QPixmap并绘制选择框
            pixmap = QPixmap.fromImage(scaled_image)
            
            if self.start_point and self.end_point:
                painter = QPainter(pixmap)
                pen = QPen(QColor(255, 0, 0), 2)
                painter.setPen(pen)
                
                # 计算选择框在缩放图像上的坐标
                x1 = int(self.start_point[0] * self.scale_factor)
                y1 = int(self.start_point[1] * self.scale_factor)
                x2 = int(self.end_point[0] * self.scale_factor)
                y2 = int(self.end_point[1] * self.scale_factor)
                
                painter.drawRect(min(x1, x2), min(y1, y2), abs(x2 - x1), abs(y2 - y1))
                painter.end()
            
            self.setPixmap(pixmap)
            
        except Exception as e:
            print(f"更新显示失败: {e}")
    
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if self.image is None:
            return
        
        if event.button() == Qt.MouseButton.LeftButton:
            # 转换坐标到原始图像坐标系
            pos = event.position()
            x = int(pos.x() / self.scale_factor)
            y = int(pos.y() / self.scale_factor)
            
            # 限制在图像范围内
            x = max(0, min(x, self.image.shape[1] - 1))
            y = max(0, min(y, self.image.shape[0] - 1))
            
            self.start_point = (x, y)
            self.end_point = None
            self.selecting = True
    
    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if self.selecting and self.start_point:
            # 转换坐标到原始图像坐标系
            pos = event.position()
            x = int(pos.x() / self.scale_factor)
            y = int(pos.y() / self.scale_factor)
            
            # 限制在图像范围内
            x = max(0, min(x, self.image.shape[1] - 1))
            y = max(0, min(y, self.image.shape[0] - 1))
            
            self.end_point = (x, y)
            self._update_display()
    
    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.MouseButton.LeftButton and self.selecting:
            self.selecting = False
            
            if self.start_point and self.end_point:
                # 计算选择区域
                x1, y1 = self.start_point
                x2, y2 = self.end_point
                
                x = min(x1, x2)
                y = min(y1, y2)
                width = abs(x2 - x1)
                height = abs(y2 - y1)
                
                if width > 10 and height > 10:  # 最小尺寸限制
                    self.region_selected.emit((x, y, width, height))


class TemplateCaptureDialogV2(QDialog):
    """重构的模板截取对话框"""
    
    template_created = pyqtSignal(dict)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = Logger()
        self.screen_capture = ScreenCaptureServiceV2()
        
        self.captured_image = None
        self.selected_region = None
        self.template_image = None
        
        self._init_ui()
        self._init_connections()
        
        self.logger.info("模板截取对话框初始化完成")
    
    def _init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("截取模板")
        self.setModal(True)
        self.resize(800, 700)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        title_label = QLabel("📷 模板截取工具")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # 截图控制组
        self._create_capture_group(layout)
        
        # 区域选择组
        self._create_selection_group(layout)
        
        # 模板信息组
        self._create_info_group(layout)
        
        # 按钮组
        self._create_button_group(layout)
    
    def _create_capture_group(self, parent_layout):
        """创建截图控制组"""
        group = QGroupBox("🖥️ 屏幕截图")
        group.setMaximumHeight(80)
        layout = QHBoxLayout(group)
        layout.setSpacing(10)
        
        # 截图按钮
        self.capture_button = QPushButton("📸 截取屏幕")
        self.capture_button.setMinimumHeight(40)
        self.capture_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 13px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        layout.addWidget(self.capture_button)
        
        # 状态标签
        self.status_label = QLabel("点击按钮开始截图")
        self.status_label.setStyleSheet("color: #7f8c8d; font-style: italic;")
        layout.addWidget(self.status_label)
        
        layout.addStretch()
        parent_layout.addWidget(group)
    
    def _create_selection_group(self, parent_layout):
        """创建区域选择组"""
        group = QGroupBox("🎯 区域选择")
        layout = QVBoxLayout(group)
        layout.setSpacing(10)
        
        # 提示标签
        hint_label = QLabel("在下方图像中拖拽鼠标选择模板区域")
        hint_label.setStyleSheet("color: #34495e; font-weight: bold;")
        layout.addWidget(hint_label)
        
        # 区域选择控件
        self.region_selector = RegionSelectorWidget()
        layout.addWidget(self.region_selector)
        
        # 区域信息
        self.region_info_label = QLabel("未选择区域")
        self.region_info_label.setStyleSheet("""
            QLabel {
                padding: 8px;
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                color: #495057;
            }
        """)
        layout.addWidget(self.region_info_label)
        
        parent_layout.addWidget(group)
    
    def _create_info_group(self, parent_layout):
        """创建模板信息组"""
        group = QGroupBox("ℹ️ 模板信息")
        group.setMaximumHeight(150)
        layout = QVBoxLayout(group)
        layout.setSpacing(8)
        
        # 名称
        name_layout = QHBoxLayout()
        name_layout.addWidget(QLabel("名称:"))
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("输入模板名称...")
        name_layout.addWidget(self.name_edit)
        layout.addLayout(name_layout)
        
        # 类别
        category_layout = QHBoxLayout()
        category_layout.addWidget(QLabel("类别:"))
        self.category_combo = QComboBox()
        self.category_combo.addItems(["通用", "按钮", "图标", "文本", "其他"])
        category_layout.addWidget(self.category_combo)
        layout.addLayout(category_layout)
        
        # 描述
        desc_layout = QVBoxLayout()
        desc_layout.addWidget(QLabel("描述:"))
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(50)
        self.description_edit.setPlaceholderText("输入模板描述...")
        desc_layout.addWidget(self.description_edit)
        layout.addLayout(desc_layout)
        
        parent_layout.addWidget(group)
    
    def _create_button_group(self, parent_layout):
        """创建按钮组"""
        layout = QHBoxLayout()
        layout.setSpacing(10)
        
        # 取消按钮
        cancel_button = QPushButton("❌ 取消")
        cancel_button.setMinimumHeight(40)
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 13px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        cancel_button.clicked.connect(self.reject)
        layout.addWidget(cancel_button)
        
        layout.addStretch()
        
        # 创建模板按钮
        self.create_button = QPushButton("✅ 创建模板")
        self.create_button.setMinimumHeight(40)
        self.create_button.setEnabled(False)
        self.create_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 13px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        layout.addWidget(self.create_button)
        
        parent_layout.addLayout(layout)
    
    def _init_connections(self):
        """初始化信号连接"""
        self.capture_button.clicked.connect(self._capture_screen)
        self.region_selector.region_selected.connect(self._on_region_selected)
        self.create_button.clicked.connect(self._create_template)
        self.name_edit.textChanged.connect(self._validate_input)
    
    def _capture_screen(self):
        """截取屏幕"""
        try:
            self.status_label.setText("正在截取屏幕...")
            
            # 隐藏对话框
            self.hide()
            
            # 等待一下让对话框完全隐藏
            QTimer.singleShot(500, self._do_capture)
            
        except Exception as e:
            self.logger.error(f"截取屏幕失败: {e}")
            self.status_label.setText(f"截图失败: {e}")
            self.show()
    
    def _do_capture(self):
        """执行截图"""
        try:
            # 截取全屏
            image = self.screen_capture.capture_fullscreen()
            
            if image is not None:
                self.captured_image = image
                self.region_selector.set_image(image)
                
                height, width = image.shape[:2]
                self.status_label.setText(f"✅ 截图成功: {width}×{height}")
                
                # 生成默认名称
                if not self.name_edit.text():
                    timestamp = int(time.time())
                    self.name_edit.setText(f"template_{timestamp}")
                
                self.logger.info(f"屏幕截图成功: {width}×{height}")
            else:
                self.status_label.setText("❌ 截图失败")
                self.logger.error("屏幕截图失败")
            
            # 显示对话框
            self.show()
            
        except Exception as e:
            self.logger.error(f"执行截图失败: {e}")
            self.status_label.setText(f"截图失败: {e}")
            self.show()
    
    def _on_region_selected(self, region):
        """处理区域选择"""
        try:
            x, y, width, height = region
            self.selected_region = region
            
            # 更新区域信息
            self.region_info_label.setText(f"已选择区域: ({x}, {y}) - {width}×{height}")
            
            # 提取模板图像
            if self.captured_image is not None:
                self.template_image = self.captured_image[y:y+height, x:x+width].copy()
                self.logger.info(f"选择模板区域: {width}×{height}")
            
            # 验证输入
            self._validate_input()
            
        except Exception as e:
            self.logger.error(f"处理区域选择失败: {e}")
    
    def _validate_input(self):
        """验证输入"""
        name_valid = bool(self.name_edit.text().strip())
        region_valid = self.selected_region is not None
        
        self.create_button.setEnabled(name_valid and region_valid)
    
    def _create_template(self):
        """创建模板"""
        try:
            if self.template_image is None:
                QMessageBox.warning(self, "错误", "请先选择模板区域")
                return
            
            name = self.name_edit.text().strip()
            if not name:
                QMessageBox.warning(self, "错误", "请输入模板名称")
                return
            
            # 保存模板图像到文件
            template_path = self._save_template_image(name, self.template_image)

            # 创建模板数据
            template_data = {
                'name': name,
                'category': self.category_combo.currentText(),
                'description': self.description_edit.toPlainText().strip(),
                'image': self.template_image,  # 保留内存中的图像
                'path': template_path,  # 添加文件路径
                'region': self.selected_region,
                'created_time': time.time()
            }
            
            # 发送信号
            self.template_created.emit(template_data)
            
            self.logger.info(f"模板创建成功: {name}")
            self.accept()
            
        except Exception as e:
            self.logger.error(f"创建模板失败: {e}")
            QMessageBox.critical(self, "错误", f"创建模板失败: {e}")

    def _save_template_image(self, name: str, image: np.ndarray) -> str:
        """保存模板图像到文件

        Args:
            name: 模板名称
            image: 模板图像

        Returns:
            保存的文件路径
        """
        try:
            # 创建模板目录
            templates_dir = Path("templates")
            templates_dir.mkdir(exist_ok=True)

            # 生成安全的文件名（避免中文路径问题）
            import re
            safe_name = re.sub(r'[^\w\-_.]', '_', name)
            timestamp = int(time.time())
            filename = f"{safe_name}_{timestamp}.png"
            filepath = templates_dir / filename

            # 保存图像
            import cv2
            success = cv2.imwrite(str(filepath), image)

            if success:
                self.logger.info(f"模板图像已保存: {filepath}")
                return str(filepath)
            else:
                self.logger.error(f"保存模板图像失败: {filepath}")
                return ""

        except Exception as e:
            self.logger.error(f"保存模板图像异常: {e}")
            return ""
