#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
中文编码环境设置脚本

自动配置中文编码环境，确保项目在中文环境下正常运行。
包括系统编码设置、字体配置、路径处理等。

Created: 2025-07-13
Author: Augment Agent
"""

import os
import sys
import locale
import platform
import subprocess
from pathlib import Path
from typing import Dict, List, Optional
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('setup_chinese_env.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)


class ChineseEnvironmentSetup:
    """中文环境设置器"""
    
    def __init__(self):
        self.system = platform.system()
        self.python_version = sys.version_info
        self.project_root = Path(__file__).parent.parent
        
    def setup_all(self):
        """设置所有中文环境配置"""
        logger.info("开始设置中文编码环境...")
        
        try:
            self.check_system_encoding()
            self.setup_python_encoding()
            self.setup_environment_variables()
            self.setup_qt_encoding()
            self.setup_opencv_encoding()
            self.verify_chinese_support()
            
            logger.info("中文编码环境设置完成！")
            
        except Exception as e:
            logger.error(f"中文环境设置失败: {e}")
            raise
    
    def check_system_encoding(self):
        """检查系统编码"""
        logger.info("检查系统编码...")
        
        # 检查系统默认编码
        default_encoding = sys.getdefaultencoding()
        filesystem_encoding = sys.getfilesystemencoding()
        locale_encoding = locale.getpreferredencoding()
        
        logger.info(f"默认编码: {default_encoding}")
        logger.info(f"文件系统编码: {filesystem_encoding}")
        logger.info(f"本地编码: {locale_encoding}")
        
        # 检查是否支持UTF-8
        if default_encoding.lower() != 'utf-8':
            logger.warning(f"系统默认编码不是UTF-8: {default_encoding}")
        
        # Windows特殊处理
        if self.system == "Windows":
            self._setup_windows_encoding()
    
    def _setup_windows_encoding(self):
        """设置Windows编码"""
        logger.info("配置Windows中文编码...")
        
        try:
            # 设置控制台代码页为UTF-8
            subprocess.run(['chcp', '65001'], shell=True, check=True, 
                         capture_output=True, text=True)
            logger.info("Windows控制台代码页设置为UTF-8")
            
        except subprocess.CalledProcessError as e:
            logger.warning(f"设置Windows代码页失败: {e}")
    
    def setup_python_encoding(self):
        """设置Python编码"""
        logger.info("设置Python编码环境...")
        
        # 检查Python编码设置
        if hasattr(sys, 'setdefaultencoding'):
            sys.setdefaultencoding('utf-8')
        
        # 设置标准输入输出编码
        if hasattr(sys.stdout, 'reconfigure'):
            sys.stdout.reconfigure(encoding='utf-8')
            sys.stderr.reconfigure(encoding='utf-8')
        
        # 创建编码配置文件
        self._create_encoding_config()
    
    def _create_encoding_config(self):
        """创建编码配置文件"""
        config_content = '''# -*- coding: utf-8 -*-
"""
Python编码配置文件
确保所有Python文件使用UTF-8编码
"""

import sys
import os

# 设置默认编码
if sys.version_info >= (3, 0):
    import io
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

# 设置环境变量
os.environ['PYTHONIOENCODING'] = 'utf-8'
os.environ['LANG'] = 'zh_CN.UTF-8'
os.environ['LC_ALL'] = 'zh_CN.UTF-8'
'''
        
        config_file = self.project_root / 'src' / 'encoding_config.py'
        config_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(config_content)
        
        logger.info(f"编码配置文件已创建: {config_file}")
    
    def setup_environment_variables(self):
        """设置环境变量"""
        logger.info("设置环境变量...")
        
        env_vars = {
            'PYTHONIOENCODING': 'utf-8',
            'LANG': 'zh_CN.UTF-8',
            'LC_ALL': 'zh_CN.UTF-8',
            'PYTHONUTF8': '1'
        }
        
        for key, value in env_vars.items():
            os.environ[key] = value
            logger.info(f"设置环境变量: {key}={value}")
        
        # 创建环境变量设置脚本
        self._create_env_script()
    
    def _create_env_script(self):
        """创建环境变量设置脚本"""
        # Windows批处理脚本
        if self.system == "Windows":
            bat_content = '''@echo off
chcp 65001 > nul
set PYTHONIOENCODING=utf-8
set LANG=zh_CN.UTF-8
set LC_ALL=zh_CN.UTF-8
set PYTHONUTF8=1
echo 中文编码环境已设置
'''
            bat_file = self.project_root / 'setup_chinese_env.bat'
            with open(bat_file, 'w', encoding='utf-8') as f:
                f.write(bat_content)
            logger.info(f"Windows环境脚本已创建: {bat_file}")
        
        # Linux/Mac shell脚本
        else:
            sh_content = '''#!/bin/bash
export PYTHONIOENCODING=utf-8
export LANG=zh_CN.UTF-8
export LC_ALL=zh_CN.UTF-8
export PYTHONUTF8=1
echo "中文编码环境已设置"
'''
            sh_file = self.project_root / 'setup_chinese_env.sh'
            with open(sh_file, 'w', encoding='utf-8') as f:
                f.write(sh_content)
            
            # 设置执行权限
            os.chmod(sh_file, 0o755)
            logger.info(f"Shell环境脚本已创建: {sh_file}")
    
    def setup_qt_encoding(self):
        """设置Qt编码"""
        logger.info("设置Qt中文编码...")
        
        qt_config = '''# -*- coding: utf-8 -*-
"""
Qt中文编码配置
"""

from PyQt6.QtCore import QTextCodec
from PyQt6.QtWidgets import QApplication
import sys

def setup_qt_chinese_encoding():
    """设置Qt中文编码"""
    try:
        # 设置Qt应用程序编码
        if hasattr(QTextCodec, 'setCodecForLocale'):
            utf8_codec = QTextCodec.codecForName("UTF-8")
            QTextCodec.setCodecForLocale(utf8_codec)
        
        # 设置应用程序属性
        if QApplication.instance() is None:
            app = QApplication(sys.argv)
        else:
            app = QApplication.instance()
        
        # 设置字体
        from PyQt6.QtGui import QFont
        font = QFont("Microsoft YaHei", 9)  # Windows
        if sys.platform != "win32":
            font = QFont("WenQuanYi Micro Hei", 9)  # Linux
        
        app.setFont(font)
        
        return app
        
    except Exception as e:
        print(f"Qt编码设置失败: {e}")
        return None

# 自动设置
if __name__ == "__main__":
    setup_qt_chinese_encoding()
'''
        
        qt_config_file = self.project_root / 'src' / 'qt_chinese_config.py'
        with open(qt_config_file, 'w', encoding='utf-8') as f:
            f.write(qt_config)
        
        logger.info(f"Qt编码配置文件已创建: {qt_config_file}")
    
    def setup_opencv_encoding(self):
        """设置OpenCV编码"""
        logger.info("设置OpenCV中文编码...")
        
        opencv_config = '''# -*- coding: utf-8 -*-
"""
OpenCV中文编码配置
解决中文路径和文本显示问题
"""

import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import os

def load_image_chinese_path(image_path):
    """加载中文路径的图像"""
    try:
        # 方法1: 使用PIL
        pil_image = Image.open(image_path)
        opencv_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
        return opencv_image
    except:
        try:
            # 方法2: 使用numpy和cv2.imdecode
            with open(image_path, 'rb') as f:
                data = f.read()
            nparr = np.frombuffer(data, np.uint8)
            opencv_image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            return opencv_image
        except Exception as e:
            print(f"加载图像失败: {e}")
            return None

def put_chinese_text(image, text, position, font_size=20, color=(0, 255, 0)):
    """在图像上添加中文文本"""
    try:
        # 转换为PIL图像
        pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        draw = ImageDraw.Draw(pil_image)
        
        # 加载中文字体
        font_path = get_chinese_font_path()
        if font_path:
            font = ImageFont.truetype(font_path, font_size)
        else:
            font = ImageFont.load_default()
        
        # 绘制文本
        draw.text(position, text, font=font, fill=color)
        
        # 转换回OpenCV格式
        opencv_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
        return opencv_image
        
    except Exception as e:
        print(f"添加中文文本失败: {e}")
        return image

def get_chinese_font_path():
    """获取中文字体路径"""
    font_paths = [
        # Windows
        "C:/Windows/Fonts/msyh.ttc",  # 微软雅黑
        "C:/Windows/Fonts/simsun.ttc",  # 宋体
        # Linux
        "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc",
        "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
        # macOS
        "/System/Library/Fonts/PingFang.ttc",
        "/System/Library/Fonts/Helvetica.ttc"
    ]
    
    for font_path in font_paths:
        if os.path.exists(font_path):
            return font_path
    
    return None

# 测试函数
def test_chinese_support():
    """测试中文支持"""
    print("测试中文编码支持...")
    
    # 测试中文字符串
    chinese_text = "中文测试文本"
    print(f"中文文本: {chinese_text}")
    
    # 测试中文路径
    test_path = "测试路径/test.txt"
    print(f"中文路径: {test_path}")
    
    print("中文编码测试完成")

if __name__ == "__main__":
    test_chinese_support()
'''
        
        opencv_config_file = self.project_root / 'src' / 'opencv_chinese_config.py'
        with open(opencv_config_file, 'w', encoding='utf-8') as f:
            f.write(opencv_config)
        
        logger.info(f"OpenCV编码配置文件已创建: {opencv_config_file}")
    
    def verify_chinese_support(self):
        """验证中文支持"""
        logger.info("验证中文编码支持...")
        
        test_cases = [
            ("中文字符串", "测试中文字符串处理"),
            ("中文路径", "测试/中文/路径/处理"),
            ("中文文件名", "中文文件名.txt"),
            ("特殊字符", "测试！@#￥%……&*（）"),
        ]
        
        for test_name, test_value in test_cases:
            try:
                # 测试编码和解码
                encoded = test_value.encode('utf-8')
                decoded = encoded.decode('utf-8')
                
                if decoded == test_value:
                    logger.info(f"✓ {test_name}: 通过")
                else:
                    logger.error(f"✗ {test_name}: 失败")
                    
            except Exception as e:
                logger.error(f"✗ {test_name}: 异常 - {e}")
        
        # 测试文件操作
        self._test_file_operations()
    
    def _test_file_operations(self):
        """测试文件操作"""
        logger.info("测试中文文件操作...")
        
        test_dir = self.project_root / "测试目录"
        test_file = test_dir / "中文测试文件.txt"
        
        try:
            # 创建目录
            test_dir.mkdir(exist_ok=True)
            
            # 写入文件
            with open(test_file, 'w', encoding='utf-8') as f:
                f.write("这是中文测试内容\n")
                f.write("包含特殊字符：！@#￥%……&*（）\n")
            
            # 读取文件
            with open(test_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            logger.info("✓ 中文文件操作: 通过")
            
            # 清理测试文件
            test_file.unlink()
            test_dir.rmdir()
            
        except Exception as e:
            logger.error(f"✗ 中文文件操作: 失败 - {e}")
    
    def create_deployment_guide(self):
        """创建部署指南"""
        guide_content = '''# 中文编码环境部署指南

## 1. 环境要求
- Python 3.8+
- UTF-8编码支持
- 中文字体支持

## 2. 自动设置
运行以下命令自动设置中文编码环境：

```bash
python scripts/setup_chinese_env.py
```

## 3. 手动设置

### Windows
```batch
chcp 65001
set PYTHONIOENCODING=utf-8
set LANG=zh_CN.UTF-8
set LC_ALL=zh_CN.UTF-8
set PYTHONUTF8=1
```

### Linux/Mac
```bash
export PYTHONIOENCODING=utf-8
export LANG=zh_CN.UTF-8
export LC_ALL=zh_CN.UTF-8
export PYTHONUTF8=1
```

## 4. 验证设置
```python
import sys
print(f"默认编码: {sys.getdefaultencoding()}")
print(f"文件系统编码: {sys.getfilesystemencoding()}")

# 测试中文字符
chinese_text = "中文测试"
print(f"中文测试: {chinese_text}")
```

## 5. 常见问题

### 问题1: 中文文件名无法读取
解决方案: 使用字节解码方式读取文件

### 问题2: GUI中文显示乱码
解决方案: 设置正确的字体和编码

### 问题3: 日志中文乱码
解决方案: 配置日志编码为UTF-8

## 6. 最佳实践
1. 所有Python文件使用UTF-8编码
2. 文件头添加编码声明: # -*- coding: utf-8 -*-
3. 使用Path对象处理文件路径
4. 显式指定文件编码参数
'''
        
        guide_file = self.project_root / 'docs' / '中文编码环境部署指南.md'
        guide_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(guide_file, 'w', encoding='utf-8') as f:
            f.write(guide_content)
        
        logger.info(f"部署指南已创建: {guide_file}")


def main():
    """主函数"""
    print("=" * 60)
    print("🈳 中文编码环境设置工具")
    print("=" * 60)
    
    try:
        setup = ChineseEnvironmentSetup()
        setup.setup_all()
        setup.create_deployment_guide()
        
        print("\n✅ 中文编码环境设置完成！")
        print("请重启应用程序以使设置生效。")
        
    except Exception as e:
        print(f"\n❌ 设置失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
