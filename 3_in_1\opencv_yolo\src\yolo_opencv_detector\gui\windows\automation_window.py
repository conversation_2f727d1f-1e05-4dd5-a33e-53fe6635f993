#!/usr/bin/env python3
"""
独立的自动化操作窗口
提供完整的自动化配置功能，支持不同屏幕分辨率
"""

from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QSplitter,
    QMenuBar, QStatusBar, QToolBar, QLabel, QPushButton,
    QMessageBox, QApplication, QFrame
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer, QSettings
from PyQt6.QtGui import QFont, QIcon, QKeySequence, QScreen, QAction
from typing import List, Dict, Any, Optional
import logging
from pathlib import Path

class AutomationWindow(QMainWindow):
    """独立的自动化操作窗口"""
    
    # 信号定义
    window_closed = pyqtSignal()  # 窗口关闭
    operation_executed = pyqtSignal(list)  # 操作执行
    settings_changed = pyqtSignal(dict)  # 设置改变
    
    def __init__(self, parent=None):
        """初始化自动化窗口"""
        super().__init__(parent)
        self.logger = logging.getLogger(__name__)
        
        # 窗口设置
        self.setWindowTitle("🤖 YOLO自动化操作配置")
        self.setWindowFlags(Qt.WindowType.Window)  # 独立窗口
        
        # 数据存储
        self.detection_targets = []
        self.current_screenshot = None
        self.settings = QSettings("YOLODetector", "AutomationWindow")
        
        # 初始化UI
        self._init_ui()
        self._setup_menu_bar()
        self._setup_tool_bar()
        self._setup_status_bar()
        self._setup_connections()
        
        # 恢复窗口状态
        self._restore_window_state()
        
        self.logger.info("自动化操作窗口初始化完成")
    
    def _init_ui(self):
        """初始化用户界面"""
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(5)
        
        # 创建主分割器（水平分割）
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(main_splitter)
        
        # 左侧：检测结果可视化
        left_panel = self._create_visualization_panel()
        main_splitter.addWidget(left_panel)
        
        # 右侧：操作配置面板
        right_panel = self._create_configuration_panel()
        main_splitter.addWidget(right_panel)
        
        # 设置分割器比例 (60:40)
        main_splitter.setSizes([600, 400])
        main_splitter.setStretchFactor(0, 1)
        main_splitter.setStretchFactor(1, 0)
        
        # 底部控制面板
        control_panel = self._create_control_panel()
        main_layout.addWidget(control_panel)
    
    def _create_visualization_panel(self) -> QWidget:
        """创建可视化面板"""
        from ..widgets.detection_visualizer import DetectionVisualizerWidget
        
        panel = QFrame()
        panel.setFrameStyle(QFrame.Shape.StyledPanel)
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 标题
        title_label = QLabel("🎯 检测结果可视化")
        title_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #2c3e50; padding: 5px;")
        layout.addWidget(title_label)
        
        # 可视化组件
        self.visualizer = DetectionVisualizerWidget()
        self.visualizer.setMinimumSize(400, 300)
        layout.addWidget(self.visualizer)
        
        # 可视化控制
        viz_controls = QHBoxLayout()
        
        self.show_labels_btn = QPushButton("🏷️ 显示标签")
        self.show_labels_btn.setCheckable(True)
        self.show_labels_btn.setChecked(True)
        self.show_labels_btn.setToolTip("显示/隐藏检测目标标签")
        viz_controls.addWidget(self.show_labels_btn)
        
        self.show_confidence_btn = QPushButton("📊 显示置信度")
        self.show_confidence_btn.setCheckable(True)
        self.show_confidence_btn.setChecked(True)
        self.show_confidence_btn.setToolTip("显示/隐藏置信度数值")
        viz_controls.addWidget(self.show_confidence_btn)
        
        self.select_all_btn = QPushButton("✅ 全选")
        self.select_all_btn.setToolTip("选择所有检测目标")
        viz_controls.addWidget(self.select_all_btn)
        
        self.clear_selection_btn = QPushButton("❌ 清除")
        self.clear_selection_btn.setToolTip("清除所有选择")
        viz_controls.addWidget(self.clear_selection_btn)
        
        viz_controls.addStretch()
        layout.addLayout(viz_controls)
        
        return panel
    
    def _create_configuration_panel(self) -> QWidget:
        """创建配置面板"""
        from ..widgets.automation_panel import AutomationPanel
        
        # 使用原有的自动化面板，但进行布局优化
        self.automation_panel = AutomationPanel()
        
        # 为独立窗口优化尺寸
        self.automation_panel.setMinimumWidth(350)
        
        return self.automation_panel
    
    def _create_control_panel(self) -> QWidget:
        """创建底部控制面板"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.Shape.StyledPanel)
        panel.setMaximumHeight(60)
        layout = QHBoxLayout(panel)
        layout.setContentsMargins(10, 5, 10, 5)
        
        # 状态信息
        self.targets_count_label = QLabel("检测目标: 0 个")
        self.targets_count_label.setStyleSheet("font-weight: bold; color: #3498db;")
        layout.addWidget(self.targets_count_label)
        
        self.selected_count_label = QLabel("已选择: 0 个")
        self.selected_count_label.setStyleSheet("font-weight: bold; color: #e74c3c;")
        layout.addWidget(self.selected_count_label)
        
        layout.addStretch()
        
        # 主要操作按钮
        self.refresh_btn = QPushButton("🔄 刷新检测")
        self.refresh_btn.setToolTip("重新获取检测结果")
        layout.addWidget(self.refresh_btn)
        
        self.preview_btn = QPushButton("👁️ 预览操作")
        self.preview_btn.setToolTip("预览配置的操作序列")
        layout.addWidget(self.preview_btn)
        
        self.execute_btn = QPushButton("▶️ 执行操作")
        self.execute_btn.setToolTip("执行配置的自动化操作")
        self.execute_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border-radius: 4px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
            QPushButton:disabled {
                background-color: #95a5a6;
            }
        """)
        layout.addWidget(self.execute_btn)
        
        return panel
    
    def _setup_menu_bar(self):
        """设置菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu("📁 文件")
        
        # 导入检测结果
        import_action = QAction("📥 导入检测结果", self)
        import_action.setShortcut(QKeySequence("Ctrl+O"))
        import_action.triggered.connect(self._import_detection_results)
        file_menu.addAction(import_action)
        
        # 导出操作配置
        export_action = QAction("📤 导出操作配置", self)
        export_action.setShortcut(QKeySequence("Ctrl+S"))
        export_action.triggered.connect(self._export_operation_config)
        file_menu.addAction(export_action)
        
        file_menu.addSeparator()
        
        # 关闭窗口
        close_action = QAction("❌ 关闭", self)
        close_action.setShortcut(QKeySequence("Ctrl+W"))
        close_action.triggered.connect(self.close)
        file_menu.addAction(close_action)
        
        # 视图菜单
        view_menu = menubar.addMenu("👁️ 视图")
        
        # 布局选项
        compact_action = QAction("📱 紧凑布局", self)
        compact_action.setCheckable(True)
        compact_action.triggered.connect(self._toggle_compact_layout)
        view_menu.addAction(compact_action)
        
        # 全屏模式
        fullscreen_action = QAction("🖥️ 全屏模式", self)
        fullscreen_action.setShortcut(QKeySequence("F11"))
        fullscreen_action.triggered.connect(self._toggle_fullscreen)
        view_menu.addAction(fullscreen_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu("❓ 帮助")
        
        # 使用指南
        guide_action = QAction("📖 使用指南", self)
        guide_action.triggered.connect(self._show_user_guide)
        help_menu.addAction(guide_action)
        
        # 关于
        about_action = QAction("ℹ️ 关于", self)
        about_action.triggered.connect(self._show_about)
        help_menu.addAction(about_action)
    
    def _setup_tool_bar(self):
        """设置工具栏"""
        toolbar = self.addToolBar("主工具栏")
        toolbar.setMovable(False)
        
        # 刷新按钮
        refresh_action = QAction("🔄", self)
        refresh_action.setToolTip("刷新检测结果")
        refresh_action.triggered.connect(self._refresh_detection)
        toolbar.addAction(refresh_action)
        
        toolbar.addSeparator()
        
        # 预览按钮
        preview_action = QAction("👁️", self)
        preview_action.setToolTip("预览操作")
        preview_action.triggered.connect(self._preview_operations)
        toolbar.addAction(preview_action)
        
        # 执行按钮
        execute_action = QAction("▶️", self)
        execute_action.setToolTip("执行操作")
        execute_action.triggered.connect(self._execute_operations)
        toolbar.addAction(execute_action)
        
        toolbar.addSeparator()
        
        # 设置按钮
        settings_action = QAction("⚙️", self)
        settings_action.setToolTip("设置")
        settings_action.triggered.connect(self._show_settings)
        toolbar.addAction(settings_action)
    
    def _setup_status_bar(self):
        """设置状态栏"""
        self.status_bar = self.statusBar()
        
        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_bar.addWidget(self.status_label)
        
        # 右侧信息
        self.resolution_label = QLabel()
        self.status_bar.addPermanentWidget(self.resolution_label)
        
        self._update_resolution_info()
    
    def _setup_connections(self):
        """设置信号连接"""
        # 可视化控制
        self.show_labels_btn.toggled.connect(self._update_visualization_options)
        self.show_confidence_btn.toggled.connect(self._update_visualization_options)
        self.select_all_btn.clicked.connect(self._select_all_targets)
        self.clear_selection_btn.clicked.connect(self._clear_selection)
        
        # 主控制按钮
        self.refresh_btn.clicked.connect(self._refresh_detection)
        self.preview_btn.clicked.connect(self._preview_operations)
        self.execute_btn.clicked.connect(self._execute_operations)
        
        # 可视化组件信号
        if hasattr(self, 'visualizer'):
            self.visualizer.selection_changed.connect(self._on_selection_changed)
            self.visualizer.box_double_clicked.connect(self._on_target_double_clicked)
    
    def _adapt_to_screen_size(self):
        """适配屏幕尺寸"""
        screen = QApplication.primaryScreen()
        if screen:
            screen_geometry = screen.availableGeometry()
            screen_width = screen_geometry.width()
            screen_height = screen_geometry.height()
            
            # 根据屏幕尺寸设置窗口大小
            if screen_width >= 1920:  # 大屏幕
                self.resize(1400, 900)
            elif screen_width >= 1366:  # 中等屏幕
                self.resize(1200, 800)
            else:  # 小屏幕
                self.resize(1000, 700)
            
            # 居中显示
            self.move(
                (screen_width - self.width()) // 2,
                (screen_height - self.height()) // 2
            )
            
            self._update_resolution_info()
    
    def _update_resolution_info(self):
        """更新分辨率信息"""
        screen = QApplication.primaryScreen()
        if screen:
            geometry = screen.geometry()
            self.resolution_label.setText(f"屏幕: {geometry.width()}×{geometry.height()}")
    
    def _restore_window_state(self):
        """恢复窗口状态"""
        # 恢复窗口几何
        geometry = self.settings.value("geometry")
        if geometry:
            self.restoreGeometry(geometry)
        else:
            self._adapt_to_screen_size()
        
        # 恢复窗口状态
        state = self.settings.value("windowState")
        if state:
            self.restoreState(state)
    
    def _save_window_state(self):
        """保存窗口状态"""
        self.settings.setValue("geometry", self.saveGeometry())
        self.settings.setValue("windowState", self.saveState())
    
    def update_detection_targets(self, targets: List[Dict[str, Any]], screenshot=None):
        """更新检测目标"""
        self.detection_targets = targets
        self.current_screenshot = screenshot
        
        # 更新可视化
        if hasattr(self, 'visualizer'):
            if screenshot:
                self.visualizer.set_image(screenshot)
            self.visualizer.update_detections(targets)
        
        # 更新自动化面板
        if hasattr(self, 'automation_panel'):
            self.automation_panel.update_detection_targets(targets)
        
        # 更新状态
        self.targets_count_label.setText(f"检测目标: {len(targets)} 个")
        self.status_label.setText(f"已加载 {len(targets)} 个检测目标")
        
        self.logger.info(f"更新检测目标: {len(targets)} 个")
    
    def _update_visualization_options(self):
        """更新可视化选项"""
        if hasattr(self, 'visualizer'):
            self.visualizer.set_display_options(
                show_labels=self.show_labels_btn.isChecked(),
                show_confidence=self.show_confidence_btn.isChecked()
            )
    
    def _select_all_targets(self):
        """选择所有目标"""
        if hasattr(self, 'visualizer'):
            self.visualizer.select_all_boxes()
    
    def _clear_selection(self):
        """清除选择"""
        if hasattr(self, 'visualizer'):
            self.visualizer.clear_selection()
    
    def _on_selection_changed(self, selected_ids: List[str]):
        """选择改变处理"""
        count = len(selected_ids)
        self.selected_count_label.setText(f"已选择: {count} 个")
        self.execute_btn.setEnabled(count > 0)
    
    def _on_target_double_clicked(self, target_id: str):
        """目标双击处理"""
        # 快速配置操作
        pass
    
    def _refresh_detection(self):
        """刷新检测"""
        self.status_label.setText("正在刷新检测结果...")
        # 这里应该触发重新检测
        pass
    
    def _preview_operations(self):
        """预览操作"""
        from ..dialogs.operation_preview_dialog import OperationPreviewDialog
        
        # 获取配置的操作
        operations = self._get_configured_operations()
        
        if not operations:
            QMessageBox.information(self, "提示", "请先配置要执行的操作")
            return
        
        # 显示预览对话框
        dialog = OperationPreviewDialog(operations, self)
        dialog.execute_confirmed.connect(self._execute_confirmed_operations)
        dialog.exec()
    
    def _execute_operations(self):
        """执行操作"""
        operations = self._get_configured_operations()
        
        if not operations:
            QMessageBox.information(self, "提示", "请先配置要执行的操作")
            return
        
        # 确认执行
        reply = QMessageBox.question(
            self, "确认执行",
            f"确定要执行 {len(operations)} 个操作吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            self.operation_executed.emit(operations)
            self.status_label.setText("正在执行操作...")
    
    def _get_configured_operations(self) -> List[Dict[str, Any]]:
        """获取配置的操作"""
        # 这里应该从自动化面板获取配置的操作
        return []
    
    def _execute_confirmed_operations(self, operations: List[Dict[str, Any]]):
        """执行确认的操作"""
        self.operation_executed.emit(operations)
        self.status_label.setText(f"执行 {len(operations)} 个操作")
    
    def _toggle_compact_layout(self, checked: bool):
        """切换紧凑布局"""
        # 实现布局切换逻辑
        pass
    
    def _toggle_fullscreen(self):
        """切换全屏模式"""
        if self.isFullScreen():
            self.showNormal()
        else:
            self.showFullScreen()
    
    def _import_detection_results(self):
        """导入检测结果"""
        pass
    
    def _export_operation_config(self):
        """导出操作配置"""
        pass
    
    def _show_settings(self):
        """显示设置"""
        pass
    
    def _show_user_guide(self):
        """显示使用指南"""
        QMessageBox.information(
            self, "使用指南",
            "🤖 自动化操作窗口使用指南\n\n"
            "1. 左侧显示检测结果的可视化\n"
            "2. 右侧配置自动化操作\n"
            "3. 底部控制操作执行\n\n"
            "快捷键:\n"
            "• Ctrl+O: 导入检测结果\n"
            "• Ctrl+S: 导出操作配置\n"
            "• F11: 全屏模式\n"
            "• Ctrl+W: 关闭窗口"
        )
    
    def _show_about(self):
        """显示关于信息"""
        QMessageBox.about(
            self, "关于",
            "🤖 YOLO自动化操作配置窗口\n\n"
            "版本: 2.0\n"
            "功能: 可视化配置自动化操作\n"
            "联系: <EMAIL>\n\n"
            "特性:\n"
            "• 独立窗口，不受主界面限制\n"
            "• 自适应屏幕分辨率\n"
            "• 完整的操作配置功能\n"
            "• 直观的可视化交互"
        )
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        self._save_window_state()
        self.window_closed.emit()
        super().closeEvent(event)
    
    def showEvent(self, event):
        """窗口显示事件"""
        super().showEvent(event)
        # 确保窗口适配当前屏幕
        QTimer.singleShot(100, self._adapt_to_screen_size)
