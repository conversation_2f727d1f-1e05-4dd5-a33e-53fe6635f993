#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
应用程序类型检测器
用于识别不同类型的应用程序（Win32、WPF、UWP、Electron等）并提供相应的优化策略
"""

import logging
import time
import win32gui
import win32process
import psutil
from typing import Dict, Optional, Tuple, Any
from pywinauto import Application
from enum import Enum

class AppType(Enum):
    """应用程序类型枚举"""
    WIN32 = "win32"
    WPF = "wpf"
    UWP = "uwp"
    ELECTRON = "electron"
    CHROME = "chrome"
    JAVA = "java"
    QT = "qt"
    UNKNOWN = "unknown"

class ApplicationTypeDetector:
    """应用程序类型检测器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.detection_cache = {}  # 缓存检测结果
        self.cache_timeout = 300  # 5分钟缓存超时
        
        # 应用程序特征数据库
        self.app_signatures = {
            AppType.ELECTRON: {
                'process_names': ['electron.exe', 'chrome.exe'],
                'class_names': ['Chrome_WidgetWin_1', 'Chrome_WidgetWin_0'],
                'window_titles': ['Electron', 'Chrome'],
                'exe_patterns': ['electron', 'chrome', 'discord', 'vscode', 'slack']
            },
            AppType.WPF: {
                'class_names': ['HwndWrapper', 'Window'],
                'process_names': ['wpf', '.net'],
                'framework_indicators': ['.net', 'wpf', 'xaml']
            },
            AppType.UWP: {
                'class_names': ['ApplicationFrameWindow', 'Windows.UI.Core.CoreWindow'],
                'process_names': ['ApplicationFrameHost.exe'],
                'package_indicators': ['Microsoft.', 'Windows.']
            },
            AppType.CHROME: {
                'process_names': ['chrome.exe', 'msedge.exe', 'brave.exe'],
                'class_names': ['Chrome_WidgetWin_1'],
                'window_titles': ['Chrome', 'Edge', 'Brave']
            },
            AppType.JAVA: {
                'process_names': ['java.exe', 'javaw.exe'],
                'class_names': ['SunAwtFrame', 'SunAwtDialog'],
                'framework_indicators': ['java', 'swing', 'awt']
            },
            AppType.QT: {
                'class_names': ['Qt5QWindowIcon', 'QWidget'],
                'process_names': ['qt'],
                'framework_indicators': ['qt5', 'qt6', 'pyqt']
            },
            AppType.WIN32: {
                'process_names': [
                    'everything.exe', 'notepad.exe', 'explorer.exe', 'calc.exe',
                    'mspaint.exe', 'winrar.exe', '7z.exe', 'totalcmd.exe',
                    'foobar2000.exe', 'vlc.exe', 'potplayer.exe'
                ],
                'class_names': [
                    'EVERYTHING', 'Notepad', 'CabinetWClass', 'CalcFrame',
                    'MSPaintApp', 'WinRAR', 'TTOTAL_CMD', 'foo_main'
                ],
                'framework_indicators': ['win32', 'gdi', 'user32']
            }
        }
    
    def detect_app_type(self, window_handle: int) -> AppType:
        """
        检测应用程序类型
        
        Args:
            window_handle: 窗口句柄
            
        Returns:
            AppType: 检测到的应用程序类型
        """
        try:
            # 检查缓存
            cache_key = str(window_handle)
            if cache_key in self.detection_cache:
                cached_time, cached_type = self.detection_cache[cache_key]
                if time.time() - cached_time < self.cache_timeout:
                    return cached_type
            
            # 获取基础信息
            window_info = self._get_window_info(window_handle)
            if not window_info:
                return AppType.UNKNOWN
            
            # 多维度检测
            detection_scores = {}
            
            # 1. 基于进程名检测
            process_score = self._detect_by_process(window_info)
            detection_scores.update(process_score)
            
            # 2. 基于窗口类名检测
            class_score = self._detect_by_class_name(window_info)
            detection_scores.update(class_score)
            
            # 3. 基于窗口标题检测
            title_score = self._detect_by_title(window_info)
            detection_scores.update(title_score)
            
            # 4. 基于进程模块检测
            module_score = self._detect_by_modules(window_info)
            detection_scores.update(module_score)
            
            # 选择得分最高的类型
            if detection_scores:
                detected_type = max(detection_scores.items(), key=lambda x: x[1])[0]
            else:
                detected_type = AppType.UNKNOWN
            
            # 缓存结果
            self.detection_cache[cache_key] = (time.time(), detected_type)
            
            # 清理过期缓存
            self._cleanup_cache()
            
            self.logger.info(f"Detected app type: {detected_type.value} for window {window_handle}")
            return detected_type
            
        except Exception as e:
            self.logger.error(f"Error detecting app type for window {window_handle}: {e}")
            return AppType.UNKNOWN
    
    def _get_window_info(self, window_handle: int) -> Optional[Dict[str, Any]]:
        """获取窗口基础信息"""
        try:
            # 获取窗口类名和标题
            class_name = win32gui.GetClassName(window_handle)
            window_title = win32gui.GetWindowText(window_handle)
            
            # 获取进程信息
            _, pid = win32process.GetWindowThreadProcessId(window_handle)
            process = psutil.Process(pid)
            
            return {
                'handle': window_handle,
                'class_name': class_name,
                'title': window_title,
                'pid': pid,
                'process_name': process.name(),
                'exe_path': process.exe(),
                'process': process
            }
            
        except Exception as e:
            self.logger.error(f"Error getting window info: {e}")
            return None
    
    def _detect_by_process(self, window_info: Dict[str, Any]) -> Dict[AppType, float]:
        """基于进程名检测"""
        scores = {}
        process_name = window_info['process_name'].lower()
        
        for app_type, signatures in self.app_signatures.items():
            score = 0.0
            process_names = signatures.get('process_names', [])
            
            for pattern in process_names:
                if pattern.lower() in process_name:
                    score += 0.8  # 进程名匹配权重较高
                    break
            
            if score > 0:
                scores[app_type] = score
        
        return scores
    
    def _detect_by_class_name(self, window_info: Dict[str, Any]) -> Dict[AppType, float]:
        """基于窗口类名检测"""
        scores = {}
        class_name = window_info['class_name']
        
        for app_type, signatures in self.app_signatures.items():
            score = 0.0
            class_names = signatures.get('class_names', [])
            
            for pattern in class_names:
                if pattern in class_name:
                    score += 0.7  # 类名匹配权重中等
                    break
            
            if score > 0:
                scores[app_type] = score
        
        return scores
    
    def _detect_by_title(self, window_info: Dict[str, Any]) -> Dict[AppType, float]:
        """基于窗口标题检测"""
        scores = {}
        title = window_info['title'].lower()
        
        for app_type, signatures in self.app_signatures.items():
            score = 0.0
            window_titles = signatures.get('window_titles', [])
            
            for pattern in window_titles:
                if pattern.lower() in title:
                    score += 0.3  # 标题匹配权重较低
                    break
            
            if score > 0:
                scores[app_type] = score
        
        return scores
    
    def _detect_by_modules(self, window_info: Dict[str, Any]) -> Dict[AppType, float]:
        """基于进程模块检测"""
        scores = {}
        
        try:
            process = window_info['process']
            # 获取进程加载的模块
            modules = [m.name().lower() for m in process.memory_maps()]
            
            for app_type, signatures in self.app_signatures.items():
                score = 0.0
                indicators = signatures.get('framework_indicators', [])
                
                for indicator in indicators:
                    if any(indicator in module for module in modules):
                        score += 0.5  # 模块匹配权重中等
                        break
                
                if score > 0:
                    scores[app_type] = score
                    
        except Exception as e:
            self.logger.debug(f"Error detecting by modules: {e}")
        
        return scores
    
    def _cleanup_cache(self):
        """清理过期缓存"""
        current_time = time.time()
        expired_keys = [
            key for key, (cached_time, _) in self.detection_cache.items()
            if current_time - cached_time > self.cache_timeout
        ]
        
        for key in expired_keys:
            del self.detection_cache[key]
    
    def get_optimization_config(self, app_type: AppType) -> Dict[str, Any]:
        """
        根据应用程序类型获取优化配置
        
        Args:
            app_type: 应用程序类型
            
        Returns:
            Dict: 优化配置
        """
        configs = {
            AppType.ELECTRON: {
                'preferred_backend': 'uia',
                'fallback_backends': ['win32'],
                'connection_timeout': 10,
                'max_tree_depth': 5,
                'max_children_per_level': 20,
                'use_caching': True,
                'throttle_interval': 0.3,
                'special_handling': True
            },
            AppType.WPF: {
                'preferred_backend': 'uia',
                'fallback_backends': [],
                'connection_timeout': 5,
                'max_tree_depth': 8,
                'max_children_per_level': 50,
                'use_caching': True,
                'throttle_interval': 0.1,
                'special_handling': False
            },
            AppType.UWP: {
                'preferred_backend': 'uia',
                'fallback_backends': [],
                'connection_timeout': 8,
                'max_tree_depth': 6,
                'max_children_per_level': 30,
                'use_caching': True,
                'throttle_interval': 0.2,
                'special_handling': True
            },
            AppType.WIN32: {
                'preferred_backend': 'win32',
                'fallback_backends': ['uia'],
                'connection_timeout': 3,
                'max_tree_depth': 10,
                'max_children_per_level': 100,
                'use_caching': False,
                'throttle_interval': 0.05,
                'special_handling': False
            },
            AppType.CHROME: {
                'preferred_backend': 'uia',
                'fallback_backends': ['win32'],
                'connection_timeout': 8,
                'max_tree_depth': 4,
                'max_children_per_level': 15,
                'use_caching': True,
                'throttle_interval': 0.4,
                'special_handling': True
            }
        }
        
        return configs.get(app_type, configs[AppType.WIN32])  # 默认使用Win32配置

# 测试函数
def test_app_type_detection():
    """测试应用程序类型检测"""
    detector = ApplicationTypeDetector()
    
    # 获取当前前台窗口进行测试
    hwnd = win32gui.GetForegroundWindow()
    if hwnd:
        app_type = detector.detect_app_type(hwnd)
        config = detector.get_optimization_config(app_type)
        
        print(f"Window Handle: {hwnd}")
        print(f"Detected App Type: {app_type.value}")
        print(f"Optimization Config: {config}")
    else:
        print("No foreground window found")

if __name__ == "__main__":
    test_app_type_detection()
