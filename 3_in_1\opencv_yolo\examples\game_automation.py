# -*- coding: utf-8 -*-
"""
游戏自动化示例
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

import sys
import time
import pyautogui
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from yolo_opencv_detector.core.detection_engine import DetectionEngine
from yolo_opencv_detector.utils.config_manager import ConfigManager
from yolo_opencv_detector.utils.logger import Logger


class GameAutomation:
    """游戏自动化类"""
    
    def __init__(self):
        """初始化游戏自动化"""
        self.logger = Logger().get_logger(__name__)
        self.config_manager = ConfigManager()
        self.detection_engine = DetectionEngine(self.config_manager)
        
        # 游戏相关配置
        self.game_window_title = "游戏窗口"
        self.target_templates = {}
        self.action_cooldown = 1.0  # 动作冷却时间
        
        # 安全设置
        pyautogui.FAILSAFE = True  # 鼠标移到左上角停止
        pyautogui.PAUSE = 0.1  # 每个动作间隔
        
        self.logger.info("游戏自动化初始化完成")
    
    def load_game_templates(self, templates_dir: Path):
        """加载游戏模板"""
        try:
            template_files = {
                "button_start": "start_button.png",
                "button_attack": "attack_button.png",
                "button_skill": "skill_button.png",
                "enemy_target": "enemy.png",
                "health_low": "health_low.png",
                "mana_low": "mana_low.png"
            }
            
            for template_name, filename in template_files.items():
                template_path = templates_dir / filename
                if template_path.exists():
                    # 这里应该调用模板管理器加载模板
                    self.target_templates[template_name] = str(template_path)
                    self.logger.info(f"加载模板: {template_name}")
                else:
                    self.logger.warning(f"模板文件不存在: {template_path}")
            
        except Exception as e:
            self.logger.error(f"加载游戏模板失败: {e}")
    
    def detect_game_elements(self):
        """检测游戏元素"""
        try:
            # 执行检测
            results = self.detection_engine.detect_once()
            
            # 分析检测结果
            game_state = {
                "enemies": [],
                "buttons": [],
                "ui_elements": [],
                "health_status": "normal",
                "mana_status": "normal"
            }
            
            for result in results:
                # 根据检测结果分类
                if result.class_name in ["person", "enemy"]:
                    game_state["enemies"].append({
                        "position": (result.bbox.center_x, result.bbox.center_y),
                        "confidence": result.confidence,
                        "size": (result.bbox.width, result.bbox.height)
                    })
                
                elif "button" in result.class_name.lower():
                    game_state["buttons"].append({
                        "type": result.class_name,
                        "position": (result.bbox.center_x, result.bbox.center_y),
                        "confidence": result.confidence
                    })
                
                # 检查模板匹配结果
                if result.template_id:
                    if "health_low" in result.template_id:
                        game_state["health_status"] = "low"
                    elif "mana_low" in result.template_id:
                        game_state["mana_status"] = "low"
            
            return game_state
            
        except Exception as e:
            self.logger.error(f"检测游戏元素失败: {e}")
            return {}
    
    def execute_combat_action(self, game_state):
        """执行战斗动作"""
        try:
            # 检查是否需要回血
            if game_state.get("health_status") == "low":
                self.use_health_potion()
                return True
            
            # 检查是否需要回蓝
            if game_state.get("mana_status") == "low":
                self.use_mana_potion()
                return True
            
            # 寻找敌人并攻击
            enemies = game_state.get("enemies", [])
            if enemies:
                # 选择最近的敌人
                closest_enemy = min(enemies, key=lambda e: e["size"][0] * e["size"][1])
                self.attack_enemy(closest_enemy["position"])
                return True
            
            # 寻找攻击按钮
            buttons = game_state.get("buttons", [])
            for button in buttons:
                if "attack" in button["type"].lower():
                    self.click_button(button["position"])
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"执行战斗动作失败: {e}")
            return False
    
    def click_button(self, position):
        """点击按钮"""
        try:
            x, y = position
            pyautogui.click(x, y)
            self.logger.info(f"点击按钮: ({x}, {y})")
            time.sleep(self.action_cooldown)
            
        except Exception as e:
            self.logger.error(f"点击按钮失败: {e}")
    
    def attack_enemy(self, position):
        """攻击敌人"""
        try:
            x, y = position
            
            # 点击敌人选中
            pyautogui.click(x, y)
            time.sleep(0.2)
            
            # 使用攻击技能
            pyautogui.press('space')  # 假设空格键是攻击
            self.logger.info(f"攻击敌人: ({x}, {y})")
            time.sleep(self.action_cooldown)
            
        except Exception as e:
            self.logger.error(f"攻击敌人失败: {e}")
    
    def use_health_potion(self):
        """使用血瓶"""
        try:
            pyautogui.press('1')  # 假设1键是血瓶
            self.logger.info("使用血瓶")
            time.sleep(self.action_cooldown)
            
        except Exception as e:
            self.logger.error(f"使用血瓶失败: {e}")
    
    def use_mana_potion(self):
        """使用蓝瓶"""
        try:
            pyautogui.press('2')  # 假设2键是蓝瓶
            self.logger.info("使用蓝瓶")
            time.sleep(self.action_cooldown)
            
        except Exception as e:
            self.logger.error(f"使用蓝瓶失败: {e}")
    
    def run_automation(self, duration_minutes=30):
        """运行自动化"""
        try:
            self.logger.info(f"开始游戏自动化，运行时长: {duration_minutes}分钟")
            
            start_time = time.time()
            end_time = start_time + (duration_minutes * 60)
            
            action_count = 0
            
            while time.time() < end_time:
                try:
                    # 检测游戏状态
                    game_state = self.detect_game_elements()
                    
                    if game_state:
                        # 执行相应动作
                        if self.execute_combat_action(game_state):
                            action_count += 1
                    
                    # 短暂休息
                    time.sleep(0.5)
                    
                    # 每分钟报告一次状态
                    if action_count % 60 == 0:
                        elapsed = (time.time() - start_time) / 60
                        self.logger.info(f"已运行 {elapsed:.1f} 分钟，执行了 {action_count} 个动作")
                
                except KeyboardInterrupt:
                    self.logger.info("用户中断自动化")
                    break
                except Exception as e:
                    self.logger.error(f"自动化循环出错: {e}")
                    time.sleep(5)  # 出错后等待5秒
            
            total_time = (time.time() - start_time) / 60
            self.logger.info(f"自动化完成，总运行时间: {total_time:.1f}分钟，总动作数: {action_count}")
            
        except Exception as e:
            self.logger.error(f"运行自动化失败: {e}")


def main():
    """主函数"""
    try:
        # 创建游戏自动化实例
        game_bot = GameAutomation()
        
        # 加载游戏模板
        templates_dir = Path(__file__).parent / "game_templates"
        if templates_dir.exists():
            game_bot.load_game_templates(templates_dir)
        else:
            print(f"警告: 游戏模板目录不存在: {templates_dir}")
            print("请在该目录下放置游戏UI元素的截图文件")
        
        # 安全提示
        print("游戏自动化即将开始...")
        print("注意事项:")
        print("1. 请确保游戏窗口在前台")
        print("2. 移动鼠标到屏幕左上角可紧急停止")
        print("3. 按Ctrl+C可正常停止")
        print("4. 请遵守游戏规则，合理使用自动化")
        
        # 倒计时
        for i in range(5, 0, -1):
            print(f"倒计时: {i}秒...")
            time.sleep(1)
        
        # 开始自动化
        game_bot.run_automation(duration_minutes=10)  # 运行10分钟
        
    except Exception as e:
        print(f"游戏自动化失败: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
