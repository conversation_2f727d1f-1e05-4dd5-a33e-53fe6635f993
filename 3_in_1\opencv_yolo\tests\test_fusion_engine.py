# -*- coding: utf-8 -*-
"""
结果融合引擎测试
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

import pytest
import time

from yolo_opencv_detector.core.fusion_engine import FusionEngine
from yolo_opencv_detector.utils.data_structures import DetectionResult, BoundingBox, DetectionSource


class TestFusionEngine:
    """结果融合引擎测试类"""
    
    @pytest.fixture
    def fusion_engine(self):
        """创建融合引擎实例"""
        return FusionEngine(
            iou_threshold=0.5,
            confidence_weight=0.6,
            template_weight=0.4,
            enable_nms=True,
            max_results=10
        )
    
    @pytest.fixture
    def yolo_results(self):
        """创建YOLO检测结果"""
        return [
            DetectionResult(
                bbox=BoundingBox(100, 100, 50, 50),
                confidence=0.9,
                class_id=1,
                class_name="person",
                source=DetectionSource.YOLO
            ),
            DetectionResult(
                bbox=BoundingBox(200, 200, 60, 60),
                confidence=0.8,
                class_id=2,
                class_name="car",
                source=DetectionSource.YOLO
            )
        ]
    
    @pytest.fixture
    def template_results(self):
        """创建模板匹配结果"""
        return [
            DetectionResult(
                bbox=BoundingBox(105, 105, 45, 45),  # 与第一个YOLO结果重叠
                confidence=0.7,
                template_id="template_1",
                source=DetectionSource.TEMPLATE
            ),
            DetectionResult(
                bbox=BoundingBox(300, 300, 40, 40),  # 独立的模板结果
                confidence=0.6,
                template_id="template_2",
                source=DetectionSource.TEMPLATE
            )
        ]
    
    def test_engine_initialization(self, fusion_engine):
        """测试融合引擎初始化"""
        assert fusion_engine is not None
        assert fusion_engine.iou_threshold == 0.5
        assert fusion_engine.confidence_weight == 0.6
        assert fusion_engine.template_weight == 0.4
        assert fusion_engine.enable_nms == True
        assert fusion_engine.max_results == 10
    
    def test_empty_results_fusion(self, fusion_engine):
        """测试空结果融合"""
        # 两个都为空
        result = fusion_engine.fuse_results([], [])
        assert result == []
        
        # YOLO为空
        template_results = [DetectionResult(
            bbox=BoundingBox(100, 100, 50, 50),
            confidence=0.8,
            source=DetectionSource.TEMPLATE
        )]
        result = fusion_engine.fuse_results([], template_results)
        assert len(result) == 1
        assert result[0].source == DetectionSource.TEMPLATE
        
        # 模板为空
        yolo_results = [DetectionResult(
            bbox=BoundingBox(100, 100, 50, 50),
            confidence=0.8,
            source=DetectionSource.YOLO
        )]
        result = fusion_engine.fuse_results(yolo_results, [])
        assert len(result) == 1
        assert result[0].source == DetectionSource.YOLO
    
    def test_basic_fusion(self, fusion_engine, yolo_results, template_results):
        """测试基本融合功能"""
        fused_results = fusion_engine.fuse_results(yolo_results, template_results)
        
        # 应该有融合结果
        assert len(fused_results) > 0
        
        # 检查是否有融合的结果
        fusion_found = any(r.source == DetectionSource.FUSION for r in fused_results)
        assert fusion_found
        
        # 检查融合结果的置信度
        for result in fused_results:
            assert 0.0 <= result.confidence <= 1.0
    
    def test_iou_calculation(self, fusion_engine):
        """测试IoU计算"""
        bbox1 = BoundingBox(0, 0, 10, 10)
        bbox2 = BoundingBox(5, 5, 10, 10)  # 部分重叠
        
        iou = fusion_engine._calculate_iou(bbox1, bbox2)
        assert 0.0 < iou < 1.0
        
        # 完全重叠
        bbox3 = BoundingBox(0, 0, 10, 10)
        iou = fusion_engine._calculate_iou(bbox1, bbox3)
        assert iou == 1.0
        
        # 不重叠
        bbox4 = BoundingBox(20, 20, 10, 10)
        iou = fusion_engine._calculate_iou(bbox1, bbox4)
        assert iou == 0.0
    
    def test_bounding_box_fusion(self, fusion_engine):
        """测试边界框融合"""
        bbox1 = BoundingBox(100, 100, 50, 50)
        bbox2 = BoundingBox(110, 110, 40, 40)
        
        fused_bbox = fusion_engine._fuse_bounding_boxes(bbox1, bbox2, 0.8, 0.6)
        
        # 融合后的边界框应该在两个原始框之间
        assert bbox1.x <= fused_bbox.x <= bbox2.x or bbox2.x <= fused_bbox.x <= bbox1.x
        assert bbox1.y <= fused_bbox.y <= bbox2.y or bbox2.y <= fused_bbox.y <= bbox1.y
        assert fused_bbox.width > 0
        assert fused_bbox.height > 0
    
    def test_detection_pair_fusion(self, fusion_engine):
        """测试检测结果对融合"""
        yolo_result = DetectionResult(
            bbox=BoundingBox(100, 100, 50, 50),
            confidence=0.9,
            class_id=1,
            class_name="person",
            source=DetectionSource.YOLO
        )
        
        template_result = DetectionResult(
            bbox=BoundingBox(105, 105, 45, 45),
            confidence=0.7,
            template_id="template_1",
            source=DetectionSource.TEMPLATE
        )
        
        fused_result = fusion_engine._fuse_detection_pair(yolo_result, template_result, 0.6)
        
        # 检查融合结果
        assert fused_result.source == DetectionSource.FUSION
        assert fused_result.class_name == "person"  # 应该保留YOLO的类别信息
        assert fused_result.template_id == "template_1"  # 应该保留模板ID
        
        # 置信度应该是加权融合的结果
        expected_confidence = 0.9 * 0.6 + 0.7 * 0.4
        assert abs(fused_result.confidence - expected_confidence) < 0.01
        
        # 应该包含融合信息
        assert "fusion_info" in fused_result.metadata
    
    def test_nms_application(self, fusion_engine):
        """测试NMS应用"""
        # 创建重叠的检测结果
        results = [
            DetectionResult(
                bbox=BoundingBox(100, 100, 50, 50),
                confidence=0.9,
                source=DetectionSource.YOLO
            ),
            DetectionResult(
                bbox=BoundingBox(110, 110, 50, 50),  # 重叠
                confidence=0.8,
                source=DetectionSource.YOLO
            ),
            DetectionResult(
                bbox=BoundingBox(200, 200, 50, 50),  # 不重叠
                confidence=0.7,
                source=DetectionSource.YOLO
            )
        ]
        
        nms_results = fusion_engine._apply_nms(results)
        
        # NMS应该减少重叠的检测
        assert len(nms_results) <= len(results)
        
        # 应该保留置信度最高的
        confidences = [r.confidence for r in nms_results]
        assert max(confidences) == 0.9
    
    def test_suppression_logic(self, fusion_engine):
        """测试抑制逻辑"""
        # 测试相同来源的抑制
        current = DetectionResult(
            bbox=BoundingBox(100, 100, 50, 50),
            confidence=0.7,
            source=DetectionSource.YOLO
        )
        
        kept = DetectionResult(
            bbox=BoundingBox(105, 105, 50, 50),
            confidence=0.9,
            source=DetectionSource.YOLO
        )
        
        should_suppress = fusion_engine._should_suppress(current, kept)
        assert should_suppress == True  # 置信度低的应该被抑制
        
        # 测试不同来源的抑制
        fusion_result = DetectionResult(
            bbox=BoundingBox(100, 100, 50, 50),
            confidence=0.8,
            source=DetectionSource.FUSION
        )
        
        should_suppress = fusion_engine._should_suppress(current, fusion_result)
        assert should_suppress == True  # 融合结果优先级更高
    
    def test_performance_stats(self, fusion_engine, yolo_results, template_results):
        """测试性能统计"""
        # 初始状态
        stats = fusion_engine.get_performance_stats()
        assert isinstance(stats, dict)
        
        # 执行一些融合操作
        fusion_engine.fuse_results(yolo_results, template_results)
        fusion_engine.fuse_results(yolo_results[:1], template_results[:1])
        
        stats = fusion_engine.get_performance_stats()
        assert "total_fusions" in stats
        assert "avg_fusion_time" in stats
        assert "fusion_distribution" in stats
        assert stats["total_fusions"] == 2
        
        # 重置统计
        fusion_engine.reset_stats()
        stats = fusion_engine.get_performance_stats()
        assert stats == {}
    
    def test_config_update(self, fusion_engine):
        """测试配置更新"""
        original_iou = fusion_engine.iou_threshold
        
        # 更新配置
        fusion_engine.update_config(
            iou_threshold=0.7,
            confidence_weight=0.8,
            template_weight=0.2,
            enable_nms=False
        )
        
        assert fusion_engine.iou_threshold == 0.7
        assert fusion_engine.enable_nms == False
        
        # 权重应该被归一化
        total_weight = fusion_engine.confidence_weight + fusion_engine.template_weight
        assert abs(total_weight - 1.0) < 0.01
        
        # 恢复配置
        fusion_engine.update_config(iou_threshold=original_iou, enable_nms=True)
    
    def test_config_validation(self, fusion_engine):
        """测试配置验证"""
        # 有效配置
        assert fusion_engine.validate_config() == True
        
        # 无效IoU阈值
        fusion_engine.iou_threshold = 1.5
        assert fusion_engine.validate_config() == False
        
        fusion_engine.iou_threshold = -0.1
        assert fusion_engine.validate_config() == False
        
        # 恢复有效配置
        fusion_engine.iou_threshold = 0.5
        
        # 无效权重
        fusion_engine.confidence_weight = -0.1
        assert fusion_engine.validate_config() == False
        
        fusion_engine.confidence_weight = 0.0
        fusion_engine.template_weight = 0.0
        assert fusion_engine.validate_config() == False
        
        # 恢复有效配置
        fusion_engine.confidence_weight = 0.6
        fusion_engine.template_weight = 0.4
        
        # 无效最大结果数
        fusion_engine.max_results = 0
        assert fusion_engine.validate_config() == False
        
        fusion_engine.max_results = -5
        assert fusion_engine.validate_config() == False
    
    def test_max_results_limit(self, fusion_engine):
        """测试最大结果数限制"""
        # 创建大量结果
        many_yolo_results = []
        for i in range(15):
            result = DetectionResult(
                bbox=BoundingBox(i * 20, i * 20, 10, 10),
                confidence=0.9 - i * 0.01,
                source=DetectionSource.YOLO
            )
            many_yolo_results.append(result)
        
        fused_results = fusion_engine.fuse_results(many_yolo_results, [])
        
        # 结果数量应该被限制
        assert len(fused_results) <= fusion_engine.max_results
        
        # 应该保留置信度最高的结果
        confidences = [r.confidence for r in fused_results]
        assert confidences == sorted(confidences, reverse=True)
    
    def test_fusion_metadata(self, fusion_engine, yolo_results, template_results):
        """测试融合元数据"""
        fused_results = fusion_engine.fuse_results(yolo_results, template_results)
        
        # 查找融合结果
        fusion_result = None
        for result in fused_results:
            if result.source == DetectionSource.FUSION:
                fusion_result = result
                break
        
        if fusion_result:
            # 检查融合信息
            assert "fusion_info" in fusion_result.metadata
            fusion_info = fusion_result.metadata["fusion_info"]
            
            assert "yolo_confidence" in fusion_info
            assert "template_confidence" in fusion_info
            assert "iou" in fusion_info
            assert "confidence_weight" in fusion_info
            assert "template_weight" in fusion_info
            assert "yolo_bbox" in fusion_info
            assert "template_bbox" in fusion_info
    
    def test_error_handling(self, fusion_engine):
        """测试错误处理"""
        # 测试无效的边界框
        invalid_bbox = BoundingBox(0, 0, 0, 0)  # 零面积
        valid_bbox = BoundingBox(10, 10, 20, 20)
        
        # 应该不会崩溃
        iou = fusion_engine._calculate_iou(invalid_bbox, valid_bbox)
        assert iou >= 0.0
        
        # 测试融合无效结果
        invalid_result = DetectionResult(
            bbox=invalid_bbox,
            confidence=0.5,
            source=DetectionSource.YOLO
        )
        
        valid_result = DetectionResult(
            bbox=valid_bbox,
            confidence=0.8,
            source=DetectionSource.TEMPLATE
        )
        
        # 应该能处理而不崩溃
        fused_results = fusion_engine.fuse_results([invalid_result], [valid_result])
        assert isinstance(fused_results, list)
