#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
应用图标集成脚本
将生成的图标集成到YOLO OpenCV检测器应用程序中
"""

import os
import sys
from pathlib import Path

def setup_project_path():
    """设置项目路径"""
    project_root = Path(__file__).parent
    src_path = project_root / "src"
    if str(src_path) not in sys.path:
        sys.path.insert(0, str(src_path))

def integrate_icons_to_main_window():
    """将图标集成到主窗口"""
    print("🖼️ 集成图标到主窗口...")
    
    main_window_file = Path("src/yolo_opencv_detector/gui/main_window_v2.py")
    
    if not main_window_file.exists():
        print(f"❌ 主窗口文件不存在: {main_window_file}")
        return False
    
    try:
        # 读取现有文件
        with open(main_window_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经添加了图标设置
        if "setWindowIcon" in content:
            print("✅ 主窗口已设置图标")
            return True
        
        # 查找合适的位置添加图标设置
        if "def __init__(self" in content:
            # 在__init__方法中添加图标设置
            init_pattern = "def __init__(self"
            init_pos = content.find(init_pattern)
            
            if init_pos != -1:
                # 找到__init__方法的结束位置
                lines = content.split('\n')
                for i, line in enumerate(lines):
                    if init_pattern in line:
                        # 在__init__方法中添加图标设置代码
                        icon_code = """
        # 设置应用程序图标
        self.setup_application_icon()"""
                        
                        # 找到合适的插入位置（通常在super().__init__之后）
                        insert_pos = i + 1
                        while insert_pos < len(lines) and (lines[insert_pos].strip().startswith("super(") or lines[insert_pos].strip() == ""):
                            insert_pos += 1
                        
                        lines.insert(insert_pos, icon_code)
                        break
                
                # 添加图标设置方法
                method_code = """
    def setup_application_icon(self):
        \"\"\"设置应用程序图标\"\"\"
        try:
            from PyQt6.QtGui import QIcon
            
            # 图标文件路径
            icon_path = Path(__file__).parent.parent.parent.parent / "icons" / "yolo_detector_app.ico"
            
            if icon_path.exists():
                # 设置窗口图标
                icon = QIcon(str(icon_path))
                self.setWindowIcon(icon)
                
                # 设置应用程序图标（任务栏）
                if hasattr(self, 'app') and self.app:
                    self.app.setWindowIcon(icon)
                
                print(f"✅ 应用程序图标已设置: {icon_path}")
            else:
                print(f"⚠️ 图标文件不存在: {icon_path}")
                
        except Exception as e:
            print(f"❌ 设置应用程序图标失败: {e}")"""
                
                # 在类的末尾添加方法
                lines.append(method_code)
                
                # 重新组合内容
                new_content = '\n'.join(lines)
                
                # 写回文件
                with open(main_window_file, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                print("✅ 主窗口图标集成完成")
                return True
        
        print("❌ 未找到合适的位置添加图标设置")
        return False
        
    except Exception as e:
        print(f"❌ 主窗口图标集成失败: {e}")
        return False

def integrate_icons_to_source_dialog():
    """将图标集成到源代码对话框"""
    print("📝 集成图标到源代码对话框...")
    
    dialog_file = Path("src/yolo_opencv_detector/gui/dialogs/source_code_dialog.py")
    
    if not dialog_file.exists():
        print(f"❌ 源代码对话框文件不存在: {dialog_file}")
        return False
    
    try:
        # 读取现有文件
        with open(dialog_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经添加了图标设置
        if "setWindowIcon" in content and "source_code_dialog" in content:
            print("✅ 源代码对话框已设置图标")
            return True
        
        # 在SourceCodeDialog类的__init__方法中添加图标设置
        if "class SourceCodeDialog" in content and "def __init__(self" in content:
            lines = content.split('\n')
            
            for i, line in enumerate(lines):
                if "class SourceCodeDialog" in line:
                    # 找到__init__方法
                    for j in range(i, len(lines)):
                        if "def __init__(self" in lines[j]:
                            # 在__init__方法中添加图标设置
                            icon_code = """
        # 设置对话框图标
        self.setup_dialog_icon()"""
                            
                            # 找到合适的插入位置
                            insert_pos = j + 1
                            while insert_pos < len(lines) and (lines[insert_pos].strip().startswith("super(") or lines[insert_pos].strip() == ""):
                                insert_pos += 1
                            
                            lines.insert(insert_pos, icon_code)
                            break
                    break
            
            # 添加图标设置方法
            method_code = """
    def setup_dialog_icon(self):
        \"\"\"设置对话框图标\"\"\"
        try:
            from PyQt6.QtGui import QIcon
            
            # 图标文件路径
            icon_path = Path(__file__).parent.parent.parent.parent / "icons" / "yolo_detector_app.ico"
            
            if icon_path.exists():
                icon = QIcon(str(icon_path))
                self.setWindowIcon(icon)
                print(f"✅ 源代码对话框图标已设置")
            else:
                print(f"⚠️ 图标文件不存在: {icon_path}")
                
        except Exception as e:
            print(f"❌ 设置对话框图标失败: {e}")"""
            
            # 在类的末尾添加方法
            lines.append(method_code)
            
            # 重新组合内容
            new_content = '\n'.join(lines)
            
            # 写回文件
            with open(dialog_file, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print("✅ 源代码对话框图标集成完成")
            return True
        
        print("❌ 未找到SourceCodeDialog类")
        return False
        
    except Exception as e:
        print(f"❌ 源代码对话框图标集成失败: {e}")
        return False

def create_icon_usage_guide():
    """创建图标使用指南"""
    print("📚 创建图标使用指南...")
    
    guide_content = """# 🎨 YOLO OpenCV检测器图标使用指南

## 📁 图标文件说明

### 主要图标文件
- `yolo_detector_app.ico` - 主应用程序图标（多尺寸ICO格式）
- `yolo_detector_taskbar.ico` - 任务栏图标（优化的小尺寸）

### PNG图标文件
- `app_icon_256.png` - 高分辨率应用图标（256x256）
- `app_icon_128.png` - 中等分辨率应用图标（128x128）
- `app_icon_64.png` - 标准应用图标（64x64）
- `app_icon_32.png` - 小尺寸应用图标（32x32）
- `taskbar_icon_32.png` - 任务栏图标（32x32）
- `taskbar_icon_24.png` - 任务栏图标（24x24）
- `taskbar_icon_16.png` - 任务栏图标（16x16）
- `simple_icon_48.png` - 简化图标（48x48）
- `simple_icon_24.png` - 简化图标（24x24）

## 🎯 图标设计元素

### 设计理念
- **主题**: YOLO目标检测 + 办公自动化
- **核心元素**: 检测框 + 鼠标光标 + 现代科技感
- **色彩方案**: 
  - 主蓝色 (#2E86AB) - 科技感和专业性
  - 强调橙色 (#F24236) - 活力和检测提示
  - 白色 (#FFFFFF) - 简洁和清晰

### 图标层次
1. **背景**: 渐变蓝色圆形背景
2. **主检测框**: 橙色边框的大检测框
3. **小检测框**: 多个小的检测框表示多目标检测
4. **鼠标光标**: 白色光标表示自动化操作
5. **文字标识**: "YOLO"文字（大尺寸图标）

## 💻 在代码中使用图标

### PyQt6应用程序
```python
from PyQt6.QtGui import QIcon
from pathlib import Path

# 设置窗口图标
icon_path = Path("icons/yolo_detector_app.ico")
if icon_path.exists():
    icon = QIcon(str(icon_path))
    window.setWindowIcon(icon)
    app.setWindowIcon(icon)  # 任务栏图标
```

### 打包应用程序时
```python
# PyInstaller spec文件中
icon='icons/yolo_detector_app.ico'
```

## 🔧 自定义图标

如需修改图标，可以：
1. 编辑 `create_app_icons.py` 脚本
2. 修改颜色、尺寸或设计元素
3. 重新运行脚本生成新图标

## 📱 不同平台适配

### Windows
- 使用 `.ico` 格式
- 支持多尺寸（16x16 到 256x256）
- 任务栏和窗口标题栏显示

### macOS
- 使用 `.icns` 格式（需要转换）
- Dock图标和应用程序图标

### Linux
- 使用 `.png` 格式
- 通常使用48x48或64x64尺寸

## 🎨 图标特色

### 视觉特点
- ✅ 现代扁平化设计
- ✅ 高对比度，易于识别
- ✅ 多尺寸优化，小图标依然清晰
- ✅ 符合Windows设计规范
- ✅ 体现YOLO检测和自动化功能

### 技术特点
- ✅ 多分辨率ICO文件
- ✅ 透明背景支持
- ✅ 抗锯齿优化
- ✅ 任务栏优化版本
- ✅ 完整的尺寸覆盖

---

**图标设计完成时间**: 2025-07-06  
**设计工具**: Python PIL  
**适用平台**: Windows, macOS, Linux  
**版权**: YOLO OpenCV检测器项目专用
"""
    
    try:
        with open("icons/图标使用指南.md", 'w', encoding='utf-8') as f:
            f.write(guide_content)
        print("✅ 图标使用指南已创建")
        return True
    except Exception as e:
        print(f"❌ 创建图标使用指南失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 YOLO OpenCV检测器图标集成")
    print("=" * 50)
    
    # 检查图标文件是否存在
    icons_dir = Path("icons")
    if not icons_dir.exists():
        print("❌ 图标目录不存在，请先运行 create_app_icons.py")
        return False
    
    main_ico = icons_dir / "yolo_detector_app.ico"
    taskbar_ico = icons_dir / "yolo_detector_taskbar.ico"
    
    if not main_ico.exists() or not taskbar_ico.exists():
        print("❌ 图标文件不完整，请先运行 create_app_icons.py")
        return False
    
    print("✅ 图标文件检查通过")
    
    # 设置项目路径
    setup_project_path()
    
    # 集成图标到应用程序
    success_count = 0
    
    # 1. 集成到主窗口
    if integrate_icons_to_main_window():
        success_count += 1
    
    # 2. 集成到源代码对话框
    if integrate_icons_to_source_dialog():
        success_count += 1
    
    # 3. 创建使用指南
    if create_icon_usage_guide():
        success_count += 1
    
    print("\n" + "=" * 50)
    print("📊 图标集成结果:")
    print("=" * 50)
    print(f"✅ 成功集成: {success_count}/3 个组件")
    
    if success_count == 3:
        print("🎉 图标集成完全成功！")
        print("\n💡 使用说明:")
        print("  1. 重新启动应用程序查看新图标")
        print("  2. 图标将显示在窗口标题栏和任务栏")
        print("  3. 查看 icons/图标使用指南.md 了解详细信息")
        return True
    else:
        print("⚠️ 部分组件集成失败，请检查错误信息")
        return False

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ 图标集成失败: {e}")
        import traceback
        traceback.print_exc()
