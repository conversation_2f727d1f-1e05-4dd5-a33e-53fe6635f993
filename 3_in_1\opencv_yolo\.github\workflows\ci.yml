name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  schedule:
    # 每天凌晨2点运行
    - cron: '0 2 * * *'

env:
  PYTHON_VERSION: '3.9'
  CUDA_VERSION: '11.8'

jobs:
  # 代码质量检查
  code-quality:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install flake8 black isort mypy pylint
        pip install -r requirements_unified.txt
    
    - name: Code formatting check
      run: |
        black --check --diff .
        isort --check-only --diff .
    
    - name: Linting
      run: |
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics
    
    - name: Type checking
      run: |
        mypy 3_in_1/opencv_yolo/src --ignore-missing-imports
    
    - name: Security check
      run: |
        pip install bandit
        bandit -r 3_in_1/opencv_yolo/src -f json -o security-report.json
    
    - name: Upload security report
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: security-report
        path: security-report.json

  # 单元测试
  unit-tests:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest]
        python-version: ['3.8', '3.9', '3.10']
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Install system dependencies (Ubuntu)
      if: matrix.os == 'ubuntu-latest'
      run: |
        sudo apt-get update
        sudo apt-get install -y libgl1-mesa-glx libglib2.0-0 libsm6 libxext6 libxrender-dev libgomp1
    
    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pytest pytest-cov pytest-xvfb pytest-qt
        pip install -r requirements_unified.txt
    
    - name: Run unit tests
      run: |
        cd 3_in_1/opencv_yolo
        python -m pytest tests/ -v --cov=src --cov-report=xml --cov-report=html
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./3_in_1/opencv_yolo/coverage.xml
        flags: unittests
        name: codecov-umbrella

  # 集成测试
  integration-tests:
    runs-on: ubuntu-latest
    needs: [code-quality, unit-tests]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements_unified.txt
        sudo apt-get update
        sudo apt-get install -y xvfb
    
    - name: Run integration tests
      run: |
        cd 3_in_1/opencv_yolo
        xvfb-run -a python -m pytest tests/integration/ -v --tb=short
    
    - name: Test model loading
      run: |
        cd 3_in_1/opencv_yolo
        python -c "
        import sys
        sys.path.insert(0, 'src')
        from yolo_opencv_detector.core.yolo_detector import YoloDetector
        detector = YoloDetector()
        print('Model loading test passed')
        "
    
    - name: Test template preview fix
      run: |
        cd 3_in_1/opencv_yolo
        python test_template_preview_fix.py

  # GPU测试（如果有GPU runner）
  gpu-tests:
    runs-on: self-hosted
    if: contains(github.event.head_commit.message, '[gpu-test]')
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install CUDA dependencies
      run: |
        python -m pip install --upgrade pip
        pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
        pip install -r requirements_unified.txt
    
    - name: Test GPU acceleration
      run: |
        cd 3_in_1/opencv_yolo
        python -c "
        import torch
        print(f'CUDA available: {torch.cuda.is_available()}')
        if torch.cuda.is_available():
            print(f'GPU count: {torch.cuda.device_count()}')
            print(f'Current device: {torch.cuda.current_device()}')
        "

  # 性能测试
  performance-tests:
    runs-on: ubuntu-latest
    needs: [unit-tests]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements_unified.txt
        pip install pytest-benchmark memory-profiler
    
    - name: Run performance tests
      run: |
        cd 3_in_1/opencv_yolo
        python -m pytest tests/performance/ -v --benchmark-only --benchmark-json=benchmark.json
    
    - name: Memory profiling
      run: |
        cd 3_in_1/opencv_yolo
        python -m memory_profiler tests/memory_test.py > memory_profile.txt
    
    - name: Upload performance results
      uses: actions/upload-artifact@v3
      with:
        name: performance-results
        path: |
          3_in_1/opencv_yolo/benchmark.json
          3_in_1/opencv_yolo/memory_profile.txt

  # 构建和打包
  build:
    runs-on: ${{ matrix.os }}
    needs: [code-quality, unit-tests]
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install build dependencies
      run: |
        python -m pip install --upgrade pip
        pip install build wheel setuptools
    
    - name: Build package
      run: |
        cd 3_in_1/opencv_yolo
        python -m build
    
    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: dist-${{ matrix.os }}
        path: 3_in_1/opencv_yolo/dist/

  # 部署到测试环境
  deploy-staging:
    runs-on: ubuntu-latest
    needs: [integration-tests, performance-tests, build]
    if: github.ref == 'refs/heads/develop'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to staging
      run: |
        echo "Deploying to staging environment..."
        # 这里添加实际的部署脚本
    
    - name: Run smoke tests
      run: |
        echo "Running smoke tests..."
        # 这里添加冒烟测试

  # 部署到生产环境
  deploy-production:
    runs-on: ubuntu-latest
    needs: [integration-tests, performance-tests, build]
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to production
      run: |
        echo "Deploying to production environment..."
        # 这里添加实际的部署脚本
    
    - name: Create release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: v${{ github.run_number }}
        release_name: Release v${{ github.run_number }}
        draft: false
        prerelease: false

  # 通知
  notify:
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production]
    if: always()
    
    steps:
    - name: Notify on success
      if: ${{ success() }}
      run: |
        echo "Pipeline completed successfully!"
        # 这里可以添加成功通知，如发送邮件、Slack消息等
    
    - name: Notify on failure
      if: ${{ failure() }}
      run: |
        echo "Pipeline failed!"
        # 这里可以添加失败通知
