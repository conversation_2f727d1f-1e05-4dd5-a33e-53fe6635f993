# -*- coding: utf-8 -*-
"""
并行检测器模块
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

import time
import threading
import queue
import multiprocessing as mp
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor, as_completed
from typing import List, Dict, Any, Optional, Callable, Tuple
import numpy as np

from ..utils.logger import Logger
from ..utils.data_structures import DetectionResult, BoundingBox, DetectionSource
from ..utils.config_manager import ConfigManager


class ParallelDetector:
    """并行检测器类"""
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化并行检测器
        
        Args:
            config_manager: 配置管理器
        """
        self.logger = Logger().get_logger(__name__)
        self.config_manager = config_manager
        
        # 并行配置
        self.max_workers = min(mp.cpu_count(), 8)  # 限制最大工作线程数
        self.use_multiprocessing = False  # 默认使用多线程
        self.batch_size = 4
        
        # 任务队列
        self.detection_queue = queue.Queue(maxsize=100)
        self.result_queue = queue.Queue()
        
        # 线程池
        self.thread_pool = None
        self.process_pool = None
        
        # 状态管理
        self.is_running = False
        self.worker_threads = []
        
        # 性能统计
        self.processed_count = 0
        self.total_processing_time = 0.0
        self.queue_wait_times = []
        
        self.logger.info(f"并行检测器初始化完成，最大工作线程数: {self.max_workers}")
    
    def configure_parallel_settings(self, 
                                   max_workers: Optional[int] = None,
                                   use_multiprocessing: bool = False,
                                   batch_size: int = 4) -> None:
        """
        配置并行设置
        
        Args:
            max_workers: 最大工作线程数
            use_multiprocessing: 是否使用多进程
            batch_size: 批处理大小
        """
        if max_workers:
            self.max_workers = min(max_workers, mp.cpu_count())
        
        self.use_multiprocessing = use_multiprocessing
        self.batch_size = batch_size
        
        self.logger.info(f"并行设置已更新: workers={self.max_workers}, "
                        f"multiprocessing={use_multiprocessing}, batch_size={batch_size}")
    
    def start_parallel_detection(self, 
                                detection_func: Callable,
                                template_func: Optional[Callable] = None) -> None:
        """
        启动并行检测
        
        Args:
            detection_func: YOLO检测函数
            template_func: 模板匹配函数
        """
        try:
            if self.is_running:
                self.logger.warning("并行检测已在运行")
                return
            
            self.is_running = True
            
            # 创建线程池
            if self.use_multiprocessing:
                self.process_pool = ProcessPoolExecutor(max_workers=self.max_workers)
                self.logger.info("使用多进程模式")
            else:
                self.thread_pool = ThreadPoolExecutor(max_workers=self.max_workers)
                self.logger.info("使用多线程模式")
            
            # 启动工作线程
            for i in range(self.max_workers):
                worker_thread = threading.Thread(
                    target=self._worker_loop,
                    args=(detection_func, template_func, i),
                    daemon=True
                )
                worker_thread.start()
                self.worker_threads.append(worker_thread)
            
            self.logger.info(f"并行检测已启动，{len(self.worker_threads)} 个工作线程")
            
        except Exception as e:
            self.logger.error(f"启动并行检测失败: {e}")
            self.is_running = False
    
    def stop_parallel_detection(self) -> None:
        """停止并行检测"""
        try:
            if not self.is_running:
                return
            
            self.is_running = False
            
            # 等待工作线程结束
            for thread in self.worker_threads:
                thread.join(timeout=5.0)
            
            # 关闭线程池
            if self.thread_pool:
                self.thread_pool.shutdown(wait=True)
                self.thread_pool = None
            
            if self.process_pool:
                self.process_pool.shutdown(wait=True)
                self.process_pool = None
            
            # 清空队列
            while not self.detection_queue.empty():
                try:
                    self.detection_queue.get_nowait()
                except queue.Empty:
                    break
            
            while not self.result_queue.empty():
                try:
                    self.result_queue.get_nowait()
                except queue.Empty:
                    break
            
            self.worker_threads.clear()
            
            self.logger.info("并行检测已停止")
            
        except Exception as e:
            self.logger.error(f"停止并行检测失败: {e}")
    
    def _worker_loop(self, 
                    detection_func: Callable,
                    template_func: Optional[Callable],
                    worker_id: int) -> None:
        """
        工作线程循环
        
        Args:
            detection_func: YOLO检测函数
            template_func: 模板匹配函数
            worker_id: 工作线程ID
        """
        self.logger.debug(f"工作线程 {worker_id} 已启动")
        
        while self.is_running:
            try:
                # 从队列获取任务
                try:
                    task = self.detection_queue.get(timeout=1.0)
                except queue.Empty:
                    continue
                
                if task is None:  # 停止信号
                    break
                
                # 处理任务
                start_time = time.time()
                result = self._process_detection_task(task, detection_func, template_func)
                processing_time = time.time() - start_time
                
                # 更新统计
                self.processed_count += 1
                self.total_processing_time += processing_time
                
                # 将结果放入结果队列
                self.result_queue.put({
                    'task_id': task.get('task_id'),
                    'result': result,
                    'processing_time': processing_time,
                    'worker_id': worker_id
                })
                
                # 标记任务完成
                self.detection_queue.task_done()
                
            except Exception as e:
                self.logger.error(f"工作线程 {worker_id} 处理任务失败: {e}")
        
        self.logger.debug(f"工作线程 {worker_id} 已结束")
    
    def _process_detection_task(self, 
                               task: Dict[str, Any],
                               detection_func: Callable,
                               template_func: Optional[Callable]) -> List[DetectionResult]:
        """
        处理检测任务
        
        Args:
            task: 检测任务
            detection_func: YOLO检测函数
            template_func: 模板匹配函数
            
        Returns:
            List[DetectionResult]: 检测结果
        """
        try:
            image = task.get('image')
            task_type = task.get('type', 'both')
            
            results = []
            
            # YOLO检测
            if task_type in ['yolo', 'both'] and detection_func:
                yolo_results = detection_func(image)
                if yolo_results:
                    results.extend(yolo_results)
            
            # 模板匹配
            if task_type in ['template', 'both'] and template_func:
                template_results = template_func(image)
                if template_results:
                    results.extend(template_results)
            
            return results
            
        except Exception as e:
            self.logger.error(f"处理检测任务失败: {e}")
            return []
    
    def submit_detection_task(self, 
                             image: np.ndarray,
                             task_type: str = 'both',
                             priority: int = 0) -> Optional[str]:
        """
        提交检测任务
        
        Args:
            image: 输入图像
            task_type: 任务类型 ('yolo', 'template', 'both')
            priority: 任务优先级
            
        Returns:
            Optional[str]: 任务ID
        """
        try:
            if not self.is_running:
                self.logger.warning("并行检测未运行")
                return None
            
            # 生成任务ID
            task_id = f"task_{int(time.time() * 1000000)}"
            
            # 创建任务
            task = {
                'task_id': task_id,
                'image': image,
                'type': task_type,
                'priority': priority,
                'submit_time': time.time()
            }
            
            # 提交任务到队列
            try:
                self.detection_queue.put(task, timeout=1.0)
                return task_id
            except queue.Full:
                self.logger.warning("检测队列已满，任务被丢弃")
                return None
                
        except Exception as e:
            self.logger.error(f"提交检测任务失败: {e}")
            return None
    
    def get_detection_result(self, timeout: float = 1.0) -> Optional[Dict[str, Any]]:
        """
        获取检测结果
        
        Args:
            timeout: 超时时间
            
        Returns:
            Optional[Dict[str, Any]]: 检测结果
        """
        try:
            return self.result_queue.get(timeout=timeout)
        except queue.Empty:
            return None
        except Exception as e:
            self.logger.error(f"获取检测结果失败: {e}")
            return None
    
    def batch_detect(self, 
                    images: List[np.ndarray],
                    detection_func: Callable,
                    template_func: Optional[Callable] = None) -> List[List[DetectionResult]]:
        """
        批量检测
        
        Args:
            images: 图像列表
            detection_func: YOLO检测函数
            template_func: 模板匹配函数
            
        Returns:
            List[List[DetectionResult]]: 批量检测结果
        """
        try:
            if not images:
                return []
            
            # 分批处理
            batches = [images[i:i + self.batch_size] 
                      for i in range(0, len(images), self.batch_size)]
            
            all_results = []
            
            if self.use_multiprocessing and self.process_pool:
                # 使用多进程
                futures = []
                for batch in batches:
                    future = self.process_pool.submit(
                        self._process_image_batch,
                        batch, detection_func, template_func
                    )
                    futures.append(future)
                
                # 收集结果
                for future in as_completed(futures):
                    batch_results = future.result()
                    all_results.extend(batch_results)
            
            elif self.thread_pool:
                # 使用多线程
                futures = []
                for batch in batches:
                    future = self.thread_pool.submit(
                        self._process_image_batch,
                        batch, detection_func, template_func
                    )
                    futures.append(future)
                
                # 收集结果
                for future in as_completed(futures):
                    batch_results = future.result()
                    all_results.extend(batch_results)
            
            else:
                # 串行处理
                for batch in batches:
                    batch_results = self._process_image_batch(
                        batch, detection_func, template_func
                    )
                    all_results.extend(batch_results)
            
            return all_results
            
        except Exception as e:
            self.logger.error(f"批量检测失败: {e}")
            return []
    
    def _process_image_batch(self, 
                           images: List[np.ndarray],
                           detection_func: Callable,
                           template_func: Optional[Callable]) -> List[List[DetectionResult]]:
        """
        处理图像批次
        
        Args:
            images: 图像列表
            detection_func: YOLO检测函数
            template_func: 模板匹配函数
            
        Returns:
            List[List[DetectionResult]]: 批次检测结果
        """
        try:
            batch_results = []
            
            for image in images:
                results = []
                
                # YOLO检测
                if detection_func:
                    yolo_results = detection_func(image)
                    if yolo_results:
                        results.extend(yolo_results)
                
                # 模板匹配
                if template_func:
                    template_results = template_func(image)
                    if template_results:
                        results.extend(template_results)
                
                batch_results.append(results)
            
            return batch_results
            
        except Exception as e:
            self.logger.error(f"处理图像批次失败: {e}")
            return []
    
    def get_queue_status(self) -> Dict[str, Any]:
        """获取队列状态"""
        return {
            "detection_queue_size": self.detection_queue.qsize(),
            "result_queue_size": self.result_queue.qsize(),
            "is_running": self.is_running,
            "worker_count": len(self.worker_threads),
            "max_workers": self.max_workers
        }
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        avg_processing_time = (self.total_processing_time / self.processed_count 
                              if self.processed_count > 0 else 0)
        
        return {
            "processed_count": self.processed_count,
            "total_processing_time": self.total_processing_time,
            "avg_processing_time": avg_processing_time,
            "throughput": (self.processed_count / self.total_processing_time 
                          if self.total_processing_time > 0 else 0),
            "queue_status": self.get_queue_status()
        }
    
    def reset_stats(self) -> None:
        """重置统计信息"""
        self.processed_count = 0
        self.total_processing_time = 0.0
        self.queue_wait_times.clear()
        
        self.logger.info("性能统计已重置")
    
    def optimize_for_realtime(self) -> None:
        """为实时检测优化设置"""
        try:
            # 减少批处理大小以降低延迟
            self.batch_size = 1
            
            # 使用多线程而非多进程（减少进程间通信开销）
            self.use_multiprocessing = False
            
            # 适当增加工作线程数
            self.max_workers = min(mp.cpu_count() + 2, 12)
            
            self.logger.info("已优化为实时检测模式")
            
        except Exception as e:
            self.logger.error(f"实时检测优化失败: {e}")
    
    def optimize_for_throughput(self) -> None:
        """为高吞吐量优化设置"""
        try:
            # 增加批处理大小
            self.batch_size = 8
            
            # 使用多进程以充分利用CPU
            self.use_multiprocessing = True
            
            # 最大化工作进程数
            self.max_workers = mp.cpu_count()
            
            self.logger.info("已优化为高吞吐量模式")
            
        except Exception as e:
            self.logger.error(f"高吞吐量优化失败: {e}")
