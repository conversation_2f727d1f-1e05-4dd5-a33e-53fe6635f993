#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用示例面板
提供完整的自动化操作场景示例和代码演示
"""

import sys
from pathlib import Path
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, QTextEdit,
    QLabel, QPushButton, QGroupBox, QScrollArea, QSplitter,
    QFrame, QMessageBox, QComboBox, QSpinBox, QCheckBox
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QTextCursor, QSyntaxHighlighter, QTextCharFormat, QColor
import re

class PythonHighlighter(QSyntaxHighlighter):
    """Python代码语法高亮"""

    def __init__(self, parent=None):
        super().__init__(parent)

        # 定义高亮规则
        self.highlighting_rules = []

        # 关键字
        keyword_format = QTextCharFormat()
        keyword_format.setForeground(QColor(0, 0, 255))
        keyword_format.setFontWeight(QFont.Weight.Bold)
        keywords = [
            'def', 'class', 'if', 'else', 'elif', 'for', 'while', 'try', 'except',
            'import', 'from', 'return', 'print', 'True', 'False', 'None'
        ]
        for keyword in keywords:
            pattern = f'\\b{keyword}\\b'
            self.highlighting_rules.append((re.compile(pattern), keyword_format))

        # 字符串
        string_format = QTextCharFormat()
        string_format.setForeground(QColor(0, 128, 0))
        self.highlighting_rules.append((re.compile(r'".*?"'), string_format))
        self.highlighting_rules.append((re.compile(r"'.*?'"), string_format))

        # 注释
        comment_format = QTextCharFormat()
        comment_format.setForeground(QColor(128, 128, 128))
        self.highlighting_rules.append((re.compile(r'#.*'), comment_format))

    def highlightBlock(self, text):
        """高亮文本块"""
        for pattern, format in self.highlighting_rules:
            for match in pattern.finditer(text):
                start, end = match.span()
                self.setFormat(start, end - start, format)

class ExamplesPanel(QWidget):
    """使用示例面板"""

    # 信号
    example_executed = pyqtSignal(str)  # 示例执行信号

    def __init__(self, config_manager=None, parent=None):
        super().__init__(parent)
        self.config_manager = config_manager
        self.init_ui()

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # 标题
        title_label = QLabel("📚 自动化操作使用示例")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        layout.addWidget(title_label)

        # 创建标签页
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)

        # 添加各种示例标签页
        self._create_office_automation_tab()
        self._create_multi_target_tab()
        self._create_target_selection_tab()
        self._create_workflow_tab()
        self._create_error_handling_tab()

    def _create_office_automation_tab(self):
        """创建办公软件自动化示例标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Vertical)
        layout.addWidget(splitter)

        # 说明区域
        desc_group = QGroupBox("📋 办公软件自动化说明")
        desc_layout = QVBoxLayout(desc_group)

        desc_text = QTextEdit()
        desc_text.setMaximumHeight(120)
        desc_text.setPlainText(
            "办公软件自动化示例演示如何使用YOLO检测结果自动操作常见的办公软件，"
            "包括点击按钮、填写表单、选择菜单等操作。\n\n"
            "适用场景：\n"
            "• Excel表格数据录入\n"
            "• Word文档格式调整\n"
            "• 浏览器表单填写\n"
            "• 软件界面自动化操作"
        )
        desc_text.setReadOnly(True)
        desc_layout.addWidget(desc_text)
        splitter.addWidget(desc_group)

        # 代码示例区域
        code_group = QGroupBox("💻 完整代码示例")
        code_layout = QVBoxLayout(code_group)

        code_text = QTextEdit()
        code_text.setFont(QFont("Consolas", 10))

        # 添加语法高亮
        highlighter = PythonHighlighter(code_text.document())

        office_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
办公软件自动化示例
演示如何使用YOLO检测结果进行办公软件自动化操作
包含完整的目标选择、坐标处理、自动化操作的实际代码演示
"""

def office_automation_example():
    """办公软件自动化完整示例"""

    # 1. 初始化检测器
    from yolo_opencv_detector.gui.dialogs.source_code_dialog import SourceCodeDialog

    class OfficeAutomator(SourceCodeDialog):
        def __init__(self):
            super().__init__()

    automator = OfficeAutomator()

    # 2. 执行屏幕检测
    print("🔍 开始检测屏幕中的办公软件界面元素...")
    results = automator.gui_detect_screen()

    if not results:
        print("❌ 未检测到任何界面元素，请确保屏幕上有可见的软件界面")
        return False

    # 3. 解析检测结果并显示详细信息
    print(f"✅ 检测到 {len(results)} 个界面元素")
    targets = automator.parse_detection_results(results)

    print("\\n📋 检测结果详细信息:")
    for i, target in enumerate(targets[:5]):  # 显示前5个目标
        coords = automator.get_target_coordinates(target, 'center')
        dimensions = automator.calculate_target_area_and_dimensions(target)
        print(f"  目标 {i+1}: {target['class_name']}")
        print(f"    置信度: {target['confidence']:.3f}")
        print(f"    中心坐标: ({coords['x']}, {coords['y']})")
        print(f"    面积: {dimensions['area']} 像素")

    # 4. 目标选择策略演示
    print("\\n🎯 目标选择策略演示:")

    # 按置信度选择
    highest_conf_target = automator.select_target_by_confidence(targets, 'highest')
    print(f"  最高置信度目标: {highest_conf_target['class_name']} ({highest_conf_target['confidence']:.3f})")

    # 按位置选择
    leftmost_target = automator.select_target_by_position(targets, 'leftmost')
    rightmost_target = automator.select_target_by_position(targets, 'rightmost')
    print(f"  最左侧目标: {leftmost_target['class_name'] if leftmost_target else '无'}")
    print(f"  最右侧目标: {rightmost_target['class_name'] if rightmost_target else '无'}")

    # 按大小选择
    largest_target = automator.select_target_by_size(targets, 'largest')
    smallest_target = automator.select_target_by_size(targets, 'smallest')
    print(f"  最大目标: {largest_target['class_name'] if largest_target else '无'}")
    print(f"  最小目标: {smallest_target['class_name'] if smallest_target else '无'}")

    # 5. 坐标处理功能演示
    print("\\n📍 坐标处理功能演示:")

    if highest_conf_target:
        # 获取所有关键点坐标
        center = automator.get_target_coordinates(highest_conf_target, 'center')
        top_left = automator.get_target_coordinates(highest_conf_target, 'top_left')
        bottom_right = automator.get_target_coordinates(highest_conf_target, 'bottom_right')

        print(f"  目标: {highest_conf_target['class_name']}")
        print(f"    中心点: ({center['x']}, {center['y']})")
        print(f"    左上角: ({top_left['x']}, {top_left['y']})")
        print(f"    右下角: ({bottom_right['x']}, {bottom_right['y']})")

        # 获取边界框信息
        corners = automator.get_bounding_box_corners(highest_conf_target)
        if corners:
            print(f"    边界框: 左上({corners['top_left']['x']}, {corners['top_left']['y']}) "
                  f"右下({corners['bottom_right']['x']}, {corners['bottom_right']['y']})")

    # 6. 办公软件自动化操作序列

    # 场景1：Excel数据录入自动化
    print("\\n📊 场景1：Excel数据录入自动化")

    # 选择最大的目标（通常是主要的输入区域）
    main_target = automator.select_target_by_size(targets, 'largest')
    if main_target:
        # 点击主要区域
        coords = automator.get_target_coordinates(main_target, 'center')
        print(f"📍 点击主要区域: ({coords['x']}, {coords['y']})")

        # 执行Excel数据录入操作序列
        excel_actions = [
            {'type': 'click', 'params': {'coordinates': coords, 'delay': 0.5}},
            {'type': 'type', 'params': {'text': '销售数据', 'typing_speed': 0.05}},
            {'type': 'shortcut', 'params': {'keys': ['tab'], 'delay': 0.3}},
            {'type': 'type', 'params': {'text': '1000', 'typing_speed': 0.05}},
            {'type': 'shortcut', 'params': {'keys': ['tab'], 'delay': 0.3}},
            {'type': 'type', 'params': {'text': '2024-01-01', 'typing_speed': 0.05}},
            {'type': 'shortcut', 'params': {'keys': ['enter'], 'delay': 0.3}},
        ]

        success = automator.perform_automation_sequence(excel_actions)
        print(f"📊 Excel数据录入: {'✅ 成功' if success else '❌ 失败'}")

    # 场景2：智能按钮点击自动化
    print("\\n🖱️ 场景2：智能按钮点击自动化")

    # 选择置信度最高的目标（通常是最清晰的按钮）
    button_target = automator.select_target_by_confidence(targets, 'highest')
    if button_target:
        button_coords = automator.get_target_coordinates(button_target, 'center')
        print(f"🎯 找到按钮目标: {button_target['class_name']}")
        print(f"📍 按钮位置: ({button_coords['x']}, {button_coords['y']})")

        # 坐标验证
        if automator.validate_coordinates(button_coords):
            print("✅ 坐标验证通过")

            # 执行按钮点击（支持不同点击类型）
            click_success = automator.perform_mouse_click(button_coords, 'left', 'single', 0.5)
            print(f"🖱️ 单击操作: {'✅ 成功' if click_success else '❌ 失败'}")

            # 等待界面响应
            automator.wait_and_delay(1.0)

            # 执行双击操作
            double_click_success = automator.perform_mouse_click(button_coords, 'left', 'double', 0.3)
            print(f"🖱️ 双击操作: {'✅ 成功' if double_click_success else '❌ 失败'}")
        else:
            print("❌ 坐标验证失败，跳过点击操作")

    # 场景3：复杂表单填写自动化
    print("\\n📝 场景3：复杂表单填写自动化")

    # 选择左上角的目标（通常是表单的第一个输入框）
    form_target = automator.select_target_by_position(targets, 'topmost')
    if form_target:
        form_coords = automator.get_target_coordinates(form_target, 'center')
        print(f"📍 表单目标位置: ({form_coords['x']}, {form_coords['y']})")

        # 复杂表单填写操作序列
        form_actions = [
            {'type': 'click', 'params': {'coordinates': form_coords, 'delay': 0.5}},
            {'type': 'shortcut', 'params': {'keys': ['ctrl', 'a'], 'delay': 0.2}},
            {'type': 'type', 'params': {'text': '张三', 'typing_speed': 0.05}},
            {'type': 'shortcut', 'params': {'keys': ['tab'], 'delay': 0.3}},
            {'type': 'type', 'params': {'text': '<EMAIL>', 'typing_speed': 0.05}},
            {'type': 'shortcut', 'params': {'keys': ['tab'], 'delay': 0.3}},
            {'type': 'type', 'params': {'text': '13800138000', 'typing_speed': 0.05}},
            {'type': 'shortcut', 'params': {'keys': ['tab'], 'delay': 0.3}},
            {'type': 'type', 'params': {'text': '北京市朝阳区', 'typing_speed': 0.05}},
        ]

        form_success = automator.perform_automation_sequence(form_actions)
        print(f"📝 表单填写: {'✅ 成功' if form_success else '❌ 失败'}")

    # 场景4：文件拖拽操作自动化
    print("\\n📁 场景4：文件拖拽操作自动化")

    if len(targets) >= 2:
        # 选择源目标和目标位置
        source_target = automator.select_target_by_position(targets, 'leftmost')
        dest_target = automator.select_target_by_position(targets, 'rightmost')

        if source_target and dest_target:
            source_coords = automator.get_target_coordinates(source_target, 'center')
            dest_coords = automator.get_target_coordinates(dest_target, 'center')

            print(f"📍 源位置: ({source_coords['x']}, {source_coords['y']})")
            print(f"📍 目标位置: ({dest_coords['x']}, {dest_coords['y']})")

            # 执行拖拽操作
            drag_success = automator.perform_mouse_drag(
                source_coords, dest_coords, duration=2.0, button='left'
            )
            print(f"📁 拖拽操作: {'✅ 成功' if drag_success else '❌ 失败'}")

    # 场景5：键盘快捷键操作自动化
    print("\\n⌨️ 场景5：键盘快捷键操作自动化")

    # 常用快捷键操作序列
    shortcut_actions = [
        {'type': 'shortcut', 'params': {'keys': ['ctrl', 'c'], 'delay': 0.3}},
        {'type': 'wait', 'params': {'seconds': 0.5}},
        {'type': 'shortcut', 'params': {'keys': ['ctrl', 'v'], 'delay': 0.3}},
        {'type': 'wait', 'params': {'seconds': 0.5}},
        {'type': 'shortcut', 'params': {'keys': ['ctrl', 's'], 'delay': 0.3}},
        {'type': 'wait', 'params': {'seconds': 0.5}},
        {'type': 'shortcut', 'params': {'keys': ['alt', 'tab'], 'delay': 0.3}},
    ]

    shortcut_success = automator.perform_automation_sequence(shortcut_actions)
    print(f"⌨️ 快捷键操作: {'✅ 成功' if shortcut_success else '❌ 失败'}")

    # 场景6：条件判断和错误处理
    print("\\n🛡️ 场景6：条件判断和错误处理")

    try:
        # 检查目标质量
        high_quality_targets = [
            t for t in targets
            if t.get('confidence', 0) > 0.8
        ]

        print(f"📊 高质量目标数量: {len(high_quality_targets)}/{len(targets)}")

        if high_quality_targets:
            best_target = high_quality_targets[0]
            coords = automator.get_target_coordinates(best_target, 'center')

            # 坐标安全验证
            if automator.validate_coordinates(coords):
                print("✅ 坐标验证通过，执行安全操作")

                # 记录操作日志
                automator.log_automation_action(
                    'safe_click',
                    {'target': best_target['class_name'], 'coordinates': coords},
                    True
                )

                # 执行安全点击
                safe_click = automator.perform_mouse_click(coords, 'left', 'single', 0.3)
                print(f"🛡️ 安全点击: {'✅ 成功' if safe_click else '❌ 失败'}")
            else:
                print("⚠️ 坐标验证失败，操作被阻止")
        else:
            print("⚠️ 无高质量目标，建议调整检测参数")

    except Exception as e:
        print(f"❌ 操作异常: {e}")
        # 异常恢复操作
        automator.perform_keyboard_shortcut(['escape'])
        print("🚑 执行异常恢复操作")

    # 7. 操作总结和最佳实践
    print("\\n📋 办公软件自动化操作完成")
    print("✅ 已演示的功能:")
    print("   • 智能目标选择（置信度/位置/大小）")
    print("   • 精确坐标处理（9个关键点）")
    print("   • 鼠标操作（点击/双击/拖拽）")
    print("   • 键盘操作（文本输入/快捷键）")
    print("   • 复合操作序列")
    print("   • 坐标验证和安全检查")
    print("   • 操作日志和错误处理")

    print("\\n💡 最佳实践建议:")
    print("   • 始终验证目标置信度 (>0.7)")
    print("   • 使用坐标验证确保安全")
    print("   • 合理设置操作延迟时间")
    print("   • 记录详细的操作日志")
    print("   • 实现异常处理和恢复机制")

    return True

# 使用方法
if __name__ == "__main__":
    # 运行办公软件自动化示例
    office_automation_example()
'''

        code_text.setPlainText(office_code)
        code_text.setReadOnly(True)
        code_layout.addWidget(code_text)

        # 运行按钮
        run_btn = QPushButton("▶️ 运行此示例")
        run_btn.setMinimumHeight(35)
        run_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        run_btn.clicked.connect(lambda: self._run_example("office_automation"))
        code_layout.addWidget(run_btn)

        splitter.addWidget(code_group)

        # 设置分割器比例
        splitter.setSizes([150, 400])

        self.tab_widget.addTab(tab, "🏢 办公软件自动化")

    def _run_example(self, example_type):
        """运行示例"""
        QMessageBox.information(
            self,
            "示例运行",
            f"正在运行 {example_type} 示例...\n\n"
            "注意：实际运行时请确保：\n"
            "1. 屏幕上有相应的软件界面\n"
            "2. 已正确配置YOLO模型\n"
            "3. 具有必要的系统权限"
        )
        self.example_executed.emit(example_type)

    def _create_multi_target_tab(self):
        """创建多目标操作示例标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Vertical)
        layout.addWidget(splitter)

        # 说明区域
        desc_group = QGroupBox("🎯 多目标操作说明")
        desc_layout = QVBoxLayout(desc_group)

        desc_text = QTextEdit()
        desc_text.setMaximumHeight(100)
        desc_text.setPlainText(
            "多目标操作示例演示如何同时处理多个检测目标，实现复杂的拖拽、"
            "序列操作等功能。\n\n"
            "适用场景：\n"
            "• 文件拖拽操作\n"
            "• 多窗口协调\n"
            "• 批量处理操作"
        )
        desc_text.setReadOnly(True)
        desc_layout.addWidget(desc_text)
        splitter.addWidget(desc_group)

        # 代码示例区域
        code_group = QGroupBox("💻 多目标操作代码")
        code_layout = QVBoxLayout(code_group)

        code_text = QTextEdit()
        code_text.setFont(QFont("Consolas", 10))
        highlighter = PythonHighlighter(code_text.document())

        multi_target_code = '''def multi_target_operation_example():
    """多目标操作完整示例 - 演示复杂的多目标协调操作"""

    # 初始化自动化器
    from yolo_opencv_detector.gui.dialogs.source_code_dialog import SourceCodeDialog

    class OfficeAutomator(SourceCodeDialog):
        def __init__(self):
            super().__init__()

    automator = OfficeAutomator()

    # 1. 执行检测并分析目标
    print("🔍 检测多个目标...")
    results = automator.gui_detect_screen()
    targets = automator.parse_detection_results(results)

    if len(targets) < 2:
        print("⚠️ 需要至少2个目标进行多目标操作")
        print("💡 建议：确保屏幕上有多个可检测的界面元素")
        return False

    print(f"✅ 找到 {len(targets)} 个目标")

    # 显示所有目标的详细信息
    print("\\n📋 目标详细信息:")
    for i, target in enumerate(targets):
        coords = automator.get_target_coordinates(target, 'center')
        dimensions = automator.calculate_target_area_and_dimensions(target)
        print(f"  目标 {i+1}: {target['class_name']}")
        print(f"    置信度: {target['confidence']:.3f}")
        print(f"    中心坐标: ({coords['x']}, {coords['y']})")
        print(f"    面积: {dimensions['area']} 像素")
        print(f"    宽高比: {dimensions['aspect_ratio']:.2f}")

    # 2. 多目标选择策略演示
    print("\\n🎯 多目标选择策略演示:")

    # 按位置选择（四个方向）
    position_targets = {}
    position_strategies = [
        ('leftmost', '最左侧'),
        ('rightmost', '最右侧'),
        ('topmost', '最上方'),
        ('bottommost', '最下方'),
        ('center', '最中心')
    ]

    for strategy, description in position_strategies:
        target = automator.select_target_by_position(targets, strategy)
        if target:
            coords = automator.get_target_coordinates(target, 'center')
            position_targets[strategy] = target
            print(f"📍 {description}目标: {target['class_name']} "
                  f"位置: ({coords['x']}, {coords['y']}) "
                  f"置信度: {target['confidence']:.3f}")

    # 按大小选择
    size_targets = {}
    largest = automator.select_target_by_size(targets, 'largest')
    smallest = automator.select_target_by_size(targets, 'smallest')

    if largest:
        size_targets['largest'] = largest
        dimensions = automator.calculate_target_area_and_dimensions(largest)
        print(f"📏 最大目标: {largest['class_name']} 面积: {dimensions['area']} 像素")

    if smallest:
        size_targets['smallest'] = smallest
        dimensions = automator.calculate_target_area_and_dimensions(smallest)
        print(f"📏 最小目标: {smallest['class_name']} 面积: {dimensions['area']} 像素")

    # 按置信度选择
    confidence_targets = {}
    highest_conf = automator.select_target_by_confidence(targets, 'highest')
    lowest_conf = automator.select_target_by_confidence(targets, 'lowest')

    if highest_conf:
        confidence_targets['highest'] = highest_conf
        print(f"🎯 最高置信度: {highest_conf['class_name']} ({highest_conf['confidence']:.3f})")

    if lowest_conf:
        confidence_targets['lowest'] = lowest_conf
        print(f"🎯 最低置信度: {lowest_conf['class_name']} ({lowest_conf['confidence']:.3f})")

    # 按类别分组
    print("\\n🏷️ 按类别分组:")
    class_groups = {}
    for target in targets:
        class_name = target.get('class_name', '未知')
        if class_name not in class_groups:
            class_groups[class_name] = []
        class_groups[class_name].append(target)

    for class_name, class_targets in class_groups.items():
        print(f"  类别 '{class_name}': {len(class_targets)} 个目标")
        if class_targets:
            best_in_class = automator.select_target_by_confidence(class_targets, 'highest')
            coords = automator.get_target_coordinates(best_in_class, 'center')
            print(f"    最佳目标: 置信度 {best_in_class['confidence']:.3f} "
                  f"位置 ({coords['x']}, {coords['y']})")

    # 3. 多目标拖拽操作示例
    print("\\n🖱️ 多目标拖拽操作示例:")

    # 场景1：左右拖拽
    if 'leftmost' in position_targets and 'rightmost' in position_targets:
        leftmost = position_targets['leftmost']
        rightmost = position_targets['rightmost']

        start_coords = automator.get_target_coordinates(leftmost, 'center')
        end_coords = automator.get_target_coordinates(rightmost, 'center')

        print(f"📍 水平拖拽: 从 ({start_coords['x']}, {start_coords['y']}) "
              f"到 ({end_coords['x']}, {end_coords['y']})")
        print(f"  源目标: {leftmost['class_name']} (置信度: {leftmost['confidence']:.3f})")
        print(f"  目标位置: {rightmost['class_name']} (置信度: {rightmost['confidence']:.3f})")

        # 执行水平拖拽
        drag_success = automator.perform_mouse_drag(
            start_coords, end_coords, duration=2.0, button='left'
        )
        print(f"🖱️ 水平拖拽: {'✅ 成功' if drag_success else '❌ 失败'}")

    # 场景2：垂直拖拽
    if 'topmost' in position_targets and 'bottommost' in position_targets:
        topmost = position_targets['topmost']
        bottommost = position_targets['bottommost']

        start_coords = automator.get_target_coordinates(topmost, 'center')
        end_coords = automator.get_target_coordinates(bottommost, 'center')

        print(f"📍 垂直拖拽: 从 ({start_coords['x']}, {start_coords['y']}) "
              f"到 ({end_coords['x']}, {end_coords['y']})")

        # 执行垂直拖拽
        drag_success = automator.perform_mouse_drag(
            start_coords, end_coords, duration=1.5, button='left'
        )
        print(f"🖱️ 垂直拖拽: {'✅ 成功' if drag_success else '❌ 失败'}")

    # 场景3：对角线拖拽
    if 'largest' in size_targets and 'smallest' in size_targets:
        large_target = size_targets['largest']
        small_target = size_targets['smallest']

        # 从大目标的左上角拖拽到小目标的右下角
        start_coords = automator.get_target_coordinates(large_target, 'top_left')
        end_coords = automator.get_target_coordinates(small_target, 'bottom_right')

        print(f"📍 对角线拖拽: 从大目标左上角到小目标右下角")
        print(f"  起点: ({start_coords['x']}, {start_coords['y']})")
        print(f"  终点: ({end_coords['x']}, {end_coords['y']})")

        # 执行对角线拖拽
        drag_success = automator.perform_mouse_drag(
            start_coords, end_coords, duration=2.5, button='left'
        )
        print(f"🖱️ 对角线拖拽: {'✅ 成功' if drag_success else '❌ 失败'}")

    # 4. 复杂序列操作示例
    print("\\n⚡ 复杂序列操作示例:")

    # 创建多目标协调操作序列
    sequence_actions = []

    # 序列1：依次点击所有高质量目标
    high_quality_targets = [t for t in targets if t.get('confidence', 0) > 0.7]
    print(f"📊 高质量目标数量: {len(high_quality_targets)}")

    for i, target in enumerate(high_quality_targets[:4]):  # 最多处理4个目标
        coords = automator.get_target_coordinates(target, 'center')
        print(f"  目标 {i+1}: {target['class_name']} 位置: ({coords['x']}, {coords['y']})")

        sequence_actions.extend([
            {'type': 'click', 'params': {'coordinates': coords, 'delay': 0.5}},
            {'type': 'wait', 'params': {'seconds': 0.3}},
        ])

    # 序列2：添加键盘操作和验证
    sequence_actions.extend([
        {'type': 'shortcut', 'params': {'keys': ['ctrl', 'c'], 'delay': 0.2}},
        {'type': 'wait', 'params': {'seconds': 0.5}},
        {'type': 'shortcut', 'params': {'keys': ['ctrl', 'v'], 'delay': 0.2}},
        {'type': 'wait', 'params': {'seconds': 0.3}},
        {'type': 'shortcut', 'params': {'keys': ['ctrl', 's'], 'delay': 0.2}},
    ])

    # 执行复杂序列
    print("🎯 执行复杂多目标序列操作...")
    sequence_success = automator.perform_automation_sequence(sequence_actions)
    print(f"⚡ 序列操作: {'✅ 成功' if sequence_success else '❌ 失败'}")

    # 5. 多目标坐标计算和分析
    print("\\n📐 多目标坐标计算和分析:")

    # 计算目标间的距离和关系
    if len(targets) >= 2:
        target1 = targets[0]
        target2 = targets[1]

        coords1 = automator.get_target_coordinates(target1, 'center')
        coords2 = automator.get_target_coordinates(target2, 'center')

        # 计算两目标间距离
        import math
        distance = math.sqrt((coords2['x'] - coords1['x'])**2 + (coords2['y'] - coords1['y'])**2)

        print(f"📏 目标间距离分析:")
        print(f"  目标1: {target1['class_name']} 位置: ({coords1['x']}, {coords1['y']})")
        print(f"  目标2: {target2['class_name']} 位置: ({coords2['x']}, {coords2['y']})")
        print(f"  直线距离: {distance:.1f} 像素")

        # 判断相对位置关系
        if coords2['x'] > coords1['x']:
            h_relation = "右侧"
        elif coords2['x'] < coords1['x']:
            h_relation = "左侧"
        else:
            h_relation = "同列"

        if coords2['y'] > coords1['y']:
            v_relation = "下方"
        elif coords2['y'] < coords1['y']:
            v_relation = "上方"
        else:
            v_relation = "同行"

        print(f"  相对位置: 目标2在目标1的{h_relation}{v_relation}")

    # 详细坐标信息展示
    print("\\n📍 详细坐标信息:")
    for i, target in enumerate(targets[:3]):  # 显示前3个目标
        print(f"\\n目标 {i+1}: {target['class_name']} (置信度: {target['confidence']:.3f})")

        # 获取所有9个关键点坐标
        key_points = ['center', 'top_left', 'top_right', 'bottom_left', 'bottom_right',
                     'top_center', 'bottom_center', 'left_center', 'right_center']

        coords_info = {}
        for point in key_points:
            coords = automator.get_target_coordinates(target, point)
            if coords:
                coords_info[point] = coords
                print(f"    {point}: ({coords['x']}, {coords['y']})")

        # 获取边界框信息
        corners = automator.get_bounding_box_corners(target)
        if corners:
            print(f"    边界框: 宽度 {corners['bottom_right']['x'] - corners['top_left']['x']} "
                  f"高度 {corners['bottom_right']['y'] - corners['top_left']['y']}")

        # 计算目标尺寸和属性
        dimensions = automator.calculate_target_area_and_dimensions(target)
        print(f"    面积: {dimensions['area']} 像素")
        print(f"    周长: {dimensions['perimeter']:.1f} 像素")
        print(f"    宽高比: {dimensions['aspect_ratio']:.2f}")

    # 6. 批量处理操作示例
    print("\\n🔄 批量处理操作示例:")

    # 按类别批量处理
    for class_name, class_targets in class_groups.items():
        if len(class_targets) > 1:
            print(f"\\n📦 批量处理类别 '{class_name}' ({len(class_targets)} 个目标):")

            for i, target in enumerate(class_targets):
                coords = automator.get_target_coordinates(target, 'center')
                print(f"  处理目标 {i+1}: 位置 ({coords['x']}, {coords['y']}) "
                      f"置信度 {target['confidence']:.3f}")

                # 执行批量操作（示例：右键点击）
                if automator.validate_coordinates(coords):
                    click_success = automator.perform_mouse_click(coords, 'right', 'single', 0.2)
                    print(f"    右键点击: {'✅ 成功' if click_success else '❌ 失败'}")

    # 7. 多目标操作总结
    print("\\n📋 多目标操作示例完成")
    print("✅ 已演示的功能:")
    print("   • 多维度目标选择（位置/大小/置信度/类别）")
    print("   • 复杂拖拽操作（水平/垂直/对角线）")
    print("   • 多目标协调序列操作")
    print("   • 目标间距离和关系分析")
    print("   • 9个关键点坐标计算")
    print("   • 按类别批量处理")
    print("   • 坐标验证和安全检查")

    print("\\n💡 适用场景:")
    print("   • 文件管理器拖拽操作")
    print("   • 多窗口应用协调")
    print("   • 批量界面元素处理")
    print("   • 复杂工作流程自动化")

    return True

# 使用方法
if __name__ == "__main__":
    multi_target_operation_example()
'''

        code_text.setPlainText(multi_target_code)
        code_text.setReadOnly(True)
        code_layout.addWidget(code_text)

        # 运行按钮
        run_btn = QPushButton("▶️ 运行此示例")
        run_btn.setMinimumHeight(35)
        run_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        run_btn.clicked.connect(lambda: self._run_example("multi_target"))
        code_layout.addWidget(run_btn)

        splitter.addWidget(code_group)
        splitter.setSizes([120, 450])

        self.tab_widget.addTab(tab, "🎯 多目标操作")

    def _create_target_selection_tab(self):
        """创建目标选择策略示例标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Vertical)
        layout.addWidget(splitter)

        # 说明区域
        desc_group = QGroupBox("🔍 目标选择策略说明")
        desc_layout = QVBoxLayout(desc_group)

        desc_text = QTextEdit()
        desc_text.setMaximumHeight(100)
        desc_text.setPlainText(
            "目标选择策略示例演示各种智能目标选择方法，帮助在复杂界面中"
            "精确定位所需的操作目标。\n\n"
            "支持策略：\n"
            "• 按置信度选择（最高/最低）\n"
            "• 按位置选择（上下左右中心）\n"
            "• 按大小选择（最大/最小）\n"
            "• 按类别选择\n"
            "• 自定义条件选择"
        )
        desc_text.setReadOnly(True)
        desc_layout.addWidget(desc_text)
        splitter.addWidget(desc_group)

        # 代码示例区域
        code_group = QGroupBox("💻 目标选择策略代码")
        code_layout = QVBoxLayout(code_group)

        code_text = QTextEdit()
        code_text.setFont(QFont("Consolas", 10))
        highlighter = PythonHighlighter(code_text.document())

        selection_code = '''def target_selection_strategies_example():
    """目标选择策略完整示例 - 演示各种智能目标选择方法"""

    # 初始化自动化器
    from yolo_opencv_detector.gui.dialogs.source_code_dialog import SourceCodeDialog

    class OfficeAutomator(SourceCodeDialog):
        def __init__(self):
            super().__init__()

    automator = OfficeAutomator()

    # 执行检测并显示基础信息
    print("🔍 执行屏幕检测...")
    results = automator.gui_detect_screen()
    targets = automator.parse_detection_results(results)

    if not targets:
        print("❌ 未检测到任何目标")
        print("💡 建议：确保屏幕上有可检测的界面元素")
        return False

    print(f"✅ 检测到 {len(targets)} 个目标")

    # 显示所有目标的基础信息
    print("\\n📋 所有目标基础信息:")
    for i, target in enumerate(targets):
        coords = automator.get_target_coordinates(target, 'center')
        dimensions = automator.calculate_target_area_and_dimensions(target)
        print(f"  目标 {i+1}: {target['class_name']}")
        print(f"    置信度: {target['confidence']:.3f}")
        print(f"    中心坐标: ({coords['x']}, {coords['y']})")
        print(f"    面积: {dimensions['area']} 像素")

    # 1. 按置信度选择策略（详细版）
    print("\\n🎯 按置信度选择策略:")

    # 选择置信度最高的目标
    highest_conf_target = automator.select_target_by_confidence(targets, 'highest')
    if highest_conf_target:
        print(f"  最高置信度目标: {highest_conf_target['class_name']} "
              f"(置信度: {highest_conf_target['confidence']:.3f})")

        # 获取多个关键点坐标
        center_coords = automator.get_target_coordinates(highest_conf_target, 'center')
        top_left_coords = automator.get_target_coordinates(highest_conf_target, 'top_left')
        bottom_right_coords = automator.get_target_coordinates(highest_conf_target, 'bottom_right')

        print(f"    中心坐标: ({center_coords['x']}, {center_coords['y']})")
        print(f"    左上角: ({top_left_coords['x']}, {top_left_coords['y']})")
        print(f"    右下角: ({bottom_right_coords['x']}, {bottom_right_coords['y']})")

        # 坐标验证和安全检查
        if automator.validate_coordinates(center_coords):
            print("    ✅ 坐标验证通过")

            # 执行精确点击
            click_success = automator.perform_mouse_click(center_coords, 'left', 'single', 0.3)
            print(f"    点击结果: {'✅ 成功' if click_success else '❌ 失败'}")

            # 记录操作日志
            automator.log_automation_action(
                'confidence_based_click',
                {'target': highest_conf_target['class_name'], 'confidence': highest_conf_target['confidence']},
                click_success
            )
        else:
            print("    ❌ 坐标验证失败，跳过操作")

    # 选择置信度最低的目标（用于对比分析）
    lowest_conf_target = automator.select_target_by_confidence(targets, 'lowest')
    if lowest_conf_target:
        print(f"  最低置信度目标: {lowest_conf_target['class_name']} "
              f"(置信度: {lowest_conf_target['confidence']:.3f})")

        # 置信度阈值检查
        if lowest_conf_target['confidence'] < 0.5:
            print("    ⚠️ 置信度过低，不建议进行自动化操作")
        else:
            print("    ✅ 置信度可接受")

    # 置信度分组分析
    print("\\n📊 置信度分组分析:")
    high_conf_targets = [t for t in targets if t['confidence'] > 0.8]
    medium_conf_targets = [t for t in targets if 0.5 <= t['confidence'] <= 0.8]
    low_conf_targets = [t for t in targets if t['confidence'] < 0.5]

    print(f"  高置信度目标 (>0.8): {len(high_conf_targets)} 个")
    print(f"  中等置信度目标 (0.5-0.8): {len(medium_conf_targets)} 个")
    print(f"  低置信度目标 (<0.5): {len(low_conf_targets)} 个")

    # 2. 按位置选择策略（详细版）
    print("\\n📍 按位置选择策略:")

    position_strategies = [
        ('leftmost', '最左侧'),
        ('rightmost', '最右侧'),
        ('topmost', '最上方'),
        ('bottommost', '最下方'),
        ('center', '最中心')
    ]

    position_targets = {}
    for strategy, description in position_strategies:
        target = automator.select_target_by_position(targets, strategy)
        if target:
            coords = automator.get_target_coordinates(target, 'center')
            position_targets[strategy] = target

            print(f"  {description}目标: {target['class_name']}")
            print(f"    位置: ({coords['x']}, {coords['y']})")
            print(f"    置信度: {target['confidence']:.3f}")

            # 获取边界框信息
            corners = automator.get_bounding_box_corners(target)
            if corners:
                width = corners['bottom_right']['x'] - corners['top_left']['x']
                height = corners['bottom_right']['y'] - corners['top_left']['y']
                print(f"    尺寸: {width}x{height} 像素")

    # 位置关系分析
    print("\\n🔍 位置关系分析:")
    if 'leftmost' in position_targets and 'rightmost' in position_targets:
        left_coords = automator.get_target_coordinates(position_targets['leftmost'], 'center')
        right_coords = automator.get_target_coordinates(position_targets['rightmost'], 'center')
        horizontal_span = right_coords['x'] - left_coords['x']
        print(f"  水平跨度: {horizontal_span} 像素")

    if 'topmost' in position_targets and 'bottommost' in position_targets:
        top_coords = automator.get_target_coordinates(position_targets['topmost'], 'center')
        bottom_coords = automator.get_target_coordinates(position_targets['bottommost'], 'center')
        vertical_span = bottom_coords['y'] - top_coords['y']
        print(f"  垂直跨度: {vertical_span} 像素")

    # 位置选择的实际应用示例
    print("\\n🎯 位置选择实际应用:")

    # 示例1：点击最左侧目标（通常是菜单或导航）
    if 'leftmost' in position_targets:
        leftmost_target = position_targets['leftmost']
        coords = automator.get_target_coordinates(leftmost_target, 'center')

        print(f"  应用场景1: 点击最左侧目标（菜单/导航）")
        print(f"    目标: {leftmost_target['class_name']}")

        if automator.validate_coordinates(coords):
            click_success = automator.perform_mouse_click(coords, 'left', 'single', 0.3)
            print(f"    操作结果: {'✅ 成功' if click_success else '❌ 失败'}")

    # 示例2：点击最右侧目标（通常是关闭按钮或设置）
    if 'rightmost' in position_targets:
        rightmost_target = position_targets['rightmost']
        coords = automator.get_target_coordinates(rightmost_target, 'center')

        print(f"  应用场景2: 点击最右侧目标（关闭/设置）")
        print(f"    目标: {rightmost_target['class_name']}")

        if automator.validate_coordinates(coords):
            # 使用右键点击（常用于设置菜单）
            right_click_success = automator.perform_mouse_click(coords, 'right', 'single', 0.3)
            print(f"    右键操作: {'✅ 成功' if right_click_success else '❌ 失败'}")

    # 示例3：点击最上方目标（通常是标题栏或工具栏）
    if 'topmost' in position_targets:
        topmost_target = position_targets['topmost']
        coords = automator.get_target_coordinates(topmost_target, 'center')

        print(f"  应用场景3: 点击最上方目标（标题栏/工具栏）")
        print(f"    目标: {topmost_target['class_name']}")

        if automator.validate_coordinates(coords):
            # 使用双击（常用于标题栏最大化）
            double_click_success = automator.perform_mouse_click(coords, 'left', 'double', 0.3)
            print(f"    双击操作: {'✅ 成功' if double_click_success else '❌ 失败'}")

    # 3. 按大小选择策略（详细版）
    print("\\n📏 按大小选择策略:")

    # 最大目标（通常是主要操作区域）
    largest_target = automator.select_target_by_size(targets, 'largest')
    if largest_target:
        dimensions = automator.calculate_target_area_and_dimensions(largest_target)
        print(f"  最大目标: {largest_target['class_name']}")
        print(f"    面积: {dimensions['area']} 像素")
        print(f"    周长: {dimensions['perimeter']:.1f} 像素")
        print(f"    宽高比: {dimensions['aspect_ratio']:.2f}")
        print(f"    置信度: {largest_target['confidence']:.3f}")

        # 在最大目标的不同位置执行操作演示
        print("    🎯 多点位操作演示:")
        positions = [
            ('center', '中心点'),
            ('top_left', '左上角'),
            ('top_right', '右上角'),
            ('bottom_left', '左下角'),
            ('bottom_right', '右下角'),
            ('top_center', '上边中心'),
            ('bottom_center', '下边中心'),
            ('left_center', '左边中心'),
            ('right_center', '右边中心')
        ]

        for pos, desc in positions:
            coords = automator.get_target_coordinates(largest_target, pos)
            if coords:
                print(f"      {desc}: ({coords['x']}, {coords['y']})")

                # 对关键位置进行实际操作演示
                if pos in ['center', 'top_left', 'bottom_right']:
                    if automator.validate_coordinates(coords):
                        # 执行轻量级操作（移动鼠标到位置）
                        print(f"        ✅ 坐标验证通过，可执行操作")

    # 最小目标分析
    smallest_target = automator.select_target_by_size(targets, 'smallest')
    if smallest_target:
        dimensions = automator.calculate_target_area_and_dimensions(smallest_target)
        print(f"  最小目标: {smallest_target['class_name']}")
        print(f"    面积: {dimensions['area']} 像素")
        print(f"    置信度: {smallest_target['confidence']:.3f}")

        # 小目标的特殊处理
        if dimensions['area'] < 100:
            print("    ⚠️ 目标过小，建议使用精确点击")
        else:
            print("    ✅ 目标大小适中")

    # 大小分组分析
    print("\\n📊 大小分组分析:")

    # 按面积分组
    large_targets = []
    medium_targets = []
    small_targets = []

    for target in targets:
        dimensions = automator.calculate_target_area_and_dimensions(target)
        area = dimensions['area']

        if area > 10000:
            large_targets.append(target)
        elif area > 1000:
            medium_targets.append(target)
        else:
            small_targets.append(target)

    print(f"  大型目标 (>10000px²): {len(large_targets)} 个")
    print(f"  中型目标 (1000-10000px²): {len(medium_targets)} 个")
    print(f"  小型目标 (<1000px²): {len(small_targets)} 个")

    # 大小选择的实际应用
    print("\\n🎯 大小选择实际应用:")

    # 应用1：在最大目标中进行文本输入
    if largest_target:
        coords = automator.get_target_coordinates(largest_target, 'center')
        print(f"  应用场景1: 在最大目标中进行文本输入")
        print(f"    目标: {largest_target['class_name']}")

        if automator.validate_coordinates(coords):
            # 模拟文本输入操作
            text_input_actions = [
                {'type': 'click', 'params': {'coordinates': coords, 'delay': 0.5}},
                {'type': 'shortcut', 'params': {'keys': ['ctrl', 'a'], 'delay': 0.2}},
                {'type': 'type', 'params': {'text': '大目标文本输入测试', 'typing_speed': 0.05}},
            ]

            sequence_success = automator.perform_automation_sequence(text_input_actions)
            print(f"    文本输入: {'✅ 成功' if sequence_success else '❌ 失败'}")

    # 应用2：精确点击小目标
    if smallest_target:
        coords = automator.get_target_coordinates(smallest_target, 'center')
        print(f"  应用场景2: 精确点击小目标")
        print(f"    目标: {smallest_target['class_name']}")

        if automator.validate_coordinates(coords):
            # 小目标使用更精确的点击
            precise_click = automator.perform_mouse_click(coords, 'left', 'single', 0.1)
            print(f"    精确点击: {'✅ 成功' if precise_click else '❌ 失败'}")

    # 4. 按类别选择策略（详细版）
    print("\\n🏷️ 按类别选择策略:")

    # 获取所有检测到的类别并进行统计
    detected_classes = list(set(target.get('class_name', '未知') for target in targets))
    print(f"  检测到的类别: {detected_classes}")
    print(f"  类别总数: {len(detected_classes)}")

    # 详细的类别分析
    class_analysis = {}
    for class_name in detected_classes:
        class_targets = automator.select_target_by_class(targets, class_name)
        if class_targets:
            # 计算该类别的统计信息
            confidences = [t['confidence'] for t in class_targets]
            areas = []
            for t in class_targets:
                dimensions = automator.calculate_target_area_and_dimensions(t)
                areas.append(dimensions['area'])

            class_analysis[class_name] = {
                'count': len(class_targets),
                'avg_confidence': sum(confidences) / len(confidences),
                'max_confidence': max(confidences),
                'min_confidence': min(confidences),
                'avg_area': sum(areas) / len(areas),
                'targets': class_targets
            }

    # 显示类别统计信息
    print("\\n📊 类别详细统计:")
    for class_name, stats in class_analysis.items():
        print(f"  类别 '{class_name}':")
        print(f"    目标数量: {stats['count']}")
        print(f"    平均置信度: {stats['avg_confidence']:.3f}")
        print(f"    置信度范围: {stats['min_confidence']:.3f} - {stats['max_confidence']:.3f}")
        print(f"    平均面积: {stats['avg_area']:.0f} 像素")

    # 按类别进行实际操作演示
    print("\\n🎯 按类别操作演示:")

    for class_name, stats in list(class_analysis.items())[:3]:  # 处理前3个类别
        print(f"\\n  处理类别 '{class_name}' ({stats['count']} 个目标):")

        class_targets = stats['targets']

        # 选择该类别中最佳的目标
        best_in_class = automator.select_target_by_confidence(class_targets, 'highest')
        coords = automator.get_target_coordinates(best_in_class, 'center')

        print(f"    最佳目标: 置信度 {best_in_class['confidence']:.3f}")
        print(f"    位置: ({coords['x']}, {coords['y']})")

        # 根据类别特点选择不同的操作策略
        if stats['avg_area'] > 5000:
            # 大型目标：适合文本输入
            print(f"    策略: 大型目标，执行文本输入操作")
            if automator.validate_coordinates(coords):
                text_actions = [
                    {'type': 'click', 'params': {'coordinates': coords, 'delay': 0.3}},
                    {'type': 'type', 'params': {'text': f'{class_name}测试', 'typing_speed': 0.05}},
                ]
                success = automator.perform_automation_sequence(text_actions)
                print(f"    文本输入: {'✅ 成功' if success else '❌ 失败'}")

        elif stats['avg_area'] < 1000:
            # 小型目标：适合精确点击
            print(f"    策略: 小型目标，执行精确点击")
            if automator.validate_coordinates(coords):
                click_success = automator.perform_mouse_click(coords, 'left', 'single', 0.1)
                print(f"    精确点击: {'✅ 成功' if click_success else '❌ 失败'}")

        else:
            # 中型目标：适合常规操作
            print(f"    策略: 中型目标，执行常规点击")
            if automator.validate_coordinates(coords):
                click_success = automator.perform_mouse_click(coords, 'left', 'single', 0.3)
                print(f"    常规点击: {'✅ 成功' if click_success else '❌ 失败'}")

        # 如果该类别有多个目标，演示批量处理
        if len(class_targets) > 1:
            print(f"    批量处理演示 ({len(class_targets)} 个目标):")
            for i, target in enumerate(class_targets[:3]):  # 最多处理3个
                coords = automator.get_target_coordinates(target, 'center')
                print(f"      目标 {i+1}: 位置 ({coords['x']}, {coords['y']}) "
                      f"置信度 {target['confidence']:.3f}")

                # 执行批量右键操作
                if automator.validate_coordinates(coords):
                    right_click = automator.perform_mouse_click(coords, 'right', 'single', 0.2)
                    print(f"        右键操作: {'✅ 成功' if right_click else '❌ 失败'}")

    # 5. 自定义条件选择策略（详细版）
    print("\\n🔧 自定义条件选择策略:")

    # 示例1：选择中等大小的目标
    def medium_size_condition(target):
        """选择中等大小的目标（面积在1000-10000像素之间）"""
        dimensions = automator.calculate_target_area_and_dimensions(target)
        area = dimensions['area']
        return 1000 < area < 10000

    medium_targets = automator.select_target_by_custom_condition(targets, medium_size_condition)
    print(f"  中等大小目标: {len(medium_targets)} 个")

    if medium_targets:
        for i, target in enumerate(medium_targets[:2]):
            coords = automator.get_target_coordinates(target, 'center')
            dimensions = automator.calculate_target_area_and_dimensions(target)
            print(f"    目标 {i+1}: {target['class_name']} 面积: {dimensions['area']} 像素")

    # 示例2：选择高置信度且位置靠左的目标
    def high_conf_left_condition(target):
        """选择高置信度且位置靠左的目标"""
        coords = automator.get_target_coordinates(target, 'center')
        return target['confidence'] > 0.8 and coords['x'] < 500

    high_conf_left_targets = automator.select_target_by_custom_condition(targets, high_conf_left_condition)
    print(f"  高置信度左侧目标: {len(high_conf_left_targets)} 个")

    # 示例3：选择宽高比接近正方形的目标
    def square_like_condition(target):
        """选择宽高比接近正方形的目标（0.8-1.2之间）"""
        dimensions = automator.calculate_target_area_and_dimensions(target)
        aspect_ratio = dimensions['aspect_ratio']
        return 0.8 <= aspect_ratio <= 1.2

    square_targets = automator.select_target_by_custom_condition(targets, square_like_condition)
    print(f"  正方形目标: {len(square_targets)} 个")

    # 示例4：选择位于屏幕中央区域的目标
    def center_region_condition(target):
        """选择位于屏幕中央区域的目标"""
        coords = automator.get_target_coordinates(target, 'center')
        # 假设屏幕分辨率为1920x1080，中央区域为中间1/3
        return 640 <= coords['x'] <= 1280 and 360 <= coords['y'] <= 720

    center_targets = automator.select_target_by_custom_condition(targets, center_region_condition)
    print(f"  中央区域目标: {len(center_targets)} 个")

    # 示例5：复合条件选择
    def complex_condition(target):
        """复合条件：高置信度 + 中等大小 + 特定位置"""
        coords = automator.get_target_coordinates(target, 'center')
        dimensions = automator.calculate_target_area_and_dimensions(target)

        # 条件1：置信度大于0.7
        high_confidence = target['confidence'] > 0.7

        # 条件2：面积在2000-8000像素之间
        good_size = 2000 <= dimensions['area'] <= 8000

        # 条件3：位于屏幕上半部分
        upper_half = coords['y'] < 540

        return high_confidence and good_size and upper_half

    complex_targets = automator.select_target_by_custom_condition(targets, complex_condition)
    print(f"  复合条件目标: {len(complex_targets)} 个")

    # 自定义条件的实际应用演示
    print("\\n🎯 自定义条件实际应用:")

    # 应用1：处理中等大小目标
    if medium_targets:
        target = medium_targets[0]
        coords = automator.get_target_coordinates(target, 'center')
        print(f"  应用1: 中等大小目标操作")
        print(f"    目标: {target['class_name']}")

        if automator.validate_coordinates(coords):
            # 中等目标适合拖拽操作
            if len(medium_targets) > 1:
                target2 = medium_targets[1]
                coords2 = automator.get_target_coordinates(target2, 'center')
                drag_success = automator.perform_mouse_drag(coords, coords2, duration=1.5)
                print(f"    拖拽操作: {'✅ 成功' if drag_success else '❌ 失败'}")

    # 应用2：处理正方形目标
    if square_targets:
        target = square_targets[0]
        coords = automator.get_target_coordinates(target, 'center')
        print(f"  应用2: 正方形目标操作")
        print(f"    目标: {target['class_name']}")

        if automator.validate_coordinates(coords):
            # 正方形目标适合旋转操作（模拟）
            rotation_actions = [
                {'type': 'click', 'params': {'coordinates': coords, 'delay': 0.3}},
                {'type': 'shortcut', 'params': {'keys': ['ctrl', 'r'], 'delay': 0.2}},
            ]
            rotation_success = automator.perform_automation_sequence(rotation_actions)
            print(f"    旋转操作: {'✅ 成功' if rotation_success else '❌ 失败'}")

    # 应用3：处理复合条件目标
    if complex_targets:
        target = complex_targets[0]
        coords = automator.get_target_coordinates(target, 'center')
        print(f"  应用3: 复合条件目标操作")
        print(f"    目标: {target['class_name']}")
        print(f"    置信度: {target['confidence']:.3f}")

        if automator.validate_coordinates(coords):
            # 复合条件目标执行复杂操作序列
            complex_actions = [
                {'type': 'click', 'params': {'coordinates': coords, 'delay': 0.3}},
                {'type': 'shortcut', 'params': {'keys': ['ctrl', 'c'], 'delay': 0.2}},
                {'type': 'wait', 'params': {'seconds': 0.5}},
                {'type': 'shortcut', 'params': {'keys': ['ctrl', 'v'], 'delay': 0.2}},
                {'type': 'shortcut', 'params': {'keys': ['enter'], 'delay': 0.2}},
            ]
            complex_success = automator.perform_automation_sequence(complex_actions)
            print(f"    复杂操作: {'✅ 成功' if complex_success else '❌ 失败'}")

    # 自定义条件函数库
    print("\\n📚 自定义条件函数库:")
    print("  ✅ medium_size_condition - 中等大小筛选")
    print("  ✅ high_conf_left_condition - 高置信度左侧筛选")
    print("  ✅ square_like_condition - 正方形目标筛选")
    print("  ✅ center_region_condition - 中央区域筛选")
    print("  ✅ complex_condition - 复合条件筛选")
    print("\\n💡 可扩展的条件类型:")
    print("  • 基于颜色的筛选")
    print("  • 基于形状的筛选")
    print("  • 基于时间的筛选")
    print("  • 基于用户行为的筛选")

    # 6. 组合策略示例（详细版）
    print("\\n🎯 组合策略示例:")

    # 组合策略1：类别 + 置信度
    if detected_classes:
        main_class = detected_classes[0]
        class_targets = automator.select_target_by_class(targets, main_class)
        if class_targets:
            best_target = automator.select_target_by_confidence(class_targets, 'highest')
            coords = automator.get_target_coordinates(best_target, 'center')

            print(f"  组合策略1: 类别 + 置信度")
            print(f"    类别: '{main_class}' 中置信度最高的目标")
            print(f"    目标信息: 置信度 {best_target['confidence']:.3f}")
            print(f"    位置: ({coords['x']}, {coords['y']})")

            # 执行精确操作
            if automator.validate_coordinates(coords):
                success = automator.perform_mouse_click(coords, 'left', 'double', 0.5)
                print(f"    双击操作: {'✅ 成功' if success else '❌ 失败'}")

    # 组合策略2：位置 + 大小
    if 'leftmost' in position_targets and largest_target:
        print(f"  组合策略2: 位置 + 大小")

        # 如果最左侧目标也是最大目标
        leftmost = position_targets['leftmost']
        if leftmost == largest_target:
            coords = automator.get_target_coordinates(leftmost, 'center')
            print(f"    最左侧目标同时也是最大目标")
            print(f"    目标: {leftmost['class_name']}")
            print(f"    执行特殊操作序列...")

            special_actions = [
                {'type': 'click', 'params': {'coordinates': coords, 'delay': 0.5}},
                {'type': 'shortcut', 'params': {'keys': ['ctrl', 'shift', 'n'], 'delay': 0.3}},
            ]
            special_success = automator.perform_automation_sequence(special_actions)
            print(f"    特殊操作: {'✅ 成功' if special_success else '❌ 失败'}")

    # 组合策略3：自定义条件 + 位置筛选
    if medium_targets:
        print(f"  组合策略3: 自定义条件 + 位置筛选")

        # 在中等大小目标中选择最左侧的
        leftmost_medium = None
        min_x = float('inf')

        for target in medium_targets:
            coords = automator.get_target_coordinates(target, 'center')
            if coords['x'] < min_x:
                min_x = coords['x']
                leftmost_medium = target

        if leftmost_medium:
            coords = automator.get_target_coordinates(leftmost_medium, 'center')
            print(f"    中等大小目标中最左侧的: {leftmost_medium['class_name']}")
            print(f"    位置: ({coords['x']}, {coords['y']})")

            if automator.validate_coordinates(coords):
                combo_success = automator.perform_mouse_click(coords, 'right', 'single', 0.3)
                print(f"    右键操作: {'✅ 成功' if combo_success else '❌ 失败'}")

    # 7. 策略选择建议
    print("\\n📋 策略选择建议:")
    print("✅ 按置信度选择:")
    print("   • 适用于需要高准确性的操作")
    print("   • 推荐阈值: >0.7 为高质量目标")
    print("\\n✅ 按位置选择:")
    print("   • 适用于界面布局相对固定的应用")
    print("   • 左侧通常是菜单，右侧通常是操作按钮")
    print("\\n✅ 按大小选择:")
    print("   • 大目标适合文本输入和拖拽")
    print("   • 小目标适合精确点击")
    print("\\n✅ 按类别选择:")
    print("   • 适用于特定类型的界面元素操作")
    print("   • 可以实现批量处理")
    print("\\n✅ 自定义条件:")
    print("   • 适用于复杂的业务逻辑")
    print("   • 可以组合多个条件实现精确筛选")
    print("\\n✅ 组合策略:")
    print("   • 适用于复杂场景的精确定位")
    print("   • 可以提高操作的可靠性")

    print("\\n✅ 目标选择策略示例完成")
    print("💡 提示: 根据不同的应用场景选择合适的目标选择策略")
    print("🔧 建议: 在实际应用中可以组合多种策略以提高准确性")

    return True

# 使用方法
if __name__ == "__main__":
    target_selection_strategies_example()
'''

        code_text.setPlainText(selection_code)
        code_text.setReadOnly(True)
        code_layout.addWidget(code_text)

        # 运行按钮
        run_btn = QPushButton("▶️ 运行此示例")
        run_btn.setMinimumHeight(35)
        run_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        run_btn.clicked.connect(lambda: self._run_example("target_selection"))
        code_layout.addWidget(run_btn)

        splitter.addWidget(code_group)
        splitter.setSizes([120, 500])

        self.tab_widget.addTab(tab, "🔍 目标选择策略")

    def _create_workflow_tab(self):
        """创建复杂工作流程示例标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Vertical)
        layout.addWidget(splitter)

        # 说明区域
        desc_group = QGroupBox("⚡ 复杂工作流程说明")
        desc_layout = QVBoxLayout(desc_group)

        desc_text = QTextEdit()
        desc_text.setMaximumHeight(100)
        desc_text.setPlainText(
            "复杂工作流程示例演示如何组合多种操作创建完整的自动化工作流程，"
            "适用于需要多步骤协调的复杂任务。\n\n"
            "包含功能：\n"
            "• 多步骤操作序列\n"
            "• 条件判断和分支\n"
            "• 循环和重试机制\n"
            "• 状态检查和验证"
        )
        desc_text.setReadOnly(True)
        desc_layout.addWidget(desc_text)
        splitter.addWidget(desc_group)

        # 代码示例区域
        code_group = QGroupBox("💻 复杂工作流程代码")
        code_layout = QVBoxLayout(code_group)

        code_text = QTextEdit()
        code_text.setFont(QFont("Consolas", 10))
        highlighter = PythonHighlighter(code_text.document())

        workflow_code = '''def complex_workflow_example():
    """复杂工作流程自动化示例 - 演示多步骤协调、条件判断、循环重试"""

    # 初始化自动化器
    from yolo_opencv_detector.gui.dialogs.source_code_dialog import SourceCodeDialog
    import time

    class OfficeAutomator(SourceCodeDialog):
        def __init__(self):
            super().__init__()

    automator = OfficeAutomator()

    print("🚀 开始复杂工作流程自动化...")

    # 工作流程配置
    workflow_config = {
        'max_retries': 3,
        'retry_delay': 1.0,
        'confidence_threshold': 0.7,
        'timeout_seconds': 30,
        'required_targets_min': 2
    }

    print(f"📋 工作流程配置:")
    for key, value in workflow_config.items():
        print(f"  {key}: {value}")

    # 工作流程步骤定义（详细版）
    workflow_steps = [
        {
            'id': 1,
            'name': '检测界面元素',
            'description': '执行屏幕检测并解析结果',
            'critical': True,
            'retry_enabled': True
        },
        {
            'id': 2,
            'name': '验证目标可用性',
            'description': '检查关键目标是否存在且可用',
            'critical': True,
            'retry_enabled': False
        },
        {
            'id': 3,
            'name': '执行主要操作序列',
            'description': '执行核心自动化操作',
            'critical': True,
            'retry_enabled': True
        },
        {
            'id': 4,
            'name': '验证操作结果',
            'description': '检查操作是否成功完成',
            'critical': False,
            'retry_enabled': True
        },
        {
            'id': 5,
            'name': '执行清理操作',
            'description': '清理临时状态和资源',
            'critical': False,
            'retry_enabled': False
        }
    ]

    # 工作流程状态跟踪
    workflow_state = {
        'current_step': 0,
        'completed_steps': [],
        'failed_steps': [],
        'targets': None,
        'required_targets': {},
        'operation_results': {},
        'start_time': time.time()
    }

    # 执行工作流程
    for step in workflow_steps:
        step_id = step['id']
        step_name = step['name']
        is_critical = step['critical']
        retry_enabled = step['retry_enabled']

        print(f"\\n📋 步骤 {step_id}: {step_name}")
        print(f"   描述: {step['description']}")
        print(f"   关键步骤: {'是' if is_critical else '否'}")
        print(f"   支持重试: {'是' if retry_enabled else '否'}")

        workflow_state['current_step'] = step_id
        step_success = False
        retry_count = 0
        max_retries = workflow_config['max_retries'] if retry_enabled else 1

        # 步骤执行循环（支持重试）
        while retry_count < max_retries and not step_success:
            if retry_count > 0:
                print(f"   🔄 重试 {retry_count}/{max_retries - 1}")
                time.sleep(workflow_config['retry_delay'] * retry_count)  # 指数退避

            try:
                # 步骤1：检测界面元素
                if step_id == 1:
                    print("   🔍 执行屏幕检测...")
                    results = automator.gui_detect_screen()

                    if not results:
                        raise Exception("检测结果为空")

                    targets = automator.parse_detection_results(results)
                    if not targets:
                        raise Exception("未解析到任何目标")

                    workflow_state['targets'] = targets
                    print(f"   ✅ 检测到 {len(targets)} 个界面元素")

                    # 显示检测质量统计
                    high_conf_count = len([t for t in targets if t['confidence'] > workflow_config['confidence_threshold']])
                    print(f"   📊 高质量目标: {high_conf_count}/{len(targets)}")

                    step_success = True

                # 步骤2：验证目标可用性
                elif step_id == 2:
                    targets = workflow_state['targets']
                    required_targets = {}

                    print("   🎯 验证关键目标...")

                    # 查找主要操作按钮（高置信度）
                    main_button = automator.select_target_by_confidence(targets, 'highest')
                    if main_button and main_button['confidence'] > workflow_config['confidence_threshold']:
                        required_targets['main_button'] = main_button
                        print(f"   ✅ 主要按钮: {main_button['class_name']} (置信度: {main_button['confidence']:.3f})")
                    else:
                        print("   ⚠️ 未找到高质量主要按钮")

                    # 查找输入区域（最大目标）
                    input_area = automator.select_target_by_size(targets, 'largest')
                    if input_area:
                        required_targets['input_area'] = input_area
                        dimensions = automator.calculate_target_area_and_dimensions(input_area)
                        print(f"   ✅ 输入区域: {input_area['class_name']} (面积: {dimensions['area']} 像素)")
                    else:
                        print("   ⚠️ 未找到输入区域")

                    # 查找辅助目标
                    leftmost = automator.select_target_by_position(targets, 'leftmost')
                    if leftmost:
                        required_targets['leftmost'] = leftmost
                        print(f"   ✅ 左侧目标: {leftmost['class_name']}")

                    rightmost = automator.select_target_by_position(targets, 'rightmost')
                    if rightmost:
                        required_targets['rightmost'] = rightmost
                        print(f"   ✅ 右侧目标: {rightmost['class_name']}")

                    # 验证目标数量
                    if len(required_targets) >= workflow_config['required_targets_min']:
                        workflow_state['required_targets'] = required_targets
                        print(f"   ✅ 找到 {len(required_targets)} 个关键目标，满足最低要求")
                        step_success = True
                    else:
                        if is_critical:
                            raise Exception(f"关键目标不足: {len(required_targets)}/{workflow_config['required_targets_min']}")
                        else:
                            print(f"   ⚠️ 目标不足但继续执行: {len(required_targets)}/{workflow_config['required_targets_min']}")
                            workflow_state['required_targets'] = required_targets
                            step_success = True

                # 步骤3：执行主要操作序列
                elif step_id == 3:
                    required_targets = workflow_state['required_targets']
                    print("   🎯 执行主要操作序列...")

                    # 创建复杂的操作序列
                    main_sequence = []
                    operation_count = 0

                    # 操作1：输入区域操作
                    if 'input_area' in required_targets:
                        input_area = required_targets['input_area']
                        input_coords = automator.get_target_coordinates(input_area, 'center')

                        print("   📝 执行输入区域操作...")
                        if automator.validate_coordinates(input_coords):
                            input_sequence = [
                                {'type': 'click', 'params': {'coordinates': input_coords, 'delay': 0.5}},
                                {'type': 'shortcut', 'params': {'keys': ['ctrl', 'a'], 'delay': 0.2}},
                                {'type': 'type', 'params': {'text': f'工作流程测试数据 - {time.strftime("%H:%M:%S")}', 'typing_speed': 0.05}},
                                {'type': 'wait', 'params': {'seconds': 1.0}},
                            ]
                            main_sequence.extend(input_sequence)
                            operation_count += 1
                            print("   ✅ 输入操作序列已添加")

                    # 操作2：主要按钮操作
                    if 'main_button' in required_targets:
                        main_button = required_targets['main_button']
                        button_coords = automator.get_target_coordinates(main_button, 'center')

                        print("   🖱️ 执行主要按钮操作...")
                        if automator.validate_coordinates(button_coords):
                            button_sequence = [
                                {'type': 'click', 'params': {'coordinates': button_coords, 'delay': 0.5}},
                                {'type': 'wait', 'params': {'seconds': 2.0}},
                            ]
                            main_sequence.extend(button_sequence)
                            operation_count += 1
                            print("   ✅ 按钮操作序列已添加")

                    # 操作3：多目标协调操作
                    if 'leftmost' in required_targets and 'rightmost' in required_targets:
                        leftmost = required_targets['leftmost']
                        rightmost = required_targets['rightmost']

                        left_coords = automator.get_target_coordinates(leftmost, 'center')
                        right_coords = automator.get_target_coordinates(rightmost, 'center')

                        print("   🔄 执行多目标协调操作...")
                        if automator.validate_coordinates(left_coords) and automator.validate_coordinates(right_coords):
                            # 先点击左侧，再拖拽到右侧
                            coordination_sequence = [
                                {'type': 'click', 'params': {'coordinates': left_coords, 'delay': 0.3}},
                                {'type': 'wait', 'params': {'seconds': 0.5}},
                                {'type': 'drag', 'params': {'start_coords': left_coords, 'end_coords': right_coords, 'duration': 1.5}},
                                {'type': 'wait', 'params': {'seconds': 1.0}},
                            ]
                            main_sequence.extend(coordination_sequence)
                            operation_count += 1
                            print("   ✅ 协调操作序列已添加")

                    # 操作4：验证和确认操作
                    verification_sequence = [
                        {'type': 'shortcut', 'params': {'keys': ['alt', 'tab'], 'delay': 0.3}},
                        {'type': 'wait', 'params': {'seconds': 0.5}},
                        {'type': 'shortcut', 'params': {'keys': ['alt', 'tab'], 'delay': 0.3}},
                        {'type': 'shortcut', 'params': {'keys': ['ctrl', 's'], 'delay': 0.3}},
                    ]
                    main_sequence.extend(verification_sequence)
                    operation_count += 1
                    print("   ✅ 验证操作序列已添加")

                    # 执行完整序列
                    if main_sequence:
                        print(f"   🚀 执行包含 {operation_count} 个操作组的序列...")
                        sequence_success = automator.perform_automation_sequence(main_sequence)

                        if sequence_success:
                            workflow_state['operation_results']['main_sequence'] = True
                            print("   ✅ 主要操作序列执行成功")
                            step_success = True
                        else:
                            raise Exception("主要操作序列执行失败")
                    else:
                        raise Exception("无可执行的操作序列")

                # 步骤4：验证操作结果
                elif step_id == 4:
                    print("   🔍 验证操作结果...")

                    # 等待界面稳定
                    time.sleep(1.0)

                    # 重新检测界面状态
                    verification_results = automator.gui_detect_screen()
                    if not verification_results:
                        print("   ⚠️ 验证检测失败，但继续执行")
                        step_success = True  # 非关键步骤
                        continue

                    verification_targets = automator.parse_detection_results(verification_results)
                    original_targets = workflow_state['targets']

                    # 比较操作前后的变化
                    original_count = len(original_targets)
                    current_count = len(verification_targets)

                    print(f"   📊 界面元素变化: {original_count} -> {current_count}")

                    # 变化分析
                    change_detected = False

                    if current_count != original_count:
                        print("   ✅ 检测到元素数量变化")
                        change_detected = True

                    # 检查是否出现新的元素类型
                    original_classes = set(t.get('class_name', '') for t in original_targets)
                    current_classes = set(t.get('class_name', '') for t in verification_targets)

                    new_classes = current_classes - original_classes
                    removed_classes = original_classes - current_classes

                    if new_classes:
                        print(f"   🆕 新增元素类型: {list(new_classes)}")
                        change_detected = True

                    if removed_classes:
                        print(f"   🗑️ 移除元素类型: {list(removed_classes)}")
                        change_detected = True

                    # 置信度变化分析
                    if verification_targets:
                        current_avg_conf = sum(t['confidence'] for t in verification_targets) / len(verification_targets)
                        original_avg_conf = sum(t['confidence'] for t in original_targets) / len(original_targets)
                        conf_change = current_avg_conf - original_avg_conf

                        print(f"   📈 平均置信度变化: {original_avg_conf:.3f} -> {current_avg_conf:.3f} ({conf_change:+.3f})")

                    # 验证结果评估
                    if change_detected:
                        workflow_state['operation_results']['verification'] = True
                        print("   ✅ 操作验证成功：检测到界面变化")
                        step_success = True
                    else:
                        print("   ⚠️ 未检测到明显变化，但操作可能仍然成功")
                        workflow_state['operation_results']['verification'] = False
                        step_success = True  # 非关键步骤

                # 步骤5：执行清理操作
                elif step_id == 5:
                    print("   🧹 执行清理操作...")

                    # 清理操作序列
                    cleanup_sequence = [
                        {'type': 'shortcut', 'params': {'keys': ['escape'], 'delay': 0.3}},
                        {'type': 'wait', 'params': {'seconds': 0.5}},
                        {'type': 'shortcut', 'params': {'keys': ['ctrl', 's'], 'delay': 0.3}},
                        {'type': 'wait', 'params': {'seconds': 0.5}},
                        {'type': 'shortcut', 'params': {'keys': ['alt', 'f4'], 'delay': 0.3}},  # 关闭窗口
                    ]

                    cleanup_success = automator.perform_automation_sequence(cleanup_sequence)

                    if cleanup_success:
                        workflow_state['operation_results']['cleanup'] = True
                        print("   ✅ 清理操作完成")
                        step_success = True
                    else:
                        print("   ⚠️ 清理操作失败，但不影响整体流程")
                        workflow_state['operation_results']['cleanup'] = False
                        step_success = True  # 非关键步骤

            except Exception as e:
                print(f"   ❌ 步骤执行异常: {e}")

                # 错误处理和恢复
                if is_critical:
                    print("   🚑 关键步骤失败，尝试恢复...")

                    # 执行恢复操作
                    try:
                        recovery_actions = [
                            {'type': 'shortcut', 'params': {'keys': ['escape'], 'delay': 0.2}},
                            {'type': 'wait', 'params': {'seconds': 0.5}},
                        ]
                        automator.perform_automation_sequence(recovery_actions)
                        print("   ✅ 恢复操作完成")
                    except:
                        print("   ❌ 恢复操作失败")

                workflow_state['failed_steps'].append(step_id)

                if not retry_enabled or retry_count >= max_retries - 1:
                    if is_critical:
                        print(f"   💥 关键步骤 {step_id} 最终失败，工作流程终止")
                        return False
                    else:
                        print(f"   ⚠️ 非关键步骤 {step_id} 失败，继续执行")
                        step_success = True

            retry_count += 1

        # 步骤完成处理
        if step_success:
            workflow_state['completed_steps'].append(step_id)
            print(f"   ✅ 步骤 {step_id} 完成")
        else:
            print(f"   ❌ 步骤 {step_id} 失败")

    # 工作流程执行总结
    end_time = time.time()
    total_duration = end_time - workflow_state['start_time']

    print("\\n📋 工作流程执行总结:")
    print(f"⏱️ 总执行时间: {total_duration:.2f} 秒")
    print(f"✅ 完成步骤: {len(workflow_state['completed_steps'])}/{len(workflow_steps)}")
    print(f"❌ 失败步骤: {len(workflow_state['failed_steps'])}")

    if workflow_state['completed_steps']:
        print(f"   完成的步骤: {workflow_state['completed_steps']}")

    if workflow_state['failed_steps']:
        print(f"   失败的步骤: {workflow_state['failed_steps']}")

    # 操作结果统计
    print("\\n📊 操作结果统计:")
    for operation, result in workflow_state['operation_results'].items():
        status = "✅ 成功" if result else "❌ 失败"
        print(f"   {operation}: {status}")

    # 性能指标
    print("\\n📈 性能指标:")
    if workflow_state['targets']:
        target_count = len(workflow_state['targets'])
        avg_processing_time = total_duration / target_count if target_count > 0 else 0
        print(f"   处理目标数量: {target_count}")
        print(f"   平均处理时间: {avg_processing_time:.3f} 秒/目标")

    success_rate = len(workflow_state['completed_steps']) / len(workflow_steps) * 100
    print(f"   成功率: {success_rate:.1f}%")

    # 工作流程特点总结
    print("\\n💡 工作流程特点:")
    print("   ✅ 多步骤协调执行")
    print("   ✅ 智能错误处理和恢复")
    print("   ✅ 状态验证机制")
    print("   ✅ 循环重试支持")
    print("   ✅ 条件判断和分支处理")
    print("   ✅ 性能监控和统计")
    print("   ✅ 自动清理功能")
    print("   ✅ 详细日志记录")

    # 最佳实践建议
    print("\\n🎯 最佳实践建议:")
    print("   • 合理设置重试次数和延迟")
    print("   • 区分关键步骤和非关键步骤")
    print("   • 实现完善的错误恢复机制")
    print("   • 监控工作流程执行性能")
    print("   • 记录详细的执行日志")
    print("   • 定期验证操作结果")

    # 适用场景
    print("\\n🏢 适用场景:")
    print("   • 复杂的办公软件自动化")
    print("   • 多步骤的数据处理流程")
    print("   • 需要验证的批量操作")
    print("   • 容错性要求高的自动化任务")
    print("   • 需要性能监控的长时间运行任务")

    # 返回执行结果
    workflow_success = len(workflow_state['failed_steps']) == 0
    print(f"\\n🎉 工作流程{'完全成功' if workflow_success else '部分成功'}!")

    return workflow_success

# 使用方法
if __name__ == "__main__":
    # 运行复杂工作流程自动化示例
    success = complex_workflow_example()
    print(f"\\n最终结果: {'✅ 成功' if success else '❌ 失败'}")
'''

        code_text.setPlainText(workflow_code)
        code_text.setReadOnly(True)
        code_layout.addWidget(code_text)

        # 运行按钮
        run_btn = QPushButton("▶️ 运行此示例")
        run_btn.setMinimumHeight(35)
        run_btn.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
        """)
        run_btn.clicked.connect(lambda: self._run_example("complex_workflow"))
        code_layout.addWidget(run_btn)

        splitter.addWidget(code_group)
        splitter.setSizes([120, 500])

        self.tab_widget.addTab(tab, "⚡ 复杂工作流程")

    def _create_error_handling_tab(self):
        """创建错误处理和安全机制示例标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Vertical)
        layout.addWidget(splitter)

        # 说明区域
        desc_group = QGroupBox("🛡️ 错误处理和安全机制说明")
        desc_layout = QVBoxLayout(desc_group)

        desc_text = QTextEdit()
        desc_text.setMaximumHeight(100)
        desc_text.setPlainText(
            "错误处理和安全机制示例演示如何在自动化操作中实现安全保护、"
            "错误恢复和异常处理。\n\n"
            "安全特性：\n"
            "• 坐标边界检查\n"
            "• 操作权限验证\n"
            "• 异常捕获和恢复\n"
            "• 操作日志记录\n"
            "• 超时和重试机制"
        )
        desc_text.setReadOnly(True)
        desc_layout.addWidget(desc_text)
        splitter.addWidget(desc_group)

        # 代码示例区域
        code_group = QGroupBox("💻 错误处理和安全机制代码")
        code_layout = QVBoxLayout(code_group)

        code_text = QTextEdit()
        code_text.setFont(QFont("Consolas", 10))
        highlighter = PythonHighlighter(code_text.document())

        error_handling_code = '''def error_handling_and_safety_example():
    """错误处理和安全机制完整示例 - 演示坐标验证、异常处理、重试机制、操作日志"""

    import time
    import traceback
    import random

    # 初始化自动化器
    from yolo_opencv_detector.gui.dialogs.source_code_dialog import SourceCodeDialog

    class OfficeAutomator(SourceCodeDialog):
        def __init__(self):
            super().__init__()

    automator = OfficeAutomator()

    print("🛡️ 错误处理和安全机制演示")
    print("=" * 60)

    # 安全配置
    safety_config = {
        'max_retries': 3,
        'retry_delay_base': 1.0,
        'confidence_threshold': 0.7,
        'coordinate_bounds': {
            'min_x': 0, 'max_x': 1920,
            'min_y': 0, 'max_y': 1080
        },
        'operation_timeout': 10.0,
        'enable_logging': True
    }

    print("📋 安全配置:")
    for key, value in safety_config.items():
        print(f"  {key}: {value}")

    # 1. 坐标安全验证（详细版）
    print("\\n📍 坐标安全验证:")

    # 测试用例集合
    test_coordinates = [
        ({'x': 500, 'y': 300}, "正常坐标"),
        ({'x': -100, 'y': 300}, "负X坐标"),
        ({'x': 500, 'y': -50}, "负Y坐标"),
        ({'x': 2000, 'y': 300}, "超出X边界"),
        ({'x': 500, 'y': 1200}, "超出Y边界"),
        ({'x': 0, 'y': 0}, "边界坐标(0,0)"),
        ({'x': 1920, 'y': 1080}, "边界坐标(max)"),
        ({}, "空坐标字典"),
        ({'x': 'invalid'}, "无效数据类型"),
        ({'x': 500}, "缺少Y坐标"),
    ]

    validation_results = []
    for coords, description in test_coordinates:
        try:
            is_valid = automator.validate_coordinates(coords)
            result = "✅ 通过" if is_valid else "❌ 被拒绝"
            validation_results.append((description, is_valid))
            print(f"  {description}: {coords} -> {result}")

            # 记录验证日志
            if safety_config['enable_logging']:
                automator.log_automation_action(
                    'coordinate_validation',
                    {'coordinates': coords, 'description': description},
                    is_valid
                )

        except Exception as e:
            print(f"  {description}: {coords} -> ❌ 验证异常: {e}")
            validation_results.append((description, False))

    # 验证统计
    passed_count = sum(1 for _, result in validation_results if result)
    total_count = len(validation_results)
    print(f"\\n📊 坐标验证统计: {passed_count}/{total_count} 通过 ({passed_count/total_count*100:.1f}%)")

    # 2. 边界条件测试
    print("\\n🔍 边界条件测试:")

    boundary_tests = [
        (safety_config['coordinate_bounds']['min_x'], safety_config['coordinate_bounds']['min_y'], "最小边界"),
        (safety_config['coordinate_bounds']['max_x'], safety_config['coordinate_bounds']['max_y'], "最大边界"),
        (safety_config['coordinate_bounds']['min_x'] - 1, safety_config['coordinate_bounds']['min_y'], "超出最小X"),
        (safety_config['coordinate_bounds']['max_x'] + 1, safety_config['coordinate_bounds']['max_y'], "超出最大X"),
        (safety_config['coordinate_bounds']['min_x'], safety_config['coordinate_bounds']['min_y'] - 1, "超出最小Y"),
        (safety_config['coordinate_bounds']['max_x'], safety_config['coordinate_bounds']['max_y'] + 1, "超出最大Y"),
    ]

    for x, y, test_name in boundary_tests:
        coords = {'x': x, 'y': y}
        try:
            is_valid = automator.validate_coordinates(coords)
            result = "✅ 通过" if is_valid else "❌ 被拒绝"
            print(f"  {test_name}: ({x}, {y}) -> {result}")
        except Exception as e:
            print(f"  {test_name}: ({x}, {y}) -> ❌ 异常: {e}")

    # 3. 检测结果验证和重试机制
    print("\\n🔍 检测结果验证和重试机制:")

    detection_attempts = 0
    max_detection_retries = safety_config['max_retries']
    targets = None

    while detection_attempts < max_detection_retries and targets is None:
        detection_attempts += 1
        print(f"\\n  🔄 检测尝试 {detection_attempts}/{max_detection_retries}:")

        try:
            # 记录检测开始
            detection_start_time = time.time()

            results = automator.gui_detect_screen()

            detection_duration = time.time() - detection_start_time
            print(f"    检测耗时: {detection_duration:.2f} 秒")

            if not results:
                print("    ⚠️ 检测结果为空")

                if detection_attempts < max_detection_retries:
                    retry_delay = safety_config['retry_delay_base'] * detection_attempts
                    print(f"    ⏳ 等待 {retry_delay} 秒后重试...")
                    time.sleep(retry_delay)
                    continue
                else:
                    print("    💥 所有检测尝试均失败")

                    # 启用备用策略
                    print("    🔄 启用备用策略...")
                    print("    💡 建议操作:")
                    print("      • 检查屏幕内容是否可见")
                    print("      • 降低检测置信度阈值")
                    print("      • 手动指定操作坐标")
                    print("      • 检查YOLO模型是否正确加载")

                    # 记录失败日志
                    if safety_config['enable_logging']:
                        automator.log_automation_action(
                            'detection_failure',
                            {'attempts': detection_attempts, 'duration': detection_duration},
                            False
                        )
                    return False

            # 解析检测结果
            targets = automator.parse_detection_results(results)

            if not targets:
                print("    ⚠️ 解析结果为空")
                targets = None
                continue

            print(f"    ✅ 成功解析 {len(targets)} 个目标")

            # 详细的目标质量分析
            quality_analysis = {
                'total': len(targets),
                'high_confidence': 0,
                'medium_confidence': 0,
                'low_confidence': 0,
                'valid_coordinates': 0,
                'invalid_coordinates': 0
            }

            for target in targets:
                confidence = target.get('confidence', 0)

                # 置信度分类
                if confidence > 0.8:
                    quality_analysis['high_confidence'] += 1
                elif confidence > 0.5:
                    quality_analysis['medium_confidence'] += 1
                else:
                    quality_analysis['low_confidence'] += 1

                # 坐标有效性检查
                try:
                    coords = automator.get_target_coordinates(target, 'center')
                    if coords and automator.validate_coordinates(coords):
                        quality_analysis['valid_coordinates'] += 1
                    else:
                        quality_analysis['invalid_coordinates'] += 1
                except:
                    quality_analysis['invalid_coordinates'] += 1

            # 显示质量分析结果
            print(f"    📊 目标质量分析:")
            print(f"      总目标数: {quality_analysis['total']}")
            print(f"      高置信度 (>0.8): {quality_analysis['high_confidence']}")
            print(f"      中置信度 (0.5-0.8): {quality_analysis['medium_confidence']}")
            print(f"      低置信度 (<0.5): {quality_analysis['low_confidence']}")
            print(f"      有效坐标: {quality_analysis['valid_coordinates']}")
            print(f"      无效坐标: {quality_analysis['invalid_coordinates']}")

            # 质量评估
            high_quality_targets = [
                t for t in targets
                if t.get('confidence', 0) > safety_config['confidence_threshold']
            ]

            quality_ratio = len(high_quality_targets) / len(targets) if targets else 0
            print(f"    🎯 高质量目标: {len(high_quality_targets)}/{len(targets)} ({quality_ratio*100:.1f}%)")

            if len(high_quality_targets) == 0:
                print("    ⚠️ 无高质量目标")

                if detection_attempts < max_detection_retries:
                    print("    🔄 尝试重新检测...")
                    targets = None
                    continue
                else:
                    print("    💡 建议降低置信度阈值或检查检测环境")

            # 记录成功的检测日志
            if safety_config['enable_logging']:
                automator.log_automation_action(
                    'detection_success',
                    {
                        'attempts': detection_attempts,
                        'duration': detection_duration,
                        'target_count': len(targets),
                        'high_quality_count': len(high_quality_targets),
                        'quality_ratio': quality_ratio
                    },
                    True
                )

        except Exception as e:
            print(f"    ❌ 检测过程异常: {e}")
            print(f"    📋 异常详情:")
            traceback.print_exc()

            # 记录异常日志
            if safety_config['enable_logging']:
                automator.log_automation_action(
                    'detection_exception',
                    {'attempts': detection_attempts, 'error': str(e)},
                    False
                )

            if detection_attempts >= max_detection_retries:
                print("    💥 检测重试次数已用完")
                return False

            # 异常恢复等待
            recovery_delay = safety_config['retry_delay_base'] * detection_attempts
            print(f"    🚑 异常恢复等待 {recovery_delay} 秒...")
            time.sleep(recovery_delay)

    if not targets:
        print("\\n💥 检测完全失败，无法继续执行")
        return False

    print(f"\\n✅ 检测成功完成，共 {detection_attempts} 次尝试")

    # 4. 操作安全执行和超时保护
    print("\\n🤖 操作安全执行和超时保护:")

    if targets:
        # 选择最佳目标进行演示
        best_target = None
        best_score = 0

        for target in targets:
            try:
                coords = automator.get_target_coordinates(target, 'center')
                if coords and automator.validate_coordinates(coords):
                    # 计算目标评分（置信度 + 坐标有效性）
                    score = target.get('confidence', 0) * 100
                    if score > best_score:
                        best_score = score
                        best_target = target
            except:
                continue

        if not best_target:
            print("  ❌ 未找到可用的安全目标")
            return False

        print(f"  🎯 选择最佳目标: {best_target['class_name']} (评分: {best_score:.1f})")

        # 多种操作的安全执行演示
        operations = [
            {
                'name': '单击操作',
                'type': 'click',
                'params': {'button': 'left', 'click_type': 'single', 'delay': 0.3}
            },
            {
                'name': '双击操作',
                'type': 'click',
                'params': {'button': 'left', 'click_type': 'double', 'delay': 0.5}
            },
            {
                'name': '右键操作',
                'type': 'click',
                'params': {'button': 'right', 'click_type': 'single', 'delay': 0.3}
            }
        ]

        for operation in operations:
            print(f"\\n  🔧 执行{operation['name']}:")

            operation_attempts = 0
            max_operation_retries = safety_config['max_retries']
            operation_success = False

            while operation_attempts < max_operation_retries and not operation_success:
                operation_attempts += 1
                print(f"    尝试 {operation_attempts}/{max_operation_retries}")

                try:
                    # 获取安全坐标
                    coords = automator.get_target_coordinates(best_target, 'center')

                    # 执行前验证
                    if not automator.validate_coordinates(coords):
                        print("    ❌ 坐标验证失败，操作被阻止")
                        break

                    print(f"    📍 目标坐标: ({coords['x']}, {coords['y']})")

                    # 记录操作开始日志
                    operation_start_time = time.time()

                    if safety_config['enable_logging']:
                        automator.log_automation_action(
                            f"{operation['type']}_start",
                            {
                                'operation': operation['name'],
                                'coordinates': coords,
                                'target': best_target['class_name'],
                                'attempt': operation_attempts
                            },
                            True
                        )

                    # 安全执行操作（带超时保护）
                    print(f"    🖱️ 执行{operation['name']}...")

                    # 超时保护
                    timeout = safety_config['operation_timeout']

                    try:
                        if operation['type'] == 'click':
                            success = automator.perform_mouse_click(
                                coords,
                                operation['params']['button'],
                                operation['params']['click_type'],
                                operation['params']['delay']
                            )
                        else:
                            success = False

                        operation_duration = time.time() - operation_start_time

                        if operation_duration > timeout:
                            print(f"    ⏰ 操作超时 ({operation_duration:.1f}s > {timeout}s)")

                            # 记录超时日志
                            if safety_config['enable_logging']:
                                automator.log_automation_action(
                                    f"{operation['type']}_timeout",
                                    {
                                        'operation': operation['name'],
                                        'duration': operation_duration,
                                        'timeout': timeout
                                    },
                                    False
                                )
                            continue

                        if success:
                            print(f"    ✅ {operation['name']}完成 (耗时: {operation_duration:.2f}s)")
                            operation_success = True

                            # 记录成功日志
                            if safety_config['enable_logging']:
                                automator.log_automation_action(
                                    f"{operation['type']}_success",
                                    {
                                        'operation': operation['name'],
                                        'duration': operation_duration,
                                        'coordinates': coords
                                    },
                                    True
                                )
                        else:
                            print(f"    ❌ {operation['name']}失败")

                            # 记录失败日志
                            if safety_config['enable_logging']:
                                automator.log_automation_action(
                                    f"{operation['type']}_failure",
                                    {
                                        'operation': operation['name'],
                                        'duration': operation_duration,
                                        'attempt': operation_attempts
                                    },
                                    False
                                )

                    except Exception as operation_error:
                        operation_duration = time.time() - operation_start_time
                        print(f"    ❌ {operation['name']}异常: {operation_error}")

                        # 记录异常日志
                        if safety_config['enable_logging']:
                            automator.log_automation_action(
                                f"{operation['type']}_exception",
                                {
                                    'operation': operation['name'],
                                    'error': str(operation_error),
                                    'duration': operation_duration,
                                    'attempt': operation_attempts
                                },
                                False
                            )

                        # 异常恢复
                        if operation_attempts < max_operation_retries:
                            recovery_delay = safety_config['retry_delay_base'] * operation_attempts
                            print(f"    🚑 异常恢复等待 {recovery_delay} 秒...")
                            time.sleep(recovery_delay)

                except Exception as coord_error:
                    print(f"    ❌ 坐标获取异常: {coord_error}")
                    break

            if not operation_success:
                print(f"    💥 {operation['name']}最终失败")

    # 5. 高级重试机制演示
    print("\\n🔄 高级重试机制演示:")

    # 模拟不同类型的失败场景
    failure_scenarios = [
        {'name': '网络超时', 'failure_rate': 0.7, 'recovery_time': 2.0},
        {'name': '资源占用', 'failure_rate': 0.5, 'recovery_time': 1.0},
        {'name': '权限不足', 'failure_rate': 0.9, 'recovery_time': 0.5},
        {'name': '系统繁忙', 'failure_rate': 0.3, 'recovery_time': 1.5},
    ]

    for scenario in failure_scenarios:
        print(f"\\n  📋 场景: {scenario['name']}")

        max_retries = safety_config['max_retries']
        retry_delay = safety_config['retry_delay_base']
        scenario_success = False

        for attempt in range(max_retries):
            print(f"    尝试 {attempt + 1}/{max_retries}:")

            try:
                # 模拟失败概率
                if random.random() < scenario['failure_rate']:
                    raise Exception(f"{scenario['name']}失败 (尝试 {attempt + 1})")

                # 模拟成功
                print(f"    ✅ {scenario['name']}操作成功")
                scenario_success = True

                # 记录成功日志
                if safety_config['enable_logging']:
                    automator.log_automation_action(
                        'retry_success',
                        {
                            'scenario': scenario['name'],
                            'attempts': attempt + 1,
                            'total_retries': max_retries
                        },
                        True
                    )
                break

            except Exception as e:
                print(f"    ❌ 操作失败: {e}")

                # 记录失败日志
                if safety_config['enable_logging']:
                    automator.log_automation_action(
                        'retry_failure',
                        {
                            'scenario': scenario['name'],
                            'attempt': attempt + 1,
                            'error': str(e)
                        },
                        False
                    )

                if attempt < max_retries - 1:
                    # 指数退避 + 场景特定恢复时间
                    current_delay = retry_delay * (2 ** attempt) + scenario['recovery_time']
                    print(f"    ⏳ 等待 {current_delay:.1f} 秒后重试...")
                    time.sleep(current_delay)
                else:
                    print(f"    💥 {scenario['name']}所有重试均失败")

        if not scenario_success:
            print(f"    🚨 {scenario['name']}场景最终失败")

    # 6. 复杂异常恢复机制
    print("\\n🚑 复杂异常恢复机制:")

    recovery_strategies = [
        {
            'name': '界面重置',
            'actions': [
                {'type': 'shortcut', 'params': {'keys': ['escape'], 'delay': 0.3}},
                {'type': 'wait', 'params': {'seconds': 0.5}},
                {'type': 'shortcut', 'params': {'keys': ['alt', 'tab'], 'delay': 0.3}},
            ]
        },
        {
            'name': '应用重启',
            'actions': [
                {'type': 'shortcut', 'params': {'keys': ['alt', 'f4'], 'delay': 0.5}},
                {'type': 'wait', 'params': {'seconds': 2.0}},
                {'type': 'shortcut', 'params': {'keys': ['ctrl', 'alt', 'del'], 'delay': 0.5}},
            ]
        },
        {
            'name': '系统恢复',
            'actions': [
                {'type': 'shortcut', 'params': {'keys': ['ctrl', 'shift', 'esc'], 'delay': 0.5}},
                {'type': 'wait', 'params': {'seconds': 1.0}},
                {'type': 'shortcut', 'params': {'keys': ['escape'], 'delay': 0.3}},
            ]
        }
    ]

    for strategy in recovery_strategies:
        print(f"\\n  🔧 恢复策略: {strategy['name']}")

        try:
            print("    🎯 执行恢复操作序列...")

            for i, action in enumerate(strategy['actions']):
                action_type = action.get('type')
                params = action.get('params', {})

                print(f"      步骤 {i+1}: {action_type}")

                try:
                    if action_type == 'shortcut':
                        keys = params.get('keys', [])
                        delay = params.get('delay', 0.1)
                        success = automator.perform_keyboard_shortcut(keys)
                        if delay > 0:
                            time.sleep(delay)
                        print(f"        快捷键 {'+'.join(keys)}: {'✅ 成功' if success else '❌ 失败'}")

                    elif action_type == 'wait':
                        seconds = params.get('seconds', 1.0)
                        time.sleep(seconds)
                        print(f"        等待 {seconds} 秒: ✅ 完成")

                    elif action_type == 'click':
                        coords = params.get('coordinates')
                        if coords and automator.validate_coordinates(coords):
                            success = automator.perform_mouse_click(coords)
                            print(f"        点击操作: {'✅ 成功' if success else '❌ 失败'}")
                        else:
                            print("        点击操作: ❌ 坐标无效")

                    else:
                        print(f"        未知操作: {action_type}")

                except Exception as step_error:
                    print(f"        ❌ 步骤异常: {step_error}")

                    # 记录恢复步骤异常
                    if safety_config['enable_logging']:
                        automator.log_automation_action(
                            'recovery_step_error',
                            {
                                'strategy': strategy['name'],
                                'step': i+1,
                                'action_type': action_type,
                                'error': str(step_error)
                            },
                            False
                        )
                    continue

            print(f"    ✅ {strategy['name']}恢复策略执行完成")

            # 记录恢复策略成功
            if safety_config['enable_logging']:
                automator.log_automation_action(
                    'recovery_strategy_success',
                    {'strategy': strategy['name']},
                    True
                )

        except Exception as strategy_error:
            print(f"    ❌ {strategy['name']}恢复策略异常: {strategy_error}")

            # 记录恢复策略失败
            if safety_config['enable_logging']:
                automator.log_automation_action(
                    'recovery_strategy_error',
                    {
                        'strategy': strategy['name'],
                        'error': str(strategy_error)
                    },
                    False
                )

    # 7. 安全机制总结和统计
    print("\\n📋 安全机制总结和统计:")

    # 统计各种操作的成功率
    print("✅ 实现的安全特性:")
    print("   • 多层次坐标边界验证")
    print("   • 智能操作超时保护")
    print("   • 全面异常捕获和处理")
    print("   • 详细操作日志记录")
    print("   • 指数退避重试机制")
    print("   • 多策略自动恢复功能")
    print("   • 实时性能监控")
    print("   • 质量评估和统计")

    print("\\n📊 安全统计信息:")
    print(f"   坐标验证测试: {len(test_coordinates)} 个用例")
    print(f"   边界条件测试: {len(boundary_tests)} 个场景")
    print(f"   重试机制测试: {len(failure_scenarios)} 个场景")
    print(f"   恢复策略测试: {len(recovery_strategies)} 个策略")
    print(f"   最大重试次数: {safety_config['max_retries']}")
    print(f"   操作超时时间: {safety_config['operation_timeout']} 秒")

    print("\\n💡 最佳实践建议:")
    print("   • 始终验证操作坐标的有效性")
    print("   • 设置合理的超时时间和重试次数")
    print("   • 记录详细的操作日志便于调试")
    print("   • 实现多层次的错误处理机制")
    print("   • 提供用户友好的错误信息")
    print("   • 定期监控和分析操作成功率")
    print("   • 根据不同场景调整恢复策略")

    print("\\n🎯 适用场景:")
    print("   • 关键业务流程自动化")
    print("   • 长时间运行的批量任务")
    print("   • 网络环境不稳定的操作")
    print("   • 需要高可靠性的自动化系统")
    print("   • 多用户并发的自动化环境")

    print("\\n✅ 错误处理和安全机制演示完成!")
    print("🛡️ 系统已具备完善的安全保护能力")

    return True

# 使用方法
if __name__ == "__main__":
    # 运行错误处理和安全机制示例
    success = error_handling_and_safety_example()
    print(f"\\n最终结果: {'✅ 成功' if success else '❌ 失败'}")
'''

        code_text.setPlainText(error_handling_code)
        code_text.setReadOnly(True)
        code_layout.addWidget(code_text)

        # 运行按钮
        run_btn = QPushButton("▶️ 运行此示例")
        run_btn.setMinimumHeight(35)
        run_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        run_btn.clicked.connect(lambda: self._run_example("error_handling"))
        code_layout.addWidget(run_btn)

        splitter.addWidget(code_group)
        splitter.setSizes([120, 500])

        self.tab_widget.addTab(tab, "🛡️ 错误处理机制")