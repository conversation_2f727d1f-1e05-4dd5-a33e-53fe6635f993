{"timestamp": "2025-07-06T18:27:53.545241", "operations": [{"action": "move", "source": "模板制作指南.md", "target": "docs\\user\\模板制作指南.md", "status": "success"}, {"action": "move", "source": "用户使用手册.md", "target": "docs\\user\\用户使用手册.md", "status": "success"}, {"action": "move", "source": "AI辅助守则——杜绝谎话连篇.md", "target": "docs\\development\\AI辅助守则——杜绝谎话连篇.md", "status": "success"}, {"action": "move", "source": "FINAL_CONSISTENCY_ANALYSIS_REPORT.md", "target": "docs\\development\\FINAL_CONSISTENCY_ANALYSIS_REPORT.md", "status": "success"}, {"action": "move", "source": "GUI检测方法完全复制版本.md", "target": "docs\\development\\GUI检测方法完全复制版本.md", "status": "success"}, {"action": "move", "source": "STARTUP_SCRIPTS_README.md", "target": "docs\\development\\STARTUP_SCRIPTS_README.md", "status": "success"}, {"action": "move", "source": "TEMPLATE_SYNC_SUCCESS_REPORT.md", "target": "docs\\development\\TEMPLATE_SYNC_SUCCESS_REPORT.md", "status": "success"}, {"action": "move", "source": "YOLO OpenCV检测器功能特性和工作流程详细说明.md", "target": "docs\\development\\YOLO OpenCV检测器功能特性和工作流程详细说明.md", "status": "success"}, {"action": "move", "source": "开发文档.md", "target": "docs\\development\\开发文档.md", "status": "success"}, {"action": "move", "source": "模板管理和检测可视化说明.md", "target": "docs\\development\\模板管理和检测可视化说明.md", "status": "success"}, {"action": "move", "source": "源代码编辑器功能说明.md", "target": "docs\\development\\源代码编辑器功能说明.md", "status": "success"}, {"action": "move", "source": "GUI标签页删除完成报告.md", "target": "docs\\reports\\GUI标签页删除完成报告.md", "status": "success"}, {"action": "move", "source": "源代码对话框优化完成报告.md", "target": "docs\\reports\\源代码对话框优化完成报告.md", "status": "success"}, {"action": "move", "source": "源代码窗口使用示例更新完成报告.md", "target": "docs\\reports\\源代码窗口使用示例更新完成报告.md", "status": "success"}, {"action": "move", "source": "项目清理和帮助系统完成报告.md", "target": "docs\\reports\\项目清理和帮助系统完成报告.md", "status": "success"}, {"action": "move", "source": "GUI与源代码检测差异问题解决方案.md", "target": "docs\\reports\\GUI与源代码检测差异问题解决方案.md", "status": "success"}, {"action": "move", "source": "代码执行错误和图标问题修复报告.md", "target": "docs\\reports\\代码执行错误和图标问题修复报告.md", "status": "success"}, {"action": "move", "source": "任务栏图标修复完成报告.md", "target": "docs\\reports\\任务栏图标修复完成报告.md", "status": "success"}, {"action": "move", "source": "标记显示问题解决方案.md", "target": "docs\\reports\\标记显示问题解决方案.md", "status": "success"}, {"action": "move", "source": "模板列表问题解决方案.md", "target": "docs\\reports\\模板列表问题解决方案.md", "status": "success"}, {"action": "move", "source": "源代码对话框环境问题解决方案.md", "target": "docs\\reports\\源代码对话框环境问题解决方案.md", "status": "success"}, {"action": "move", "source": "DETECTION_CONSISTENCY_FIX_COMPLETE.md", "target": "docs\\archive\\DETECTION_CONSISTENCY_FIX_COMPLETE.md", "status": "success"}, {"action": "move", "source": "MODULE_IMPORT_FIX_COMPLETE.md", "target": "docs\\archive\\MODULE_IMPORT_FIX_COMPLETE.md", "status": "success"}, {"action": "move", "source": "README_automation_complete.md", "target": "docs\\archive\\README_automation_complete.md", "status": "success"}, {"action": "move", "source": "TEMPLATE_MATCHING_FIX_COMPLETE.md", "target": "docs\\archive\\TEMPLATE_MATCHING_FIX_COMPLETE.md", "status": "success"}, {"action": "move", "source": "UTF8_ENCODING_FIX_SUMMARY.md", "target": "docs\\archive\\UTF8_ENCODING_FIX_SUMMARY.md", "status": "success"}, {"action": "move", "source": "UTF8_FIX_COMPLETE.md", "target": "docs\\archive\\UTF8_FIX_COMPLETE.md", "status": "success"}]}