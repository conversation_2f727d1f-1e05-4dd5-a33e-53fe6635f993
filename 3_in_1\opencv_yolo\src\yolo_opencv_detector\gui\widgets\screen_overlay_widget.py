#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
屏幕叠加标注组件
在屏幕上实时显示检测结果的半透明标注
作者: Cursor AI
创建时间: 2025-07-05
编码标准: UTF-8无BOM
"""

from typing import List, Dict, Any, Optional
from PyQt6.QtWidgets import QWidget, QApplication
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QRect
from PyQt6.QtGui import QPainter, QPen, QBrush, QColor, QFont, QFontMetrics

from ...utils.logger import Logger


class ScreenOverlayWidget(QWidget):
    """屏幕叠加标注组件"""
    
    # 信号定义
    overlay_clicked = pyqtSignal(dict)  # 点击检测框时发出
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = Logger()
        
        # 窗口属性 - 全屏透明叠加
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint |
            Qt.WindowType.WindowStaysOnTopHint |
            Qt.WindowType.Tool |
            Qt.WindowType.WindowTransparentForInput  # 允许鼠标穿透
        )
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        
        # 设置为全屏大小
        screen = QApplication.primaryScreen().geometry()
        self.setGeometry(screen)
        
        # 检测结果数据
        self.detections = []
        self.show_labels = True
        self.show_confidence = True
        
        # 显示控制
        self.fade_timer = QTimer()
        self.fade_timer.timeout.connect(self._fade_out)
        self.opacity = 1.0
        self.auto_fade_duration = 3000  # 3秒后自动淡出
        
        # 样式配置
        self.box_color = QColor(0, 255, 0, 180)  # 半透明绿色
        self.text_color = QColor(255, 255, 255, 255)  # 白色文字
        self.background_color = QColor(0, 0, 0, 120)  # 半透明黑色背景
        self.box_thickness = 2
        
        self.logger.info("屏幕叠加标注组件初始化完成")
    
    def show_detections(self, detections: List[Dict[str, Any]], auto_fade: bool = True):
        """显示检测结果
        
        Args:
            detections: 检测结果列表，格式：
                [{"bbox": [x, y, w, h], "confidence": 0.85, "class_name": "person"}, ...]
            auto_fade: 是否自动淡出
        """
        self.detections = detections
        self.opacity = 1.0
        
        if detections:
            self.show()
            self.update()  # 触发重绘
            
            if auto_fade:
                self.fade_timer.start(self.auto_fade_duration)
            
            self.logger.debug(f"显示{len(detections)}个检测结果")
        else:
            self.hide()
    
    def hide_overlay(self):
        """隐藏叠加层"""
        self.fade_timer.stop()
        self.hide()
    
    def set_style(self, box_color=None, text_color=None,
                  background_color=None, thickness: int = None):
        """设置显示样式"""
        if box_color:
            if isinstance(box_color, (tuple, list)) and len(box_color) >= 3:
                # 处理RGBA元组
                if len(box_color) == 4:
                    self.box_color = QColor(box_color[0], box_color[1], box_color[2], box_color[3])
                else:
                    self.box_color = QColor(box_color[0], box_color[1], box_color[2])
            else:
                self.box_color = box_color

        if text_color:
            if isinstance(text_color, (tuple, list)) and len(text_color) >= 3:
                if len(text_color) == 4:
                    self.text_color = QColor(text_color[0], text_color[1], text_color[2], text_color[3])
                else:
                    self.text_color = QColor(text_color[0], text_color[1], text_color[2])
            else:
                self.text_color = text_color

        if background_color:
            if isinstance(background_color, (tuple, list)) and len(background_color) >= 3:
                if len(background_color) == 4:
                    self.background_color = QColor(background_color[0], background_color[1], background_color[2], background_color[3])
                else:
                    self.background_color = QColor(background_color[0], background_color[1], background_color[2])
            else:
                self.background_color = background_color

        if thickness:
            self.box_thickness = thickness

        if self.isVisible():
            self.update()
    
    def set_display_options(self, show_labels: bool = True, show_confidence: bool = True):
        """设置显示选项"""
        self.show_labels = show_labels
        self.show_confidence = show_confidence
        
        if self.isVisible():
            self.update()
    
    def _fade_out(self):
        """淡出效果"""
        self.opacity -= 0.1
        if self.opacity <= 0:
            self.fade_timer.stop()
            self.hide()
        else:
            self.update()
    
    def paintEvent(self, event):
        """绘制事件"""
        if not self.detections:
            return
        
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 应用透明度
        painter.setOpacity(self.opacity)
        
        # 绘制每个检测结果
        for detection in self.detections:
            self._draw_detection(painter, detection)
        
        painter.end()
    
    def _draw_detection(self, painter: QPainter, detection: Dict[str, Any]):
        """绘制单个检测结果"""
        try:
            # 获取边界框信息
            bbox = detection.get("bbox", [])
            if len(bbox) != 4:
                return
            
            x, y, w, h = bbox
            confidence = detection.get("confidence", 0.0)
            class_name = detection.get("class_name", "unknown")
            
            # 绘制边界框
            box_color = QColor(self.box_color)
            box_color.setAlpha(int(box_color.alpha() * self.opacity))
            
            pen = QPen(box_color, self.box_thickness)
            painter.setPen(pen)
            painter.setBrush(QBrush(Qt.BrushStyle.NoBrush))
            painter.drawRect(x, y, w, h)
            
            # 绘制标签和置信度
            if self.show_labels or self.show_confidence:
                self._draw_label(painter, x, y, class_name, confidence)
        
        except Exception as e:
            self.logger.error(f"绘制检测结果失败: {e}")
    
    def _draw_label(self, painter: QPainter, x: int, y: int, 
                   class_name: str, confidence: float):
        """绘制标签文字"""
        # 构建标签文字
        label_parts = []
        if self.show_labels:
            label_parts.append(class_name)
        if self.show_confidence:
            label_parts.append(f"{confidence:.1%}")
        
        if not label_parts:
            return
        
        label_text = " ".join(label_parts)
        
        # 设置字体
        font = QFont("Arial", 12, QFont.Weight.Bold)
        painter.setFont(font)
        
        # 计算文字尺寸
        font_metrics = QFontMetrics(font)
        text_rect = font_metrics.boundingRect(label_text)
        
        # 调整标签位置（避免超出屏幕）
        label_x = x
        label_y = y - text_rect.height() - 5
        
        if label_y < 0:
            label_y = y + 5  # 如果上方空间不够，显示在下方
        
        # 绘制背景
        background_rect = QRect(
            label_x - 2,
            label_y - 2,
            text_rect.width() + 4,
            text_rect.height() + 4
        )
        
        background_color = QColor(self.background_color)
        background_color.setAlpha(int(background_color.alpha() * self.opacity))
        
        painter.setBrush(QBrush(background_color))
        painter.setPen(QPen(Qt.PenStyle.NoPen))
        painter.drawRect(background_rect)
        
        # 绘制文字
        text_color = QColor(self.text_color)
        text_color.setAlpha(int(text_color.alpha() * self.opacity))
        
        painter.setPen(QPen(text_color))
        painter.drawText(label_x, label_y + text_rect.height(), label_text)
    
    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            # 检查是否点击了某个检测框
            click_pos = event.position().toPoint()
            
            for detection in self.detections:
                bbox = detection.get("bbox", [])
                if len(bbox) == 4:
                    x, y, w, h = bbox
                    if x <= click_pos.x() <= x + w and y <= click_pos.y() <= y + h:
                        self.overlay_clicked.emit(detection)
                        break
    
    def keyPressEvent(self, event):
        """键盘事件"""
        if event.key() == Qt.Key.Key_Escape:
            self.hide_overlay()
        super().keyPressEvent(event)


class ScreenOverlayManager:
    """屏幕叠加管理器"""
    
    def __init__(self):
        self.logger = Logger()
        self.overlay_widget = None
        self.is_enabled = True
    
    def initialize(self):
        """初始化叠加层"""
        if not self.overlay_widget:
            self.overlay_widget = ScreenOverlayWidget()
            self.logger.info("屏幕叠加管理器初始化完成")
    
    def show_detections(self, detections: List[Dict[str, Any]], auto_fade: bool = True):
        """显示检测结果"""
        if not self.is_enabled:
            return
        
        if not self.overlay_widget:
            self.initialize()
        
        self.overlay_widget.show_detections(detections, auto_fade)
    
    def hide_overlay(self):
        """隐藏叠加层"""
        if self.overlay_widget:
            self.overlay_widget.hide_overlay()
    
    def set_enabled(self, enabled: bool):
        """设置是否启用叠加显示"""
        self.is_enabled = enabled
        if not enabled and self.overlay_widget:
            self.overlay_widget.hide_overlay()
    
    def set_style(self, **kwargs):
        """设置显示样式"""
        if self.overlay_widget:
            self.overlay_widget.set_style(**kwargs)
    
    def set_display_options(self, **kwargs):
        """设置显示选项"""
        if self.overlay_widget:
            self.overlay_widget.set_display_options(**kwargs)
    
    def cleanup(self):
        """清理资源"""
        if self.overlay_widget:
            self.overlay_widget.hide_overlay()
            self.overlay_widget.deleteLater()
            self.overlay_widget = None


# 全局叠加管理器实例
_overlay_manager = None

def get_overlay_manager() -> ScreenOverlayManager:
    """获取全局叠加管理器实例"""
    global _overlay_manager
    if _overlay_manager is None:
        _overlay_manager = ScreenOverlayManager()
    return _overlay_manager
