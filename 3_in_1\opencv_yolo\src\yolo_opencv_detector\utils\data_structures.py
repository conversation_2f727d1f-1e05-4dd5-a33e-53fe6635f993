# -*- coding: utf-8 -*-
"""
数据结构定义
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any, Tuple
from enum import Enum
import json
import time
from pathlib import Path


class DetectionSource(Enum):
    """检测来源枚举"""
    YOLO = "yolo"
    TEMPLATE = "template"
    FUSION = "fusion"


@dataclass
class BoundingBox:
    """边界框数据结构"""
    x: int
    y: int
    width: int
    height: int
    
    @property
    def x2(self) -> int:
        """右下角x坐标"""
        return self.x + self.width
    
    @property
    def y2(self) -> int:
        """右下角y坐标"""
        return self.y + self.height
    
    @property
    def center_x(self) -> int:
        """中心点x坐标"""
        return self.x + self.width // 2
    
    @property
    def center_y(self) -> int:
        """中心点y坐标"""
        return self.y + self.height // 2
    
    @property
    def area(self) -> int:
        """边界框面积"""
        return self.width * self.height
    
    def to_tuple(self) -> Tuple[int, int, int, int]:
        """转换为元组格式 (x, y, width, height)"""
        return (self.x, self.y, self.width, self.height)
    
    def to_xyxy(self) -> Tuple[int, int, int, int]:
        """转换为xyxy格式 (x1, y1, x2, y2)"""
        return (self.x, self.y, self.x2, self.y2)
    
    def iou(self, other: 'BoundingBox') -> float:
        """计算与另一个边界框的IoU"""
        # 计算交集
        x1 = max(self.x, other.x)
        y1 = max(self.y, other.y)
        x2 = min(self.x2, other.x2)
        y2 = min(self.y2, other.y2)
        
        if x2 <= x1 or y2 <= y1:
            return 0.0
        
        intersection = (x2 - x1) * (y2 - y1)
        union = self.area + other.area - intersection
        
        return intersection / union if union > 0 else 0.0


@dataclass
class DetectionResult:
    """检测结果数据结构"""
    bbox: BoundingBox
    confidence: float
    class_id: Optional[int] = None
    class_name: Optional[str] = None
    template_id: Optional[str] = None
    source: DetectionSource = DetectionSource.YOLO
    timestamp: float = field(default_factory=time.time)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "bbox": self.bbox.to_tuple(),
            "confidence": self.confidence,
            "class_id": self.class_id,
            "class_name": self.class_name,
            "template_id": self.template_id,
            "source": self.source.value,
            "timestamp": self.timestamp,
            "metadata": self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DetectionResult':
        """从字典创建检测结果"""
        bbox_data = data["bbox"]
        bbox = BoundingBox(bbox_data[0], bbox_data[1], bbox_data[2], bbox_data[3])
        
        return cls(
            bbox=bbox,
            confidence=data["confidence"],
            class_id=data.get("class_id"),
            class_name=data.get("class_name"),
            template_id=data.get("template_id"),
            source=DetectionSource(data.get("source", "yolo")),
            timestamp=data.get("timestamp", time.time()),
            metadata=data.get("metadata", {})
        )
    
    def to_json(self) -> str:
        """转换为JSON字符串"""
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=2)


@dataclass
class TemplateInfo:
    """模板信息数据结构"""
    template_id: str
    name: str
    description: str
    file_path: Path
    category: str = "default"
    tags: List[str] = field(default_factory=list)
    created_time: float = field(default_factory=time.time)
    updated_time: float = field(default_factory=time.time)
    usage_count: int = 0
    success_rate: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "template_id": self.template_id,
            "name": self.name,
            "description": self.description,
            "file_path": str(self.file_path),
            "category": self.category,
            "tags": self.tags,
            "created_time": self.created_time,
            "updated_time": self.updated_time,
            "usage_count": self.usage_count,
            "success_rate": self.success_rate,
            "metadata": self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TemplateInfo':
        """从字典创建模板信息"""
        return cls(
            template_id=data["template_id"],
            name=data["name"],
            description=data["description"],
            file_path=Path(data["file_path"]),
            category=data.get("category", "default"),
            tags=data.get("tags", []),
            created_time=data.get("created_time", time.time()),
            updated_time=data.get("updated_time", time.time()),
            usage_count=data.get("usage_count", 0),
            success_rate=data.get("success_rate", 0.0),
            metadata=data.get("metadata", {})
        )


@dataclass
class ScreenInfo:
    """屏幕信息数据结构"""
    monitor_id: int
    x: int
    y: int
    width: int
    height: int
    is_primary: bool = False
    scale_factor: float = 1.0
    name: str = ""
    
    @property
    def bounds(self) -> BoundingBox:
        """屏幕边界"""
        return BoundingBox(self.x, self.y, self.width, self.height)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "monitor_id": self.monitor_id,
            "x": self.x,
            "y": self.y,
            "width": self.width,
            "height": self.height,
            "is_primary": self.is_primary,
            "scale_factor": self.scale_factor,
            "name": self.name
        }


@dataclass
class PerformanceMetrics:
    """性能指标数据结构"""
    timestamp: float = field(default_factory=time.time)
    cpu_usage: float = 0.0
    memory_usage: float = 0.0
    gpu_usage: float = 0.0
    gpu_memory: float = 0.0
    detection_time: float = 0.0
    fusion_time: float = 0.0
    total_time: float = 0.0
    fps: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "timestamp": self.timestamp,
            "cpu_usage": self.cpu_usage,
            "memory_usage": self.memory_usage,
            "gpu_usage": self.gpu_usage,
            "gpu_memory": self.gpu_memory,
            "detection_time": self.detection_time,
            "fusion_time": self.fusion_time,
            "total_time": self.total_time,
            "fps": self.fps
        }


@dataclass
class ScriptAction:
    """脚本动作数据结构"""
    action_type: str  # click, drag, key, wait
    x: Optional[int] = None
    y: Optional[int] = None
    x2: Optional[int] = None
    y2: Optional[int] = None
    key: Optional[str] = None
    duration: float = 0.0
    delay: float = 0.0
    comment: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "action_type": self.action_type,
            "x": self.x,
            "y": self.y,
            "x2": self.x2,
            "y2": self.y2,
            "key": self.key,
            "duration": self.duration,
            "delay": self.delay,
            "comment": self.comment
        }


@dataclass
class DetectionSession:
    """检测会话数据结构"""
    session_id: str
    start_time: float = field(default_factory=time.time)
    end_time: Optional[float] = None
    results: List[DetectionResult] = field(default_factory=list)
    performance: Optional[PerformanceMetrics] = None
    config_snapshot: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def duration(self) -> float:
        """会话持续时间"""
        if self.end_time:
            return self.end_time - self.start_time
        return time.time() - self.start_time
    
    def add_result(self, result: DetectionResult) -> None:
        """添加检测结果"""
        self.results.append(result)
    
    def end_session(self) -> None:
        """结束会话"""
        self.end_time = time.time()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "session_id": self.session_id,
            "start_time": self.start_time,
            "end_time": self.end_time,
            "results": [result.to_dict() for result in self.results],
            "performance": self.performance.to_dict() if self.performance else None,
            "config_snapshot": self.config_snapshot,
            "duration": self.duration
        }
