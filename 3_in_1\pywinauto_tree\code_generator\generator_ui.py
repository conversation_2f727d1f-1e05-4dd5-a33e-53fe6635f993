#!/usr/bin/env python
# -*- coding:utf-8 -*-

import tkinter as tk
from tkinter import ttk, messagebox
import logging
from typing import Dict, Any, Optional
from pathlib import Path
import json
import os

from .window_locator import WindowLocatorStrategy, get_available_strategies as get_window_strategies
from .control_locator import ControlLocatorStrategy, get_available_strategies as get_control_strategies
from .code_snippet import CodeSnippetManager

class CodeGeneratorDialog:
    def __init__(self, parent, control=None, window=None):
        """初始化代码生成器对话框
        
        Args:
            parent: 父窗口
            control: 选中的控件对象(可选)
            window: 控件所属的窗口对象(可选)
        """
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("生成定位代码")
        self.dialog.geometry("1200x600")
        self.dialog.transient(parent)  # 设置为父窗口的临时窗口
        
        # 设置窗口图标
        try:
            # 获取当前文件所在目录
            current_dir = os.path.dirname(os.path.abspath(__file__))
            # 构建图标路径
            icon_path = os.path.join(current_dir, '..', 'icons', 'code_generator.png')
            if os.path.exists(icon_path):
                icon = tk.PhotoImage(file=icon_path)
                self.dialog.iconphoto(True, icon)
                # 保存图标引用，防止被垃圾回收
                self._icon = icon
        except Exception as e:
            logging.warning(f"Failed to set code generator window icon: {e}")
        
        # 保存控件和窗口对象
        self.control = control
        self.window = window
        
        # 初始化代码片段管理器
        self.snippet_manager = CodeSnippetManager()
        
        # 初始化定位策略
        self.window_strategies = get_window_strategies()
        self.control_strategies = get_control_strategies()
        
        # 创建主界面
        self._create_toolbar()
        self._create_main_layout()
        
        # 绑定事件
        self._bind_events()
        
        # 初始化变量
        self.current_window_strategy: Optional[str] = None
        self.current_control_strategy: Optional[str] = None
        self.window_params: Dict[str, Any] = {}
        self.control_params: Dict[str, Any] = {}
        
        # 如果提供了控件和窗口信息,自动填充参数
        if control and window:
            self._auto_fill_params()
    
    def _create_toolbar(self):
        """创建工具栏"""
        toolbar = ttk.Frame(self.dialog)
        
        # 添加工具栏按钮
        ttk.Button(toolbar, text="保存片段", command=self._save_snippet).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="管理片段", command=self._manage_snippets).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="测试定位", command=self._test_location).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="清空", command=self._clear_all).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="复制代码", command=self._copy_code).pack(side=tk.LEFT, padx=2)
        
        toolbar.pack(side=tk.TOP, fill=tk.X, padx=5, pady=3)
    
    def _create_main_layout(self):
        """创建主布局"""
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(expand=True, fill=tk.BOTH, padx=5, pady=5)
        
        # 创建三列布局
        window_frame = self._create_window_frame(main_frame)
        control_frame = self._create_control_frame(main_frame)
        code_frame = self._create_code_frame(main_frame)
        
        window_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
        control_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
        code_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
    
    def _create_window_frame(self, parent):
        """创建窗口定位框架"""
        frame = ttk.LabelFrame(parent, text="窗口定位")
        
        # 策略选择
        ttk.Label(frame, text="定位策略:").pack(anchor=tk.W, padx=5, pady=5)
        self.window_strategy_var = tk.StringVar()
        strategy_combo = ttk.Combobox(frame, textvariable=self.window_strategy_var)
        strategy_combo['values'] = list(self.window_strategies.keys())
        strategy_combo.pack(fill=tk.X, padx=5, pady=5)
        strategy_combo.bind('<<ComboboxSelected>>', self._on_window_strategy_changed)
        
        # 参数输入区域
        self.window_params_frame = ttk.Frame(frame)
        self.window_params_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        return frame
    
    def _create_control_frame(self, parent):
        """创建控件定位框架"""
        frame = ttk.LabelFrame(parent, text="控件定位")
        
        # 策略选择
        ttk.Label(frame, text="定位策略:").pack(anchor=tk.W, padx=5, pady=5)
        self.control_strategy_var = tk.StringVar()
        strategy_combo = ttk.Combobox(frame, textvariable=self.control_strategy_var)
        strategy_combo['values'] = list(self.control_strategies.keys())
        strategy_combo.pack(fill=tk.X, padx=5, pady=5)
        strategy_combo.bind('<<ComboboxSelected>>', self._on_control_strategy_changed)
        
        # 参数输入区域
        self.control_params_frame = ttk.Frame(frame)
        self.control_params_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        return frame
    
    def _create_code_frame(self, parent):
        """创建代码预览框架"""
        frame = ttk.LabelFrame(parent, text="代码预览")
        
        # 代码预览区域
        self.code_text = tk.Text(frame, wrap=tk.NONE)
        self.code_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(frame, orient=tk.VERTICAL, command=self.code_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.code_text.config(yscrollcommand=scrollbar.set)
        
        return frame
    
    def _bind_events(self):
        """绑定事件"""
        # 窗口关闭事件
        self.dialog.protocol("WM_DELETE_WINDOW", self._on_closing)
        
        # 参数变更事件
        self.dialog.bind('<<ParamsChanged>>', self._update_code)
    
    def _on_window_strategy_changed(self, event):
        """窗口定位策略变更事件"""
        strategy = self.window_strategy_var.get()
        if strategy in self.window_strategies:
            self.current_window_strategy = strategy
            self._update_window_params_frame()
            self._update_code()
    
    def _on_control_strategy_changed(self, event):
        """控件定位策略变更事件"""
        strategy = self.control_strategy_var.get()
        if strategy in self.control_strategies:
            self.current_control_strategy = strategy
            self._update_control_params_frame()
            self._update_code()
    
    def _update_window_params_frame(self):
        """更新窗口参数输入框"""
        # 清空现有的输入框
        for widget in self.window_params_frame.winfo_children():
            widget.destroy()
        
        if not self.current_window_strategy:
            return
        
        # 获取策略所需参数
        strategy = self.window_strategies[self.current_window_strategy]
        params = strategy.required_params
        
        # 创建参数输入框
        self.window_param_vars = {}
        for param_name, param_desc in params.items():
            frame = ttk.Frame(self.window_params_frame)
            frame.pack(fill=tk.X, padx=5, pady=2)
            
            ttk.Label(frame, text=f"{param_desc}:").pack(side=tk.LEFT)
            var = tk.StringVar(value=self.window_params.get(param_name, ''))
            entry = ttk.Entry(frame, textvariable=var)
            entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
            
            self.window_param_vars[param_name] = var
            var.trace_add('write', lambda *args: self._on_params_changed())
    
    def _update_control_params_frame(self):
        """更新控件参数输入框"""
        # 清空现有的输入框
        for widget in self.control_params_frame.winfo_children():
            widget.destroy()
        
        if not self.current_control_strategy:
            return
        
        # 获取策略所需参数
        strategy = self.control_strategies[self.current_control_strategy]
        params = strategy.required_params
        
        # 创建参数输入框
        self.control_param_vars = {}
        for param_name, param_desc in params.items():
            frame = ttk.Frame(self.control_params_frame)
            frame.pack(fill=tk.X, padx=5, pady=2)
            
            ttk.Label(frame, text=f"{param_desc}:").pack(side=tk.LEFT)
            var = tk.StringVar(value=self.control_params.get(param_name, ''))
            entry = ttk.Entry(frame, textvariable=var)
            entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
            
            self.control_param_vars[param_name] = var
            var.trace_add('write', lambda *args: self._on_params_changed())
    
    def _on_params_changed(self):
        """参数变更处理"""
        # 更新参数字典
        if hasattr(self, 'window_param_vars'):
            self.window_params = {
                name: var.get()
                for name, var in self.window_param_vars.items()
            }
        
        if hasattr(self, 'control_param_vars'):
            self.control_params = {
                name: var.get()
                for name, var in self.control_param_vars.items()
            }
        
        # 触发代码更新
        self.dialog.event_generate('<<ParamsChanged>>')
    
    def _update_code(self, event=None):
        """更新代码预览"""
        self.code_text.delete('1.0', tk.END)
        
        code = []
        
        # 生成窗口定位代码
        if self.current_window_strategy:
            strategy = self.window_strategies[self.current_window_strategy]
            window_code = strategy.generate_code(self.window_params)
            code.append(window_code)
        
        # 生成控件定位代码
        if self.current_control_strategy:
            strategy = self.control_strategies[self.current_control_strategy]
            control_code = strategy.generate_code(self.control_params)
            code.append(control_code)
        
        # 显示代码
        if code:
            self.code_text.insert('1.0', '\n\n'.join(code))
    
    def _save_snippet(self):
        """保存代码片段"""
        if not self.code_text.get('1.0', tk.END).strip():
            messagebox.showwarning("警告", "没有可保存的代码")
            return
        
        # 创建保存对话框
        dialog = tk.Toplevel(self.dialog)
        dialog.title("保存代码片段")
        dialog.geometry("400x300")
        
        # 添加输入框
        ttk.Label(dialog, text="标题:").pack(padx=5, pady=5)
        title_var = tk.StringVar()
        ttk.Entry(dialog, textvariable=title_var).pack(fill=tk.X, padx=5)
        
        ttk.Label(dialog, text="描述:").pack(padx=5, pady=5)
        desc_text = tk.Text(dialog, height=3)
        desc_text.pack(fill=tk.X, padx=5)
        
        ttk.Label(dialog, text="标签 (用逗号分隔):").pack(padx=5, pady=5)
        tags_var = tk.StringVar()
        ttk.Entry(dialog, textvariable=tags_var).pack(fill=tk.X, padx=5)
        
        def save():
            title = title_var.get().strip()
            if not title:
                messagebox.showwarning("警告", "标题不能为空")
                return
            
            description = desc_text.get('1.0', tk.END).strip()
            tags = [tag.strip() for tag in tags_var.get().split(',') if tag.strip()]
            code = self.code_text.get('1.0', tk.END).strip()
            
            try:
                self.snippet_manager.add_snippet(
                    title=title,
                    description=description,
                    code=code,
                    tags=tags
                )
                messagebox.showinfo("成功", "代码片段保存成功")
                dialog.destroy()
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {e}")
        
        ttk.Button(dialog, text="保存", command=save).pack(pady=10)
    
    def _import_snippets(self):
        """导入代码片段"""
        pass
    
    def _export_snippets(self):
        """导出代码片段"""
        pass
    
    def _clear_all(self):
        """清空所有输入"""
        # 清空策略选择
        self.window_strategy_var.set('')
        self.control_strategy_var.set('')
        self.current_window_strategy = None
        self.current_control_strategy = None
        
        # 清空参数
        self.window_params = {}
        self.control_params = {}
        
        # 清空参数输入框
        self._update_window_params_frame()
        self._update_control_params_frame()
        
        # 清空代码预览
        self.code_text.delete('1.0', tk.END)
    
    def _copy_code(self):
        """复制代码到剪贴板"""
        code = self.code_text.get('1.0', tk.END).strip()
        if code:
            self.dialog.clipboard_clear()
            self.dialog.clipboard_append(code)
            messagebox.showinfo("成功", "代码已复制到剪贴板")
        else:
            messagebox.showwarning("警告", "没有可复制的代码")
    
    def _test_location(self):
        """测试定位"""
        if not self.current_window_strategy:
            messagebox.showwarning("警告", "请先选择窗口定位策略")
            return
        
        try:
            # 测试窗口定位
            window_strategy = self.window_strategies[self.current_window_strategy]
            app = window_strategy.test_location(self.window_params)
            
            if not app:
                messagebox.showerror("错误", "窗口定位失败")
                return
            
            window = app.top_window()
            
            # 测试控件定位
            if self.current_control_strategy:
                control_strategy = self.control_strategies[self.current_control_strategy]
                control = control_strategy.test_location(window, self.control_params)
                
                if control:
                    messagebox.showinfo("成功", "控件定位成功")
                else:
                    messagebox.showerror("错误", "控件定位失败")
            else:
                messagebox.showinfo("成功", "窗口定位成功")
                
        except Exception as e:
            messagebox.showerror("错误", f"测试失败: {e}")
    
    def _show_control_tree(self):
        """显示控件树"""
        pass
    
    def _show_help(self):
        """显示帮助信息"""
        help_text = """
代码生成器使用说明：

1. 窗口定位
   - 选择合适的定位策略
   - 填写必要的参数
   - 可以使用测试按钮验证

2. 控件定位
   - 选择合适的定位策略
   - 填写必要的参数
   - 可以使用测试按钮验证

3. 代码管理
   - 可以保存常用的代码片段
   - 支持导入导出功能
   - 可以复制生成的代码

4. 其他功能
   - 可以查看目标窗口的控件树
   - 支持多种定位策略组合
   - 提供实时代码预览
"""
        dialog = tk.Toplevel(self.dialog)
        dialog.title("使用说明")
        dialog.geometry("600x400")
        
        text = tk.Text(dialog, wrap=tk.WORD)
        text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        text.insert('1.0', help_text)
        text.config(state=tk.DISABLED)
    
    def _show_about(self):
        """显示关于信息"""
        about_text = """
代码生成器

版本: 1.0.0
作者: Claude
许可: MIT

这是一个用于生成 pywinauto 自动化测试代码的工具。
它可以帮助你快速生成窗口和控件的定位代码。

主要功能：
- 支持多种定位策略
- 实时代码预览
- 代码片段管理
- 定位测试
"""
        messagebox.showinfo("关于", about_text)
    
    def _on_closing(self):
        """窗口关闭处理"""
        # 清理资源
        if hasattr(self, 'snippet_manager'):
            self.snippet_manager.save()  # 保存代码片段
        
        # 销毁窗口
        self.dialog.destroy()
    
    def _auto_fill_params(self):
        """根据提供的控件和窗口信息自动填充参数"""
        if self.window:
            # 获取窗口信息
            window_title = self.window.window_text()
            window_handle = self.window.handle
            
            # 设置窗口定位策略
            if window_handle:
                self.window_strategy_var.set('handle')
                self.window_params['handle'] = str(window_handle)
            elif window_title:
                self.window_strategy_var.set('title')
                self.window_params['title'] = window_title
            
            # 触发窗口策略变更事件
            self._on_window_strategy_changed(None)
        
        if self.control:
            # 获取控件信息
            auto_id = self.control.automation_id()
            name = self.control.window_text()
            class_name = self.control.class_name()
            
            # 设置控件定位策略
            if auto_id:
                self.control_strategy_var.set('auto_id')
                self.control_params['auto_id'] = auto_id
            elif name:
                self.control_strategy_var.set('name')
                self.control_params['name'] = name
            elif class_name:
                self.control_strategy_var.set('class_name')
                self.control_params['class_name'] = class_name
            
            # 触发控件策略变更事件
            self._on_control_strategy_changed(None)
    
    def show(self):
        """显示对话框"""
        # 绑定关闭事件
        self.dialog.protocol("WM_DELETE_WINDOW", self._on_closing)
        
        # 模态显示
        self.dialog.grab_set()
        self.dialog.focus_set()  # 设置焦点
        
        # 等待窗口关闭
        self.dialog.wait_window()

    def _manage_snippets(self):
        """管理代码片段"""
        # 创建代码片段管理窗口
        dialog = tk.Toplevel(self.dialog)
        dialog.title("代码片段管理")
        dialog.geometry("800x600")
        dialog.transient(self.dialog)
        
        # 创建分割窗口
        paned = ttk.PanedWindow(dialog, orient=tk.HORIZONTAL)
        paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 左侧列表框架
        list_frame = ttk.Frame(paned)
        paned.add(list_frame, weight=1)
        
        # 搜索框
        search_frame = ttk.Frame(list_frame)
        search_frame.pack(fill=tk.X, padx=5, pady=5)
        
        search_var = tk.StringVar()
        ttk.Entry(search_frame, textvariable=search_var).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(search_frame, text="搜索", command=lambda: self._filter_snippets(search_var.get(), snippet_tree)).pack(side=tk.LEFT, padx=5)
        
        # 代码片段列表
        snippet_tree = ttk.Treeview(list_frame, columns=("title", "tags"), show="headings")
        snippet_tree.heading("title", text="标题")
        snippet_tree.heading("tags", text="标签")
        snippet_tree.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 右侧详情框架
        detail_frame = ttk.Frame(paned)
        paned.add(detail_frame, weight=2)
        
        # 详情区域
        ttk.Label(detail_frame, text="标题:").pack(anchor=tk.W, padx=5, pady=2)
        title_var = tk.StringVar()
        title_entry = ttk.Entry(detail_frame, textvariable=title_var)
        title_entry.pack(fill=tk.X, padx=5, pady=2)
        
        ttk.Label(detail_frame, text="描述:").pack(anchor=tk.W, padx=5, pady=2)
        desc_text = tk.Text(detail_frame, height=3)
        desc_text.pack(fill=tk.X, padx=5, pady=2)
        
        ttk.Label(detail_frame, text="标签:").pack(anchor=tk.W, padx=5, pady=2)
        tags_var = tk.StringVar()
        tags_entry = ttk.Entry(detail_frame, textvariable=tags_var)
        tags_entry.pack(fill=tk.X, padx=5, pady=2)
        
        ttk.Label(detail_frame, text="代码:").pack(anchor=tk.W, padx=5, pady=2)
        code_text = tk.Text(detail_frame, height=10)
        code_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=2)
        
        # 按钮区域
        btn_frame = ttk.Frame(detail_frame)
        btn_frame.pack(fill=tk.X, padx=5, pady=5)
        
        def update_snippet():
            """更新选中的代码片段"""
            selection = snippet_tree.selection()
            if not selection:
                return
                
            item = selection[0]
            title = title_var.get().strip()
            if not title:
                messagebox.showwarning("警告", "标题不能为空")
                return
                
            description = desc_text.get('1.0', tk.END).strip()
            tags = [tag.strip() for tag in tags_var.get().split(',') if tag.strip()]
            code = code_text.get('1.0', tk.END).strip()
            
            try:
                self.snippet_manager.update_snippet(
                    snippet_tree.item(item)['values'][0],  # 原标题
                    title=title,
                    description=description,
                    code=code,
                    tags=tags
                )
                # 更新列表显示
                snippet_tree.item(item, values=(title, ', '.join(tags)))
                messagebox.showinfo("成功", "代码片段更新成功")
            except Exception as e:
                messagebox.showerror("错误", f"更新失败: {e}")
        
        def delete_snippet():
            """删除选中的代码片段"""
            selection = snippet_tree.selection()
            if not selection:
                return
                
            if not messagebox.askyesno("确认", "确定要删除选中的代码片段吗?"):
                return
                
            item = selection[0]
            title = snippet_tree.item(item)['values'][0]
            
            try:
                self.snippet_manager.delete_snippet(title)
                snippet_tree.delete(item)
                clear_detail()
                messagebox.showinfo("成功", "代码片段删除成功")
            except Exception as e:
                messagebox.showerror("错误", f"删除失败: {e}")
        
        def clear_detail():
            """清空详情区域"""
            title_var.set('')
            desc_text.delete('1.0', tk.END)
            tags_var.set('')
            code_text.delete('1.0', tk.END)
        
        def on_select(event):
            """选中列表项时的处理"""
            selection = snippet_tree.selection()
            if not selection:
                return
                
            item = selection[0]
            title = snippet_tree.item(item)['values'][0]
            snippet = self.snippet_manager.get_snippet(title)
            
            if snippet:
                title_var.set(snippet['title'])
                desc_text.delete('1.0', tk.END)
                desc_text.insert('1.0', snippet['description'])
                tags_var.set(', '.join(snippet['tags']))
                code_text.delete('1.0', tk.END)
                code_text.insert('1.0', snippet['code'])
        
        # 绑定选择事件
        snippet_tree.bind('<<TreeviewSelect>>', on_select)
        
        # 添加按钮
        ttk.Button(btn_frame, text="更新", command=update_snippet).pack(side=tk.LEFT, padx=2)
        ttk.Button(btn_frame, text="删除", command=delete_snippet).pack(side=tk.LEFT, padx=2)
        ttk.Button(btn_frame, text="清空", command=clear_detail).pack(side=tk.LEFT, padx=2)
        ttk.Button(btn_frame, text="导入", command=self._import_snippets).pack(side=tk.LEFT, padx=2)
        ttk.Button(btn_frame, text="导出", command=self._export_snippets).pack(side=tk.LEFT, padx=2)
        
        # 加载现有代码片段
        for snippet in self.snippet_manager.get_all_snippets():
            snippet_tree.insert('', 'end', values=(snippet['title'], ', '.join(snippet['tags'])))
        
        def _filter_snippets(self, search_text, tree):
            """过滤代码片段"""
            # 清空树
            for item in tree.get_children():
                tree.delete(item)
            
            # 重新添加匹配的项
            search_text = search_text.lower()
            for snippet in self.snippet_manager.get_all_snippets():
                if (search_text in snippet['title'].lower() or
                    search_text in snippet['description'].lower() or
                    any(search_text in tag.lower() for tag in snippet['tags'])):
                    tree.insert('', 'end', values=(snippet['title'], ', '.join(snippet['tags'])))
