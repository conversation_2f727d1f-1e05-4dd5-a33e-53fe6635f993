@echo off
echo 🔧 Windows环境修复脚本
echo ========================

echo 📁 当前目录: %CD%
echo.

echo 🔍 检查虚拟环境...
if exist "venv\Scripts\python.exe" (
    echo ✅ 虚拟环境存在
) else (
    echo ❌ 虚拟环境不存在，正在创建...
    python -m venv venv
    if errorlevel 1 (
        echo ❌ 创建虚拟环境失败
        pause
        exit /b 1
    )
)

echo.
echo 🚀 激活虚拟环境...
call venv\Scripts\activate.bat

echo.
echo 🐍 测试Python...
venv\Scripts\python.exe --version
if errorlevel 1 (
    echo ❌ Python无法运行
    pause
    exit /b 1
) else (
    echo ✅ Python运行正常
)

echo.
echo 📦 升级pip...
venv\Scripts\python.exe -m pip install --upgrade pip

echo.
echo 🧪 运行基础测试...
venv\Scripts\python.exe -c "import sys; print(f'Python路径: {sys.executable}'); print('✅ Python环境正常')"

echo.
echo 📋 检查是否存在验证脚本...
if exist "verify_deployment.py" (
    echo ✅ 验证脚本存在，运行验证...
    venv\Scripts\python.exe verify_deployment.py
) else (
    echo ⚠️ 验证脚本不存在，创建简单测试...
    echo import sys > simple_test.py
    echo print("🐍 Python版本:", sys.version) >> simple_test.py
    echo print("📁 Python路径:", sys.executable) >> simple_test.py
    echo print("✅ 环境测试通过") >> simple_test.py
    
    venv\Scripts\python.exe simple_test.py
)

echo.
echo 🎯 修复完成！
echo 💡 使用方法:
echo    1. 激活环境: call venv\Scripts\activate.bat
echo    2. 运行Python: venv\Scripts\python.exe 你的脚本.py
echo    3. 或直接使用: python 你的脚本.py (在激活环境后)
echo.
pause
