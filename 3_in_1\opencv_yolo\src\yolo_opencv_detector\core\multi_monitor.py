# -*- coding: utf-8 -*-
"""
多显示器支持模块
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

import time
from typing import List, Optional, Dict, Any, Tuple
import numpy as np

try:
    import win32gui
    import win32api
    import win32con
    WIN32_AVAILABLE = True
except ImportError:
    WIN32_AVAILABLE = False

from ..utils.logger import Logger
from ..utils.data_structures import ScreenInfo


class MultiMonitorManager:
    """多显示器管理器"""
    
    def __init__(self):
        """初始化多显示器管理器"""
        self.logger = Logger().get_logger(__name__)
        self.monitors: List[ScreenInfo] = []
        self.virtual_screen_bounds: Optional[Tuple[int, int, int, int]] = None
        self.last_detection_time = 0
        self.detection_interval = 5.0  # 5秒检测间隔
        
        # 初始检测
        self.detect_monitors()
    
    def detect_monitors(self, force_refresh: bool = False) -> List[ScreenInfo]:
        """
        检测所有显示器
        
        Args:
            force_refresh: 是否强制刷新
            
        Returns:
            List[ScreenInfo]: 显示器列表
        """
        current_time = time.time()
        
        # 检查是否需要刷新
        if (not force_refresh and 
            self.monitors and 
            current_time - self.last_detection_time < self.detection_interval):
            return self.monitors
        
        try:
            self.monitors.clear()
            
            if WIN32_AVAILABLE:
                self._detect_monitors_win32()
            else:
                self._detect_monitors_fallback()
            
            # 计算虚拟屏幕边界
            self._calculate_virtual_screen_bounds()
            
            self.last_detection_time = current_time
            self.logger.info(f"检测到 {len(self.monitors)} 个显示器")
            
            return self.monitors
            
        except Exception as e:
            self.logger.error(f"显示器检测失败: {e}")
            if not self.monitors:
                self._create_fallback_monitor()
            return self.monitors
    
    def _detect_monitors_win32(self) -> None:
        """使用Win32 API检测显示器"""
        monitor_count = 0
        
        def enum_callback(hmonitor, hdc, rect, data):
            nonlocal monitor_count
            # 忽略未使用的参数
            _ = hdc, rect, data
            
            try:
                monitor_info = win32api.GetMonitorInfo(hmonitor)
                monitor_rect = monitor_info['Monitor']
                work_rect = monitor_info['Work']
                device_name = monitor_info.get('Device', f'Monitor {monitor_count}')
                
                # 获取显示器详细信息
                try:
                    display_device = win32api.EnumDisplayDevices(device_name, 0)
                    monitor_name = display_device.DeviceString
                except:
                    monitor_name = device_name
                
                # 获取DPI信息
                try:
                    dpi = win32api.GetDeviceCaps(win32gui.GetDC(0), win32con.LOGPIXELSX)
                    scale_factor = dpi / 96.0
                except:
                    scale_factor = 1.0
                
                screen_info = ScreenInfo(
                    monitor_id=monitor_count,
                    x=monitor_rect[0],
                    y=monitor_rect[1],
                    width=monitor_rect[2] - monitor_rect[0],
                    height=monitor_rect[3] - monitor_rect[1],
                    is_primary=monitor_info['Flags'] & win32con.MONITORINFOF_PRIMARY != 0,
                    scale_factor=scale_factor,
                    name=monitor_name
                )
                
                self.monitors.append(screen_info)
                monitor_count += 1
                
            except Exception as e:
                self.logger.error(f"处理显示器信息失败: {e}")
            
            return True
        
        win32api.EnumDisplayMonitors(None, None, enum_callback, None)
    
    def _detect_monitors_fallback(self) -> None:
        """回退检测方法"""
        try:
            from PIL import ImageGrab
            
            # 获取主显示器尺寸
            with ImageGrab.grab() as img:
                screen_info = ScreenInfo(
                    monitor_id=0,
                    x=0,
                    y=0,
                    width=img.width,
                    height=img.height,
                    is_primary=True,
                    scale_factor=1.0,
                    name="Primary Monitor"
                )
                self.monitors.append(screen_info)
                
        except Exception as e:
            self.logger.error(f"回退检测失败: {e}")
            self._create_fallback_monitor()
    
    def _create_fallback_monitor(self) -> None:
        """创建回退显示器"""
        fallback_monitor = ScreenInfo(
            monitor_id=0,
            x=0,
            y=0,
            width=1920,
            height=1080,
            is_primary=True,
            scale_factor=1.0,
            name="Fallback Monitor"
        )
        self.monitors = [fallback_monitor]
        self.logger.warning("使用回退显示器配置")
    
    def _calculate_virtual_screen_bounds(self) -> None:
        """计算虚拟屏幕边界"""
        if not self.monitors:
            self.virtual_screen_bounds = None
            return
        
        min_x = min(monitor.x for monitor in self.monitors)
        min_y = min(monitor.y for monitor in self.monitors)
        max_x = max(monitor.x + monitor.width for monitor in self.monitors)
        max_y = max(monitor.y + monitor.height for monitor in self.monitors)
        
        self.virtual_screen_bounds = (min_x, min_y, max_x - min_x, max_y - min_y)
    
    def get_monitor_by_id(self, monitor_id: int) -> Optional[ScreenInfo]:
        """
        根据ID获取显示器
        
        Args:
            monitor_id: 显示器ID
            
        Returns:
            Optional[ScreenInfo]: 显示器信息
        """
        for monitor in self.monitors:
            if monitor.monitor_id == monitor_id:
                return monitor
        return None
    
    def get_primary_monitor(self) -> Optional[ScreenInfo]:
        """
        获取主显示器
        
        Returns:
            Optional[ScreenInfo]: 主显示器信息
        """
        for monitor in self.monitors:
            if monitor.is_primary:
                return monitor
        
        # 如果没有找到主显示器，返回第一个
        return self.monitors[0] if self.monitors else None
    
    def get_monitor_at_point(self, x: int, y: int) -> Optional[ScreenInfo]:
        """
        获取包含指定点的显示器
        
        Args:
            x: X坐标
            y: Y坐标
            
        Returns:
            Optional[ScreenInfo]: 显示器信息
        """
        for monitor in self.monitors:
            if (monitor.x <= x < monitor.x + monitor.width and
                monitor.y <= y < monitor.y + monitor.height):
                return monitor
        return None
    
    def get_monitor_containing_rect(self, x: int, y: int, width: int, height: int) -> Optional[ScreenInfo]:
        """
        获取包含指定矩形的显示器
        
        Args:
            x: 矩形左上角X坐标
            y: 矩形左上角Y坐标
            width: 矩形宽度
            height: 矩形高度
            
        Returns:
            Optional[ScreenInfo]: 显示器信息
        """
        rect_right = x + width
        rect_bottom = y + height
        
        best_monitor = None
        max_overlap = 0
        
        for monitor in self.monitors:
            # 计算重叠区域
            overlap_left = max(x, monitor.x)
            overlap_top = max(y, monitor.y)
            overlap_right = min(rect_right, monitor.x + monitor.width)
            overlap_bottom = min(rect_bottom, monitor.y + monitor.height)
            
            if overlap_left < overlap_right and overlap_top < overlap_bottom:
                overlap_area = (overlap_right - overlap_left) * (overlap_bottom - overlap_top)
                if overlap_area > max_overlap:
                    max_overlap = overlap_area
                    best_monitor = monitor
        
        return best_monitor
    
    def get_virtual_screen_bounds(self) -> Optional[Tuple[int, int, int, int]]:
        """
        获取虚拟屏幕边界
        
        Returns:
            Optional[Tuple[int, int, int, int]]: (x, y, width, height)
        """
        return self.virtual_screen_bounds
    
    def normalize_coordinates(self, x: int, y: int, from_monitor_id: Optional[int] = None) -> Tuple[int, int]:
        """
        标准化坐标到虚拟屏幕坐标系
        
        Args:
            x: X坐标
            y: Y坐标
            from_monitor_id: 源显示器ID，None表示已经是虚拟坐标
            
        Returns:
            Tuple[int, int]: 标准化后的坐标
        """
        if from_monitor_id is None:
            return x, y
        
        monitor = self.get_monitor_by_id(from_monitor_id)
        if monitor is None:
            return x, y
        
        return x + monitor.x, y + monitor.y
    
    def denormalize_coordinates(self, x: int, y: int, to_monitor_id: int) -> Tuple[int, int]:
        """
        将虚拟屏幕坐标转换为显示器本地坐标
        
        Args:
            x: 虚拟屏幕X坐标
            y: 虚拟屏幕Y坐标
            to_monitor_id: 目标显示器ID
            
        Returns:
            Tuple[int, int]: 本地坐标
        """
        monitor = self.get_monitor_by_id(to_monitor_id)
        if monitor is None:
            return x, y
        
        return x - monitor.x, y - monitor.y
    
    def get_monitor_stats(self) -> Dict[str, Any]:
        """
        获取显示器统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        if not self.monitors:
            return {}
        
        total_area = sum(monitor.width * monitor.height for monitor in self.monitors)
        primary_monitor = self.get_primary_monitor()
        
        return {
            "monitor_count": len(self.monitors),
            "primary_monitor_id": primary_monitor.monitor_id if primary_monitor else None,
            "total_screen_area": total_area,
            "virtual_screen_bounds": self.virtual_screen_bounds,
            "monitors": [monitor.to_dict() for monitor in self.monitors],
            "last_detection_time": self.last_detection_time
        }
    
    def is_point_visible(self, x: int, y: int) -> bool:
        """
        检查点是否在可见屏幕区域内
        
        Args:
            x: X坐标
            y: Y坐标
            
        Returns:
            bool: 是否可见
        """
        return self.get_monitor_at_point(x, y) is not None
    
    def clamp_to_visible_area(self, x: int, y: int, width: int, height: int) -> Tuple[int, int, int, int]:
        """
        将矩形限制在可见区域内
        
        Args:
            x: 矩形X坐标
            y: 矩形Y坐标
            width: 矩形宽度
            height: 矩形高度
            
        Returns:
            Tuple[int, int, int, int]: 调整后的矩形
        """
        if not self.virtual_screen_bounds:
            return x, y, width, height
        
        vx, vy, vw, vh = self.virtual_screen_bounds
        
        # 限制位置
        x = max(vx, min(x, vx + vw - width))
        y = max(vy, min(y, vy + vh - height))
        
        # 限制尺寸
        width = min(width, vx + vw - x)
        height = min(height, vy + vh - y)
        
        return x, y, width, height
