#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
优化的连接管理器
根据应用程序类型提供优化的连接策略
"""

import logging
import time
import threading
from typing import Dict, Optional, Tuple, Any
from pywinauto import Application
from pywinauto.application import AppStartError, ProcessNotFoundError
import win32gui
import win32con
from app_type_detector import ApplicationTypeDetector, AppType

class OptimizedConnectionManager:
    """优化的连接管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.detector = ApplicationTypeDetector()
        self.connection_cache = {}  # 连接缓存
        self.cache_lock = threading.Lock()
        self.max_cache_size = 20
        self.cache_timeout = 60  # 1分钟缓存超时
        
    def connect_to_window(self, window_handle: int) -> Tuple[Optional[Application], Optional[Any], AppType]:
        """
        连接到指定窗口
        
        Args:
            window_handle: 窗口句柄
            
        Returns:
            Tuple[Application, Window, AppType]: 应用程序对象、窗口对象、应用类型
        """
        try:
            # 检查缓存
            with self.cache_lock:
                cache_key = str(window_handle)
                if cache_key in self.connection_cache:
                    cached_time, app, window, app_type = self.connection_cache[cache_key]
                    if time.time() - cached_time < self.cache_timeout:
                        # 验证连接是否仍然有效
                        if self._validate_connection(app, window):
                            self.logger.debug(f"Using cached connection for window {window_handle}")
                            return app, window, app_type
                        else:
                            # 连接已失效，从缓存中移除
                            del self.connection_cache[cache_key]
            
            # 检测应用程序类型
            app_type = self.detector.detect_app_type(window_handle)
            self.logger.info(f"Detected app type: {app_type.value} for window {window_handle}")
            
            # 获取优化配置
            config = self.detector.get_optimization_config(app_type)
            
            # 根据应用类型选择连接策略
            app, window = self._connect_by_type(window_handle, app_type, config)
            
            if app and window:
                # 缓存连接
                with self.cache_lock:
                    self.connection_cache[cache_key] = (time.time(), app, window, app_type)
                    self._cleanup_cache()
                
                self.logger.info(f"Successfully connected to {app_type.value} application")
                return app, window, app_type
            else:
                self.logger.error(f"Failed to connect to window {window_handle}")
                return None, None, app_type
                
        except Exception as e:
            self.logger.error(f"Error connecting to window {window_handle}: {e}")
            return None, None, AppType.UNKNOWN
    
    def _connect_by_type(self, window_handle: int, app_type: AppType, config: Dict[str, Any]) -> Tuple[Optional[Application], Optional[Any]]:
        """根据应用程序类型连接"""
        
        if app_type == AppType.ELECTRON:
            return self._connect_electron_app(window_handle, config)
        elif app_type == AppType.WPF:
            return self._connect_wpf_app(window_handle, config)
        elif app_type == AppType.UWP:
            return self._connect_uwp_app(window_handle, config)
        elif app_type == AppType.CHROME:
            return self._connect_chrome_app(window_handle, config)
        else:
            return self._connect_standard_app(window_handle, config)
    
    def _connect_electron_app(self, window_handle: int, config: Dict[str, Any]) -> Tuple[Optional[Application], Optional[Any]]:
        """连接Electron应用程序"""
        try:
            self.logger.debug("Connecting to Electron application with special handling")
            
            # Electron应用程序特殊处理
            backends = [config['preferred_backend']] + config['fallback_backends']
            timeout = config['connection_timeout']
            
            for backend in backends:
                try:
                    self.logger.debug(f"Trying backend: {backend}")
                    
                    # 对于Electron应用，先检查窗口是否响应
                    if not self._check_window_responsiveness(window_handle, timeout=2):
                        self.logger.warning("Window appears unresponsive, trying anyway...")
                    
                    app = Application(backend=backend).connect(handle=window_handle, timeout=timeout)
                    window = app.window(handle=window_handle)
                    
                    # 验证连接
                    if window.exists(timeout=2):
                        self.logger.info(f"Successfully connected to Electron app using {backend}")
                        return app, window
                    
                except Exception as e:
                    self.logger.debug(f"Backend {backend} failed: {e}")
                    continue
            
            return None, None
            
        except Exception as e:
            self.logger.error(f"Error connecting to Electron app: {e}")
            return None, None
    
    def _connect_wpf_app(self, window_handle: int, config: Dict[str, Any]) -> Tuple[Optional[Application], Optional[Any]]:
        """连接WPF应用程序"""
        try:
            self.logger.debug("Connecting to WPF application")
            
            backend = config['preferred_backend']
            timeout = config['connection_timeout']
            
            app = Application(backend=backend).connect(handle=window_handle, timeout=timeout)
            window = app.window(handle=window_handle)
            
            if window.exists():
                return app, window
            
            return None, None
            
        except Exception as e:
            self.logger.error(f"Error connecting to WPF app: {e}")
            return None, None
    
    def _connect_uwp_app(self, window_handle: int, config: Dict[str, Any]) -> Tuple[Optional[Application], Optional[Any]]:
        """连接UWP应用程序"""
        try:
            self.logger.debug("Connecting to UWP application")
            
            # UWP应用程序需要特殊处理
            backend = config['preferred_backend']
            timeout = config['connection_timeout']
            
            # 先尝试通过句柄连接
            app = Application(backend=backend).connect(handle=window_handle, timeout=timeout)
            window = app.window(handle=window_handle)
            
            if window.exists():
                return app, window
            
            return None, None
            
        except Exception as e:
            self.logger.error(f"Error connecting to UWP app: {e}")
            return None, None
    
    def _connect_chrome_app(self, window_handle: int, config: Dict[str, Any]) -> Tuple[Optional[Application], Optional[Any]]:
        """连接Chrome类应用程序"""
        try:
            self.logger.debug("Connecting to Chrome-based application")
            
            backends = [config['preferred_backend']] + config['fallback_backends']
            timeout = config['connection_timeout']
            
            for backend in backends:
                try:
                    app = Application(backend=backend).connect(handle=window_handle, timeout=timeout)
                    window = app.window(handle=window_handle)
                    
                    if window.exists():
                        return app, window
                        
                except Exception as e:
                    self.logger.debug(f"Backend {backend} failed: {e}")
                    continue
            
            return None, None
            
        except Exception as e:
            self.logger.error(f"Error connecting to Chrome app: {e}")
            return None, None
    
    def _connect_standard_app(self, window_handle: int, config: Dict[str, Any]) -> Tuple[Optional[Application], Optional[Any]]:
        """连接标准应用程序"""
        try:
            self.logger.debug("Connecting to standard application")
            
            backends = [config['preferred_backend']] + config['fallback_backends']
            timeout = config['connection_timeout']
            
            for backend in backends:
                try:
                    app = Application(backend=backend).connect(handle=window_handle, timeout=timeout)
                    window = app.window(handle=window_handle)
                    
                    if window.exists():
                        return app, window
                        
                except Exception as e:
                    self.logger.debug(f"Backend {backend} failed: {e}")
                    continue
            
            return None, None
            
        except Exception as e:
            self.logger.error(f"Error connecting to standard app: {e}")
            return None, None
    
    def _check_window_responsiveness(self, window_handle: int, timeout: float = 2.0) -> bool:
        """检查窗口响应性"""
        try:
            # 发送一个简单的消息来检查窗口是否响应
            import win32api
            result = win32api.SendMessageTimeout(
                window_handle, 
                win32con.WM_NULL, 
                0, 0, 
                win32con.SMTO_ABORTIFHUNG, 
                int(timeout * 1000)
            )
            return result[0] != 0
        except:
            return False
    
    def _validate_connection(self, app: Application, window: Any) -> bool:
        """验证连接是否仍然有效"""
        try:
            return window.exists(timeout=1)
        except:
            return False
    
    def _cleanup_cache(self):
        """清理缓存"""
        if len(self.connection_cache) > self.max_cache_size:
            # 移除最旧的缓存项
            oldest_key = min(self.connection_cache.keys(), 
                           key=lambda k: self.connection_cache[k][0])
            del self.connection_cache[oldest_key]
        
        # 移除过期的缓存项
        current_time = time.time()
        expired_keys = [
            key for key, (cached_time, _, _, _) in self.connection_cache.items()
            if current_time - cached_time > self.cache_timeout
        ]
        
        for key in expired_keys:
            del self.connection_cache[key]
    
    def clear_cache(self):
        """清空缓存"""
        with self.cache_lock:
            self.connection_cache.clear()
            self.logger.info("Connection cache cleared")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self.cache_lock:
            return {
                'cache_size': len(self.connection_cache),
                'max_cache_size': self.max_cache_size,
                'cache_timeout': self.cache_timeout
            }

# 测试函数
def test_optimized_connection():
    """测试优化连接管理器"""
    manager = OptimizedConnectionManager()
    
    # 获取当前前台窗口进行测试
    hwnd = win32gui.GetForegroundWindow()
    if hwnd:
        print(f"Testing connection to window: {hwnd}")
        
        start_time = time.time()
        app, window, app_type = manager.connect_to_window(hwnd)
        end_time = time.time()
        
        if app and window:
            print(f"✅ Connection successful!")
            print(f"App Type: {app_type.value}")
            print(f"Connection Time: {end_time - start_time:.3f}s")
            print(f"Window Title: {window.window_text()}")
        else:
            print(f"❌ Connection failed")
        
        # 测试缓存
        print("\nTesting cache...")
        start_time = time.time()
        app2, window2, app_type2 = manager.connect_to_window(hwnd)
        end_time = time.time()
        
        if app2 and window2:
            print(f"✅ Cached connection successful!")
            print(f"Cached Connection Time: {end_time - start_time:.3f}s")
        
        # 显示缓存统计
        stats = manager.get_cache_stats()
        print(f"Cache Stats: {stats}")
    else:
        print("No foreground window found")

if __name__ == "__main__":
    test_optimized_connection()
