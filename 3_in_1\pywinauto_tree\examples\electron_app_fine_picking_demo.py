#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Electron应用精细控件拾取演示

专门演示在Electron应用（如Clash for Windows）中的精细控件拾取功能。
展示如何准确定位最小粒度的可交互控件。
"""

import asyncio
import time
import logging
import sys
import os

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from enhanced_control_picker import EnhancedControlPicker, ControlContext
from fine_grained_control_detector import (
    FineGrainedControlDetector, ControlCandidate, ControlInteractivity
)
from unittest.mock import Mock


def create_electron_app_mock():
    """创建模拟的Electron应用层次结构（类似Clash for Windows）"""
    
    # 主窗口
    main_window = Mock()
    main_window.rectangle.return_value = Mock(left=0, top=0, right=1200, bottom=800)
    main_window.control_type.return_value = "Window"
    main_window.automation_id.return_value = "main_window"
    main_window.class_name.return_value = "Chrome_WidgetWin_1"
    main_window.window_text.return_value = "Clash for Windows"
    main_window.is_enabled.return_value = True
    main_window.is_visible.return_value = True
    
    # 主容器
    main_container = Mock()
    main_container.rectangle.return_value = Mock(left=0, top=0, right=1200, bottom=800)
    main_container.control_type.return_value = "Pane"
    main_container.automation_id.return_value = "main_container"
    main_container.class_name.return_value = "Chrome_RenderWidgetHostHWND"
    main_container.window_text.return_value = ""
    main_container.is_enabled.return_value = True
    main_container.is_visible.return_value = True
    
    # 标题栏
    title_bar = Mock()
    title_bar.rectangle.return_value = Mock(left=0, top=0, right=1200, bottom=40)
    title_bar.control_type.return_value = "TitleBar"
    title_bar.automation_id.return_value = "title_bar"
    title_bar.class_name.return_value = "TitleBarClass"
    title_bar.window_text.return_value = ""
    title_bar.is_enabled.return_value = True
    title_bar.is_visible.return_value = True
    
    # 最小化按钮
    minimize_btn = Mock()
    minimize_btn.rectangle.return_value = Mock(left=1080, top=5, right=1120, bottom=35)
    minimize_btn.control_type.return_value = "Button"
    minimize_btn.automation_id.return_value = "minimize_btn"
    minimize_btn.class_name.return_value = "MinimizeButton"
    minimize_btn.window_text.return_value = "−"
    minimize_btn.is_enabled.return_value = True
    minimize_btn.is_visible.return_value = True
    minimize_btn.children.return_value = []
    
    # 关闭按钮
    close_btn = Mock()
    close_btn.rectangle.return_value = Mock(left=1160, top=5, right=1195, bottom=35)
    close_btn.control_type.return_value = "Button"
    close_btn.automation_id.return_value = "close_btn"
    close_btn.class_name.return_value = "CloseButton"
    close_btn.window_text.return_value = "×"
    close_btn.is_enabled.return_value = True
    close_btn.is_visible.return_value = True
    close_btn.children.return_value = []
    
    # 侧边栏
    sidebar = Mock()
    sidebar.rectangle.return_value = Mock(left=0, top=40, right=200, bottom=800)
    sidebar.control_type.return_value = "Pane"
    sidebar.automation_id.return_value = "sidebar"
    sidebar.class_name.return_value = "SidebarPane"
    sidebar.window_text.return_value = ""
    sidebar.is_enabled.return_value = True
    sidebar.is_visible.return_value = True
    
    # 代理模式按钮
    proxy_mode_btn = Mock()
    proxy_mode_btn.rectangle.return_value = Mock(left=20, top=100, right=180, bottom=140)
    proxy_mode_btn.control_type.return_value = "Button"
    proxy_mode_btn.automation_id.return_value = "proxy_mode_btn"
    proxy_mode_btn.class_name.return_value = "ProxyModeButton"
    proxy_mode_btn.window_text.return_value = "代理模式"
    proxy_mode_btn.is_enabled.return_value = True
    proxy_mode_btn.is_visible.return_value = True
    proxy_mode_btn.children.return_value = []
    
    # 规则按钮
    rules_btn = Mock()
    rules_btn.rectangle.return_value = Mock(left=20, top=150, right=180, bottom=190)
    rules_btn.control_type.return_value = "Button"
    rules_btn.automation_id.return_value = "rules_btn"
    rules_btn.class_name.return_value = "RulesButton"
    rules_btn.window_text.return_value = "规则"
    rules_btn.is_enabled.return_value = True
    rules_btn.is_visible.return_value = True
    rules_btn.children.return_value = []
    
    # 主内容区域
    content_area = Mock()
    content_area.rectangle.return_value = Mock(left=200, top=40, right=1200, bottom=800)
    content_area.control_type.return_value = "Pane"
    content_area.automation_id.return_value = "content_area"
    content_area.class_name.return_value = "ContentPane"
    content_area.window_text.return_value = ""
    content_area.is_enabled.return_value = True
    content_area.is_visible.return_value = True
    
    # 终端控件（TermControl）
    term_control = Mock()
    term_control.rectangle.return_value = Mock(left=220, top=60, right=1180, bottom=780)
    term_control.control_type.return_value = "Edit"
    term_control.automation_id.return_value = "TermControl"
    term_control.class_name.return_value = "TerminalControl"
    term_control.window_text.return_value = ""
    term_control.is_enabled.return_value = True
    term_control.is_visible.return_value = True
    term_control.children.return_value = []
    
    # 设置层次关系
    title_bar.children.return_value = [minimize_btn, close_btn]
    sidebar.children.return_value = [proxy_mode_btn, rules_btn]
    content_area.children.return_value = [term_control]
    main_container.children.return_value = [title_bar, sidebar, content_area]
    main_window.children.return_value = [main_container]
    
    return {
        'main_window': main_window,
        'main_container': main_container,
        'title_bar': title_bar,
        'minimize_btn': minimize_btn,
        'close_btn': close_btn,
        'sidebar': sidebar,
        'proxy_mode_btn': proxy_mode_btn,
        'rules_btn': rules_btn,
        'content_area': content_area,
        'term_control': term_control
    }


async def demo_fine_grained_detection():
    """演示精细控件检测"""
    print("🔍 Electron应用精细控件检测演示")
    print("=" * 60)
    
    # 创建模拟的Electron应用
    app_mocks = create_electron_app_mock()
    main_window = app_mocks['main_window']
    
    # 创建精细控件检测器
    detector = FineGrainedControlDetector()
    
    # 测试不同位置的控件检测
    test_positions = [
        (1177, 20, "关闭按钮"),      # 关闭按钮位置
        (1100, 20, "最小化按钮"),    # 最小化按钮位置
        (100, 120, "代理模式按钮"),  # 代理模式按钮位置
        (100, 170, "规则按钮"),      # 规则按钮位置
        (700, 400, "终端控件"),      # 终端控件位置
        (600, 20, "标题栏"),         # 标题栏位置（非交互区域）
    ]
    
    print("测试不同位置的精细控件检测:")
    print("-" * 60)
    
    for x, y, description in test_positions:
        print(f"\n📍 测试位置: ({x}, {y}) - {description}")
        
        start_time = time.time()
        result = detector.find_finest_control_at_position(main_window, x, y)
        detection_time = time.time() - start_time
        
        if result:
            print(f"  ✅ 检测成功 (用时: {detection_time*1000:.2f}ms)")
            print(f"     控件类型: {result.control_type}")
            print(f"     AutomationId: {result.automation_id}")
            print(f"     类名: {result.class_name}")
            print(f"     标题: {result.title}")
            print(f"     面积: {result.area} 像素")
            print(f"     层次深度: {result.hierarchy_depth}")
            print(f"     距离鼠标: {result.distance_from_mouse:.1f} 像素")
            print(f"     交互性评分: {result.interactivity_score}")
            print(f"     总评分: {result.total_score:.2f}")
            
            # 分析检测质量
            if result.control_type in ['Button', 'Edit']:
                print(f"     🎯 检测质量: 优秀 (找到了具体的交互控件)")
            elif result.control_type in ['TitleBar', 'ToolBar']:
                print(f"     ⚠️ 检测质量: 一般 (找到了容器控件)")
            else:
                print(f"     ❓ 检测质量: 需要改进")
        else:
            print(f"  ❌ 检测失败 (用时: {detection_time*1000:.2f}ms)")


async def demo_enhanced_picker_integration():
    """演示增强控件拾取器集成"""
    print("\n🚀 增强控件拾取器集成演示")
    print("=" * 60)
    
    # 创建增强控件拾取器
    picker = EnhancedControlPicker()
    
    # 模拟控件拾取过程
    print("模拟控件拾取过程:")
    
    # 创建模拟上下文
    context = ControlContext(
        hwnd=12345,
        position=(1177, 20),  # 关闭按钮位置
        app_type="electron"
    )
    
    # 模拟窗口
    app_mocks = create_electron_app_mock()
    main_window = app_mocks['main_window']
    
    try:
        # 提取控件信息
        control_info = await picker._extract_control_info(main_window, context)
        
        if control_info:
            print(f"  ✅ 控件信息提取成功:")
            print(f"     控件类型: {control_info.get('control_type', 'Unknown')}")
            print(f"     AutomationId: {control_info.get('automation_id', 'None')}")
            print(f"     类名: {control_info.get('class_name', 'None')}")
            print(f"     标题: {control_info.get('title', 'None')}")
            
            # 新增的精细信息
            if 'area' in control_info:
                print(f"     面积: {control_info['area']} 像素")
                print(f"     层次深度: {control_info['hierarchy_depth']}")
                print(f"     距离鼠标: {control_info['distance_from_mouse']:.1f} 像素")
                print(f"     交互性评分: {control_info['interactivity_score']}")
                print(f"     总评分: {control_info['total_score']:.2f}")
                print(f"     是否启用: {control_info['is_enabled']}")
                print(f"     是否可见: {control_info['is_visible']}")
        else:
            print(f"  ❌ 控件信息提取失败")
    
    except Exception as e:
        print(f"  ❌ 提取过程中发生错误: {e}")
    
    # 显示性能统计
    stats = picker.get_performance_stats()
    print(f"\n📊 性能统计:")
    print(f"     精细检测次数: {stats['fine_grained_picks']}")
    print(f"     平均层次深度: {stats['hierarchy_depth_avg']:.2f}")


async def demo_comparison_with_traditional():
    """演示与传统方法的对比"""
    print("\n📊 精细检测 vs 传统检测对比")
    print("=" * 60)
    
    app_mocks = create_electron_app_mock()
    main_window = app_mocks['main_window']
    
    # 测试位置：关闭按钮
    test_x, test_y = 1177, 20
    
    print(f"测试位置: ({test_x}, {test_y}) - 关闭按钮区域")
    
    # 1. 精细检测
    print("\n🎯 精细检测结果:")
    detector = FineGrainedControlDetector()
    
    start_time = time.time()
    fine_result = detector.find_finest_control_at_position(main_window, test_x, test_y)
    fine_time = time.time() - start_time
    
    if fine_result:
        print(f"  控件类型: {fine_result.control_type}")
        print(f"  AutomationId: {fine_result.automation_id}")
        print(f"  面积: {fine_result.area} 像素")
        print(f"  层次深度: {fine_result.hierarchy_depth}")
        print(f"  交互性评分: {fine_result.interactivity_score}")
        print(f"  检测时间: {fine_time*1000:.2f}ms")
    
    # 2. 模拟传统检测（简单的best_match）
    print("\n🔍 传统检测结果:")
    
    start_time = time.time()
    # 模拟传统方法只能找到大容器
    traditional_result = app_mocks['main_container']  # 通常只能找到主容器
    traditional_time = time.time() - start_time
    
    print(f"  控件类型: {traditional_result.control_type()}")
    print(f"  AutomationId: {traditional_result.automation_id()}")
    print(f"  面积: {1200 * 800} 像素 (整个窗口)")
    print(f"  层次深度: 1")
    print(f"  交互性评分: 40 (容器)")
    print(f"  检测时间: {traditional_time*1000:.2f}ms")
    
    # 3. 对比分析
    print("\n📈 对比分析:")
    if fine_result:
        area_improvement = (1200 * 800) / fine_result.area
        specificity_improvement = fine_result.hierarchy_depth
        interactivity_improvement = fine_result.interactivity_score / 40
        
        print(f"  面积精确度提升: {area_improvement:.1f}x")
        print(f"  层次深度提升: {specificity_improvement}x")
        print(f"  交互性评分提升: {interactivity_improvement:.1f}x")
        print(f"  检测时间对比: 精细检测 {fine_time*1000:.2f}ms vs 传统检测 {traditional_time*1000:.2f}ms")


async def main():
    """主演示函数"""
    print("🎮 Electron应用精细控件拾取演示")
    print("专门针对Clash for Windows等Electron应用优化")
    print("=" * 80)
    
    # 配置日志
    logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
    
    try:
        await demo_fine_grained_detection()
        await demo_enhanced_picker_integration()
        await demo_comparison_with_traditional()
        
        print("\n🎉 演示完成！")
        print("\n📋 精细控件检测的优势:")
        print("  ✅ 准确定位最小粒度的可交互控件")
        print("  ✅ 智能区分容器和具体控件")
        print("  ✅ 基于交互性评分选择最佳控件")
        print("  ✅ 支持复杂的Electron应用层次结构")
        print("  ✅ 提供详细的控件分析信息")
        
        print("\n🎯 特别适用于:")
        print("  • Electron应用 (如Clash for Windows)")
        print("  • 复杂的Web应用")
        print("  • 多层嵌套的UI结构")
        print("  • 需要精确控件定位的自动化场景")
        
    except Exception as e:
        print(f"\n💥 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
