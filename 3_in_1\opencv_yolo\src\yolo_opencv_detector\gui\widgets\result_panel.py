# -*- coding: utf-8 -*-
"""
结果面板组件
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

from typing import List, Dict, Any, Optional
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, QTableWidget,
    QTableWidgetItem, QPushButton, QLabel, QComboBox, QCheckBox,
    QHeaderView, QAbstractItemView, QMenu, QFileDialog, QMessageBox
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QAction, QColor

from ...utils.logger import Logger
from ...utils.config_manager import ConfigManager


class ResultPanel(QWidget):
    """结果面板类"""
    
    # 信号定义
    result_selected = pyqtSignal(str)    # 结果选择
    result_exported = pyqtSignal(str)    # 结果导出
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化结果面板
        
        Args:
            config_manager: 配置管理器
        """
        super().__init__()
        
        self.logger = Logger().get_logger(__name__)
        self.config_manager = config_manager
        
        # 组件初始化
        self.results_table = None
        self.filter_combo = None
        self.show_yolo_checkbox = None
        self.show_template_checkbox = None
        self.show_fusion_checkbox = None
        self.clear_button = None
        self.export_button = None
        self.stats_label = None
        
        # 数据存储
        self.all_results: List[Dict[str, Any]] = []
        self.filtered_results: List[Dict[str, Any]] = []
        
        # 初始化界面
        self._init_ui()
        self._init_connections()
        
        self.logger.info("结果面板初始化完成")
    
    def _init_ui(self) -> None:
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        
        # 过滤控制组
        filter_group = QGroupBox("结果过滤")
        filter_layout = QVBoxLayout(filter_group)
        
        # 过滤器行
        filter_row = QHBoxLayout()
        
        type_label = QLabel("类型:")
        type_label.setToolTip("选择结果过滤类型\n"
                             "全部：显示所有检测结果\n"
                             "高置信度：只显示高质量检测\n"
                             "低置信度：显示可疑检测结果\n"
                             "最近检测：显示最新的检测")
        filter_row.addWidget(type_label)

        self.filter_combo = QComboBox()
        self.filter_combo.addItems(["全部", "高置信度", "低置信度", "最近检测"])
        self.filter_combo.setToolTip("过滤检测结果显示\n"
                                    "可以按置信度或时间筛选\n"
                                    "便于查看特定类型的结果")
        filter_row.addWidget(self.filter_combo)
        
        filter_layout.addLayout(filter_row)
        
        # 来源过滤行
        source_row = QHBoxLayout()
        
        self.show_yolo_checkbox = QCheckBox("YOLO")
        self.show_yolo_checkbox.setChecked(True)
        self.show_yolo_checkbox.setToolTip("显示YOLO检测结果\n"
                                          "基于深度学习的目标检测\n"
                                          "可检测多种常见物体")
        source_row.addWidget(self.show_yolo_checkbox)

        self.show_template_checkbox = QCheckBox("模板匹配")
        self.show_template_checkbox.setChecked(True)
        self.show_template_checkbox.setToolTip("显示模板匹配结果\n"
                                              "基于图像相似度的检测\n"
                                              "适合特定界面元素")
        source_row.addWidget(self.show_template_checkbox)

        self.show_fusion_checkbox = QCheckBox("融合结果")
        self.show_fusion_checkbox.setChecked(True)
        self.show_fusion_checkbox.setToolTip("显示融合检测结果\n"
                                            "结合YOLO和模板匹配\n"
                                            "提供更准确的检测")
        source_row.addWidget(self.show_fusion_checkbox)
        
        filter_layout.addLayout(source_row)
        
        layout.addWidget(filter_group)
        
        # 结果表格
        results_group = QGroupBox("检测结果")
        results_layout = QVBoxLayout(results_group)
        
        # 创建表格
        self.results_table = QTableWidget()
        self.results_table.setColumnCount(7)
        self.results_table.setHorizontalHeaderLabels([
            "时间", "来源", "类别", "置信度", "位置", "尺寸", "状态"
        ])
        self.results_table.setToolTip("检测结果详细列表\n"
                                     "点击行选择结果，右键显示菜单\n"
                                     "可以查看、复制、删除结果\n"
                                     "双击查看详细信息")

        # 设置表格属性
        self.results_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.results_table.setAlternatingRowColors(True)
        self.results_table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        
        # 设置列宽
        header = self.results_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)  # 时间
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)  # 来源
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)           # 类别
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)  # 置信度
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)  # 位置
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)  # 尺寸
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.ResizeToContents)  # 状态
        
        results_layout.addWidget(self.results_table)
        
        # 控制按钮行
        button_row = QHBoxLayout()
        
        self.clear_button = QPushButton("清除")
        self.clear_button.setToolTip("清除所有检测结果\n"
                                    "删除表格中的所有数据\n"
                                    "操作不可撤销，请谨慎使用")
        button_row.addWidget(self.clear_button)

        self.export_button = QPushButton("导出")
        self.export_button.setToolTip("导出结果到文件\n"
                                     "支持CSV、JSON格式\n"
                                     "包含所有检测详细信息\n"
                                     "便于数据分析和报告")
        button_row.addWidget(self.export_button)

        button_row.addStretch()

        # 统计标签
        self.stats_label = QLabel("总计: 0 个结果")
        self.stats_label.setToolTip("显示当前过滤条件下的结果数量\n"
                                   "实时更新统计信息\n"
                                   "包括总数和过滤后的数量")
        button_row.addWidget(self.stats_label)
        
        results_layout.addLayout(button_row)
        
        layout.addWidget(results_group)
    
    def _init_connections(self) -> None:
        """初始化信号连接"""
        # 过滤器连接
        self.filter_combo.currentTextChanged.connect(self._apply_filters)
        self.show_yolo_checkbox.toggled.connect(self._apply_filters)
        self.show_template_checkbox.toggled.connect(self._apply_filters)
        self.show_fusion_checkbox.toggled.connect(self._apply_filters)
        
        # 表格连接
        self.results_table.itemSelectionChanged.connect(self._on_selection_changed)
        self.results_table.customContextMenuRequested.connect(self._show_context_menu)
        
        # 按钮连接
        self.clear_button.clicked.connect(self._clear_results)
        self.export_button.clicked.connect(self._export_results)
    
    def add_results(self, results: List[Dict[str, Any]]) -> None:
        """
        添加检测结果
        
        Args:
            results: 检测结果列表
        """
        try:
            self.all_results.extend(results)
            self._apply_filters()
            self.logger.debug(f"添加了 {len(results)} 个检测结果")
            
        except Exception as e:
            self.logger.error(f"添加结果失败: {e}")
    
    def set_results(self, results: List[Dict[str, Any]]) -> None:
        """
        设置检测结果（替换现有结果）
        
        Args:
            results: 检测结果列表
        """
        try:
            self.all_results = results.copy()
            self._apply_filters()
            self.logger.debug(f"设置了 {len(results)} 个检测结果")
            
        except Exception as e:
            self.logger.error(f"设置结果失败: {e}")
    
    def _apply_filters(self) -> None:
        """应用过滤器"""
        try:
            # 获取过滤条件
            filter_type = self.filter_combo.currentText()
            show_yolo = self.show_yolo_checkbox.isChecked()
            show_template = self.show_template_checkbox.isChecked()
            show_fusion = self.show_fusion_checkbox.isChecked()
            
            # 过滤结果
            self.filtered_results = []
            
            for result in self.all_results:
                # 来源过滤
                source = result.get("source", "").lower()
                if source == "yolo" and not show_yolo:
                    continue
                if source == "template" and not show_template:
                    continue
                if source == "fusion" and not show_fusion:
                    continue
                
                # 类型过滤
                confidence = result.get("confidence", 0.0)
                if filter_type == "高置信度" and confidence < 0.8:
                    continue
                if filter_type == "低置信度" and confidence >= 0.8:
                    continue
                if filter_type == "最近检测":
                    # 这里可以添加时间过滤逻辑
                    pass
                
                self.filtered_results.append(result)
            
            # 更新表格显示
            self._update_table()
            
        except Exception as e:
            self.logger.error(f"应用过滤器失败: {e}")
    
    def _update_table(self) -> None:
        """更新表格显示"""
        try:
            # 清空表格
            self.results_table.setRowCount(0)
            
            # 添加过滤后的结果
            for i, result in enumerate(self.filtered_results):
                self.results_table.insertRow(i)
                
                # 时间
                timestamp = result.get("timestamp", 0)
                time_str = self._format_timestamp(timestamp)
                self.results_table.setItem(i, 0, QTableWidgetItem(time_str))
                
                # 来源
                source = result.get("source", "unknown")
                source_item = QTableWidgetItem(source)
                source_item.setBackground(self._get_source_color(source))
                self.results_table.setItem(i, 1, source_item)
                
                # 类别
                class_name = result.get("class_name", "未知")
                self.results_table.setItem(i, 2, QTableWidgetItem(class_name))
                
                # 置信度
                confidence = result.get("confidence", 0.0)
                confidence_item = QTableWidgetItem(f"{confidence:.3f}")
                confidence_item.setBackground(self._get_confidence_color(confidence))
                self.results_table.setItem(i, 3, confidence_item)
                
                # 位置
                bbox = result.get("bbox", {})
                x = bbox.get("x", 0)
                y = bbox.get("y", 0)
                position_str = f"({x}, {y})"
                self.results_table.setItem(i, 4, QTableWidgetItem(position_str))
                
                # 尺寸
                width = bbox.get("width", 0)
                height = bbox.get("height", 0)
                size_str = f"{width}×{height}"
                self.results_table.setItem(i, 5, QTableWidgetItem(size_str))
                
                # 状态
                status = "正常"
                self.results_table.setItem(i, 6, QTableWidgetItem(status))
                
                # 存储结果ID
                self.results_table.item(i, 0).setData(Qt.ItemDataRole.UserRole, i)
            
            # 更新统计信息
            self._update_stats()
            
        except Exception as e:
            self.logger.error(f"更新表格失败: {e}")
    
    def _format_timestamp(self, timestamp: float) -> str:
        """格式化时间戳"""
        try:
            import datetime
            dt = datetime.datetime.fromtimestamp(timestamp)
            return dt.strftime("%H:%M:%S")
        except:
            return "未知"
    
    def _get_source_color(self, source: str) -> QColor:
        """获取来源颜色"""
        color_map = {
            "yolo": QColor(200, 255, 200),      # 浅绿色
            "template": QColor(255, 200, 200),   # 浅红色
            "fusion": QColor(200, 200, 255)     # 浅蓝色
        }
        return color_map.get(source.lower(), QColor(240, 240, 240))
    
    def _get_confidence_color(self, confidence: float) -> QColor:
        """获取置信度颜色"""
        if confidence >= 0.9:
            return QColor(144, 238, 144)  # 浅绿色
        elif confidence >= 0.7:
            return QColor(255, 255, 144)  # 浅黄色
        elif confidence >= 0.5:
            return QColor(255, 200, 144)  # 浅橙色
        else:
            return QColor(255, 144, 144)  # 浅红色
    
    def _update_stats(self) -> None:
        """更新统计信息"""
        try:
            total_count = len(self.filtered_results)
            
            # 计算各来源数量
            yolo_count = sum(1 for r in self.filtered_results if r.get("source", "").lower() == "yolo")
            template_count = sum(1 for r in self.filtered_results if r.get("source", "").lower() == "template")
            fusion_count = sum(1 for r in self.filtered_results if r.get("source", "").lower() == "fusion")
            
            stats_text = f"总计: {total_count} 个结果 (YOLO: {yolo_count}, 模板: {template_count}, 融合: {fusion_count})"
            self.stats_label.setText(stats_text)
            
        except Exception as e:
            self.logger.error(f"更新统计失败: {e}")
    
    def _on_selection_changed(self) -> None:
        """选择变更处理"""
        try:
            current_row = self.results_table.currentRow()
            if current_row >= 0:
                result_index = self.results_table.item(current_row, 0).data(Qt.ItemDataRole.UserRole)
                if result_index is not None and result_index < len(self.filtered_results):
                    result = self.filtered_results[result_index]
                    result_id = str(result_index)
                    self.result_selected.emit(result_id)
                    
        except Exception as e:
            self.logger.error(f"选择处理失败: {e}")
    
    def _show_context_menu(self, position) -> None:
        """显示右键菜单"""
        try:
            if self.results_table.itemAt(position):
                menu = QMenu(self)
                
                # 查看详情
                detail_action = QAction("查看详情", self)
                detail_action.triggered.connect(self._show_result_detail)
                menu.addAction(detail_action)
                
                # 复制信息
                copy_action = QAction("复制信息", self)
                copy_action.triggered.connect(self._copy_result_info)
                menu.addAction(copy_action)
                
                menu.addSeparator()
                
                # 删除结果
                delete_action = QAction("删除", self)
                delete_action.triggered.connect(self._delete_result)
                menu.addAction(delete_action)
                
                menu.exec(self.results_table.mapToGlobal(position))
                
        except Exception as e:
            self.logger.error(f"显示右键菜单失败: {e}")
    
    def _show_result_detail(self) -> None:
        """显示结果详情"""
        try:
            current_row = self.results_table.currentRow()
            if current_row >= 0:
                result_index = self.results_table.item(current_row, 0).data(Qt.ItemDataRole.UserRole)
                if result_index is not None and result_index < len(self.filtered_results):
                    result = self.filtered_results[result_index]
                    
                    # 格式化详情信息
                    detail_text = self._format_result_detail(result)
                    QMessageBox.information(self, "结果详情", detail_text)
                    
        except Exception as e:
            self.logger.error(f"显示结果详情失败: {e}")
    
    def _format_result_detail(self, result: Dict[str, Any]) -> str:
        """格式化结果详情"""
        try:
            lines = []
            lines.append(f"来源: {result.get('source', '未知')}")
            lines.append(f"类别: {result.get('class_name', '未知')}")
            lines.append(f"置信度: {result.get('confidence', 0.0):.4f}")
            
            bbox = result.get('bbox', {})
            lines.append(f"位置: ({bbox.get('x', 0)}, {bbox.get('y', 0)})")
            lines.append(f"尺寸: {bbox.get('width', 0)} × {bbox.get('height', 0)}")
            
            timestamp = result.get('timestamp', 0)
            if timestamp:
                import datetime
                dt = datetime.datetime.fromtimestamp(timestamp)
                lines.append(f"时间: {dt.strftime('%Y-%m-%d %H:%M:%S')}")
            
            metadata = result.get('metadata', {})
            if metadata:
                lines.append("\n元数据:")
                for key, value in metadata.items():
                    lines.append(f"  {key}: {value}")
            
            return "\n".join(lines)
            
        except Exception as e:
            self.logger.error(f"格式化结果详情失败: {e}")
            return "详情格式化失败"
    
    def _copy_result_info(self) -> None:
        """复制结果信息"""
        try:
            current_row = self.results_table.currentRow()
            if current_row >= 0:
                result_index = self.results_table.item(current_row, 0).data(Qt.ItemDataRole.UserRole)
                if result_index is not None and result_index < len(self.filtered_results):
                    result = self.filtered_results[result_index]
                    detail_text = self._format_result_detail(result)
                    
                    from PyQt6.QtWidgets import QApplication
                    clipboard = QApplication.clipboard()
                    clipboard.setText(detail_text)
                    
                    self.logger.info("结果信息已复制到剪贴板")
                    
        except Exception as e:
            self.logger.error(f"复制结果信息失败: {e}")
    
    def _delete_result(self) -> None:
        """删除结果"""
        try:
            current_row = self.results_table.currentRow()
            if current_row >= 0:
                result_index = self.results_table.item(current_row, 0).data(Qt.ItemDataRole.UserRole)
                if result_index is not None and result_index < len(self.filtered_results):
                    # 从过滤结果中删除
                    deleted_result = self.filtered_results.pop(result_index)
                    
                    # 从全部结果中删除
                    if deleted_result in self.all_results:
                        self.all_results.remove(deleted_result)
                    
                    # 更新表格
                    self._update_table()
                    
                    self.logger.info("结果已删除")
                    
        except Exception as e:
            self.logger.error(f"删除结果失败: {e}")
    
    def _clear_results(self) -> None:
        """清除所有结果"""
        try:
            reply = QMessageBox.question(
                self, "确认清除",
                "确定要清除所有检测结果吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                self.all_results.clear()
                self.filtered_results.clear()
                self._update_table()
                self.logger.info("所有结果已清除")
                
        except Exception as e:
            self.logger.error(f"清除结果失败: {e}")
    
    def _export_results(self) -> None:
        """导出结果"""
        try:
            if not self.filtered_results:
                QMessageBox.information(self, "提示", "没有结果可导出")
                return
            
            # 选择导出文件
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出检测结果", "",
                "JSON文件 (*.json);;CSV文件 (*.csv);;所有文件 (*)"
            )
            
            if file_path:
                # 这里应该调用结果处理器导出结果
                self.result_exported.emit(file_path)
                self.logger.info(f"结果导出到: {file_path}")
                QMessageBox.information(self, "成功", "结果导出成功")
                
        except Exception as e:
            self.logger.error(f"导出结果失败: {e}")
            QMessageBox.warning(self, "错误", f"导出结果失败: {e}")
    
    def get_selected_result(self) -> Optional[Dict[str, Any]]:
        """获取选中的结果"""
        try:
            current_row = self.results_table.currentRow()
            if current_row >= 0:
                result_index = self.results_table.item(current_row, 0).data(Qt.ItemDataRole.UserRole)
                if result_index is not None and result_index < len(self.filtered_results):
                    return self.filtered_results[result_index]
            return None
            
        except Exception as e:
            self.logger.error(f"获取选中结果失败: {e}")
            return None
