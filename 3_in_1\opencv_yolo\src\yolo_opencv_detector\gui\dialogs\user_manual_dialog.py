# -*- coding: utf-8 -*-
"""
用户手册对话框
显示详细的用户使用手册，支持Markdown格式渲染
"""

import sys
from pathlib import Path
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTextEdit, QPushButton,
    QLabel, QTabWidget, QWidget, QScrollArea, QSplitter,
    QMessageBox, QApplication
)
from PyQt6.QtCore import Qt, QUrl
from PyQt6.QtGui import QFont, QTextCursor, QDesktopServices

class UserManualDialog(QDialog):
    """用户手册对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("📖 用户使用手册")
        self.setGeometry(100, 100, 1000, 700)
        self.setWindowFlags(Qt.WindowType.Window | Qt.WindowType.WindowCloseButtonHint | Qt.WindowType.WindowMaximizeButtonHint)
        
        self.init_ui()
        self.load_manual_content()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 标题
        title_label = QLabel("📖 YOLO OpenCV检测器用户使用手册")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        layout.addWidget(title_label)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # 创建各个标签页
        self.create_overview_tab()
        self.create_installation_tab()
        self.create_gui_guide_tab()
        self.create_detection_modes_tab()
        self.create_examples_tab()
        self.create_faq_tab()
        
        # 按钮布局
        button_layout = QHBoxLayout()
        
        # 打开完整手册按钮
        self.open_full_manual_btn = QPushButton("📄 打开完整手册文件")
        self.open_full_manual_btn.clicked.connect(self.open_full_manual)
        self.open_full_manual_btn.setToolTip("在默认程序中打开完整的用户手册Markdown文件")
        button_layout.addWidget(self.open_full_manual_btn)
        
        button_layout.addStretch()
        
        # 关闭按钮
        self.close_btn = QPushButton("关闭")
        self.close_btn.clicked.connect(self.close)
        button_layout.addWidget(self.close_btn)
        
        layout.addLayout(button_layout)
    
    def create_overview_tab(self):
        """创建概览标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        content = QTextEdit()
        content.setReadOnly(True)
        content.setHtml("""
        <h2>🎯 工具简介</h2>
        <p>YOLO OpenCV检测器是一款基于人工智能的智能界面检测工具，专为Windows界面自动化而设计。</p>
        
        <h3>🌟 主要功能特点</h3>
        <ul>
            <li><strong>🔍 智能检测</strong>: 基于YOLOv8模型的实时界面元素检测</li>
            <li><strong>📋 模板匹配</strong>: 传统模板匹配技术，适用于特定界面元素</li>
            <li><strong>🎯 结果融合</strong>: 智能融合多种检测结果，提高准确性</li>
            <li><strong>📷 实时截图</strong>: 高性能屏幕捕获和实时显示</li>
            <li><strong>🤖 自动化操作</strong>: 完整的鼠标键盘自动化操作框架</li>
            <li><strong>📚 使用示例</strong>: 丰富的代码示例和最佳实践指南</li>
        </ul>
        
        <h3>🎨 应用场景</h3>
        <ul>
            <li><strong>办公自动化</strong>: Excel、Word、PowerPoint等办公软件操作</li>
            <li><strong>软件测试</strong>: GUI自动化测试和回归测试</li>
            <li><strong>游戏辅助</strong>: 游戏界面元素识别和自动化操作</li>
            <li><strong>数据录入</strong>: 批量数据处理和表单填写</li>
            <li><strong>界面监控</strong>: 实时监控界面变化和状态</li>
        </ul>
        """)
        
        layout.addWidget(content)
        self.tab_widget.addTab(tab, "🎯 工具概览")
    
    def create_installation_tab(self):
        """创建安装指南标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        content = QTextEdit()
        content.setReadOnly(True)
        content.setFont(QFont("Consolas", 10))
        content.setHtml("""
        <h2>🚀 安装和环境配置</h2>
        
        <h3>📋 系统要求</h3>
        <ul>
            <li><strong>操作系统</strong>: Windows 10/11 (64位)</li>
            <li><strong>Python版本</strong>: Python 3.8+</li>
            <li><strong>内存</strong>: 建议8GB以上</li>
            <li><strong>显卡</strong>: 支持CUDA的NVIDIA显卡（可选）</li>
            <li><strong>磁盘空间</strong>: 至少2GB可用空间</li>
        </ul>
        
        <h3>🔧 安装步骤</h3>
        <h4>1. 环境准备</h4>
        <pre style="background-color: #f8f9fa; padding: 10px; border-radius: 5px;">
# 1. 创建虚拟环境
python -m venv venv

# 2. 激活虚拟环境 (Windows)
venv\\Scripts\\activate
# 或直接运行：
activate_env.bat
        </pre>
        
        <h4>2. 依赖安装</h4>
        <pre style="background-color: #f8f9fa; padding: 10px; border-radius: 5px;">
# 升级pip
python -m pip install --upgrade pip

# 安装项目依赖
pip install -r requirements.txt
        </pre>
        
        <h4>3. 模型下载</h4>
        <pre style="background-color: #f8f9fa; padding: 10px; border-radius: 5px;">
# 下载YOLO模型文件
python download_yolo_models.py
        </pre>
        
        <h4>4. 启动应用</h4>
        <pre style="background-color: #f8f9fa; padding: 10px; border-radius: 5px;">
# 方式1：使用批处理文件
start_yolo_detector.bat

# 方式2：使用Python命令
python -m yolo_opencv_detector.main

# 方式3：快速启动
quick_start.bat
        </pre>
        """)
        
        layout.addWidget(content)
        self.tab_widget.addTab(tab, "🚀 安装指南")
    
    def create_gui_guide_tab(self):
        """创建GUI指南标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        content = QTextEdit()
        content.setReadOnly(True)
        content.setHtml("""
        <h2>🖥️ GUI界面详细说明</h2>
        
        <h3>📐 界面布局概览</h3>
        <p>YOLO OpenCV检测器采用三面板布局设计：</p>
        <ul>
            <li><strong>左侧面板</strong>: 检测控制、模板管理</li>
            <li><strong>中央面板</strong>: 实时截图显示、检测结果标注</li>
            <li><strong>右侧面板</strong>: 配置设置、状态监控</li>
        </ul>
        
        <h3>🎮 左侧面板：检测控制</h3>
        <h4>🎯 检测标签页</h4>
        <ul>
            <li><strong>检测模式选择</strong>: YOLO检测、模板匹配、融合模式</li>
            <li><strong>检测控制按钮</strong>: 开始/停止检测、单次截图、重置设置</li>
            <li><strong>检测设置</strong>: 置信度阈值、检测间隔、最大检测数</li>
        </ul>
        
        <h4>📁 模板标签页</h4>
        <ul>
            <li><strong>模板管理</strong>: 添加、编辑、删除、预览模板</li>
            <li><strong>模板创建</strong>: 截图选择、参数调整、文件保存</li>
        </ul>
        
        <h3>📸 中央面板：截图显示</h3>
        <ul>
            <li><strong>实时显示</strong>: 高清截图、检测标注、交互操作</li>
            <li><strong>图像交互</strong>: 缩放控制、平移操作、区域选择</li>
            <li><strong>检测结果</strong>: 边界框、置信度、类别标签、坐标信息</li>
        </ul>
        
        <h3>⚙️ 右侧面板：配置和状态</h3>
        <h4>⚙️ 配置标签页</h4>
        <ul>
            <li><strong>YOLO配置</strong>: 模型选择、参数调整</li>
            <li><strong>模板匹配配置</strong>: 匹配方法、相似度阈值</li>
            <li><strong>结果融合配置</strong>: 融合策略、权重分配</li>
        </ul>
        
        <h4>📈 状态标签页</h4>
        <ul>
            <li><strong>系统监控</strong>: CPU、内存、GPU使用率</li>
            <li><strong>检测统计</strong>: 检测次数、成功率、平均耗时</li>
            <li><strong>性能图表</strong>: 实时性能曲线、资源使用图表</li>
        </ul>
        """)
        
        layout.addWidget(content)
        self.tab_widget.addTab(tab, "🖥️ GUI指南")
    
    def create_detection_modes_tab(self):
        """创建检测模式标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        content = QTextEdit()
        content.setReadOnly(True)
        content.setHtml("""
        <h2>🔍 检测模式使用方法</h2>
        
        <h3>🤖 YOLO检测模式</h3>
        <p>基于深度学习的智能检测方法，适用于复杂界面和多目标场景。</p>
        
        <h4>使用步骤</h4>
        <ol>
            <li>选择检测模式: 在左侧面板选择"YOLO检测"</li>
            <li>配置模型: 在右侧配置面板选择合适的YOLO模型</li>
            <li>调整参数: 设置置信度阈值和检测间隔</li>
            <li>开始检测: 点击"开始检测"按钮</li>
            <li>查看结果: 在中央面板查看检测结果</li>
        </ol>
        
        <h4>参数建议</h4>
        <ul>
            <li><strong>置信度阈值</strong>: 建议0.5-0.8，过低误检，过高漏检</li>
            <li><strong>检测间隔</strong>: 实时检测建议500-1000ms</li>
            <li><strong>模型选择</strong>: 实时应用推荐YOLOv8n，精度要求高推荐YOLOv8l</li>
        </ul>
        
        <h3>📋 模板匹配模式</h3>
        <p>基于图像相似度的传统检测方法，适用于固定界面元素的精确定位。</p>
        
        <h4>使用步骤</h4>
        <ol>
            <li>创建模板: 点击"添加模板"，选择目标区域</li>
            <li>配置参数: 设置相似度阈值和匹配方法</li>
            <li>执行匹配: 选择"模板匹配"模式，开始检测</li>
        </ol>
        
        <h4>模板创建最佳实践</h4>
        <ul>
            <li><strong>特征明显</strong>: 避免纯色或重复图案</li>
            <li><strong>适当尺寸</strong>: 建议50x50到200x200像素</li>
            <li><strong>避免变化</strong>: 不包含时间、数字等动态内容</li>
            <li><strong>高对比度</strong>: 选择与背景对比度高的区域</li>
        </ul>
        
        <h3>🎯 融合模式</h3>
        <p>结合YOLO检测和模板匹配的优势，提供更高的检测准确性。</p>
        
        <h4>配置建议</h4>
        <ul>
            <li><strong>权重分配</strong>: YOLO权重0.6，模板匹配权重0.4</li>
            <li><strong>去重阈值</strong>: 设置为50-100像素</li>
            <li><strong>置信度要求</strong>: 融合结果置信度建议>0.7</li>
        </ul>
        """)
        
        layout.addWidget(content)
        self.tab_widget.addTab(tab, "🔍 检测模式")
    
    def create_examples_tab(self):
        """创建使用示例标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        content = QTextEdit()
        content.setReadOnly(True)
        content.setHtml("""
        <h2>📚 源代码窗口和使用示例</h2>
        
        <h3>💻 源代码窗口功能</h3>
        <p>源代码窗口为开发者提供了完整的代码编辑和执行环境。</p>
        
        <h4>访问方式</h4>
        <ul>
            <li>通过菜单栏: "工具" → "源代码编辑器"</li>
            <li>使用快捷键: Ctrl+E</li>
            <li>点击工具栏的"📄 源代码"按钮</li>
        </ul>
        
        <h4>窗口组成</h4>
        <ul>
            <li><strong>🎯 GUI检测复制</strong>: 完整复制GUI检测方法的代码</li>
            <li><strong>📚 完整使用示例</strong>: 4个完整的自动化操作示例</li>
        </ul>
        
        <h3>📖 使用示例详解</h3>
        
        <h4>🏢 办公软件自动化示例</h4>
        <ul>
            <li><strong>功能特点</strong>: 智能目标选择、坐标处理、安全操作</li>
            <li><strong>应用场景</strong>: Excel录入、Word调整、浏览器填写</li>
            <li><strong>核心功能</strong>: 检测→选择→验证→操作</li>
        </ul>
        
        <h4>🎯 多目标操作示例</h4>
        <ul>
            <li><strong>功能特点</strong>: 多维度选择、复杂拖拽、协调操作</li>
            <li><strong>应用场景</strong>: 文件拖拽、多窗口协调、批量处理</li>
            <li><strong>核心功能</strong>: 多选择→坐标计算→拖拽操作</li>
        </ul>
        
        <h4>🔍 目标选择策略示例</h4>
        <ul>
            <li><strong>功能特点</strong>: 多种选择策略、自定义条件</li>
            <li><strong>选择方式</strong>: 置信度、位置、大小、类别、自定义</li>
            <li><strong>核心功能</strong>: 策略选择→条件筛选→结果输出</li>
        </ul>
        
        <h4>🛡️ 错误处理机制示例</h4>
        <ul>
            <li><strong>功能特点</strong>: 坐标验证、超时保护、异常处理</li>
            <li><strong>安全机制</strong>: 边界检查、重试机制、日志记录</li>
            <li><strong>核心功能</strong>: 验证→重试→恢复→记录</li>
        </ul>
        
        <h3>🚀 代码执行和调试</h3>
        <h4>运行示例代码</h4>
        <ol>
            <li>在源代码窗口选择相应的示例</li>
            <li>根据需要修改参数</li>
            <li>点击"▶️ 运行代码"按钮</li>
            <li>在输出窗口查看执行结果</li>
        </ol>
        
        <h4>调试技巧</h4>
        <ul>
            <li>使用print()语句输出调试信息</li>
            <li>设置合适的延迟时间观察操作过程</li>
            <li>逐步执行复杂的操作序列</li>
            <li>记录操作日志便于问题排查</li>
        </ul>
        """)
        
        layout.addWidget(content)
        self.tab_widget.addTab(tab, "📚 使用示例")
    
    def create_faq_tab(self):
        """创建常见问题标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        content = QTextEdit()
        content.setReadOnly(True)
        content.setHtml("""
        <h2>❓ 常见问题解答</h2>
        
        <h3>🔧 技术问题</h3>
        
        <h4>Q1: 检测结果不准确怎么办？</h4>
        <p><strong>A</strong>:</p>
        <ul>
            <li>调整置信度阈值，建议从0.5开始逐步调整</li>
            <li>尝试不同的YOLO模型，复杂场景使用YOLOv8l或YOLOv8x</li>
            <li>确保截图清晰，避免模糊或遮挡</li>
            <li>考虑使用模板匹配作为补充</li>
        </ul>
        
        <h4>Q2: 程序运行缓慢怎么解决？</h4>
        <p><strong>A</strong>:</p>
        <ul>
            <li>使用较小的YOLO模型（YOLOv8n）</li>
            <li>增加检测间隔时间</li>
            <li>关闭不必要的后台程序</li>
            <li>考虑使用GPU加速（需要CUDA支持）</li>
        </ul>
        
        <h4>Q3: 模板匹配失效怎么办？</h4>
        <p><strong>A</strong>:</p>
        <ul>
            <li>检查模板图像是否与当前界面一致</li>
            <li>降低相似度阈值（但不要低于0.7）</li>
            <li>重新创建模板，选择更具特征性的区域</li>
            <li>确保界面缩放比例与创建模板时一致</li>
        </ul>
        
        <h3>🖥️ 界面问题</h3>
        
        <h4>Q4: 界面显示异常或重叠？</h4>
        <p><strong>A</strong>:</p>
        <ul>
            <li>使用菜单栏的"工具" → "重置布局"</li>
            <li>调整屏幕分辨率和缩放设置</li>
            <li>重启应用程序</li>
            <li>检查显卡驱动是否最新</li>
        </ul>
        
        <h4>Q5: 截图功能无法使用？</h4>
        <p><strong>A</strong>:</p>
        <ul>
            <li>确保程序有屏幕捕获权限</li>
            <li>检查是否有其他程序占用屏幕资源</li>
            <li>尝试以管理员身份运行程序</li>
            <li>重启计算机后再试</li>
        </ul>
        
        <h3>🔒 权限问题</h3>
        
        <h4>Q6: 自动化操作无法执行？</h4>
        <p><strong>A</strong>:</p>
        <ul>
            <li>以管理员身份运行程序</li>
            <li>检查Windows安全设置，允许程序控制鼠标键盘</li>
            <li>关闭杀毒软件的实时保护（临时）</li>
            <li>确保目标应用程序没有管理员权限要求</li>
        </ul>
        
        <h3>📞 技术支持</h3>
        <p>如果您在使用过程中遇到其他问题，可以通过以下方式获取帮助：</p>
        <ul>
            <li><strong>📧 邮箱支持</strong>: <EMAIL></li>
            <li><strong>📖 在线文档</strong>: 查看项目README.md文件</li>
            <li><strong>🐛 问题反馈</strong>: 通过GitHub Issues报告问题</li>
        </ul>
        """)
        
        layout.addWidget(content)
        self.tab_widget.addTab(tab, "❓ 常见问题")
    
    def load_manual_content(self):
        """加载手册内容"""
        # 这里可以添加从文件加载内容的逻辑
        pass
    
    def open_full_manual(self):
        """打开完整的用户手册文件"""
        try:
            # 查找用户手册文件
            manual_path = Path("用户使用手册.md")
            
            if not manual_path.exists():
                # 尝试在项目根目录查找
                project_root = Path(__file__).parent.parent.parent.parent.parent
                manual_path = project_root / "用户使用手册.md"
            
            if manual_path.exists():
                # 使用默认程序打开文件
                QDesktopServices.openUrl(QUrl.fromLocalFile(str(manual_path.absolute())))
            else:
                QMessageBox.warning(
                    self,
                    "文件未找到",
                    "未找到用户使用手册文件 (用户使用手册.md)\n\n"
                    "请确保文件位于项目根目录下。"
                )
        except Exception as e:
            QMessageBox.critical(
                self,
                "打开失败",
                f"无法打开用户手册文件: {e}"
            )

if __name__ == "__main__":
    app = QApplication(sys.argv)
    dialog = UserManualDialog()
    dialog.show()
    sys.exit(app.exec())
