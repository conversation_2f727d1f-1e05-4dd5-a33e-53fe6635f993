#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于YOLO深度学习检测结合OpenCV模板匹配的Windows屏幕识别工具
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

from setuptools import setup, find_packages
import os

# 读取README文件
def read_readme():
    readme_path = os.path.join(os.path.dirname(__file__), 'README.md')
    if os.path.exists(readme_path):
        with open(readme_path, 'r', encoding='utf-8') as f:
            return f.read()
    return "基于YOLO深度学习检测结合OpenCV模板匹配的Windows屏幕识别工具"

# 读取requirements.txt
def read_requirements():
    requirements_path = os.path.join(os.path.dirname(__file__), 'requirements.txt')
    requirements = []
    if os.path.exists(requirements_path):
        with open(requirements_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and not line.startswith('--'):
                    requirements.append(line)
    return requirements

setup(
    name="yolo-opencv-screen-detector",
    version="1.0.0",
    author="AI Assistant",
    author_email="<EMAIL>",
    description="基于YOLO深度学习检测结合OpenCV模板匹配的Windows屏幕识别工具",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/example/yolo-opencv-screen-detector",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: Microsoft :: Windows",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Software Development :: Libraries :: Python Modules",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Scientific/Engineering :: Image Recognition",
    ],
    python_requires=">=3.9",
    install_requires=read_requirements(),
    extras_require={
        "dev": [
            "pytest>=7.4.0",
            "pytest-asyncio>=0.21.0",
            "pytest-cov>=4.1.0",
            "pytest-qt>=4.2.0",
            "black>=23.0.0",
            "flake8>=6.0.0",
            "mypy>=1.5.0",
        ],
        "gpu": [
            "torch+cu118",
            "torchvision+cu118",
        ],
    },
    entry_points={
        "console_scripts": [
            "yolo-screen-detector=yolo_opencv_detector.main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "yolo_opencv_detector": [
            "configs/*.yaml",
            "models/*.pt",
            "templates/*.png",
        ],
    },
    zip_safe=False,
)
