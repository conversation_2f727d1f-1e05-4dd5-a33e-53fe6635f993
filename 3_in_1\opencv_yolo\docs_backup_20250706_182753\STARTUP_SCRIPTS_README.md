# YOLO OpenCV Detector - Startup Scripts

This document describes the available startup scripts for the YOLO OpenCV Detector application.

## Available Scripts

### 1. `start_yolo_detector.bat` - Professional Startup Script

**Description**: Comprehensive startup script with full system checks and setup.

**Features**:
- ✅ Python installation verification
- ✅ Virtual environment management
- ✅ Dependency installation
- ✅ Directory structure setup
- ✅ YOLO model verification and download
- ✅ System information display
- ✅ Error handling and troubleshooting

**Usage**:
```batch
start_yolo_detector.bat
```

**Best for**: First-time users, production deployment, complete system setup

---

### 2. `quick_start.bat` - Simple Quick Start

**Description**: Minimal startup script for experienced users.

**Features**:
- ✅ Basic Python check
- ✅ Virtual environment activation
- ✅ Essential directory creation
- ✅ Fast application launch

**Usage**:
```batch
quick_start.bat
```

**Best for**: Daily use, experienced users, quick testing

---

### 3. `dev_start.bat` - Developer Mode

**Description**: Advanced startup script with debugging and development features.

**Features**:
- ✅ Debug logging enabled
- ✅ Performance monitoring
- ✅ Development tools installation
- ✅ Environment variable setup
- ✅ Log file management
- ✅ Detailed error tracking

**Usage**:
```batch
dev_start.bat
```

**Best for**: Developers, debugging, performance analysis, troubleshooting

## Prerequisites

### System Requirements
- **Operating System**: Windows 10/11
- **Python**: Version 3.9 or higher
- **Memory**: Minimum 4GB RAM (8GB recommended)
- **Storage**: At least 2GB free space

### Python Packages
The scripts will automatically install required packages:
- `ultralytics` - YOLO models
- `opencv-python` - Computer vision
- `PyQt6` - GUI framework
- `numpy` - Numerical computing
- `pillow` - Image processing
- `mss` - Screen capture

## Directory Structure

The scripts will create the following directories:

```
project_root/
├── venv/                 # Virtual environment
├── models/               # YOLO model files
├── templates/            # Template images
├── screenshots/          # Captured screenshots
├── logs/                 # Application logs (dev mode)
├── src/                  # Source code
└── requirements.txt      # Python dependencies
```

## Troubleshooting

### Common Issues

#### 1. Python Not Found
**Error**: `Python is not installed or not in PATH`

**Solution**:
1. Download Python from https://www.python.org/downloads/
2. During installation, check "Add Python to PATH"
3. Restart command prompt and try again

#### 2. Virtual Environment Creation Failed
**Error**: `Failed to create virtual environment`

**Solution**:
1. Run command prompt as Administrator
2. Ensure you have write permissions in the project directory
3. Check available disk space

#### 3. Dependency Installation Failed
**Error**: `Failed to install dependencies`

**Solution**:
1. Check internet connection
2. Try running: `pip install --upgrade pip`
3. Use: `pip install -r requirements.txt --verbose` for detailed output

#### 4. Application File Not Found
**Error**: `Main application file not found`

**Solution**:
1. Ensure you're in the correct project directory
2. Check if `src/yolo_opencv_detector/main_v2.py` exists
3. Verify project structure is intact

### Debug Mode

For detailed troubleshooting, use the developer script:
```batch
dev_start.bat
```

This will provide:
- Detailed logging
- Environment information
- Dependency status
- Performance metrics

## Environment Variables

The scripts set the following environment variables:

### Standard Mode
- `PYTHONPATH`: Project source directory
- `VIRTUAL_ENV`: Virtual environment path

### Developer Mode
- `PYTHONUNBUFFERED=1`: Immediate output
- `YOLO_DEBUG=1`: Enable YOLO debugging
- `YOLO_VERBOSE=1`: Verbose YOLO output
- `OPENCV_LOG_LEVEL=DEBUG`: OpenCV debug logging

## Performance Tips

### For Better Performance
1. **Use SSD storage** for faster model loading
2. **Close unnecessary applications** to free memory
3. **Use GPU acceleration** if CUDA is available
4. **Regular cleanup** of screenshots directory

### Memory Optimization
- Use YOLOv8n for lower memory usage
- Reduce detection frequency in real-time mode
- Close other applications during detection

## Security Notes

### Safe Practices
- ✅ Scripts create isolated virtual environment
- ✅ No system-wide package modifications
- ✅ Local directory operations only
- ✅ No network access except for package downloads

### Permissions
- Scripts require write access to project directory
- No administrator privileges needed (unless specified)
- Virtual environment is user-local

## Support

### Getting Help
1. **Check logs**: Use `dev_start.bat` for detailed logs
2. **Verify setup**: Ensure all prerequisites are met
3. **Update dependencies**: Run `pip install --upgrade -r requirements.txt`
4. **Clean install**: Delete `venv` folder and restart

### Reporting Issues
When reporting issues, please include:
- Operating system version
- Python version
- Error messages from startup script
- Log files (if available)

---

**Note**: These scripts are designed for Windows systems. For Linux/macOS, equivalent shell scripts would be needed.
