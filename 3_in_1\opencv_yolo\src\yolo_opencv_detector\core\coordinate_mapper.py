# -*- coding: utf-8 -*-
"""
坐标映射器模块
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

from typing import Tuple, Optional, Dict, Any, List
import numpy as np

from ..utils.logger import Logger
from ..utils.data_structures import BoundingBox, DetectionResult, ScreenInfo
from .multi_monitor import MultiMonitorManager


class CoordinateMapper:
    """坐标映射器类"""
    
    def __init__(self, monitor_manager: Optional[MultiMonitorManager] = None):
        """
        初始化坐标映射器
        
        Args:
            monitor_manager: 多显示器管理器，None时自动创建
        """
        self.logger = Logger().get_logger(__name__)
        self.monitor_manager = monitor_manager or MultiMonitorManager()
        
        # 坐标变换历史
        self.transform_history: List[Dict[str, Any]] = []
        self.max_history_size = 100
        
        self.logger.info("坐标映射器初始化完成")
    
    def screen_to_virtual(self, x: int, y: int, monitor_id: int) -> Tuple[int, int]:
        """
        将屏幕坐标转换为虚拟屏幕坐标
        
        Args:
            x: 屏幕X坐标
            y: 屏幕Y坐标
            monitor_id: 显示器ID
            
        Returns:
            Tuple[int, int]: 虚拟屏幕坐标
        """
        try:
            monitor = self.monitor_manager.get_monitor_by_id(monitor_id)
            if monitor is None:
                self.logger.warning(f"未找到显示器 {monitor_id}，使用原始坐标")
                return x, y
            
            virtual_x = x + monitor.x
            virtual_y = y + monitor.y
            
            self._record_transform("screen_to_virtual", {
                "input": (x, y),
                "output": (virtual_x, virtual_y),
                "monitor_id": monitor_id,
                "monitor_offset": (monitor.x, monitor.y)
            })
            
            return virtual_x, virtual_y
            
        except Exception as e:
            self.logger.error(f"屏幕到虚拟坐标转换失败: {e}")
            return x, y
    
    def virtual_to_screen(self, x: int, y: int, target_monitor_id: Optional[int] = None) -> Tuple[int, int, int]:
        """
        将虚拟屏幕坐标转换为屏幕坐标
        
        Args:
            x: 虚拟屏幕X坐标
            y: 虚拟屏幕Y坐标
            target_monitor_id: 目标显示器ID，None时自动检测
            
        Returns:
            Tuple[int, int, int]: (屏幕X坐标, 屏幕Y坐标, 显示器ID)
        """
        try:
            # 自动检测目标显示器
            if target_monitor_id is None:
                monitor = self.monitor_manager.get_monitor_at_point(x, y)
                if monitor is None:
                    # 如果点不在任何显示器内，使用主显示器
                    monitor = self.monitor_manager.get_primary_monitor()
                    if monitor is None:
                        self.logger.error("无法找到有效的显示器")
                        return x, y, 0
            else:
                monitor = self.monitor_manager.get_monitor_by_id(target_monitor_id)
                if monitor is None:
                    self.logger.warning(f"未找到显示器 {target_monitor_id}，使用原始坐标")
                    return x, y, target_monitor_id
            
            screen_x = x - monitor.x
            screen_y = y - monitor.y
            
            self._record_transform("virtual_to_screen", {
                "input": (x, y),
                "output": (screen_x, screen_y),
                "monitor_id": monitor.monitor_id,
                "monitor_offset": (monitor.x, monitor.y)
            })
            
            return screen_x, screen_y, monitor.monitor_id
            
        except Exception as e:
            self.logger.error(f"虚拟到屏幕坐标转换失败: {e}")
            return x, y, target_monitor_id or 0
    
    def scale_coordinates(self, x: int, y: int, 
                         from_size: Tuple[int, int], 
                         to_size: Tuple[int, int]) -> Tuple[int, int]:
        """
        缩放坐标
        
        Args:
            x: X坐标
            y: Y坐标
            from_size: 源尺寸 (width, height)
            to_size: 目标尺寸 (width, height)
            
        Returns:
            Tuple[int, int]: 缩放后的坐标
        """
        try:
            from_width, from_height = from_size
            to_width, to_height = to_size
            
            if from_width == 0 or from_height == 0:
                self.logger.error("源尺寸不能为零")
                return x, y
            
            scale_x = to_width / from_width
            scale_y = to_height / from_height
            
            scaled_x = int(x * scale_x)
            scaled_y = int(y * scale_y)
            
            self._record_transform("scale_coordinates", {
                "input": (x, y),
                "output": (scaled_x, scaled_y),
                "from_size": from_size,
                "to_size": to_size,
                "scale_factors": (scale_x, scale_y)
            })
            
            return scaled_x, scaled_y
            
        except Exception as e:
            self.logger.error(f"坐标缩放失败: {e}")
            return x, y
    
    def transform_bounding_box(self, bbox: BoundingBox, 
                              from_monitor_id: int,
                              to_monitor_id: Optional[int] = None,
                              scale_factor: Optional[Tuple[float, float]] = None) -> BoundingBox:
        """
        变换边界框坐标
        
        Args:
            bbox: 原始边界框
            from_monitor_id: 源显示器ID
            to_monitor_id: 目标显示器ID，None时使用虚拟坐标
            scale_factor: 缩放因子 (scale_x, scale_y)
            
        Returns:
            BoundingBox: 变换后的边界框
        """
        try:
            # 转换为虚拟坐标
            virtual_x, virtual_y = self.screen_to_virtual(bbox.x, bbox.y, from_monitor_id)
            virtual_x2, virtual_y2 = self.screen_to_virtual(bbox.x + bbox.width, bbox.y + bbox.height, from_monitor_id)
            
            # 应用缩放
            if scale_factor is not None:
                scale_x, scale_y = scale_factor
                virtual_x = int(virtual_x * scale_x)
                virtual_y = int(virtual_y * scale_y)
                virtual_x2 = int(virtual_x2 * scale_x)
                virtual_y2 = int(virtual_y2 * scale_y)
            
            # 转换到目标坐标系
            if to_monitor_id is not None:
                target_x, target_y, _ = self.virtual_to_screen(virtual_x, virtual_y, to_monitor_id)
                target_x2, target_y2, _ = self.virtual_to_screen(virtual_x2, virtual_y2, to_monitor_id)
            else:
                target_x, target_y = virtual_x, virtual_y
                target_x2, target_y2 = virtual_x2, virtual_y2
            
            # 创建新的边界框
            new_bbox = BoundingBox(
                x=target_x,
                y=target_y,
                width=target_x2 - target_x,
                height=target_y2 - target_y
            )
            
            self._record_transform("transform_bounding_box", {
                "input_bbox": bbox.to_tuple(),
                "output_bbox": new_bbox.to_tuple(),
                "from_monitor_id": from_monitor_id,
                "to_monitor_id": to_monitor_id,
                "scale_factor": scale_factor
            })
            
            return new_bbox
            
        except Exception as e:
            self.logger.error(f"边界框变换失败: {e}")
            return bbox
    
    def transform_detection_results(self, results: List[DetectionResult],
                                   from_monitor_id: int,
                                   to_monitor_id: Optional[int] = None,
                                   scale_factor: Optional[Tuple[float, float]] = None) -> List[DetectionResult]:
        """
        变换检测结果坐标
        
        Args:
            results: 检测结果列表
            from_monitor_id: 源显示器ID
            to_monitor_id: 目标显示器ID
            scale_factor: 缩放因子
            
        Returns:
            List[DetectionResult]: 变换后的检测结果
        """
        transformed_results = []
        
        for result in results:
            try:
                # 变换边界框
                new_bbox = self.transform_bounding_box(
                    result.bbox, from_monitor_id, to_monitor_id, scale_factor
                )
                
                # 创建新的检测结果
                new_result = DetectionResult(
                    bbox=new_bbox,
                    confidence=result.confidence,
                    class_id=result.class_id,
                    class_name=result.class_name,
                    template_id=result.template_id,
                    source=result.source,
                    timestamp=result.timestamp,
                    metadata=result.metadata.copy()
                )
                
                # 添加变换信息到元数据
                new_result.metadata.update({
                    "coordinate_transform": {
                        "from_monitor_id": from_monitor_id,
                        "to_monitor_id": to_monitor_id,
                        "scale_factor": scale_factor,
                        "original_bbox": result.bbox.to_tuple()
                    }
                })
                
                transformed_results.append(new_result)
                
            except Exception as e:
                self.logger.error(f"检测结果变换失败: {e}")
                # 保留原始结果
                transformed_results.append(result)
        
        return transformed_results
    
    def get_relative_position(self, x: int, y: int, monitor_id: int) -> Tuple[float, float]:
        """
        获取相对位置（0-1范围）
        
        Args:
            x: X坐标
            y: Y坐标
            monitor_id: 显示器ID
            
        Returns:
            Tuple[float, float]: 相对位置 (rel_x, rel_y)
        """
        try:
            monitor = self.monitor_manager.get_monitor_by_id(monitor_id)
            if monitor is None:
                self.logger.warning(f"未找到显示器 {monitor_id}")
                return 0.0, 0.0
            
            # 转换为显示器本地坐标
            local_x = x - monitor.x
            local_y = y - monitor.y
            
            # 计算相对位置
            rel_x = local_x / monitor.width if monitor.width > 0 else 0.0
            rel_y = local_y / monitor.height if monitor.height > 0 else 0.0
            
            # 限制在0-1范围内
            rel_x = max(0.0, min(1.0, rel_x))
            rel_y = max(0.0, min(1.0, rel_y))
            
            return rel_x, rel_y
            
        except Exception as e:
            self.logger.error(f"相对位置计算失败: {e}")
            return 0.0, 0.0
    
    def from_relative_position(self, rel_x: float, rel_y: float, monitor_id: int) -> Tuple[int, int]:
        """
        从相对位置转换为绝对坐标
        
        Args:
            rel_x: 相对X位置 (0-1)
            rel_y: 相对Y位置 (0-1)
            monitor_id: 显示器ID
            
        Returns:
            Tuple[int, int]: 绝对坐标
        """
        try:
            monitor = self.monitor_manager.get_monitor_by_id(monitor_id)
            if monitor is None:
                self.logger.warning(f"未找到显示器 {monitor_id}")
                return 0, 0
            
            # 计算绝对坐标
            abs_x = int(rel_x * monitor.width) + monitor.x
            abs_y = int(rel_y * monitor.height) + monitor.y
            
            return abs_x, abs_y
            
        except Exception as e:
            self.logger.error(f"绝对坐标计算失败: {e}")
            return 0, 0
    
    def validate_coordinates(self, x: int, y: int, monitor_id: Optional[int] = None) -> bool:
        """
        验证坐标是否有效
        
        Args:
            x: X坐标
            y: Y坐标
            monitor_id: 显示器ID，None时检查所有显示器
            
        Returns:
            bool: 坐标是否有效
        """
        try:
            if monitor_id is not None:
                monitor = self.monitor_manager.get_monitor_by_id(monitor_id)
                if monitor is None:
                    return False
                
                return (monitor.x <= x < monitor.x + monitor.width and
                        monitor.y <= y < monitor.y + monitor.height)
            else:
                # 检查是否在任何显示器内
                return self.monitor_manager.get_monitor_at_point(x, y) is not None
                
        except Exception as e:
            self.logger.error(f"坐标验证失败: {e}")
            return False
    
    def clamp_to_monitor(self, x: int, y: int, monitor_id: int) -> Tuple[int, int]:
        """
        将坐标限制在指定显示器内
        
        Args:
            x: X坐标
            y: Y坐标
            monitor_id: 显示器ID
            
        Returns:
            Tuple[int, int]: 限制后的坐标
        """
        try:
            monitor = self.monitor_manager.get_monitor_by_id(monitor_id)
            if monitor is None:
                self.logger.warning(f"未找到显示器 {monitor_id}")
                return x, y
            
            clamped_x = max(monitor.x, min(x, monitor.x + monitor.width - 1))
            clamped_y = max(monitor.y, min(y, monitor.y + monitor.height - 1))
            
            return clamped_x, clamped_y
            
        except Exception as e:
            self.logger.error(f"坐标限制失败: {e}")
            return x, y
    
    def _record_transform(self, transform_type: str, transform_data: Dict[str, Any]) -> None:
        """记录坐标变换历史"""
        try:
            import time
            
            record = {
                "timestamp": time.time(),
                "type": transform_type,
                "data": transform_data
            }
            
            self.transform_history.append(record)
            
            # 限制历史记录大小
            if len(self.transform_history) > self.max_history_size:
                self.transform_history.pop(0)
                
        except Exception as e:
            self.logger.error(f"记录变换历史失败: {e}")
    
    def get_transform_history(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        获取坐标变换历史
        
        Args:
            limit: 限制返回数量
            
        Returns:
            List[Dict[str, Any]]: 变换历史
        """
        if limit is None:
            return self.transform_history.copy()
        else:
            return self.transform_history[-limit:].copy()
    
    def clear_history(self) -> None:
        """清空变换历史"""
        self.transform_history.clear()
        self.logger.info("坐标变换历史已清空")
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        return {
            "transform_history_count": len(self.transform_history),
            "max_history_size": self.max_history_size,
            "monitor_manager_stats": self.monitor_manager.get_monitor_stats()
        }
