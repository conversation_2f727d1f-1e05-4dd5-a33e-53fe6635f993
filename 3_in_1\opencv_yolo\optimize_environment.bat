@echo off
REM YOLO OpenCV检测器虚拟环境优化脚本
REM 生成时间: 2025-07-06 19:00:20

echo 🔧 开始虚拟环境优化
echo ================================

echo 📋 当前环境信息:
python --version
pip --version

echo.
echo 💾 创建备份...
pip freeze > env_backup_before_optimization.txt
echo ✅ 环境备份完成


echo.
echo 🗑️ 移除未使用的依赖包...
pip uninstall -y asyncio  # Python内置
pip uninstall -y sqlite3  # Python内置，无需安装

echo.
echo 📦 安装缺失的依赖包...
pip install packaging
pip install requests
pip install mss
pip install build
pip install pytesseract
pip install chardet
pip install easyocr
pip install paddleocr

echo.
echo 🔄 更新requirements.txt...
pip freeze > requirements_optimized.txt

echo.
echo ✅ 优化完成!
echo 📄 新的依赖列表已保存到: requirements_optimized.txt
echo 💾 原环境备份: env_backup_before_optimization.txt

pause
