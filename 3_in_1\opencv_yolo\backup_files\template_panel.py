# -*- coding: utf-8 -*-
"""
模板面板组件
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

from typing import List, Dict, Any, Optional
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, QListWidget,
    QListWidgetItem, QPushButton, QLabel, QLineEdit, QComboBox,
    QTextEdit, QFileDialog, QMessageBox, QMenu, QInputDialog, QDialog
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QPixmap, QIcon, QAction

from ...utils.logger import Logger
from ...utils.config_manager import ConfigManager


class TemplatePanel(QWidget):
    """模板面板类"""
    
    # 信号定义
    template_selected = pyqtSignal(str)  # 模板选择
    template_added = pyqtSignal(str)     # 模板添加
    template_removed = pyqtSignal(str)   # 模板删除
    template_updated = pyqtSignal(str)   # 模板更新
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化模板面板
        
        Args:
            config_manager: 配置管理器
        """
        super().__init__()
        
        self.logger = Logger().get_logger(__name__)
        self.config_manager = config_manager
        
        # 组件初始化
        self.template_list = None
        self.add_button = None
        self.remove_button = None
        self.edit_button = None
        self.import_button = None
        self.export_button = None
        
        # 模板信息组件
        self.name_edit = None
        self.category_combo = None
        self.description_edit = None
        self.preview_label = None
        
        # 当前选中的模板
        self.current_template_id: Optional[str] = None
        
        # 初始化界面
        self._init_ui()
        self._init_connections()
        self._load_templates()
        
        self.logger.info("模板面板初始化完成")
    
    def _init_ui(self) -> None:
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)  # 增加组件间距避免重叠
        layout.setContentsMargins(8, 10, 8, 8)  # 设置边距

        # 添加标题和操作指导
        header_widget = QWidget()
        header_layout = QVBoxLayout(header_widget)
        header_layout.setContentsMargins(0, 0, 0, 0)

        title_label = QLabel("📋 模板管理")
        title_label.setStyleSheet(
            "QLabel { "
            "font-size: 14px; "
            "font-weight: bold; "
            "color: #2c3e50; "
            "padding: 5px 0px; "
            "}"
        )
        header_layout.addWidget(title_label)

        help_label = QLabel("💡 创建和管理检测模板")
        help_label.setStyleSheet(
            "QLabel { "
            "font-size: 11px; "
            "color: #7f8c8d; "
            "font-style: italic; "
            "margin-bottom: 5px; "
            "}"
        )
        header_layout.addWidget(help_label)
        layout.addWidget(header_widget)

        # 模板列表组
        list_group = QGroupBox("📁 模板列表")
        list_group.setMinimumHeight(120)  # 大幅减少高度
        list_group.setMaximumHeight(160)  # 限制最大高度
        list_group.setStyleSheet("QGroupBox { font-weight: bold; }")
        list_layout = QVBoxLayout(list_group)
        list_layout.setSpacing(5)  # 紧凑间距
        
        # 模板列表
        self.template_list = QListWidget()
        self.template_list.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        list_layout.addWidget(self.template_list)
        
        # 操作提示
        tip_label = QLabel("📋 步骤2: 管理检测模板")
        tip_label.setStyleSheet(
            "QLabel { "
            "color: #34495e; "
            "font-size: 11px; "
            "padding: 3px; "
            "background-color: #ecf0f1; "
            "border-radius: 3px; "
            "margin: 5px 0px; "
            "}"
        )
        list_layout.addWidget(tip_label)

        # 主要操作按钮行
        main_button_layout = QHBoxLayout()
        main_button_layout.setSpacing(6)

        self.add_button = QPushButton("📁 添加模板")
        self.add_button.setMinimumHeight(45)  # 增加按钮高度
        self.add_button.setStyleSheet(
            "QPushButton { "
            "background-color: #3498db; "
            "color: white; "
            "border: none; "
            "border-radius: 6px; "
            "font-size: 12px; "
            "font-weight: bold; "
            "padding: 8px 12px; "
            "} "
            "QPushButton:hover { "
            "background-color: #2980b9; "
            "} "
            "QPushButton:pressed { background-color: #21618c; }"
        )
        self.add_button.setToolTip("从文件添加新模板")
        main_button_layout.addWidget(self.add_button)

        self.capture_button = QPushButton("📷 截取模板")
        self.capture_button.setMinimumHeight(45)  # 增加按钮高度
        self.capture_button.setStyleSheet(
            "QPushButton { "
            "background-color: #e67e22; "
            "color: white; "
            "border: none; "
            "border-radius: 6px; "
            "font-size: 12px; "
            "font-weight: bold; "
            "padding: 8px 12px; "
            "} "
            "QPushButton:hover { "
            "background-color: #d35400; "
            "} "
            "QPushButton:pressed { background-color: #a04000; }"
        )
        self.capture_button.setToolTip("从屏幕截取创建模板")
        main_button_layout.addWidget(self.capture_button)

        list_layout.addLayout(main_button_layout)

        # 管理操作按钮行
        manage_button_layout = QHBoxLayout()
        manage_button_layout.setSpacing(6)

        self.edit_button = QPushButton("✏️ 编辑")
        self.edit_button.setMinimumHeight(32)
        self.edit_button.setStyleSheet(
            "QPushButton { "
            "background-color: #f39c12; "
            "color: white; "
            "border: none; "
            "border-radius: 4px; "
            "font-size: 11px; "
            "} "
            "QPushButton:hover { background-color: #e67e22; } "
            "QPushButton:disabled { background-color: #bdc3c7; }"
        )
        self.edit_button.setToolTip("编辑选中模板")
        self.edit_button.setEnabled(False)
        manage_button_layout.addWidget(self.edit_button)

        self.remove_button = QPushButton("🗑️ 删除")
        self.remove_button.setMinimumHeight(32)
        self.remove_button.setStyleSheet(
            "QPushButton { "
            "background-color: #e74c3c; "
            "color: white; "
            "border: none; "
            "border-radius: 4px; "
            "font-size: 11px; "
            "} "
            "QPushButton:hover { background-color: #c0392b; } "
            "QPushButton:disabled { background-color: #bdc3c7; }"
        )
        self.remove_button.setToolTip("删除选中模板")
        self.remove_button.setEnabled(False)
        manage_button_layout.addWidget(self.remove_button)

        list_layout.addLayout(manage_button_layout)
        
        # 导入导出按钮
        io_layout = QHBoxLayout()
        
        self.import_button = QPushButton("导入")
        self.import_button.setToolTip("导入模板")
        io_layout.addWidget(self.import_button)
        
        self.export_button = QPushButton("导出")
        self.export_button.setToolTip("导出模板")
        io_layout.addWidget(self.export_button)
        
        list_layout.addLayout(io_layout)
        
        layout.addWidget(list_group)
        
        # 模板信息组
        info_group = QGroupBox("模板信息")
        info_group.setMinimumHeight(80)   # 进一步减少高度
        info_group.setMaximumHeight(100)  # 严格限制最大高度
        info_layout = QVBoxLayout(info_group)
        info_layout.setSpacing(2)  # 极紧凑间距
        
        # 名称
        name_layout = QHBoxLayout()
        name_layout.addWidget(QLabel("名称:"))
        self.name_edit = QLineEdit()
        self.name_edit.setReadOnly(True)
        name_layout.addWidget(self.name_edit)
        info_layout.addLayout(name_layout)
        
        # 类别
        category_layout = QHBoxLayout()
        category_layout.addWidget(QLabel("类别:"))
        self.category_combo = QComboBox()
        self.category_combo.setEnabled(False)
        category_layout.addWidget(self.category_combo)
        info_layout.addLayout(category_layout)
        
        # 描述
        info_layout.addWidget(QLabel("描述:"))
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(25)  # 进一步减少高度
        self.description_edit.setMinimumHeight(25)
        self.description_edit.setReadOnly(True)
        info_layout.addWidget(self.description_edit)
        
        # 预览
        info_layout.addWidget(QLabel("预览:"))
        self.preview_label = QLabel()
        self.preview_label.setMinimumHeight(30)  # 大幅减少预览区域高度
        self.preview_label.setMaximumHeight(30)  # 严格限制最大高度
        self.preview_label.setStyleSheet("QLabel { border: 1px solid gray; background-color: white; }")
        self.preview_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.preview_label.setText("无预览")
        info_layout.addWidget(self.preview_label)
        
        layout.addWidget(info_group)
    
    def _init_connections(self) -> None:
        """初始化信号连接"""
        # 列表选择
        self.template_list.itemSelectionChanged.connect(self._on_template_selection_changed)
        self.template_list.customContextMenuRequested.connect(self._show_context_menu)
        
        # 按钮连接
        self.add_button.clicked.connect(self._add_template)
        self.capture_button.clicked.connect(self._capture_template)
        self.remove_button.clicked.connect(self._remove_template)
        self.edit_button.clicked.connect(self._edit_template)
        self.import_button.clicked.connect(self._import_templates)
        self.export_button.clicked.connect(self._export_templates)
    
    def _load_templates(self) -> None:
        """加载模板列表"""
        try:
            self.template_list.clear()
            
            # 这里应该从模板管理器加载实际的模板
            # 暂时使用示例数据
            sample_templates = [
                {"id": "template_1", "name": "按钮模板", "category": "UI", "description": "通用按钮模板"},
                {"id": "template_2", "name": "图标模板", "category": "图标", "description": "应用图标模板"},
                {"id": "template_3", "name": "文本模板", "category": "文本", "description": "文本识别模板"}
            ]
            
            for template in sample_templates:
                item = QListWidgetItem(template["name"])
                item.setData(Qt.ItemDataRole.UserRole, template["id"])
                item.setToolTip(template["description"])
                self.template_list.addItem(item)
            
            self.logger.info(f"加载了 {len(sample_templates)} 个模板")
            
        except Exception as e:
            self.logger.error(f"加载模板失败: {e}")
    
    def _on_template_selection_changed(self) -> None:
        """模板选择变更"""
        try:
            current_item = self.template_list.currentItem()
            
            if current_item:
                template_id = current_item.data(Qt.ItemDataRole.UserRole)
                self.current_template_id = template_id
                
                # 启用按钮
                self.remove_button.setEnabled(True)
                self.edit_button.setEnabled(True)
                
                # 加载模板信息
                self._load_template_info(template_id)
                
                # 发送选择信号
                self.template_selected.emit(template_id)
                
            else:
                self.current_template_id = None
                self.remove_button.setEnabled(False)
                self.edit_button.setEnabled(False)
                self._clear_template_info()
                
        except Exception as e:
            self.logger.error(f"模板选择处理失败: {e}")
    
    def _load_template_info(self, template_id: str) -> None:
        """加载模板信息"""
        try:
            # 这里应该从模板管理器获取实际的模板信息
            # 暂时使用示例数据
            template_info = {
                "name": f"模板 {template_id}",
                "category": "示例",
                "description": f"这是模板 {template_id} 的描述信息"
            }
            
            # 更新界面
            self.name_edit.setText(template_info["name"])
            self.category_combo.setCurrentText(template_info["category"])
            self.description_edit.setText(template_info["description"])
            
            # 加载预览图
            self._load_template_preview(template_id)
            
        except Exception as e:
            self.logger.error(f"加载模板信息失败: {e}")
    
    def _load_template_preview(self, template_id: str) -> None:
        """加载模板预览"""
        try:
            # 这里应该加载实际的模板图像
            # 暂时显示占位符
            self.preview_label.setText(f"模板 {template_id} 预览")
            
        except Exception as e:
            self.logger.error(f"加载模板预览失败: {e}")
    
    def _clear_template_info(self) -> None:
        """清除模板信息"""
        self.name_edit.clear()
        self.category_combo.setCurrentIndex(0)
        self.description_edit.clear()
        self.preview_label.setText("无预览")
    
    def _show_context_menu(self, position) -> None:
        """显示右键菜单"""
        try:
            item = self.template_list.itemAt(position)
            if item:
                menu = QMenu(self)
                
                # 编辑动作
                edit_action = QAction("编辑", self)
                edit_action.triggered.connect(self._edit_template)
                menu.addAction(edit_action)
                
                # 删除动作
                delete_action = QAction("删除", self)
                delete_action.triggered.connect(self._remove_template)
                menu.addAction(delete_action)
                
                menu.addSeparator()
                
                # 复制动作
                copy_action = QAction("复制", self)
                copy_action.triggered.connect(self._copy_template)
                menu.addAction(copy_action)
                
                # 重命名动作
                rename_action = QAction("重命名", self)
                rename_action.triggered.connect(self._rename_template)
                menu.addAction(rename_action)
                
                menu.exec(self.template_list.mapToGlobal(position))
                
        except Exception as e:
            self.logger.error(f"显示右键菜单失败: {e}")
    
    def _add_template(self) -> None:
        """添加模板"""
        try:
            # 打开文件对话框选择图像
            file_path, _ = QFileDialog.getOpenFileName(
                self, "选择模板图像", "",
                "图像文件 (*.png *.jpg *.jpeg *.bmp);;所有文件 (*)"
            )
            
            if file_path:
                # 获取模板名称
                name, ok = QInputDialog.getText(self, "模板名称", "请输入模板名称:")
                if ok and name:
                    # 这里应该调用模板管理器添加模板
                    template_id = f"template_{len(self.template_list) + 1}"
                    
                    # 添加到列表
                    item = QListWidgetItem(name)
                    item.setData(Qt.ItemDataRole.UserRole, template_id)
                    self.template_list.addItem(item)
                    
                    # 发送添加信号
                    self.template_added.emit(template_id)
                    
                    self.logger.info(f"添加模板: {name}")
                    
        except Exception as e:
            self.logger.error(f"添加模板失败: {e}")
            QMessageBox.warning(self, "错误", f"添加模板失败: {e}")

    def _capture_template(self) -> None:
        """从屏幕截取创建模板"""
        try:
            from ..improved_template_dialog import ImprovedTemplateDialog

            dialog = ImprovedTemplateDialog(self)
            dialog.template_created.connect(self._on_template_created)

            if dialog.exec() == QDialog.DialogCode.Accepted:
                pass  # 模板已在信号处理中添加

        except Exception as e:
            self.logger.error(f"截取模板失败: {e}")
            QMessageBox.critical(self, "错误", f"截取模板失败: {e}")

    def _on_template_created(self, template_id: str, template_data: dict) -> None:
        """模板创建完成"""
        try:
            # 添加到列表
            item = QListWidgetItem(template_data["name"])
            item.setData(Qt.ItemDataRole.UserRole, template_id)
            self.template_list.addItem(item)

            # 发送添加信号
            self.template_added.emit(template_id)

            self.logger.info(f"从截取创建模板: {template_id}")

        except Exception as e:
            self.logger.error(f"添加截取的模板失败: {e}")

    def _remove_template(self) -> None:
        """删除模板"""
        try:
            if not self.current_template_id:
                return
            
            # 确认删除
            reply = QMessageBox.question(
                self, "确认删除",
                "确定要删除选中的模板吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                # 从列表中删除
                current_row = self.template_list.currentRow()
                self.template_list.takeItem(current_row)
                
                # 发送删除信号
                self.template_removed.emit(self.current_template_id)
                
                self.logger.info(f"删除模板: {self.current_template_id}")
                
        except Exception as e:
            self.logger.error(f"删除模板失败: {e}")
            QMessageBox.warning(self, "错误", f"删除模板失败: {e}")
    
    def _edit_template(self) -> None:
        """编辑模板"""
        try:
            if not self.current_template_id:
                return
            
            # 这里应该打开模板编辑对话框
            self.logger.info(f"编辑模板: {self.current_template_id}")
            QMessageBox.information(self, "提示", "模板编辑功能待实现")
            
        except Exception as e:
            self.logger.error(f"编辑模板失败: {e}")
    
    def _copy_template(self) -> None:
        """复制模板"""
        try:
            if not self.current_template_id:
                return
            
            # 获取新名称
            name, ok = QInputDialog.getText(self, "复制模板", "请输入新模板名称:")
            if ok and name:
                # 这里应该调用模板管理器复制模板
                new_template_id = f"template_{len(self.template_list) + 1}"
                
                # 添加到列表
                item = QListWidgetItem(name)
                item.setData(Qt.ItemDataRole.UserRole, new_template_id)
                self.template_list.addItem(item)
                
                self.logger.info(f"复制模板: {self.current_template_id} -> {new_template_id}")
                
        except Exception as e:
            self.logger.error(f"复制模板失败: {e}")
    
    def _rename_template(self) -> None:
        """重命名模板"""
        try:
            if not self.current_template_id:
                return
            
            current_item = self.template_list.currentItem()
            if current_item:
                old_name = current_item.text()
                name, ok = QInputDialog.getText(self, "重命名模板", "请输入新名称:", text=old_name)
                if ok and name and name != old_name:
                    current_item.setText(name)
                    self.name_edit.setText(name)
                    
                    # 发送更新信号
                    self.template_updated.emit(self.current_template_id)
                    
                    self.logger.info(f"重命名模板: {old_name} -> {name}")
                    
        except Exception as e:
            self.logger.error(f"重命名模板失败: {e}")
    
    def _import_templates(self) -> None:
        """导入模板"""
        try:
            # 选择导入目录
            dir_path = QFileDialog.getExistingDirectory(self, "选择模板导入目录")
            if dir_path:
                # 这里应该调用模板管理器导入模板
                self.logger.info(f"导入模板目录: {dir_path}")
                QMessageBox.information(self, "提示", "模板导入功能待实现")
                
        except Exception as e:
            self.logger.error(f"导入模板失败: {e}")
            QMessageBox.warning(self, "错误", f"导入模板失败: {e}")
    
    def _export_templates(self) -> None:
        """导出模板"""
        try:
            # 选择导出目录
            dir_path = QFileDialog.getExistingDirectory(self, "选择模板导出目录")
            if dir_path:
                # 这里应该调用模板管理器导出模板
                self.logger.info(f"导出模板到目录: {dir_path}")
                QMessageBox.information(self, "提示", "模板导出功能待实现")
                
        except Exception as e:
            self.logger.error(f"导出模板失败: {e}")
            QMessageBox.warning(self, "错误", f"导出模板失败: {e}")
    
    def refresh_templates(self) -> None:
        """刷新模板列表"""
        self._load_templates()
    
    def get_selected_template_id(self) -> Optional[str]:
        """获取选中的模板ID"""
        return self.current_template_id
    
    def select_template(self, template_id: str) -> None:
        """选择指定模板"""
        try:
            for i in range(self.template_list.count()):
                item = self.template_list.item(i)
                if item.data(Qt.ItemDataRole.UserRole) == template_id:
                    self.template_list.setCurrentItem(item)
                    break
        except Exception as e:
            self.logger.error(f"选择模板失败: {e}")
