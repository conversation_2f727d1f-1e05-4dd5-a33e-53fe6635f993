#!/usr/bin/env python
# -*- coding:utf-8 -*-

import sqlite3
import json
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path
from datetime import datetime

class CodeSnippetManager:
    """代码片段管理器"""
    
    def __init__(self, db_path: str = None):
        """初始化代码片段管理器
        
        Args:
            db_path: 数据库文件路径，如果为None则使用默认路径
        """
        if db_path is None:
            # 在用户目录下创建数据库
            db_path = str(Path.home() / '.code_generator' / 'snippets.db')
        
        # 确保目录存在
        Path(db_path).parent.mkdir(parents=True, exist_ok=True)
        
        self.db_path = db_path
        self._init_db()
    
    def _init_db(self):
        """初始化数据库"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 创建分类表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS categories (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL UNIQUE,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 创建代码片段表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS snippets (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title TEXT NOT NULL,
                    description TEXT,
                    code TEXT NOT NULL,
                    category_id INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (category_id) REFERENCES categories (id)
                )
            """)
            
            # 创建标签表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS tags (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL UNIQUE
                )
            """)
            
            # 创建代码片段-标签关联表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS snippet_tags (
                    snippet_id INTEGER,
                    tag_id INTEGER,
                    PRIMARY KEY (snippet_id, tag_id),
                    FOREIGN KEY (snippet_id) REFERENCES snippets (id),
                    FOREIGN KEY (tag_id) REFERENCES tags (id)
                )
            """)
            
            # 添加默认分类
            default_categories = [
                ('窗口定位', '窗口定位相关的代码片段'),
                ('控件定位', '控件定位相关的代码片段'),
                ('组合定位', '组合定位相关的代码片段'),
                ('自定义', '自定义代码片段')
            ]
            
            cursor.executemany(
                "INSERT OR IGNORE INTO categories (name, description) VALUES (?, ?)",
                default_categories
            )
            
            conn.commit()
    
    def add_category(self, name: str, description: str = '') -> int:
        """添加分类
        
        Args:
            name: 分类名称
            description: 分类描述
            
        Returns:
            新添加的分类ID
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute(
                "INSERT INTO categories (name, description) VALUES (?, ?)",
                (name, description)
            )
            conn.commit()
            return cursor.lastrowid
    
    def get_categories(self) -> List[Dict[str, Any]]:
        """获取所有分类
        
        Returns:
            分类列表
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM categories ORDER BY name")
            return [dict(row) for row in cursor.fetchall()]
    
    def add_snippet(self, title: str, code: str, description: str = '',
                   category_id: Optional[int] = None, tags: List[str] = None) -> int:
        """添加代码片段
        
        Args:
            title: 标题
            code: 代码内容
            description: 描述
            category_id: 分类ID
            tags: 标签列表
            
        Returns:
            新添加的代码片段ID
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 插入代码片段
            cursor.execute(
                """INSERT INTO snippets 
                   (title, description, code, category_id)
                   VALUES (?, ?, ?, ?)""",
                (title, description, code, category_id)
            )
            snippet_id = cursor.lastrowid
            
            # 处理标签
            if tags:
                for tag in tags:
                    # 插入标签（如果不存在）
                    cursor.execute(
                        "INSERT OR IGNORE INTO tags (name) VALUES (?)",
                        (tag,)
                    )
                    
                    # 获取标签ID
                    cursor.execute("SELECT id FROM tags WHERE name = ?", (tag,))
                    tag_id = cursor.fetchone()[0]
                    
                    # 关联代码片段和标签
                    cursor.execute(
                        "INSERT INTO snippet_tags (snippet_id, tag_id) VALUES (?, ?)",
                        (snippet_id, tag_id)
                    )
            
            conn.commit()
            return snippet_id
    
    def get_snippet(self, snippet_id: int) -> Optional[Dict[str, Any]]:
        """获取代码片段
        
        Args:
            snippet_id: 代码片段ID
            
        Returns:
            代码片段信息，如果不存在则返回None
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 获取代码片段基本信息
            cursor.execute("""
                SELECT s.*, c.name as category_name 
                FROM snippets s
                LEFT JOIN categories c ON s.category_id = c.id
                WHERE s.id = ?
            """, (snippet_id,))
            
            row = cursor.fetchone()
            if not row:
                return None
            
            snippet = dict(row)
            
            # 获取标签
            cursor.execute("""
                SELECT t.name 
                FROM tags t
                JOIN snippet_tags st ON t.id = st.tag_id
                WHERE st.snippet_id = ?
            """, (snippet_id,))
            
            snippet['tags'] = [row[0] for row in cursor.fetchall()]
            
            return snippet
    
    def update_snippet(self, snippet_id: int, title: str = None, code: str = None,
                      description: str = None, category_id: int = None,
                      tags: List[str] = None) -> bool:
        """更新代码片段
        
        Args:
            snippet_id: 代码片段ID
            title: 新标题
            code: 新代码内容
            description: 新描述
            category_id: 新分类ID
            tags: 新标签列表
            
        Returns:
            更新是否成功
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 检查代码片段是否存在
            cursor.execute("SELECT id FROM snippets WHERE id = ?", (snippet_id,))
            if not cursor.fetchone():
                return False
            
            # 更新基本信息
            update_fields = []
            params = []
            
            if title is not None:
                update_fields.append("title = ?")
                params.append(title)
            if code is not None:
                update_fields.append("code = ?")
                params.append(code)
            if description is not None:
                update_fields.append("description = ?")
                params.append(description)
            if category_id is not None:
                update_fields.append("category_id = ?")
                params.append(category_id)
            
            if update_fields:
                update_fields.append("updated_at = CURRENT_TIMESTAMP")
                params.append(snippet_id)
                
                cursor.execute(
                    f"""UPDATE snippets 
                        SET {', '.join(update_fields)}
                        WHERE id = ?""",
                    params
                )
            
            # 更新标签
            if tags is not None:
                # 删除旧的标签关联
                cursor.execute(
                    "DELETE FROM snippet_tags WHERE snippet_id = ?",
                    (snippet_id,)
                )
                
                # 添加新的标签
                for tag in tags:
                    cursor.execute(
                        "INSERT OR IGNORE INTO tags (name) VALUES (?)",
                        (tag,)
                    )
                    
                    cursor.execute(
                        "SELECT id FROM tags WHERE name = ?",
                        (tag,)
                    )
                    tag_id = cursor.fetchone()[0]
                    
                    cursor.execute(
                        """INSERT INTO snippet_tags (snippet_id, tag_id)
                           VALUES (?, ?)""",
                        (snippet_id, tag_id)
                    )
            
            conn.commit()
            return True
    
    def delete_snippet(self, snippet_id: int) -> bool:
        """删除代码片段
        
        Args:
            snippet_id: 代码片段ID
            
        Returns:
            删除是否成功
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 删除标签关联
            cursor.execute(
                "DELETE FROM snippet_tags WHERE snippet_id = ?",
                (snippet_id,)
            )
            
            # 删除代码片段
            cursor.execute(
                "DELETE FROM snippets WHERE id = ?",
                (snippet_id,)
            )
            
            conn.commit()
            return cursor.rowcount > 0
    
    def search_snippets(self, keyword: str = None, category_id: int = None,
                       tags: List[str] = None) -> List[Dict[str, Any]]:
        """搜索代码片段
        
        Args:
            keyword: 关键字（搜索标题和描述）
            category_id: 分类ID
            tags: 标签列表（必须包含所有指定的标签）
            
        Returns:
            符合条件的代码片段列表
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            query = """
                SELECT DISTINCT s.*, c.name as category_name
                FROM snippets s
                LEFT JOIN categories c ON s.category_id = c.id
            """
            
            conditions = []
            params = []
            
            # 关键字搜索
            if keyword:
                conditions.append("""
                    (s.title LIKE ? OR s.description LIKE ? OR s.code LIKE ?)
                """)
                params.extend([f"%{keyword}%"] * 3)
            
            # 分类过滤
            if category_id is not None:
                conditions.append("s.category_id = ?")
                params.append(category_id)
            
            # 标签过滤
            if tags:
                tag_placeholders = ','.join(['?'] * len(tags))
                query += f"""
                    JOIN snippet_tags st ON s.id = st.snippet_id
                    JOIN tags t ON st.tag_id = t.id
                """
                conditions.append(f"t.name IN ({tag_placeholders})")
                params.extend(tags)
                
                # 确保包含所有标签
                query += f"""
                    GROUP BY s.id
                    HAVING COUNT(DISTINCT t.name) = {len(tags)}
                """
            
            # 添加WHERE子句
            if conditions:
                query += " WHERE " + " AND ".join(conditions)
            
            # 添加排序
            query += " ORDER BY s.updated_at DESC"
            
            cursor.execute(query, params)
            snippets = [dict(row) for row in cursor.fetchall()]
            
            # 获取每个代码片段的标签
            for snippet in snippets:
                cursor.execute("""
                    SELECT t.name
                    FROM tags t
                    JOIN snippet_tags st ON t.id = st.tag_id
                    WHERE st.snippet_id = ?
                """, (snippet['id'],))
                snippet['tags'] = [row[0] for row in cursor.fetchall()]
            
            return snippets
    
    def export_snippets(self, file_path: str):
        """导出代码片段到文件
        
        Args:
            file_path: 导出文件路径
        """
        snippets = self.search_snippets()
        
        data = {
            'version': '1.0',
            'exported_at': datetime.now().isoformat(),
            'snippets': snippets
        }
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    
    def import_snippets(self, file_path: str) -> int:
        """从文件导入代码片段
        
        Args:
            file_path: 导入文件路径
            
        Returns:
            导入的代码片段数量
        """
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if not isinstance(data, dict) or 'snippets' not in data:
            raise ValueError("无效的导入文件格式")
        
        count = 0
        for snippet in data['snippets']:
            try:
                self.add_snippet(
                    title=snippet['title'],
                    code=snippet['code'],
                    description=snippet.get('description', ''),
                    category_id=snippet.get('category_id'),
                    tags=snippet.get('tags', [])
                )
                count += 1
            except Exception as e:
                logging.error(f"导入代码片段失败: {e}")
        
        return count

def test_snippet_manager():
    """测试代码片段管理器"""
    # 创建临时数据库
    manager = CodeSnippetManager(':memory:')
    
    # 测试添加分类
    category_id = manager.add_category('测试分类', '这是一个测试分类')
    print(f"\n添加分类成功: {category_id}")
    
    # 测试获取分类
    categories = manager.get_categories()
    print("\n所有分类:")
    for category in categories:
        print(f"- {category['name']}: {category['description']}")
    
    # 测试添加代码片段
    snippet_id = manager.add_snippet(
        title='测试代码片段',
        code='print("Hello, World!")',
        description='这是一个测试代码片段',
        category_id=category_id,
        tags=['测试', 'Python']
    )
    print(f"\n添加代码片段成功: {snippet_id}")
    
    # 测试获取代码片段
    snippet = manager.get_snippet(snippet_id)
    print("\n代码片段详情:")
    print(f"- 标题: {snippet['title']}")
    print(f"- 描述: {snippet['description']}")
    print(f"- 代码: {snippet['code']}")
    print(f"- 标签: {', '.join(snippet['tags'])}")
    
    # 测试更新代码片段
    success = manager.update_snippet(
        snippet_id,
        title='更新后的标题',
        tags=['测试', 'Python', '更新']
    )
    print(f"\n更新代码片段: {'成功' if success else '失败'}")
    
    # 测试搜索代码片段
    snippets = manager.search_snippets(keyword='测试', tags=['Python'])
    print("\n搜索结果:")
    for snippet in snippets:
        print(f"- {snippet['title']}")
        print(f"  标签: {', '.join(snippet['tags'])}")
    
    # 测试删除代码片段
    success = manager.delete_snippet(snippet_id)
    print(f"\n删除代码片段: {'成功' if success else '失败'}")

if __name__ == "__main__":
    test_snippet_manager()
