#!/usr/bin/env python3
"""
操作预览对话框
显示即将执行的自动化操作序列
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QTableWidget, QTableWidgetItem, QTextEdit, QGroupBox,
    QProgressBar, QCheckBox, QSpinBox, QHeaderView, QFrame,
    QMessageBox, QSplitter
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QFont, QColor, QIcon
from typing import List, Dict, Any
import logging

class OperationPreviewDialog(QDialog):
    """操作预览对话框"""
    
    # 信号定义
    execute_confirmed = pyqtSignal(list)  # 确认执行操作序列
    test_requested = pyqtSignal(int)      # 请求测试单个操作
    
    def __init__(self, operations: List[Dict[str, Any]], parent=None):
        """
        初始化操作预览对话框
        
        Args:
            operations: 操作序列列表
            parent: 父窗口
        """
        super().__init__(parent)
        self.logger = logging.getLogger(__name__)
        
        self.operations = operations
        self.selected_operations = []
        
        self._init_ui()
        self._setup_connections()
        self._load_operations()
        
        self.logger.info(f"操作预览对话框初始化完成，包含 {len(operations)} 个操作")
    
    def _init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("🔍 操作预览")
        self.setModal(True)
        self.resize(800, 600)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # 标题
        title_label = QLabel("操作序列预览")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        layout.addWidget(title_label)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Vertical)
        layout.addWidget(splitter)
        
        # 上部：操作列表
        operations_group = self._create_operations_group()
        splitter.addWidget(operations_group)
        
        # 下部：操作详情
        details_group = self._create_details_group()
        splitter.addWidget(details_group)
        
        # 设置分割器比例
        splitter.setSizes([400, 200])
        
        # 执行选项
        options_group = self._create_options_group()
        layout.addWidget(options_group)
        
        # 按钮区域
        buttons_layout = self._create_buttons_layout()
        layout.addLayout(buttons_layout)
    
    def _create_operations_group(self) -> QGroupBox:
        """创建操作列表组"""
        group = QGroupBox("📋 操作序列")
        layout = QVBoxLayout(group)
        
        # 操作表格
        self.operations_table = QTableWidget()
        self.operations_table.setColumnCount(6)
        self.operations_table.setHorizontalHeaderLabels([
            "选择", "序号", "类型", "目标", "参数", "延时"
        ])
        
        # 设置列宽
        header = self.operations_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.Fixed)
        
        self.operations_table.setColumnWidth(0, 50)   # 选择
        self.operations_table.setColumnWidth(1, 60)   # 序号
        self.operations_table.setColumnWidth(2, 100)  # 类型
        self.operations_table.setColumnWidth(5, 80)   # 延时
        
        self.operations_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.operations_table.setAlternatingRowColors(True)
        
        layout.addWidget(self.operations_table)
        
        # 操作控制按钮
        control_layout = QHBoxLayout()
        
        self.select_all_btn = QPushButton("✅ 全选")
        self.select_all_btn.setToolTip("选择所有操作")
        control_layout.addWidget(self.select_all_btn)
        
        self.select_none_btn = QPushButton("❌ 全不选")
        self.select_none_btn.setToolTip("取消选择所有操作")
        control_layout.addWidget(self.select_none_btn)
        
        self.test_selected_btn = QPushButton("🧪 测试选中")
        self.test_selected_btn.setToolTip("测试选中的操作")
        control_layout.addWidget(self.test_selected_btn)
        
        control_layout.addStretch()
        
        # 统计信息
        self.stats_label = QLabel("总计: 0 个操作")
        self.stats_label.setStyleSheet("color: #7f8c8d; font-size: 11px;")
        control_layout.addWidget(self.stats_label)
        
        layout.addLayout(control_layout)
        
        return group
    
    def _create_details_group(self) -> QGroupBox:
        """创建操作详情组"""
        group = QGroupBox("📝 操作详情")
        layout = QVBoxLayout(group)
        
        self.details_text = QTextEdit()
        self.details_text.setReadOnly(True)
        self.details_text.setMaximumHeight(150)
        self.details_text.setPlaceholderText("选择操作查看详细信息...")
        self.details_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 8px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 11px;
            }
        """)
        
        layout.addWidget(self.details_text)
        
        return group
    
    def _create_options_group(self) -> QGroupBox:
        """创建执行选项组"""
        group = QGroupBox("⚙️ 执行选项")
        layout = QHBoxLayout(group)
        
        # 执行模式
        self.dry_run_checkbox = QCheckBox("模拟执行（不实际操作）")
        self.dry_run_checkbox.setToolTip("启用后只显示操作过程，不实际执行")
        layout.addWidget(self.dry_run_checkbox)
        
        layout.addWidget(QLabel("|"))
        
        # 执行间隔
        layout.addWidget(QLabel("操作间隔:"))
        self.interval_spinbox = QSpinBox()
        self.interval_spinbox.setRange(0, 5000)
        self.interval_spinbox.setValue(500)
        self.interval_spinbox.setSuffix(" ms")
        self.interval_spinbox.setToolTip("每个操作之间的延时间隔")
        layout.addWidget(self.interval_spinbox)
        
        layout.addWidget(QLabel("|"))
        
        # 错误处理
        self.stop_on_error_checkbox = QCheckBox("遇到错误时停止")
        self.stop_on_error_checkbox.setChecked(True)
        self.stop_on_error_checkbox.setToolTip("操作失败时是否停止整个序列")
        layout.addWidget(self.stop_on_error_checkbox)
        
        layout.addStretch()
        
        return group
    
    def _create_buttons_layout(self) -> QHBoxLayout:
        """创建按钮布局"""
        layout = QHBoxLayout()
        
        # 左侧信息
        self.selected_count_label = QLabel("已选择: 0 个操作")
        self.selected_count_label.setStyleSheet("font-weight: bold; color: #3498db;")
        layout.addWidget(self.selected_count_label)
        
        layout.addStretch()
        
        # 右侧按钮
        self.save_btn = QPushButton("💾 保存为模板")
        self.save_btn.setToolTip("将当前操作序列保存为模板")
        layout.addWidget(self.save_btn)
        
        self.test_all_btn = QPushButton("🧪 测试全部")
        self.test_all_btn.setToolTip("测试所有选中的操作")
        layout.addWidget(self.test_all_btn)
        
        self.cancel_btn = QPushButton("❌ 取消")
        self.cancel_btn.setToolTip("取消操作")
        layout.addWidget(self.cancel_btn)
        
        self.execute_btn = QPushButton("▶️ 执行")
        self.execute_btn.setToolTip("执行选中的操作")
        self.execute_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border-radius: 4px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
            QPushButton:disabled {
                background-color: #95a5a6;
            }
        """)
        layout.addWidget(self.execute_btn)
        
        return layout
    
    def _setup_connections(self):
        """设置信号连接"""
        # 表格选择
        self.operations_table.itemSelectionChanged.connect(self._on_selection_changed)
        self.operations_table.cellClicked.connect(self._on_cell_clicked)
        
        # 控制按钮
        self.select_all_btn.clicked.connect(self._select_all)
        self.select_none_btn.clicked.connect(self._select_none)
        self.test_selected_btn.clicked.connect(self._test_selected)
        
        # 主按钮
        self.save_btn.clicked.connect(self._save_template)
        self.test_all_btn.clicked.connect(self._test_all)
        self.cancel_btn.clicked.connect(self.reject)
        self.execute_btn.clicked.connect(self._execute)
    
    def _load_operations(self):
        """加载操作到表格"""
        self.operations_table.setRowCount(len(self.operations))
        
        for i, operation in enumerate(self.operations):
            # 选择复选框
            checkbox = QCheckBox()
            checkbox.setChecked(True)  # 默认选中
            self.operations_table.setCellWidget(i, 0, checkbox)
            
            # 序号
            seq_item = QTableWidgetItem(str(i + 1))
            seq_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.operations_table.setItem(i, 1, seq_item)
            
            # 操作类型
            op_type = operation.get('type', 'unknown')
            type_item = QTableWidgetItem(self._format_operation_type(op_type))
            self.operations_table.setItem(i, 2, type_item)
            
            # 目标信息
            target = operation.get('target', {})
            target_text = self._format_target_info(target)
            target_item = QTableWidgetItem(target_text)
            self.operations_table.setItem(i, 3, target_item)
            
            # 参数信息
            params = operation.get('parameters', {})
            params_text = self._format_parameters(params)
            params_item = QTableWidgetItem(params_text)
            self.operations_table.setItem(i, 4, params_item)
            
            # 延时
            delay = operation.get('delay', 0)
            delay_item = QTableWidgetItem(f"{delay}ms")
            delay_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.operations_table.setItem(i, 5, delay_item)
        
        self._update_stats()
        self._update_selected_count()
    
    def _format_operation_type(self, op_type: str) -> str:
        """格式化操作类型显示"""
        type_map = {
            'click': '🖱️ 点击',
            'type': '⌨️ 输入',
            'hotkey': '🔗 快捷键',
            'drag': '↔️ 拖拽',
            'scroll': '🔄 滚动',
            'wait': '⏱️ 等待'
        }
        return type_map.get(op_type, f"❓ {op_type}")
    
    def _format_target_info(self, target: Dict[str, Any]) -> str:
        """格式化目标信息"""
        if 'position' in target:
            pos = target['position']
            return f"位置: ({pos[0]}, {pos[1]})"
        elif 'label' in target:
            return f"目标: {target['label']}"
        else:
            return "无目标"
    
    def _format_parameters(self, params: Dict[str, Any]) -> str:
        """格式化参数信息"""
        if not params:
            return "无参数"
        
        param_strs = []
        for key, value in params.items():
            if key == 'text':
                param_strs.append(f"文本: {value[:20]}...")
            elif key == 'keys':
                param_strs.append(f"按键: {'+'.join(value)}")
            elif key == 'button':
                param_strs.append(f"按钮: {value}")
            else:
                param_strs.append(f"{key}: {value}")
        
        return " | ".join(param_strs)
    
    def _on_selection_changed(self):
        """表格选择改变"""
        current_row = self.operations_table.currentRow()
        if 0 <= current_row < len(self.operations):
            operation = self.operations[current_row]
            self._show_operation_details(operation)
    
    def _on_cell_clicked(self, row: int, column: int):
        """单元格点击"""
        if column == 0:  # 选择列
            self._update_selected_count()
    
    def _show_operation_details(self, operation: Dict[str, Any]):
        """显示操作详情"""
        details = []
        details.append(f"操作类型: {operation.get('type', 'unknown')}")
        details.append(f"描述: {operation.get('description', '无描述')}")
        
        if 'target' in operation:
            target = operation['target']
            details.append("目标信息:")
            for key, value in target.items():
                details.append(f"  {key}: {value}")
        
        if 'parameters' in operation:
            params = operation['parameters']
            details.append("参数信息:")
            for key, value in params.items():
                details.append(f"  {key}: {value}")
        
        details.append(f"执行延时: {operation.get('delay', 0)}ms")
        
        self.details_text.setPlainText("\n".join(details))
    
    def _select_all(self):
        """全选操作"""
        for i in range(self.operations_table.rowCount()):
            checkbox = self.operations_table.cellWidget(i, 0)
            if checkbox:
                checkbox.setChecked(True)
        self._update_selected_count()
    
    def _select_none(self):
        """全不选操作"""
        for i in range(self.operations_table.rowCount()):
            checkbox = self.operations_table.cellWidget(i, 0)
            if checkbox:
                checkbox.setChecked(False)
        self._update_selected_count()
    
    def _get_selected_operations(self) -> List[Dict[str, Any]]:
        """获取选中的操作"""
        selected = []
        for i in range(self.operations_table.rowCount()):
            checkbox = self.operations_table.cellWidget(i, 0)
            if checkbox and checkbox.isChecked():
                selected.append(self.operations[i])
        return selected
    
    def _update_selected_count(self):
        """更新选中数量"""
        selected = self._get_selected_operations()
        count = len(selected)
        self.selected_count_label.setText(f"已选择: {count} 个操作")
        self.execute_btn.setEnabled(count > 0)
    
    def _update_stats(self):
        """更新统计信息"""
        total = len(self.operations)
        self.stats_label.setText(f"总计: {total} 个操作")
    
    def _test_selected(self):
        """测试选中操作"""
        selected = self._get_selected_operations()
        if not selected:
            QMessageBox.warning(self, "提示", "请先选择要测试的操作")
            return
        
        # 这里可以实现测试逻辑
        QMessageBox.information(self, "测试", f"将测试 {len(selected)} 个操作")
    
    def _test_all(self):
        """测试全部操作"""
        self._test_selected()
    
    def _save_template(self):
        """保存为模板"""
        selected = self._get_selected_operations()
        if not selected:
            QMessageBox.warning(self, "提示", "请先选择要保存的操作")
            return
        
        # 这里可以实现保存模板逻辑
        QMessageBox.information(self, "保存", f"将保存 {len(selected)} 个操作为模板")
    
    def _execute(self):
        """执行操作"""
        selected = self._get_selected_operations()
        if not selected:
            QMessageBox.warning(self, "提示", "请先选择要执行的操作")
            return
        
        # 确认执行
        reply = QMessageBox.question(
            self, "确认执行",
            f"确定要执行 {len(selected)} 个操作吗？\n\n"
            f"模拟执行: {'是' if self.dry_run_checkbox.isChecked() else '否'}\n"
            f"操作间隔: {self.interval_spinbox.value()}ms\n"
            f"遇错停止: {'是' if self.stop_on_error_checkbox.isChecked() else '否'}",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            # 添加执行选项到操作中
            for operation in selected:
                operation['dry_run'] = self.dry_run_checkbox.isChecked()
                operation['interval'] = self.interval_spinbox.value()
                operation['stop_on_error'] = self.stop_on_error_checkbox.isChecked()
            
            self.execute_confirmed.emit(selected)
            self.accept()
