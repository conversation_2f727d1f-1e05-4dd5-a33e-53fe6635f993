# -*- coding: utf-8 -*-
"""
置信度加权融合模块
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

import numpy as np
from typing import List, Dict, Any, Optional, Tuple, Callable
from enum import Enum
import math

from ..utils.logger import Logger
from ..utils.data_structures import DetectionResult, BoundingBox, DetectionSource


class FusionMethod(Enum):
    """融合方法枚举"""
    WEIGHTED_AVERAGE = "weighted_average"
    MAXIMUM = "maximum"
    MINIMUM = "minimum"
    GEOMETRIC_MEAN = "geometric_mean"
    HARMONIC_MEAN = "harmonic_mean"
    BAYESIAN = "bayesian"
    DEMPSTER_SHAFER = "dempster_shafer"


class ConfidenceFusion:
    """置信度融合器类"""
    
    def __init__(self):
        """初始化置信度融合器"""
        self.logger = Logger().get_logger(__name__)
        
        # 融合方法映射
        self.fusion_methods = {
            FusionMethod.WEIGHTED_AVERAGE: self._weighted_average_fusion,
            FusionMethod.MAXIMUM: self._maximum_fusion,
            FusionMethod.MINIMUM: self._minimum_fusion,
            FusionMethod.GEOMETRIC_MEAN: self._geometric_mean_fusion,
            FusionMethod.HARMONIC_MEAN: self._harmonic_mean_fusion,
            FusionMethod.BAYESIAN: self._bayesian_fusion,
            FusionMethod.DEMPSTER_SHAFER: self._dempster_shafer_fusion
        }
    
    def fuse_confidences(self,
                        confidences: List[float],
                        weights: Optional[List[float]] = None,
                        method: FusionMethod = FusionMethod.WEIGHTED_AVERAGE,
                        **kwargs) -> float:
        """
        融合多个置信度值
        
        Args:
            confidences: 置信度列表
            weights: 权重列表，None时使用均等权重
            method: 融合方法
            **kwargs: 其他参数
            
        Returns:
            float: 融合后的置信度
        """
        try:
            if not confidences:
                return 0.0
            
            if len(confidences) == 1:
                return confidences[0]
            
            # 验证输入
            confidences = [max(0.0, min(1.0, c)) for c in confidences]
            
            if weights is None:
                weights = [1.0] * len(confidences)
            elif len(weights) != len(confidences):
                self.logger.warning("权重数量与置信度数量不匹配，使用均等权重")
                weights = [1.0] * len(confidences)
            
            # 归一化权重
            total_weight = sum(weights)
            if total_weight > 0:
                weights = [w / total_weight for w in weights]
            else:
                weights = [1.0 / len(weights)] * len(weights)
            
            # 应用融合方法
            fusion_func = self.fusion_methods.get(method, self._weighted_average_fusion)
            result = fusion_func(confidences, weights, **kwargs)
            
            # 确保结果在有效范围内
            return max(0.0, min(1.0, result))
            
        except Exception as e:
            self.logger.error(f"置信度融合失败: {e}")
            return max(confidences) if confidences else 0.0
    
    def _weighted_average_fusion(self, confidences: List[float], weights: List[float], **kwargs) -> float:
        """加权平均融合"""
        return sum(c * w for c, w in zip(confidences, weights))
    
    def _maximum_fusion(self, confidences: List[float], weights: List[float], **kwargs) -> float:
        """最大值融合"""
        return max(confidences)
    
    def _minimum_fusion(self, confidences: List[float], weights: List[float], **kwargs) -> float:
        """最小值融合"""
        return min(confidences)
    
    def _geometric_mean_fusion(self, confidences: List[float], weights: List[float], **kwargs) -> float:
        """几何平均融合"""
        try:
            # 避免零值导致的问题
            safe_confidences = [max(1e-10, c) for c in confidences]
            
            # 加权几何平均
            log_sum = sum(w * math.log(c) for c, w in zip(safe_confidences, weights))
            return math.exp(log_sum)
            
        except Exception as e:
            self.logger.error(f"几何平均融合失败: {e}")
            return self._weighted_average_fusion(confidences, weights)
    
    def _harmonic_mean_fusion(self, confidences: List[float], weights: List[float], **kwargs) -> float:
        """调和平均融合"""
        try:
            # 避免零值
            safe_confidences = [max(1e-10, c) for c in confidences]
            
            # 加权调和平均
            weighted_reciprocal_sum = sum(w / c for c, w in zip(safe_confidences, weights))
            return 1.0 / weighted_reciprocal_sum if weighted_reciprocal_sum > 0 else 0.0
            
        except Exception as e:
            self.logger.error(f"调和平均融合失败: {e}")
            return self._weighted_average_fusion(confidences, weights)
    
    def _bayesian_fusion(self, confidences: List[float], weights: List[float], **kwargs) -> float:
        """贝叶斯融合"""
        try:
            # 将置信度转换为概率
            priors = kwargs.get('priors', [0.5] * len(confidences))
            
            # 计算似然比
            likelihood_ratios = []
            for conf, prior in zip(confidences, priors):
                if prior > 0 and prior < 1:
                    # 似然比 = P(evidence|H1) / P(evidence|H0)
                    likelihood_ratio = (conf / prior) / ((1 - conf) / (1 - prior))
                    likelihood_ratios.append(likelihood_ratio)
                else:
                    likelihood_ratios.append(1.0)
            
            # 加权组合似然比
            combined_lr = 1.0
            for lr, weight in zip(likelihood_ratios, weights):
                combined_lr *= (lr ** weight)
            
            # 转换回概率
            prior_odds = 1.0  # 假设先验概率为0.5
            posterior_odds = prior_odds * combined_lr
            posterior_prob = posterior_odds / (1 + posterior_odds)
            
            return posterior_prob
            
        except Exception as e:
            self.logger.error(f"贝叶斯融合失败: {e}")
            return self._weighted_average_fusion(confidences, weights)
    
    def _dempster_shafer_fusion(self, confidences: List[float], weights: List[float], **kwargs) -> float:
        """Dempster-Shafer证据理论融合"""
        try:
            # 简化的DS融合，将置信度视为基本概率分配
            # m(A) = confidence, m(Ω) = 1 - confidence
            
            # 初始化
            m_target = confidences[0] * weights[0]
            m_unknown = (1 - confidences[0]) * weights[0]
            
            # 逐步融合
            for i in range(1, len(confidences)):
                conf = confidences[i]
                weight = weights[i]
                
                # 新证据的基本概率分配
                m2_target = conf * weight
                m2_unknown = (1 - conf) * weight
                
                # Dempster组合规则
                # K = 冲突系数（这里简化为0）
                k = 0.0
                
                # 组合后的基本概率分配
                new_m_target = (m_target * m2_target + m_target * m2_unknown + m_unknown * m2_target) / (1 - k)
                new_m_unknown = (m_unknown * m2_unknown) / (1 - k)
                
                m_target = new_m_target
                m_unknown = new_m_unknown
            
            return m_target
            
        except Exception as e:
            self.logger.error(f"Dempster-Shafer融合失败: {e}")
            return self._weighted_average_fusion(confidences, weights)
    
    def adaptive_weight_calculation(self,
                                  results: List[DetectionResult],
                                  base_weights: Optional[Dict[DetectionSource, float]] = None) -> List[float]:
        """
        自适应权重计算
        
        Args:
            results: 检测结果列表
            base_weights: 基础权重字典
            
        Returns:
            List[float]: 计算得到的权重列表
        """
        try:
            if not results:
                return []
            
            # 默认基础权重
            if base_weights is None:
                base_weights = {
                    DetectionSource.YOLO: 0.6,
                    DetectionSource.TEMPLATE: 0.4,
                    DetectionSource.FUSION: 0.8
                }
            
            weights = []
            
            for result in results:
                # 基础权重
                base_weight = base_weights.get(result.source, 0.5)
                
                # 置信度调整
                confidence_factor = result.confidence
                
                # 边界框面积调整（较大的框可能更可靠）
                area_factor = min(1.0, result.bbox.area / 10000.0)  # 归一化到合理范围
                
                # 时间衰减（较新的检测更可靠）
                import time
                time_factor = max(0.1, 1.0 - (time.time() - result.timestamp) / 60.0)  # 1分钟内衰减
                
                # 组合权重
                adaptive_weight = base_weight * confidence_factor * (0.5 + 0.5 * area_factor) * time_factor
                weights.append(adaptive_weight)
            
            # 归一化权重
            total_weight = sum(weights)
            if total_weight > 0:
                weights = [w / total_weight for w in weights]
            else:
                weights = [1.0 / len(weights)] * len(weights)
            
            return weights
            
        except Exception as e:
            self.logger.error(f"自适应权重计算失败: {e}")
            return [1.0 / len(results)] * len(results)
    
    def uncertainty_aware_fusion(self,
                                confidences: List[float],
                                uncertainties: List[float],
                                method: FusionMethod = FusionMethod.WEIGHTED_AVERAGE) -> Tuple[float, float]:
        """
        考虑不确定性的融合
        
        Args:
            confidences: 置信度列表
            uncertainties: 不确定性列表
            method: 融合方法
            
        Returns:
            Tuple[float, float]: (融合置信度, 融合不确定性)
        """
        try:
            if not confidences or not uncertainties:
                return 0.0, 1.0
            
            if len(confidences) != len(uncertainties):
                self.logger.warning("置信度和不确定性数量不匹配")
                uncertainties = uncertainties[:len(confidences)] + [0.5] * (len(confidences) - len(uncertainties))
            
            # 基于不确定性计算权重（不确定性越低，权重越高）
            precision_weights = [1.0 / (u + 1e-10) for u in uncertainties]
            total_precision = sum(precision_weights)
            weights = [w / total_precision for w in precision_weights]
            
            # 融合置信度
            fused_confidence = self.fuse_confidences(confidences, weights, method)
            
            # 融合不确定性（加权平均）
            fused_uncertainty = sum(u * w for u, w in zip(uncertainties, weights))
            
            return fused_confidence, fused_uncertainty
            
        except Exception as e:
            self.logger.error(f"不确定性感知融合失败: {e}")
            return max(confidences) if confidences else 0.0, min(uncertainties) if uncertainties else 1.0
    
    def temporal_fusion(self,
                       historical_results: List[List[DetectionResult]],
                       decay_factor: float = 0.9,
                       max_history: int = 10) -> List[DetectionResult]:
        """
        时序融合
        
        Args:
            historical_results: 历史检测结果列表
            decay_factor: 时间衰减因子
            max_history: 最大历史长度
            
        Returns:
            List[DetectionResult]: 时序融合后的结果
        """
        try:
            if not historical_results:
                return []
            
            # 限制历史长度
            recent_results = historical_results[-max_history:]
            
            # 为每个时间步分配权重
            time_weights = [decay_factor ** i for i in range(len(recent_results))]
            time_weights.reverse()  # 最新的权重最大
            
            # 归一化时间权重
            total_time_weight = sum(time_weights)
            if total_time_weight > 0:
                time_weights = [w / total_time_weight for w in time_weights]
            
            # 收集所有检测结果
            all_results = []
            for results, time_weight in zip(recent_results, time_weights):
                for result in results:
                    # 调整置信度
                    adjusted_result = DetectionResult(
                        bbox=result.bbox,
                        confidence=result.confidence * time_weight,
                        class_id=result.class_id,
                        class_name=result.class_name,
                        template_id=result.template_id,
                        source=result.source,
                        timestamp=result.timestamp,
                        metadata=result.metadata.copy()
                    )
                    adjusted_result.metadata["temporal_weight"] = time_weight
                    all_results.append(adjusted_result)
            
            return all_results
            
        except Exception as e:
            self.logger.error(f"时序融合失败: {e}")
            return historical_results[-1] if historical_results else []
    
    def multi_scale_fusion(self,
                          scale_results: Dict[float, List[DetectionResult]],
                          scale_weights: Optional[Dict[float, float]] = None) -> List[DetectionResult]:
        """
        多尺度融合
        
        Args:
            scale_results: 不同尺度的检测结果
            scale_weights: 尺度权重
            
        Returns:
            List[DetectionResult]: 多尺度融合后的结果
        """
        try:
            if not scale_results:
                return []
            
            # 默认尺度权重
            if scale_weights is None:
                scale_weights = {scale: 1.0 for scale in scale_results.keys()}
            
            # 归一化尺度权重
            total_scale_weight = sum(scale_weights.values())
            if total_scale_weight > 0:
                scale_weights = {k: v / total_scale_weight for k, v in scale_weights.items()}
            
            # 收集所有结果
            all_results = []
            for scale, results in scale_results.items():
                scale_weight = scale_weights.get(scale, 1.0)
                
                for result in results:
                    # 调整置信度
                    adjusted_result = DetectionResult(
                        bbox=result.bbox,
                        confidence=result.confidence * scale_weight,
                        class_id=result.class_id,
                        class_name=result.class_name,
                        template_id=result.template_id,
                        source=result.source,
                        timestamp=result.timestamp,
                        metadata=result.metadata.copy()
                    )
                    adjusted_result.metadata["scale"] = scale
                    adjusted_result.metadata["scale_weight"] = scale_weight
                    all_results.append(adjusted_result)
            
            return all_results
            
        except Exception as e:
            self.logger.error(f"多尺度融合失败: {e}")
            # 返回最大尺度的结果
            max_scale = max(scale_results.keys()) if scale_results else 1.0
            return scale_results.get(max_scale, [])
    
    def get_fusion_stats(self, 
                        original_confidences: List[float],
                        fused_confidence: float,
                        method: FusionMethod) -> Dict[str, Any]:
        """
        获取融合统计信息
        
        Args:
            original_confidences: 原始置信度列表
            fused_confidence: 融合后置信度
            method: 融合方法
            
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            if not original_confidences:
                return {}
            
            return {
                "method": method.value,
                "input_count": len(original_confidences),
                "original_confidences": original_confidences,
                "fused_confidence": fused_confidence,
                "confidence_gain": fused_confidence - max(original_confidences),
                "confidence_variance": np.var(original_confidences),
                "confidence_std": np.std(original_confidences),
                "min_confidence": min(original_confidences),
                "max_confidence": max(original_confidences),
                "avg_confidence": np.mean(original_confidences)
            }
            
        except Exception as e:
            self.logger.error(f"融合统计计算失败: {e}")
            return {}
