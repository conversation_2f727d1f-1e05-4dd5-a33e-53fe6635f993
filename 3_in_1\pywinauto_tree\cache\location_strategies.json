{"d034894335913d2743e29244212050b3": {"environment": {"os": "Windows", "os_version": "10.0.26100", "machine": "AMD64", "processor": "Intel64 Family 6 Model 183 Stepping 1, GenuineIntel", "python_version": "3.10.11", "pywinauto_version": "0.6.8"}, "app_info": {"process_name": "Cursor.exe", "window_title": "control_tree_viewer.py - pytree - Cursor", "app_version": "-12041.0.0.0", "file_path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\Cursor.exe", "create_time": "2025-01-21T17:53:20.952993"}, "window_info": {"hwnd": 460520, "title": "control_tree_viewer.py - pytree - Cursor", "class": "Chrome_WidgetWin_1", "pid": 8344, "process_name": "Cursor.exe"}, "window_strategy": "# 标题精确匹配\nfrom pywinauto import Desktop, Application\nwindow = Desktop(backend='uia').window(title='control_tree_viewer.py - pytree - Cursor')\n", "last_update": "2025-01-21T20:58:57.367831"}}