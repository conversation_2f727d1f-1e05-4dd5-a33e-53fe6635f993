# -*- coding: utf-8 -*-
"""
改进的模板创建对话框
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
    QLabel, QLineEdit, QTextEdit, QGroupBox,
    QDoubleSpinBox, QMessageBox, QFileDialog, QProgressBar
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QPixmap, QImage, QFont

import cv2
import numpy as np
from pathlib import Path
import time

from ..utils.logger import Logger
from .screen_capture_overlay import start_screen_capture


class ImagePreviewWidget(QLabel):
    """图像预览组件，支持缩放和平移"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMinimumSize(400, 300)
        self.setStyleSheet("border: 2px solid #ccc; background-color: #f9f9f9;")
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 图像数据
        self.original_image = None
        self.display_pixmap = None
        self.scale_factor = 1.0
        
        # 设置默认文本
        self.setText("📷 图像预览区域\n\n点击上方按钮获取图像")
        self.setWordWrap(True)
    
    def set_image(self, cv_image):
        """设置要显示的图像"""
        try:
            self.original_image = cv_image.copy()
            self._update_display()
            
        except Exception as e:
            print(f"设置图像失败: {e}")
    
    def _update_display(self):
        """更新显示"""
        if self.original_image is None:
            return
        
        try:
            # 转换为QPixmap
            height, width = self.original_image.shape[:2]
            bytes_per_line = 3 * width
            rgb_image = cv2.cvtColor(self.original_image, cv2.COLOR_BGR2RGB)
            q_image = QImage(rgb_image.data, width, height, bytes_per_line, QImage.Format.Format_RGB888)
            pixmap = QPixmap.fromImage(q_image)
            
            # 缩放到组件大小
            scaled_pixmap = pixmap.scaled(
                self.size(),
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation
            )
            
            self.display_pixmap = scaled_pixmap
            self.setPixmap(scaled_pixmap)
            
        except Exception as e:
            print(f"更新显示失败: {e}")
    
    def resizeEvent(self, event):
        """窗口大小改变事件"""
        super().resizeEvent(event)
        if self.original_image is not None:
            QTimer.singleShot(100, self._update_display)


class ImprovedTemplateDialog(QDialog):
    """改进的模板创建对话框"""
    
    template_created = pyqtSignal(str, dict)  # template_id, template_data
    
    def __init__(self, parent=None):
        """
        初始化改进的模板创建对话框
        
        Args:
            parent: 父窗口
        """
        super().__init__(parent)
        self.logger = Logger().get_logger(__name__)
        
        # 数据
        self.captured_image = None
        self.capture_overlay = None
        
        self._setup_ui()
        self._connect_signals()
        
        self.logger.info("改进的模板创建对话框初始化完成")
    
    def _setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("创建模板 - 改进版")
        self.setModal(True)
        self.resize(700, 800)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        
        # 标题
        title_label = QLabel("🎯 创建检测模板")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("QLabel { color: #2c3e50; padding: 10px; }")
        layout.addWidget(title_label)
        
        # 获取图像组
        capture_group = QGroupBox("📷 获取图像")
        capture_group.setStyleSheet("QGroupBox { font-weight: bold; }")
        capture_layout = QVBoxLayout(capture_group)
        capture_layout.setSpacing(15)
        
        # 说明文本
        info_label = QLabel(
            "🔹 选择获取模板图像的方式：\n"
            "• 屏幕区域截取：直接在屏幕上选择需要的区域\n"
            "• 文件导入：从本地图像文件中选择"
        )
        info_label.setWordWrap(True)
        info_label.setStyleSheet(
            "QLabel { "
            "background-color: #e8f4fd; "
            "padding: 15px; "
            "border: 1px solid #bee5eb; "
            "border-radius: 8px; "
            "color: #0c5460; "
            "}"
        )
        capture_layout.addWidget(info_label)
        
        # 按钮组
        button_layout = QHBoxLayout()
        button_layout.setSpacing(15)
        
        self.screen_capture_btn = QPushButton("🖥️ 屏幕区域截取")
        self.screen_capture_btn.setMinimumHeight(50)
        self.screen_capture_btn.setStyleSheet(
            "QPushButton { "
            "background-color: #007bff; "
            "color: white; "
            "border: none; "
            "border-radius: 8px; "
            "font-size: 14px; "
            "font-weight: bold; "
            "} "
            "QPushButton:hover { background-color: #0056b3; } "
            "QPushButton:pressed { background-color: #004085; }"
        )
        button_layout.addWidget(self.screen_capture_btn)
        
        self.file_import_btn = QPushButton("📁 文件导入")
        self.file_import_btn.setMinimumHeight(50)
        self.file_import_btn.setStyleSheet(
            "QPushButton { "
            "background-color: #28a745; "
            "color: white; "
            "border: none; "
            "border-radius: 8px; "
            "font-size: 14px; "
            "font-weight: bold; "
            "} "
            "QPushButton:hover { background-color: #1e7e34; } "
            "QPushButton:pressed { background-color: #155724; }"
        )
        button_layout.addWidget(self.file_import_btn)
        
        capture_layout.addLayout(button_layout)
        
        # 状态标签
        self.status_label = QLabel("📋 请选择获取图像的方式")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.status_label.setStyleSheet(
            "QLabel { "
            "padding: 10px; "
            "background-color: #f8f9fa; "
            "border: 1px solid #dee2e6; "
            "border-radius: 5px; "
            "color: #495057; "
            "}"
        )
        capture_layout.addWidget(self.status_label)
        
        layout.addWidget(capture_group)
        
        # 预览组
        preview_group = QGroupBox("🖼️ 图像预览")
        preview_group.setStyleSheet("QGroupBox { font-weight: bold; }")
        preview_layout = QVBoxLayout(preview_group)
        
        self.preview_widget = ImagePreviewWidget()
        preview_layout.addWidget(self.preview_widget)
        
        layout.addWidget(preview_group)
        
        # 模板信息组
        info_group = QGroupBox("📝 模板信息")
        info_group.setStyleSheet("QGroupBox { font-weight: bold; }")
        info_layout = QVBoxLayout(info_group)
        info_layout.setSpacing(15)
        
        # 模板名称
        name_layout = QHBoxLayout()
        name_layout.addWidget(QLabel("名称:"))
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("输入模板名称...")
        self.name_edit.setMinimumHeight(35)
        name_layout.addWidget(self.name_edit)
        info_layout.addLayout(name_layout)
        
        # 模板描述
        desc_layout = QVBoxLayout()
        desc_layout.addWidget(QLabel("描述:"))
        self.desc_edit = QTextEdit()
        self.desc_edit.setMaximumHeight(80)
        self.desc_edit.setPlaceholderText("输入模板描述...")
        desc_layout.addWidget(self.desc_edit)
        info_layout.addLayout(desc_layout)
        
        # 匹配阈值
        threshold_layout = QHBoxLayout()
        threshold_layout.addWidget(QLabel("匹配阈值:"))
        self.threshold_spin = QDoubleSpinBox()
        self.threshold_spin.setRange(0.1, 1.0)
        self.threshold_spin.setSingleStep(0.05)
        self.threshold_spin.setDecimals(2)
        self.threshold_spin.setValue(0.8)
        self.threshold_spin.setMinimumHeight(35)
        threshold_layout.addWidget(self.threshold_spin)
        threshold_layout.addStretch()
        info_layout.addLayout(threshold_layout)
        
        layout.addWidget(info_group)
        
        # 按钮组
        button_layout = QHBoxLayout()
        
        self.cancel_btn = QPushButton("❌ 取消")
        self.cancel_btn.setMinimumHeight(40)
        self.cancel_btn.setStyleSheet(
            "QPushButton { "
            "background-color: #6c757d; "
            "color: white; "
            "border: none; "
            "border-radius: 5px; "
            "font-size: 14px; "
            "} "
            "QPushButton:hover { background-color: #545b62; }"
        )
        button_layout.addWidget(self.cancel_btn)
        
        button_layout.addStretch()
        
        self.create_btn = QPushButton("✅ 创建模板")
        self.create_btn.setEnabled(False)
        self.create_btn.setMinimumHeight(40)
        self.create_btn.setStyleSheet(
            "QPushButton { "
            "background-color: #dc3545; "
            "color: white; "
            "border: none; "
            "border-radius: 5px; "
            "font-size: 14px; "
            "font-weight: bold; "
            "} "
            "QPushButton:hover { background-color: #c82333; } "
            "QPushButton:disabled { background-color: #cccccc; }"
        )
        button_layout.addWidget(self.create_btn)
        
        layout.addLayout(button_layout)
    
    def _connect_signals(self):
        """连接信号"""
        self.screen_capture_btn.clicked.connect(self._start_screen_capture)
        self.file_import_btn.clicked.connect(self._import_from_file)
        self.create_btn.clicked.connect(self._create_template)
        self.cancel_btn.clicked.connect(self.reject)
        
        # 文本变化
        self.name_edit.textChanged.connect(self._check_can_create)
    
    def _start_screen_capture(self):
        """开始屏幕区域截取"""
        try:
            self.status_label.setText("🎯 请在屏幕上拖拽选择需要的区域...")
            self.status_label.setStyleSheet(
                "QLabel { "
                "padding: 10px; "
                "background-color: #fff3cd; "
                "border: 1px solid #ffeaa7; "
                "border-radius: 5px; "
                "color: #856404; "
                "}"
            )
            
            # 隐藏对话框
            self.hide()
            
            # 启动屏幕截图覆盖层
            self.capture_overlay = start_screen_capture()
            self.capture_overlay.region_captured.connect(self._on_region_captured)
            self.capture_overlay.capture_cancelled.connect(self._on_capture_cancelled)
            
        except Exception as e:
            self.logger.error(f"开始屏幕截取失败: {e}")
            QMessageBox.critical(self, "错误", f"开始屏幕截取失败: {e}")
    
    def _on_region_captured(self, x, y, width, height, image):
        """区域截取完成回调"""
        try:
            self.captured_image = image
            self.preview_widget.set_image(image)
            
            self.status_label.setText(f"✅ 区域截取成功: {width}×{height} 像素")
            self.status_label.setStyleSheet(
                "QLabel { "
                "padding: 10px; "
                "background-color: #d4edda; "
                "border: 1px solid #c3e6cb; "
                "border-radius: 5px; "
                "color: #155724; "
                "}"
            )
            
            # 生成默认名称
            if not self.name_edit.text():
                timestamp = int(time.time())
                self.name_edit.setText(f"region_{timestamp}")
            
            self._check_can_create()
            self.logger.info(f"区域截取成功: {width}×{height}")
            
        except Exception as e:
            self.logger.error(f"处理截取区域失败: {e}")
            QMessageBox.critical(self, "错误", f"处理截取区域失败: {e}")
        
        finally:
            self.show()
    
    def _on_capture_cancelled(self):
        """截取取消回调"""
        self.status_label.setText("❌ 截取已取消，请重新选择")
        self.status_label.setStyleSheet(
            "QLabel { "
            "padding: 10px; "
            "background-color: #f8d7da; "
            "border: 1px solid #f5c6cb; "
            "border-radius: 5px; "
            "color: #721c24; "
            "}"
        )
        self.show()
    
    def _import_from_file(self):
        """从文件导入图像"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "选择模板图像",
                "",
                "图像文件 (*.png *.jpg *.jpeg *.bmp *.tiff)"
            )
            
            if file_path:
                self.captured_image = cv2.imread(file_path)
                
                if self.captured_image is not None:
                    self.preview_widget.set_image(self.captured_image)
                    
                    height, width = self.captured_image.shape[:2]
                    self.status_label.setText(f"✅ 文件导入成功: {Path(file_path).name} ({width}×{height})")
                    self.status_label.setStyleSheet(
                        "QLabel { "
                        "padding: 10px; "
                        "background-color: #d4edda; "
                        "border: 1px solid #c3e6cb; "
                        "border-radius: 5px; "
                        "color: #155724; "
                        "}"
                    )
                    
                    if not self.name_edit.text():
                        file_name = Path(file_path).stem
                        self.name_edit.setText(f"template_{file_name}")
                    
                    self._check_can_create()
                    self.logger.info(f"从文件导入图像成功: {file_path}")
                else:
                    QMessageBox.critical(self, "错误", "无法加载图像文件")
                    
        except Exception as e:
            self.logger.error(f"从文件导入图像失败: {e}")
            QMessageBox.critical(self, "错误", f"导入图像失败: {e}")
    
    def _check_can_create(self):
        """检查是否可以创建模板"""
        can_create = (
            self.captured_image is not None and
            len(self.name_edit.text().strip()) > 0
        )
        self.create_btn.setEnabled(can_create)
        
        if can_create:
            self.create_btn.setStyleSheet(
                "QPushButton { "
                "background-color: #28a745; "
                "color: white; "
                "border: none; "
                "border-radius: 5px; "
                "font-size: 14px; "
                "font-weight: bold; "
                "} "
                "QPushButton:hover { background-color: #1e7e34; }"
            )
    
    def _create_template(self):
        """创建模板"""
        try:
            if self.captured_image is None:
                QMessageBox.warning(self, "警告", "请先获取图像")
                return
            
            name = self.name_edit.text().strip()
            if not name:
                QMessageBox.warning(self, "警告", "请输入模板名称")
                return
            
            # 生成模板ID
            template_id = name.lower().replace(" ", "_").replace("-", "_")
            
            # 保存模板图像
            templates_dir = Path("templates")
            templates_dir.mkdir(exist_ok=True)
            
            template_file = templates_dir / f"{template_id}.png"
            cv2.imwrite(str(template_file), self.captured_image)
            
            # 创建模板数据
            template_data = {
                "name": name,
                "description": self.desc_edit.toPlainText(),
                "file_path": str(template_file),
                "threshold": self.threshold_spin.value(),
                "image_size": self.captured_image.shape[:2],
                "created_time": time.time()
            }
            
            # 发射信号
            self.template_created.emit(template_id, template_data)
            
            QMessageBox.information(self, "成功", f"模板 '{name}' 创建成功！")
            self.accept()
            
        except Exception as e:
            self.logger.error(f"创建模板失败: {e}")
            QMessageBox.critical(self, "错误", f"创建模板失败: {e}")
