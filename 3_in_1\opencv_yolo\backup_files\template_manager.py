# -*- coding: utf-8 -*-
"""
模板管理器模块
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

import sqlite3
import uuid
import shutil
from pathlib import Path
from typing import List, Optional, Dict, Any, Union
import json
import time
import cv2
import numpy as np
from PIL import Image

from ..utils.logger import Logger
from ..utils.data_structures import TemplateInfo
from ..utils.constants import DATA_DIR, TEMPLATES_DIR, TEMPLATES_DB_FILE, SUPPORTED_IMAGE_EXTENSIONS


class TemplateManager:
    """模板管理器类"""
    
    def __init__(self, db_path: Optional[Path] = None, templates_dir: Optional[Path] = None):
        """
        初始化模板管理器
        
        Args:
            db_path: 数据库文件路径
            templates_dir: 模板文件目录
        """
        self.logger = Logger().get_logger(__name__)
        
        # 路径设置
        self.db_path = db_path or (DATA_DIR / TEMPLATES_DB_FILE)
        self.templates_dir = templates_dir or TEMPLATES_DIR
        
        # 确保目录存在
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        self.templates_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化数据库
        self._init_database()
        
        self.logger.info(f"模板管理器初始化完成 - 数据库: {self.db_path}")
    
    def _init_database(self) -> None:
        """初始化数据库"""
        try:
            with sqlite3.connect(str(self.db_path)) as conn:
                cursor = conn.cursor()
                
                # 创建模板表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS templates (
                        template_id TEXT PRIMARY KEY,
                        name TEXT NOT NULL,
                        description TEXT,
                        file_path TEXT NOT NULL,
                        category TEXT DEFAULT 'default',
                        tags TEXT,  -- JSON格式存储标签列表
                        created_time REAL NOT NULL,
                        updated_time REAL NOT NULL,
                        usage_count INTEGER DEFAULT 0,
                        success_rate REAL DEFAULT 0.0,
                        metadata TEXT  -- JSON格式存储元数据
                    )
                ''')
                
                # 创建索引
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_category ON templates(category)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_created_time ON templates(created_time)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_usage_count ON templates(usage_count)')
                
                conn.commit()
                
            self.logger.info("数据库初始化完成")
            
        except Exception as e:
            self.logger.error(f"数据库初始化失败: {e}")
            raise
    
    def add_template(self, 
                    name: str,
                    image: Union[np.ndarray, Image.Image, str, Path],
                    description: str = "",
                    category: str = "default",
                    tags: List[str] = None,
                    metadata: Dict[str, Any] = None) -> Optional[str]:
        """
        添加模板
        
        Args:
            name: 模板名称
            image: 模板图像
            description: 模板描述
            category: 模板类别
            tags: 标签列表
            metadata: 元数据
            
        Returns:
            Optional[str]: 模板ID，失败返回None
        """
        try:
            # 生成模板ID
            template_id = str(uuid.uuid4())
            
            # 保存图像文件
            file_path = self._save_template_image(template_id, image)
            if file_path is None:
                return None
            
            # 创建模板信息
            template_info = TemplateInfo(
                template_id=template_id,
                name=name,
                description=description,
                file_path=file_path,
                category=category,
                tags=tags or [],
                metadata=metadata or {}
            )
            
            # 保存到数据库
            if self._save_template_to_db(template_info):
                self.logger.info(f"模板添加成功: {name} (ID: {template_id})")
                return template_id
            else:
                # 删除已保存的文件
                if file_path.exists():
                    file_path.unlink()
                return None
                
        except Exception as e:
            self.logger.error(f"添加模板失败: {e}")
            return None
    
    def _save_template_image(self, template_id: str, image: Union[np.ndarray, Image.Image, str, Path]) -> Optional[Path]:
        """保存模板图像"""
        try:
            # 目标文件路径
            target_path = self.templates_dir / f"{template_id}.png"
            
            # 处理不同类型的输入
            if isinstance(image, (str, Path)):
                # 文件路径，复制文件
                source_path = Path(image)
                if not source_path.exists():
                    self.logger.error(f"源图像文件不存在: {source_path}")
                    return None
                shutil.copy2(source_path, target_path)
                
            elif isinstance(image, Image.Image):
                # PIL图像
                image.save(target_path, "PNG")
                
            elif isinstance(image, np.ndarray):
                # numpy数组
                if image.ndim == 3:
                    # 假设是BGR格式，转换为RGB保存
                    image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                    Image.fromarray(image_rgb).save(target_path, "PNG")
                elif image.ndim == 2:
                    # 灰度图
                    Image.fromarray(image).save(target_path, "PNG")
                else:
                    self.logger.error(f"不支持的图像维度: {image.ndim}")
                    return None
            else:
                self.logger.error(f"不支持的图像类型: {type(image)}")
                return None
            
            return target_path
            
        except Exception as e:
            self.logger.error(f"保存模板图像失败: {e}")
            return None
    
    def _save_template_to_db(self, template_info: TemplateInfo) -> bool:
        """保存模板信息到数据库"""
        try:
            with sqlite3.connect(str(self.db_path)) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO templates (
                        template_id, name, description, file_path, category,
                        tags, created_time, updated_time, usage_count,
                        success_rate, metadata
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    template_info.template_id,
                    template_info.name,
                    template_info.description,
                    str(template_info.file_path),
                    template_info.category,
                    json.dumps(template_info.tags, ensure_ascii=False),
                    template_info.created_time,
                    template_info.updated_time,
                    template_info.usage_count,
                    template_info.success_rate,
                    json.dumps(template_info.metadata, ensure_ascii=False)
                ))
                
                conn.commit()
                return True
                
        except Exception as e:
            self.logger.error(f"保存模板到数据库失败: {e}")
            return False
    
    def get_template(self, template_id: str) -> Optional[TemplateInfo]:
        """
        获取模板信息
        
        Args:
            template_id: 模板ID
            
        Returns:
            Optional[TemplateInfo]: 模板信息，不存在返回None
        """
        try:
            with sqlite3.connect(str(self.db_path)) as conn:
                cursor = conn.cursor()
                
                cursor.execute('SELECT * FROM templates WHERE template_id = ?', (template_id,))
                row = cursor.fetchone()
                
                if row:
                    return self._row_to_template_info(row)
                else:
                    return None
                    
        except Exception as e:
            self.logger.error(f"获取模板失败: {e}")
            return None
    
    def list_templates(self, 
                      category: Optional[str] = None,
                      tags: Optional[List[str]] = None,
                      limit: Optional[int] = None,
                      offset: int = 0) -> List[TemplateInfo]:
        """
        列出模板
        
        Args:
            category: 类别过滤
            tags: 标签过滤
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            List[TemplateInfo]: 模板列表
        """
        try:
            with sqlite3.connect(str(self.db_path)) as conn:
                cursor = conn.cursor()
                
                # 构建查询
                query = 'SELECT * FROM templates WHERE 1=1'
                params = []
                
                if category:
                    query += ' AND category = ?'
                    params.append(category)
                
                if tags:
                    # 简单的标签匹配（包含任一标签）
                    tag_conditions = []
                    for tag in tags:
                        tag_conditions.append('tags LIKE ?')
                        params.append(f'%"{tag}"%')
                    query += f' AND ({" OR ".join(tag_conditions)})'
                
                query += ' ORDER BY created_time DESC'
                
                if limit:
                    query += ' LIMIT ? OFFSET ?'
                    params.extend([limit, offset])
                
                cursor.execute(query, params)
                rows = cursor.fetchall()
                
                return [self._row_to_template_info(row) for row in rows]
                
        except Exception as e:
            self.logger.error(f"列出模板失败: {e}")
            return []
    
    def _row_to_template_info(self, row: tuple) -> TemplateInfo:
        """将数据库行转换为TemplateInfo对象"""
        return TemplateInfo(
            template_id=row[0],
            name=row[1],
            description=row[2] or "",
            file_path=Path(row[3]),
            category=row[4] or "default",
            tags=json.loads(row[5]) if row[5] else [],
            created_time=row[6],
            updated_time=row[7],
            usage_count=row[8],
            success_rate=row[9],
            metadata=json.loads(row[10]) if row[10] else {}
        )

    def update_template(self, template_id: str, **kwargs) -> bool:
        """
        更新模板信息

        Args:
            template_id: 模板ID
            **kwargs: 要更新的字段

        Returns:
            bool: 更新是否成功
        """
        try:
            # 获取现有模板信息
            template_info = self.get_template(template_id)
            if template_info is None:
                self.logger.error(f"模板不存在: {template_id}")
                return False

            # 更新字段
            update_fields = []
            params = []

            if "name" in kwargs:
                update_fields.append("name = ?")
                params.append(kwargs["name"])

            if "description" in kwargs:
                update_fields.append("description = ?")
                params.append(kwargs["description"])

            if "category" in kwargs:
                update_fields.append("category = ?")
                params.append(kwargs["category"])

            if "tags" in kwargs:
                update_fields.append("tags = ?")
                params.append(json.dumps(kwargs["tags"], ensure_ascii=False))

            if "metadata" in kwargs:
                update_fields.append("metadata = ?")
                params.append(json.dumps(kwargs["metadata"], ensure_ascii=False))

            # 更新时间
            update_fields.append("updated_time = ?")
            params.append(time.time())

            # 添加WHERE条件
            params.append(template_id)

            if not update_fields:
                self.logger.warning("没有要更新的字段")
                return True

            # 执行更新
            with sqlite3.connect(str(self.db_path)) as conn:
                cursor = conn.cursor()

                query = f"UPDATE templates SET {', '.join(update_fields)} WHERE template_id = ?"
                cursor.execute(query, params)

                conn.commit()

                if cursor.rowcount > 0:
                    self.logger.info(f"模板更新成功: {template_id}")
                    return True
                else:
                    self.logger.warning(f"模板更新无变化: {template_id}")
                    return False

        except Exception as e:
            self.logger.error(f"更新模板失败: {e}")
            return False

    def remove_template(self, template_id: str, delete_file: bool = True) -> bool:
        """
        删除模板

        Args:
            template_id: 模板ID
            delete_file: 是否删除文件

        Returns:
            bool: 删除是否成功
        """
        try:
            # 获取模板信息
            template_info = self.get_template(template_id)
            if template_info is None:
                self.logger.error(f"模板不存在: {template_id}")
                return False

            # 从数据库删除
            with sqlite3.connect(str(self.db_path)) as conn:
                cursor = conn.cursor()

                cursor.execute('DELETE FROM templates WHERE template_id = ?', (template_id,))
                conn.commit()

                if cursor.rowcount == 0:
                    self.logger.warning(f"数据库中未找到模板: {template_id}")
                    return False

            # 删除文件
            if delete_file and template_info.file_path.exists():
                try:
                    template_info.file_path.unlink()
                    self.logger.info(f"模板文件已删除: {template_info.file_path}")
                except Exception as e:
                    self.logger.warning(f"删除模板文件失败: {e}")

            self.logger.info(f"模板删除成功: {template_id}")
            return True

        except Exception as e:
            self.logger.error(f"删除模板失败: {e}")
            return False

    def update_usage_stats(self, template_id: str, success: bool) -> bool:
        """
        更新使用统计

        Args:
            template_id: 模板ID
            success: 是否成功匹配

        Returns:
            bool: 更新是否成功
        """
        try:
            with sqlite3.connect(str(self.db_path)) as conn:
                cursor = conn.cursor()

                # 获取当前统计
                cursor.execute('SELECT usage_count, success_rate FROM templates WHERE template_id = ?',
                             (template_id,))
                row = cursor.fetchone()

                if row is None:
                    self.logger.error(f"模板不存在: {template_id}")
                    return False

                current_usage, current_success_rate = row

                # 计算新的统计
                new_usage = current_usage + 1
                if current_usage == 0:
                    new_success_rate = 1.0 if success else 0.0
                else:
                    total_successes = current_success_rate * current_usage
                    if success:
                        total_successes += 1
                    new_success_rate = total_successes / new_usage

                # 更新数据库
                cursor.execute('''
                    UPDATE templates
                    SET usage_count = ?, success_rate = ?, updated_time = ?
                    WHERE template_id = ?
                ''', (new_usage, new_success_rate, time.time(), template_id))

                conn.commit()

                self.logger.debug(f"使用统计已更新: {template_id} (使用次数: {new_usage}, 成功率: {new_success_rate:.2f})")
                return True

        except Exception as e:
            self.logger.error(f"更新使用统计失败: {e}")
            return False

    def get_categories(self) -> List[str]:
        """获取所有类别"""
        try:
            with sqlite3.connect(str(self.db_path)) as conn:
                cursor = conn.cursor()

                cursor.execute('SELECT DISTINCT category FROM templates ORDER BY category')
                rows = cursor.fetchall()

                return [row[0] for row in rows if row[0]]

        except Exception as e:
            self.logger.error(f"获取类别失败: {e}")
            return []

    def get_all_tags(self) -> List[str]:
        """获取所有标签"""
        try:
            all_tags = set()

            with sqlite3.connect(str(self.db_path)) as conn:
                cursor = conn.cursor()

                cursor.execute('SELECT tags FROM templates WHERE tags IS NOT NULL')
                rows = cursor.fetchall()

                for row in rows:
                    if row[0]:
                        tags = json.loads(row[0])
                        all_tags.update(tags)

                return sorted(list(all_tags))

        except Exception as e:
            self.logger.error(f"获取标签失败: {e}")
            return []

    def search_templates(self, query: str) -> List[TemplateInfo]:
        """
        搜索模板

        Args:
            query: 搜索关键词

        Returns:
            List[TemplateInfo]: 匹配的模板列表
        """
        try:
            with sqlite3.connect(str(self.db_path)) as conn:
                cursor = conn.cursor()

                # 在名称、描述、标签中搜索
                cursor.execute('''
                    SELECT * FROM templates
                    WHERE name LIKE ? OR description LIKE ? OR tags LIKE ?
                    ORDER BY usage_count DESC, success_rate DESC
                ''', (f'%{query}%', f'%{query}%', f'%{query}%'))

                rows = cursor.fetchall()
                return [self._row_to_template_info(row) for row in rows]

        except Exception as e:
            self.logger.error(f"搜索模板失败: {e}")
            return []

    def export_templates(self, export_path: Path, template_ids: Optional[List[str]] = None) -> bool:
        """
        导出模板

        Args:
            export_path: 导出路径
            template_ids: 要导出的模板ID列表，None表示导出所有

        Returns:
            bool: 导出是否成功
        """
        try:
            export_path = Path(export_path)
            export_path.mkdir(parents=True, exist_ok=True)

            # 获取要导出的模板
            if template_ids is None:
                templates = self.list_templates()
            else:
                templates = [self.get_template(tid) for tid in template_ids]
                templates = [t for t in templates if t is not None]

            # 导出模板文件和信息
            export_info = []

            for template in templates:
                # 复制图像文件
                if template.file_path.exists():
                    target_file = export_path / f"{template.template_id}.png"
                    shutil.copy2(template.file_path, target_file)

                    # 记录模板信息
                    export_info.append(template.to_dict())

            # 保存模板信息
            info_file = export_path / "templates_info.json"
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(export_info, f, ensure_ascii=False, indent=2)

            self.logger.info(f"模板导出成功: {len(templates)} 个模板导出到 {export_path}")
            return True

        except Exception as e:
            self.logger.error(f"导出模板失败: {e}")
            return False

    def import_templates(self, import_path: Path) -> int:
        """
        导入模板

        Args:
            import_path: 导入路径

        Returns:
            int: 成功导入的模板数量
        """
        try:
            import_path = Path(import_path)
            info_file = import_path / "templates_info.json"

            if not info_file.exists():
                self.logger.error(f"模板信息文件不存在: {info_file}")
                return 0

            # 读取模板信息
            with open(info_file, 'r', encoding='utf-8') as f:
                templates_info = json.load(f)

            imported_count = 0

            for template_data in templates_info:
                try:
                    # 检查图像文件是否存在
                    image_file = import_path / f"{template_data['template_id']}.png"
                    if not image_file.exists():
                        self.logger.warning(f"图像文件不存在: {image_file}")
                        continue

                    # 添加模板（系统会自动生成新的模板ID）
                    result = self.add_template(
                        name=template_data['name'],
                        image=image_file,
                        description=template_data.get('description', ''),
                        category=template_data.get('category', 'default'),
                        tags=template_data.get('tags', []),
                        metadata=template_data.get('metadata', {})
                    )

                    if result:
                        imported_count += 1

                except Exception as e:
                    self.logger.error(f"导入单个模板失败: {e}")
                    continue

            self.logger.info(f"模板导入完成: {imported_count} 个模板成功导入")
            return imported_count

        except Exception as e:
            self.logger.error(f"导入模板失败: {e}")
            return 0

    def cleanup_orphaned_files(self) -> int:
        """
        清理孤立的模板文件

        Returns:
            int: 清理的文件数量
        """
        try:
            # 获取数据库中的所有文件路径
            with sqlite3.connect(str(self.db_path)) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT file_path FROM templates')
                db_files = {Path(row[0]) for row in cursor.fetchall()}

            # 扫描模板目录中的所有文件
            actual_files = set(self.templates_dir.glob("*.png"))

            # 找出孤立文件
            orphaned_files = actual_files - db_files

            # 删除孤立文件
            deleted_count = 0
            for file_path in orphaned_files:
                try:
                    file_path.unlink()
                    deleted_count += 1
                    self.logger.debug(f"删除孤立文件: {file_path}")
                except Exception as e:
                    self.logger.warning(f"删除孤立文件失败 {file_path}: {e}")

            if deleted_count > 0:
                self.logger.info(f"清理完成: 删除了 {deleted_count} 个孤立文件")

            return deleted_count

        except Exception as e:
            self.logger.error(f"清理孤立文件失败: {e}")
            return 0

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        try:
            with sqlite3.connect(str(self.db_path)) as conn:
                cursor = conn.cursor()

                # 总数统计
                cursor.execute('SELECT COUNT(*) FROM templates')
                total_count = cursor.fetchone()[0]

                # 类别统计
                cursor.execute('SELECT category, COUNT(*) FROM templates GROUP BY category')
                category_stats = dict(cursor.fetchall())

                # 使用统计
                cursor.execute('SELECT AVG(usage_count), AVG(success_rate) FROM templates')
                avg_usage, avg_success = cursor.fetchone()

                return {
                    "total_templates": total_count,
                    "categories": category_stats,
                    "avg_usage_count": avg_usage or 0,
                    "avg_success_rate": avg_success or 0,
                    "database_path": str(self.db_path),
                    "templates_directory": str(self.templates_dir)
                }

        except Exception as e:
            self.logger.error(f"获取统计信息失败: {e}")
            return {}
