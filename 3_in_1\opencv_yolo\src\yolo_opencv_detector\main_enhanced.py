#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLO OpenCV检测器主程序 - 增强图标版本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

def setup_windows_taskbar_icon(app, logger):
    """设置Windows任务栏图标"""
    try:
        from PyQt6.QtGui import QIcon
        
        # 图标文件路径（按优先级）
        icon_paths = [
            project_root / "icons" / "yolo_detector_taskbar.ico",
            project_root / "icons" / "yolo_detector_app.ico",
            project_root / "icons" / "app_icon_64.png",
            project_root / "icons" / "app_icon_32.png"
        ]
        
        selected_icon = None
        for icon_path in icon_paths:
            if icon_path.exists():
                selected_icon = icon_path
                break
        
        if not selected_icon:
            logger.warning("未找到图标文件")
            return False
        
        # 创建图标
        icon = QIcon(str(selected_icon))
        
        # 设置应用程序图标
        app.setWindowIcon(icon)
        
        # Windows特定设置
        try:
            # 设置应用程序用户模型ID（重要！）
            import ctypes
            ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID(
                "YOLOOpenCVDetector.MainApp.v2.0"
            )
            logger.info("✅ Windows应用程序ID设置成功")
        except Exception as e:
            logger.warning(f"Windows应用程序ID设置失败: {e}")
        
        # 设置应用程序属性
        app.setApplicationName("YOLO OpenCV检测器")
        app.setApplicationDisplayName("YOLO OpenCV检测器")
        app.setApplicationVersion("2.0.0")
        app.setOrganizationName("YOLO OpenCV Project")
        app.setOrganizationDomain("yolo-opencv-detector.local")
        
        logger.info(f"✅ 任务栏图标设置成功: {selected_icon.name}")
        return True
        
    except Exception as e:
        logger.error(f"设置任务栏图标失败: {e}")
        return False

def main():
    """主函数"""
    try:
        # 导入PyQt6
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import Qt
        from PyQt6.QtGui import QIcon
        
        # 导入项目模块
        from yolo_opencv_detector.utils.logger import Logger
        from yolo_opencv_detector.utils.config_manager import ConfigManager
        from yolo_opencv_detector.gui.main_window_v2 import MainWindowV2
        
        # 初始化日志
        logger = Logger()
        logger.info("YOLO OpenCV检测器启动 - 增强图标版本")
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 设置高DPI支持
        try:
            app.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps, True)
        except AttributeError:
            pass
        
        # 设置任务栏图标（关键步骤）
        setup_windows_taskbar_icon(app, logger)
        
        # 创建配置管理器
        config_manager = ConfigManager()
        
        # 创建主窗口
        main_window = MainWindowV2(config_manager)
        
        # 显示主窗口
        main_window.show()
        
        logger.info("重构版GUI应用程序启动成功")
        
        # 运行应用程序
        sys.exit(app.exec())
        
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保已安装PyQt6: pip install PyQt6")
        sys.exit(1)
    except Exception as e:
        print(f"应用程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
