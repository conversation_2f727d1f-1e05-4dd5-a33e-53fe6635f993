# -*- coding: utf-8 -*-
"""
检测面板组件
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

from typing import Dict, Any, Optional, List
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, QLabel,
    QPushButton, QComboBox, QSpinBox, QDoubleSpinBox, QCheckBox,
    QSlider, QProgressBar, QTextEdit, QSplitter
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QPixmap, QPainter, QPen, QColor

from ...utils.logger import Logger
from ...utils.config_manager import ConfigManager
from .screenshot_widget import ScreenshotWidget


class DetectionPanel(QWidget):
    """检测面板类"""
    
    # 信号定义
    detection_requested = pyqtSignal(dict)  # 检测请求
    screenshot_requested = pyqtSignal(dict)  # 截图请求
    detection_started = pyqtSignal()
    detection_stopped = pyqtSignal()
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化检测面板
        
        Args:
            config_manager: 配置管理器
        """
        super().__init__()
        
        self.logger = Logger().get_logger(__name__)
        self.config_manager = config_manager
        
        # 状态管理
        self.is_detecting = False
        self.current_screenshot = None
        
        # 组件初始化
        self.screenshot_widget = None
        self.control_group = None
        self.settings_group = None
        self.status_group = None
        
        # 控制组件
        self.start_button = None
        self.stop_button = None
        self.screenshot_button = None
        self.auto_detect_checkbox = None
        
        # 设置组件
        self.monitor_combo = None
        self.confidence_slider = None
        self.nms_threshold_slider = None
        self.detection_interval_spinbox = None
        
        # 状态组件
        self.status_label = None
        self.progress_bar = None
        self.log_text = None
        
        # 定时器
        self.detection_timer = QTimer()
        self.detection_timer.timeout.connect(self._auto_detect)
        
        # 初始化界面
        self._init_ui()
        self._init_connections()
        self._load_settings()
        
        self.logger.info("检测面板初始化完成")
    
    def _init_ui(self) -> None:
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)  # 设置组件间距
        layout.setContentsMargins(8, 10, 8, 8)  # 设置边距

        # 添加标题和操作指导
        header_widget = QWidget()
        header_layout = QVBoxLayout(header_widget)
        header_layout.setContentsMargins(0, 0, 0, 0)

        title_label = QLabel("🎯 实时检测")
        title_label.setStyleSheet(
            "QLabel { "
            "font-size: 14px; "
            "font-weight: bold; "
            "color: #2c3e50; "
            "padding: 5px 0px; "
            "}"
        )
        header_layout.addWidget(title_label)

        help_label = QLabel("💡 配置检测参数后点击开始检测")
        help_label.setStyleSheet(
            "QLabel { "
            "font-size: 11px; "
            "color: #7f8c8d; "
            "font-style: italic; "
            "margin-bottom: 5px; "
            "}"
        )
        header_layout.addWidget(help_label)
        layout.addWidget(header_widget)

        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Vertical)
        layout.addWidget(splitter)

        # 截图显示区域
        self.screenshot_widget = ScreenshotWidget()
        splitter.addWidget(self.screenshot_widget)

        # 控制面板
        control_widget = QWidget()
        control_layout = QVBoxLayout(control_widget)
        control_layout.setSpacing(15)  # 进一步增加组件间距
        control_layout.setContentsMargins(5, 5, 5, 5)  # 增加边距

        # 控制组
        self._create_control_group()
        control_layout.addWidget(self.control_group)

        # 设置组
        self._create_settings_group()
        control_layout.addWidget(self.settings_group)

        # 状态组
        self._create_status_group()
        control_layout.addWidget(self.status_group)

        splitter.addWidget(control_widget)

        # 设置分割器比例 - 优化空间分配
        splitter.setSizes([250, 450])  # 平衡分配空间
    
    def _create_control_group(self) -> None:
        """创建控制组"""
        self.control_group = QGroupBox("🎮 检测控制")
        self.control_group.setMinimumHeight(120)  # 减少最小高度
        self.control_group.setMaximumHeight(140)  # 限制最大高度
        self.control_group.setStyleSheet("QGroupBox { font-weight: bold; }")
        layout = QVBoxLayout(self.control_group)
        layout.setSpacing(8)  # 减少组件间距

        # 操作提示
        tip_label = QLabel("📋 步骤1: 选择检测操作")
        tip_label.setStyleSheet(
            "QLabel { "
            "color: #34495e; "
            "font-size: 11px; "
            "padding: 3px; "
            "background-color: #ecf0f1; "
            "border-radius: 3px; "
            "}"
        )
        layout.addWidget(tip_label)

        # 按钮行
        button_layout = QHBoxLayout()
        button_layout.setSpacing(8)  # 设置按钮间距

        # 开始检测按钮
        self.start_button = QPushButton("▶️ 开始检测")
        self.start_button.setMinimumHeight(40)  # 增加按钮高度
        self.start_button.setStyleSheet(
            "QPushButton { "
            "background-color: #27ae60; "
            "color: white; "
            "border: none; "
            "border-radius: 5px; "
            "font-weight: bold; "
            "font-size: 12px; "
            "} "
            "QPushButton:hover { background-color: #229954; } "
            "QPushButton:pressed { background-color: #1e8449; }"
        )
        self.start_button.setToolTip("开始实时检测 - 将持续监控屏幕变化")
        button_layout.addWidget(self.start_button)

        # 停止检测按钮
        self.stop_button = QPushButton("⏹️ 停止检测")
        self.stop_button.setMinimumHeight(40)
        self.stop_button.setStyleSheet(
            "QPushButton { "
            "background-color: #e74c3c; "
            "color: white; "
            "border: none; "
            "border-radius: 5px; "
            "font-weight: bold; "
            "font-size: 12px; "
            "} "
            "QPushButton:hover { background-color: #c0392b; } "
            "QPushButton:pressed { background-color: #a93226; } "
            "QPushButton:disabled { background-color: #bdc3c7; }"
        )
        self.stop_button.setEnabled(False)
        self.stop_button.setToolTip("停止当前检测任务")
        button_layout.addWidget(self.stop_button)

        # 截图按钮
        self.screenshot_button = QPushButton("📷 立即截图")
        self.screenshot_button.setMinimumHeight(40)
        self.screenshot_button.setStyleSheet(
            "QPushButton { "
            "background-color: #3498db; "
            "color: white; "
            "border: none; "
            "border-radius: 5px; "
            "font-weight: bold; "
            "font-size: 12px; "
            "} "
            "QPushButton:hover { background-color: #2980b9; } "
            "QPushButton:pressed { background-color: #21618c; }"
        )
        self.screenshot_button.setToolTip("立即截取当前屏幕进行检测")
        button_layout.addWidget(self.screenshot_button)

        layout.addLayout(button_layout)

        # 自动检测选项
        auto_layout = QHBoxLayout()
        self.auto_detect_checkbox = QCheckBox("🔄 自动检测模式")
        self.auto_detect_checkbox.setStyleSheet(
            "QCheckBox { "
            "font-size: 12px; "
            "color: #2c3e50; "
            "} "
            "QCheckBox::indicator { "
            "width: 18px; "
            "height: 18px; "
            "}"
        )
        self.auto_detect_checkbox.setToolTip("启用后将按设定间隔自动进行检测")
        auto_layout.addWidget(self.auto_detect_checkbox)
        auto_layout.addStretch()
        layout.addLayout(auto_layout)
    
    def _create_settings_group(self) -> None:
        """创建设置组"""
        self.settings_group = QGroupBox("检测设置")
        self.settings_group.setMinimumHeight(120)  # 减少最小高度
        self.settings_group.setMaximumHeight(140)  # 限制最大高度
        layout = QVBoxLayout(self.settings_group)
        layout.setSpacing(6)  # 设置紧凑间距
        
        # 显示器选择
        monitor_layout = QHBoxLayout()
        monitor_layout.addWidget(QLabel("显示器:"))
        self.monitor_combo = QComboBox()
        self.monitor_combo.addItems(["主显示器", "显示器1", "显示器2"])
        monitor_layout.addWidget(self.monitor_combo)
        layout.addLayout(monitor_layout)
        
        # 置信度阈值
        confidence_layout = QHBoxLayout()
        confidence_layout.addWidget(QLabel("置信度阈值:"))
        self.confidence_slider = QSlider(Qt.Orientation.Horizontal)
        self.confidence_slider.setRange(1, 100)
        self.confidence_slider.setValue(50)
        self.confidence_label = QLabel("0.50")
        confidence_layout.addWidget(self.confidence_slider)
        confidence_layout.addWidget(self.confidence_label)
        layout.addLayout(confidence_layout)
        
        # NMS阈值
        nms_layout = QHBoxLayout()
        nms_layout.addWidget(QLabel("NMS阈值:"))
        self.nms_threshold_slider = QSlider(Qt.Orientation.Horizontal)
        self.nms_threshold_slider.setRange(1, 100)
        self.nms_threshold_slider.setValue(50)
        self.nms_label = QLabel("0.50")
        nms_layout.addWidget(self.nms_threshold_slider)
        nms_layout.addWidget(self.nms_label)
        layout.addLayout(nms_layout)
        
        # 检测间隔
        interval_layout = QHBoxLayout()
        interval_layout.addWidget(QLabel("检测间隔(秒):"))
        self.detection_interval_spinbox = QDoubleSpinBox()
        self.detection_interval_spinbox.setRange(0.1, 10.0)
        self.detection_interval_spinbox.setValue(1.0)
        self.detection_interval_spinbox.setSingleStep(0.1)
        interval_layout.addWidget(self.detection_interval_spinbox)
        layout.addLayout(interval_layout)
    
    def _create_status_group(self) -> None:
        """创建状态组"""
        self.status_group = QGroupBox("检测状态")
        self.status_group.setMinimumHeight(120)  # 大幅减少高度避免被裁剪
        self.status_group.setMaximumHeight(140)  # 严格限制最大高度
        layout = QVBoxLayout(self.status_group)
        layout.setSpacing(4)  # 设置紧凑间距
        
        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("QLabel { color: green; font-weight: bold; }")
        layout.addWidget(self.status_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 日志文本
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(50)   # 大幅减少日志区域高度
        self.log_text.setMinimumHeight(40)   # 大幅减少最小高度
        self.log_text.setReadOnly(True)
        layout.addWidget(self.log_text)
    
    def _init_connections(self) -> None:
        """初始化信号连接"""
        # 按钮连接
        self.start_button.clicked.connect(self._start_detection)
        self.stop_button.clicked.connect(self._stop_detection)
        self.screenshot_button.clicked.connect(self._take_screenshot)
        
        # 设置变更连接
        self.confidence_slider.valueChanged.connect(self._update_confidence_label)
        self.nms_threshold_slider.valueChanged.connect(self._update_nms_label)
        self.auto_detect_checkbox.toggled.connect(self._toggle_auto_detect)
        self.detection_interval_spinbox.valueChanged.connect(self._update_detection_interval)
        
        # 截图组件连接
        if self.screenshot_widget:
            self.screenshot_widget.region_selected.connect(self._handle_region_selection)
    
    def _load_settings(self) -> None:
        """加载设置"""
        try:
            # 加载检测配置
            detection_config = self.config_manager.detection
            
            # 设置置信度阈值
            if hasattr(detection_config, 'confidence_threshold'):
                value = int(detection_config.confidence_threshold * 100)
                self.confidence_slider.setValue(value)
                self._update_confidence_label(value)
            
            # 设置NMS阈值
            if hasattr(detection_config, 'nms_threshold'):
                value = int(detection_config.nms_threshold * 100)
                self.nms_threshold_slider.setValue(value)
                self._update_nms_label(value)
            
            # 设置检测间隔
            if hasattr(detection_config, 'detection_interval'):
                self.detection_interval_spinbox.setValue(detection_config.detection_interval)
            
        except Exception as e:
            self.logger.error(f"设置加载失败: {e}")
    
    def _start_detection(self) -> None:
        """开始检测"""
        if not self.is_detecting:
            self.is_detecting = True
            
            # 更新UI状态
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            self.status_label.setText("检测中...")
            self.status_label.setStyleSheet("QLabel { color: orange; font-weight: bold; }")
            
            # 启动自动检测定时器
            if self.auto_detect_checkbox.isChecked():
                interval = int(self.detection_interval_spinbox.value() * 1000)
                self.detection_timer.start(interval)
            
            # 发送检测开始信号
            self.detection_started.emit()
            
            # 执行首次检测
            self._perform_detection()
            
            self._add_log("开始检测")
            self.logger.info("开始检测")
    
    def _stop_detection(self) -> None:
        """停止检测"""
        if self.is_detecting:
            self.is_detecting = False
            
            # 停止定时器
            self.detection_timer.stop()
            
            # 更新UI状态
            self.start_button.setEnabled(True)
            self.stop_button.setEnabled(False)
            self.status_label.setText("已停止")
            self.status_label.setStyleSheet("QLabel { color: red; font-weight: bold; }")
            self.progress_bar.setVisible(False)
            
            # 发送检测停止信号
            self.detection_stopped.emit()
            
            self._add_log("停止检测")
            self.logger.info("停止检测")
    
    def _take_screenshot(self) -> None:
        """立即截图"""
        try:
            # 获取检测参数
            params = self._get_detection_params()
            
            # 发送截图请求信号
            self.screenshot_requested.emit(params)
            
            self._add_log("执行截图")
            self.logger.info("执行截图")
            
        except Exception as e:
            self._add_log(f"截图失败: {e}")
            self.logger.error(f"截图失败: {e}")
    
    def _auto_detect(self) -> None:
        """自动检测"""
        if self.is_detecting and self.auto_detect_checkbox.isChecked():
            self._perform_detection()
    
    def _perform_detection(self) -> None:
        """执行检测"""
        try:
            # 显示进度条
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)  # 不确定进度
            
            # 获取检测参数
            params = self._get_detection_params()
            
            # 发送检测请求信号
            self.detection_requested.emit(params)
            
            self._add_log("执行检测")
            
        except Exception as e:
            self._add_log(f"检测失败: {e}")
            self.logger.error(f"检测失败: {e}")
    
    def _get_detection_params(self) -> Dict[str, Any]:
        """获取检测参数"""
        return {
            "monitor_id": self.monitor_combo.currentIndex(),
            "confidence_threshold": self.confidence_slider.value() / 100.0,
            "nms_threshold": self.nms_threshold_slider.value() / 100.0,
            "auto_detect": self.auto_detect_checkbox.isChecked(),
            "detection_interval": self.detection_interval_spinbox.value()
        }
    
    def _update_confidence_label(self, value: int) -> None:
        """更新置信度标签"""
        self.confidence_label.setText(f"{value / 100.0:.2f}")
    
    def _update_nms_label(self, value: int) -> None:
        """更新NMS标签"""
        self.nms_label.setText(f"{value / 100.0:.2f}")
    
    def _toggle_auto_detect(self, checked: bool) -> None:
        """切换自动检测"""
        if checked and self.is_detecting:
            interval = int(self.detection_interval_spinbox.value() * 1000)
            self.detection_timer.start(interval)
        else:
            self.detection_timer.stop()
    
    def _update_detection_interval(self, value: float) -> None:
        """更新检测间隔"""
        if self.detection_timer.isActive():
            interval = int(value * 1000)
            self.detection_timer.start(interval)
    
    def _handle_region_selection(self, region: Dict[str, int]) -> None:
        """处理区域选择"""
        self.logger.info(f"选择区域: {region}")
        # 可以在这里处理区域选择逻辑
    
    def _add_log(self, message: str) -> None:
        """添加日志"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        self.log_text.append(log_message)
        
        # 限制日志行数
        if self.log_text.document().blockCount() > 100:
            cursor = self.log_text.textCursor()
            cursor.movePosition(cursor.MoveOperation.Start)
            cursor.select(cursor.SelectionType.BlockUnderCursor)
            cursor.removeSelectedText()
    
    def update_screenshot(self, screenshot: QPixmap) -> None:
        """更新截图显示"""
        if self.screenshot_widget:
            self.screenshot_widget.set_screenshot(screenshot)
            self.current_screenshot = screenshot
    
    def update_detection_results(self, results: List[Dict[str, Any]]) -> None:
        """更新检测结果显示"""
        if self.screenshot_widget and results:
            self.screenshot_widget.set_detection_results(results)
            self._add_log(f"检测到 {len(results)} 个目标")
        
        # 隐藏进度条
        self.progress_bar.setVisible(False)
    
    def set_detection_status(self, status: str, color: str = "black") -> None:
        """设置检测状态"""
        self.status_label.setText(status)
        self.status_label.setStyleSheet(f"QLabel {{ color: {color}; font-weight: bold; }}")
    
    def get_current_settings(self) -> Dict[str, Any]:
        """获取当前设置"""
        return {
            "monitor_id": self.monitor_combo.currentIndex(),
            "confidence_threshold": self.confidence_slider.value() / 100.0,
            "nms_threshold": self.nms_threshold_slider.value() / 100.0,
            "auto_detect": self.auto_detect_checkbox.isChecked(),
            "detection_interval": self.detection_interval_spinbox.value()
        }
