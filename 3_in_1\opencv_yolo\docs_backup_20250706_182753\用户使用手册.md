# 📖 YOLO OpenCV检测器用户使用手册

## 🎯 工具简介

YOLO OpenCV检测器是一款基于人工智能的智能界面检测工具，专为Windows界面自动化而设计。它结合了先进的YOLO目标检测技术和传统的模板匹配算法，为用户提供精确、高效的界面元素识别和自动化操作功能。

### 🌟 主要功能特点

- **🔍 智能检测**: 基于YOLOv8模型的实时界面元素检测
- **📋 模板匹配**: 传统模板匹配技术，适用于特定界面元素
- **🎯 结果融合**: 智能融合多种检测结果，提高准确性
- **📷 实时截图**: 高性能屏幕捕获和实时显示
- **🤖 自动化操作**: 完整的鼠标键盘自动化操作框架
- **📚 使用示例**: 丰富的代码示例和最佳实践指南

### 🎨 应用场景

- **办公自动化**: Excel、Word、PowerPoint等办公软件操作
- **软件测试**: GUI自动化测试和回归测试
- **游戏辅助**: 游戏界面元素识别和自动化操作
- **数据录入**: 批量数据处理和表单填写
- **界面监控**: 实时监控界面变化和状态

## 🚀 安装和环境配置

### 📋 系统要求

- **操作系统**: Windows 10/11 (64位)
- **Python版本**: Python 3.8+ 
- **内存**: 建议8GB以上
- **显卡**: 支持CUDA的NVIDIA显卡（可选，用于加速）
- **磁盘空间**: 至少2GB可用空间

### 🔧 安装步骤

#### 1. **环境准备**
```bash
# 1. 克隆或下载项目
git clone <项目地址>
cd yolo_opencv_run

# 2. 创建虚拟环境
python -m venv venv

# 3. 激活虚拟环境
# Windows:
venv\Scripts\activate
# 或直接运行：
activate_env.bat
```

#### 2. **依赖安装**
```bash
# 升级pip
python -m pip install --upgrade pip

# 安装项目依赖
pip install -r requirements.txt
```

#### 3. **模型下载**
```bash
# 下载YOLO模型文件
python download_yolo_models.py
```

#### 4. **启动应用**
```bash
# 方式1：使用批处理文件
start_yolo_detector.bat

# 方式2：使用Python命令
python -m yolo_opencv_detector.main

# 方式3：快速启动
quick_start.bat
```

### ⚠️ 常见安装问题

#### **问题1**: PyQt6安装失败
```bash
# 解决方案：使用conda安装
conda install pyqt6 -c conda-forge
```

#### **问题2**: CUDA相关错误
```bash
# 解决方案：安装CPU版本的PyTorch
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
```

#### **问题3**: 权限问题
- 以管理员身份运行命令提示符
- 确保Python和pip有足够的权限

## 🖥️ GUI界面详细说明

### 📐 界面布局概览

YOLO OpenCV检测器采用三面板布局设计：

```
┌─────────────────────────────────────────────────────────┐
│                    菜单栏和工具栏                        │
├──────────────┬──────────────────────┬──────────────────┤
│              │                      │                  │
│   左侧面板    │      中央面板         │    右侧面板       │
│              │                      │                  │
│ • 检测控制    │   • 实时截图显示      │  • 配置设置       │
│ • 模板管理    │   • 检测结果标注      │  • 状态监控       │
│              │   • 图像交互操作      │                  │
│              │                      │                  │
├──────────────┴──────────────────────┴──────────────────┤
│                      状态栏                             │
└─────────────────────────────────────────────────────────┘
```

### 🎮 左侧面板：检测控制

#### **🎯 检测标签页**

**检测模式选择**
- **YOLO检测**: 使用深度学习模型进行智能检测
- **模板匹配**: 使用传统图像匹配技术
- **融合模式**: 结合两种方法的优势

**检测控制按钮**
- **📷 开始检测**: 启动实时检测功能
- **⏹️ 停止检测**: 停止当前检测任务
- **📸 单次截图**: 执行一次性截图检测
- **🔄 重置设置**: 恢复默认检测参数

**检测设置**
- **置信度阈值**: 调整检测结果的可信度要求 (0.1-1.0)
- **检测间隔**: 设置实时检测的时间间隔 (100-5000ms)
- **最大检测数**: 限制单次检测的最大目标数量

#### **📁 模板标签页**

**模板管理功能**
- **➕ 添加模板**: 创建新的模板文件
- **📝 编辑模板**: 修改现有模板属性
- **🗑️ 删除模板**: 移除不需要的模板
- **👁️ 预览模板**: 查看模板图像和信息

**模板创建流程**
1. 点击"添加模板"按钮
2. 使用截图工具选择目标区域
3. 输入模板名称和描述
4. 调整匹配参数
5. 保存模板文件

### 📸 中央面板：截图显示

#### **实时显示功能**
- **高清截图**: 实时显示当前屏幕内容
- **检测标注**: 在截图上标注检测到的目标
- **交互操作**: 支持鼠标点击和区域选择

#### **图像交互**
- **缩放控制**: 鼠标滚轮缩放图像 (10%-500%)
- **平移操作**: Ctrl+左键拖拽平移图像
- **区域选择**: 左键拖拽选择感兴趣区域

#### **检测结果显示**
- **边界框**: 绿色矩形框标注检测目标
- **置信度**: 显示检测结果的可信度分数
- **类别标签**: 显示目标的分类名称
- **坐标信息**: 显示目标的精确位置

### ⚙️ 右侧面板：配置和状态

#### **⚙️ 配置标签页**

**YOLO配置**
- **模型选择**: 选择不同的YOLOv8模型变体
  - `YOLOv8n`: 轻量级，速度快，适合实时应用
  - `YOLOv8s`: 平衡性能，适合一般用途
  - `YOLOv8m`: 中等精度，适合复杂场景
  - `YOLOv8l`: 高精度，适合精确检测
  - `YOLOv8x`: 最高精度，适合离线分析

**模板匹配配置**
- **匹配方法**: 选择模板匹配算法
- **相似度阈值**: 设置模板匹配的相似度要求
- **搜索区域**: 限制模板搜索的范围

**结果融合配置**
- **融合策略**: 选择结果合并方式
- **权重分配**: 设置不同检测方法的权重
- **去重参数**: 配置重复结果的过滤规则

#### **📈 状态标签页**

**系统状态监控**
- **CPU使用率**: 实时显示处理器占用情况
- **内存使用**: 监控内存消耗状态
- **GPU状态**: 显示显卡使用情况（如果可用）

**检测统计信息**
- **检测次数**: 累计检测执行次数
- **成功率**: 检测成功的百分比
- **平均耗时**: 单次检测的平均处理时间
- **目标统计**: 各类目标的检测数量

**性能图表**
- **实时性能曲线**: 显示检测性能变化趋势
- **资源使用图表**: 监控系统资源消耗
- **错误日志**: 记录和显示系统错误信息

## 🔍 检测模式使用方法

### 🤖 YOLO检测模式

YOLO检测是基于深度学习的智能检测方法，适用于复杂界面和多目标场景。

#### **使用步骤**
1. **选择检测模式**: 在左侧面板选择"YOLO检测"
2. **配置模型**: 在右侧配置面板选择合适的YOLO模型
3. **调整参数**: 设置置信度阈值和检测间隔
4. **开始检测**: 点击"开始检测"按钮
5. **查看结果**: 在中央面板查看检测结果

#### **参数说明**
- **置信度阈值**: 建议设置为0.5-0.8，过低会产生误检，过高会漏检
- **检测间隔**: 实时检测建议500-1000ms，单次检测可设为0
- **模型选择**: 
  - 实时应用推荐YOLOv8n
  - 精度要求高推荐YOLOv8l或YOLOv8x

#### **适用场景**
- 复杂界面的多目标检测
- 不规则形状的界面元素
- 动态变化的界面内容
- 需要分类识别的场景

### 📋 模板匹配模式

模板匹配是基于图像相似度的传统检测方法，适用于固定界面元素的精确定位。

#### **使用步骤**
1. **创建模板**: 
   - 点击"添加模板"按钮
   - 使用截图工具选择目标区域
   - 输入模板名称和描述
   - 保存模板文件

2. **配置匹配参数**:
   - 设置相似度阈值（建议0.8-0.95）
   - 选择匹配方法（推荐TM_CCOEFF_NORMED）
   - 配置搜索区域（可选）

3. **执行匹配**:
   - 选择"模板匹配"模式
   - 选择要使用的模板
   - 点击"开始检测"

#### **模板创建最佳实践**
- **选择特征明显的区域**: 避免纯色或重复图案
- **适当的尺寸**: 模板不宜过大或过小，建议50x50到200x200像素
- **避免变化元素**: 不要包含时间、数字等动态内容
- **高对比度**: 选择与背景对比度高的区域

#### **适用场景**
- 固定位置的按钮和图标
- 特定的界面元素（如logo、标识）
- 需要精确像素级匹配的场景
- 简单界面的快速定位

### 🎯 融合模式

融合模式结合YOLO检测和模板匹配的优势，提供更高的检测准确性和鲁棒性。

#### **工作原理**
1. 同时执行YOLO检测和模板匹配
2. 对检测结果进行智能融合
3. 去除重复和冲突的结果
4. 输出最优的检测结果

#### **配置建议**
- **权重分配**: YOLO权重0.6，模板匹配权重0.4
- **去重阈值**: 设置为50-100像素
- **置信度要求**: 融合结果置信度建议>0.7

## 📚 源代码窗口和使用示例

### 💻 源代码窗口功能

源代码窗口是YOLO OpenCV检测器的高级功能，为开发者提供了完整的代码编辑和执行环境。

#### **访问方式**
- 通过菜单栏："工具" → "源代码编辑器"
- 使用快捷键：`Ctrl+E`
- 点击工具栏的"📄 源代码"按钮

#### **窗口组成**
- **🎯 GUI检测复制**: 完整复制GUI检测方法的代码
- **📚 完整使用示例**: 4个完整的自动化操作示例

### 📖 使用示例详解

源代码窗口包含4个完整的自动化操作示例，涵盖了从基础操作到高级应用的全方位场景。

#### **🏢 办公软件自动化示例**

**功能特点**:
- 智能目标选择和坐标处理
- 安全的鼠标点击和键盘操作
- 完整的错误处理机制

**应用场景**:
- Excel表格数据录入
- Word文档格式调整
- PowerPoint幻灯片操作
- 浏览器表单填写

**核心代码结构**:
```python
# 1. 初始化检测器
automator = OfficeAutomator()

# 2. 执行屏幕检测
results = automator.gui_detect_screen()
targets = automator.parse_detection_results(results)

# 3. 智能目标选择
best_target = automator.select_target_by_confidence(targets, 'highest')

# 4. 获取坐标并验证
coords = automator.get_target_coordinates(best_target, 'center')
if automator.validate_coordinates(coords):
    # 5. 执行自动化操作
    success = automator.perform_mouse_click(coords, 'left', 'single', 0.5)
```

#### **🎯 多目标操作示例**

**功能特点**:
- 多维度目标选择（位置/大小/置信度/类别）
- 复杂拖拽操作（水平/垂直/对角线）
- 多目标协调序列操作

**应用场景**:
- 文件拖拽操作
- 多窗口协调
- 批量处理操作
- 界面元素重排

**核心功能**:
```python
# 多目标选择
leftmost = automator.select_target_by_position(targets, 'leftmost')
rightmost = automator.select_target_by_position(targets, 'rightmost')

# 拖拽操作
drag_success = automator.perform_mouse_drag(
    left_coords, right_coords, 1.0, 'left'
)
```

#### **🔍 目标选择策略示例**

**功能特点**:
- 按置信度选择（最高/最低/分组分析）
- 按位置选择（上下左右中心+关系分析）
- 按大小选择（面积分组+特殊处理）
- 自定义条件选择

**选择策略**:
```python
# 按置信度选择
highest_conf = selector.select_target_by_confidence(targets, 'highest')

# 按位置选择
center_target = selector.select_target_by_position(targets, 'center')

# 按大小选择
largest_target = selector.select_target_by_size(targets, 'largest')

# 自定义条件选择
def high_confidence_condition(target):
    return target.get('confidence', 0) > 0.8

high_conf_targets = selector.select_target_by_custom_condition(
    targets, high_confidence_condition
)
```

#### **🛡️ 错误处理机制示例**

**功能特点**:
- 多层次坐标边界验证
- 智能操作超时保护
- 全面异常捕获和处理
- 指数退避重试机制

**安全机制**:
```python
# 坐标验证
if automator.validate_coordinates(coords):
    # 重试机制
    max_retries = 3
    for attempt in range(max_retries):
        try:
            success = automator.perform_mouse_click(coords, 'left', 'single', 0.3)
            if success:
                break
        except Exception as e:
            if attempt == max_retries - 1:
                print("❌ 达到最大重试次数，操作失败")
                return False
            time.sleep(1)
```

### 🚀 代码执行和调试

#### **运行示例代码**
1. 在源代码窗口选择相应的示例
2. 根据需要修改参数
3. 点击"▶️ 运行代码"按钮
4. 在输出窗口查看执行结果

#### **调试技巧**
- 使用`print()`语句输出调试信息
- 设置合适的延迟时间观察操作过程
- 逐步执行复杂的操作序列
- 记录操作日志便于问题排查

## ❓ 常见问题解答

### 🔧 技术问题

#### **Q1: 检测结果不准确怎么办？**
**A**: 
- 调整置信度阈值，建议从0.5开始逐步调整
- 尝试不同的YOLO模型，复杂场景使用YOLOv8l或YOLOv8x
- 确保截图清晰，避免模糊或遮挡
- 考虑使用模板匹配作为补充

#### **Q2: 程序运行缓慢怎么解决？**
**A**:
- 使用较小的YOLO模型（YOLOv8n）
- 增加检测间隔时间
- 关闭不必要的后台程序
- 考虑使用GPU加速（需要CUDA支持）

#### **Q3: 模板匹配失效怎么办？**
**A**:
- 检查模板图像是否与当前界面一致
- 降低相似度阈值（但不要低于0.7）
- 重新创建模板，选择更具特征性的区域
- 确保界面缩放比例与创建模板时一致

### 🖥️ 界面问题

#### **Q4: 界面显示异常或重叠？**
**A**:
- 使用菜单栏的"视图" → "重置布局"
- 调整屏幕分辨率和缩放设置
- 重启应用程序
- 检查显卡驱动是否最新

#### **Q5: 截图功能无法使用？**
**A**:
- 确保程序有屏幕捕获权限
- 检查是否有其他程序占用屏幕资源
- 尝试以管理员身份运行程序
- 重启计算机后再试

### 🔒 权限问题

#### **Q6: 自动化操作无法执行？**
**A**:
- 以管理员身份运行程序
- 检查Windows安全设置，允许程序控制鼠标键盘
- 关闭杀毒软件的实时保护（临时）
- 确保目标应用程序没有管理员权限要求

## ⚙️ 配置参数说明

### 🎯 检测参数

| 参数名称 | 取值范围 | 默认值 | 说明 |
|---------|---------|--------|------|
| 置信度阈值 | 0.1-1.0 | 0.5 | 检测结果的最低可信度要求 |
| 检测间隔 | 100-5000ms | 500ms | 实时检测的时间间隔 |
| 最大检测数 | 1-100 | 20 | 单次检测的最大目标数量 |
| NMS阈值 | 0.1-0.9 | 0.4 | 非极大值抑制阈值 |

### 📋 模板匹配参数

| 参数名称 | 取值范围 | 默认值 | 说明 |
|---------|---------|--------|------|
| 相似度阈值 | 0.5-1.0 | 0.8 | 模板匹配的最低相似度 |
| 匹配方法 | 6种算法 | TM_CCOEFF_NORMED | 模板匹配算法 |
| 搜索区域 | 全屏/自定义 | 全屏 | 模板搜索的范围限制 |

### 🎨 界面参数

| 参数名称 | 取值范围 | 默认值 | 说明 |
|---------|---------|--------|------|
| 界面主题 | 浅色/深色 | 浅色 | 应用程序界面主题 |
| 字体大小 | 8-16pt | 10pt | 界面文字大小 |
| 截图质量 | 50-100% | 90% | 截图压缩质量 |
| 刷新频率 | 10-60fps | 30fps | 界面刷新频率 |

---

## 📞 技术支持

如果您在使用过程中遇到问题，可以通过以下方式获取帮助：

- **📧 邮箱支持**: <EMAIL>
- **📖 在线文档**: 查看项目README.md文件
- **🐛 问题反馈**: 通过GitHub Issues报告问题
- **💬 社区讨论**: 参与项目讨论区

---

**版本信息**: v2.0  
**更新日期**: 2025年7月6日  
**文档版本**: 1.0
