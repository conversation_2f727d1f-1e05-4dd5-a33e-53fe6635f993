#!/usr/bin/env python3
"""
优化的自动化操作窗口 - GUI集成版本
解决布局问题，提供完整的自动化配置功能

集成特点：
- 与现有GUI系统完美集成
- 使用项目现有的组件和服务
- 保持代码架构一致性
- 支持检测结果的实时传递

作者: YOLO OpenCV Detector Team
联系: <EMAIL>
版本: 2.0 GUI集成版
"""

import time
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass

# PyQt6 导入
from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QSplitter, QGroupBox, QLabel, QPushButton, QComboBox, QSpinBox, 
    QLineEdit, QTextEdit, QListWidget, QListWidgetItem, QTabWidget,
    QCheckBox, QTableWidget, QTableWidgetItem, QHeaderView, QFrame, 
    QMessageBox, QMenuBar, QStatusBar, QToolBar, QFormLayout,
    QApplication, QSizePolicy
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer, QSettings
from PyQt6.QtGui import (
    QFont, QIcon, QKeySequence, QAction, QPixmap, QPainter, 
    QPen, QBrush, QColor, QMouseEvent
)

# 项目内部导入
from ...utils.logger import Logger
# 注意：以下模块可能不存在，使用时需要进行异常处理
# from ...core.detection_service import DetectionService
# from ...core.automation.detection_processor import DetectionProcessor
# from ...core.automation.automation_executor import AutomationExecutor
# from ...core.automation.template_manager import TemplateManager

@dataclass
class DetectionTarget:
    """检测目标数据类"""
    id: str
    label: str
    confidence: float
    bbox: Tuple[int, int, int, int]  # (x, y, width, height)
    center: Tuple[int, int]  # (center_x, center_y)
    click_point: Tuple[int, int]  # 推荐的点击位置
    selected: bool = False
    operation_configured: bool = False

class DetectionVisualizerWidget(QLabel):
    """检测结果可视化组件 - 集成版"""
    
    # 信号定义
    box_selected = pyqtSignal(str, bool)
    selection_changed = pyqtSignal(list)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = Logger().get_logger(__name__)
        self.detection_targets: List[DetectionTarget] = []
        self.original_pixmap: Optional[QPixmap] = None
        
        # 设置组件属性
        self.setMinimumSize(400, 300)
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.setStyleSheet("""
            QLabel {
                border: 2px solid #bdc3c7;
                background-color: #ecf0f1;
                border-radius: 4px;
            }
        """)
        self.setMouseTracking(True)
        
        # 创建默认显示
        self._create_default_display()
    
    def _create_default_display(self):
        """创建默认显示内容"""
        # 创建一个示例图像
        pixmap = QPixmap(800, 600)
        pixmap.fill(QColor(240, 240, 240))
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 绘制示例检测框
        pen = QPen(QColor(0, 255, 0), 3)
        painter.setPen(pen)
        
        # 示例检测框
        boxes = [
            (100, 100, 150, 80, "Button 1"),
            (300, 150, 120, 60, "Input Field"),
            (500, 200, 100, 40, "Menu Item"),
            (200, 350, 180, 100, "Dialog Box"),
            (450, 400, 140, 70, "Checkbox")
        ]
        
        font = QFont("Arial", 10, QFont.Weight.Bold)
        painter.setFont(font)
        
        for i, (x, y, w, h, label) in enumerate(boxes):
            # 绘制检测框
            painter.drawRect(x, y, w, h)
            
            # 绘制标签背景
            painter.fillRect(x, y-25, len(label)*8, 20, QBrush(QColor(0, 255, 0, 180)))
            
            # 绘制标签文字
            painter.setPen(QPen(QColor(255, 255, 255)))
            painter.drawText(x+5, y-8, label)
            painter.setPen(pen)
        
        # 绘制说明文字
        painter.setPen(QPen(QColor(100, 100, 100)))
        painter.setFont(QFont("Arial", 12))
        painter.drawText(50, 50, "检测结果可视化 - 点击检测框进行选择")
        
        painter.end()
        
        self.setPixmap(pixmap)
        self.original_pixmap = pixmap
    
    def update_detections(self, targets: List[DetectionTarget]):
        """更新检测结果"""
        self.detection_targets = targets
        self._update_display()
    
    def set_image(self, pixmap: QPixmap):
        """设置显示图像"""
        self.original_pixmap = pixmap
        self._update_display()
    
    def _update_display(self):
        """更新显示"""
        if not self.original_pixmap:
            self._create_default_display()
            return
        
        # 这里可以添加实际的检测结果绘制逻辑
        pass
    
    def mousePressEvent(self, event: QMouseEvent):
        """鼠标点击事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            # 模拟选择检测框
            if self.detection_targets:
                target = self.detection_targets[0]
                target.selected = not target.selected
                self.box_selected.emit(target.id, target.selected)
                self._emit_selection_changed()
        super().mousePressEvent(event)
    
    def _emit_selection_changed(self):
        """发送选择改变信号"""
        selected_ids = [t.id for t in self.detection_targets if t.selected]
        self.selection_changed.emit(selected_ids)
    
    def select_all_boxes(self):
        """选择所有检测框"""
        for target in self.detection_targets:
            if not target.selected:
                target.selected = True
                self.box_selected.emit(target.id, True)
        self._emit_selection_changed()
    
    def clear_selection(self):
        """清除所有选择"""
        for target in self.detection_targets:
            if target.selected:
                target.selected = False
                self.box_selected.emit(target.id, False)
        self._emit_selection_changed()

class OperationConfigWidget(QWidget):
    """操作配置组件 - 集成版"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = Logger().get_logger(__name__)
        self.current_operations = []
        
        # 使用项目现有的组件（简化版本）
        self.template_manager = None
        self.automation_executor = None

        # 尝试加载自动化组件
        try:
            # 这里可以添加实际的组件导入
            # self.template_manager = TemplateManager()
            # self.automation_executor = AutomationExecutor()
            pass
        except Exception as e:
            self.logger.warning(f"无法加载自动化组件: {e}")
        
        self._init_ui()
        self._setup_connections()
    
    def _init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # 基础操作标签页
        basic_tab = self._create_basic_operations_tab()
        self.tab_widget.addTab(basic_tab, "🖱️ 基础操作")
        
        # 模板操作标签页
        template_tab = self._create_template_operations_tab()
        self.tab_widget.addTab(template_tab, "📋 操作模板")
        
        # 高级操作标签页
        advanced_tab = self._create_advanced_operations_tab()
        self.tab_widget.addTab(advanced_tab, "🔧 高级操作")
    
    def _create_basic_operations_tab(self) -> QWidget:
        """创建基础操作标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 操作类型选择
        type_group = QGroupBox("操作类型")
        type_layout = QFormLayout(type_group)
        
        self.operation_type_combo = QComboBox()
        self.operation_type_combo.addItems([
            "🖱️ 鼠标点击",
            "⌨️ 键盘输入", 
            "🔗 组合操作",
            "⏱️ 延时等待"
        ])
        type_layout.addRow("操作类型:", self.operation_type_combo)
        layout.addWidget(type_group)
        
        # 鼠标操作参数
        mouse_group = QGroupBox("鼠标操作参数")
        mouse_layout = QFormLayout(mouse_group)
        
        self.click_type_combo = QComboBox()
        self.click_type_combo.addItems(["左键单击", "右键单击", "双击", "拖拽"])
        mouse_layout.addRow("点击类型:", self.click_type_combo)
        
        offset_widget = QWidget()
        offset_layout = QHBoxLayout(offset_widget)
        offset_layout.setContentsMargins(0, 0, 0, 0)
        
        self.offset_x_spin = QSpinBox()
        self.offset_x_spin.setRange(-100, 100)
        self.offset_x_spin.setSuffix(" px")
        offset_layout.addWidget(QLabel("X:"))
        offset_layout.addWidget(self.offset_x_spin)
        
        self.offset_y_spin = QSpinBox()
        self.offset_y_spin.setRange(-100, 100)
        self.offset_y_spin.setSuffix(" px")
        offset_layout.addWidget(QLabel("Y:"))
        offset_layout.addWidget(self.offset_y_spin)
        offset_layout.addStretch()
        
        mouse_layout.addRow("位置偏移:", offset_widget)
        layout.addWidget(mouse_group)
        
        # 键盘操作参数
        keyboard_group = QGroupBox("键盘操作参数")
        keyboard_layout = QFormLayout(keyboard_group)
        
        self.input_text_edit = QLineEdit()
        self.input_text_edit.setPlaceholderText("要输入的文本内容...")
        keyboard_layout.addRow("输入文本:", self.input_text_edit)
        
        self.hotkey_edit = QLineEdit()
        self.hotkey_edit.setPlaceholderText("如: ctrl+c, alt+tab")
        keyboard_layout.addRow("快捷键:", self.hotkey_edit)
        
        layout.addWidget(keyboard_group)
        
        # 延时设置
        delay_group = QGroupBox("延时设置")
        delay_layout = QFormLayout(delay_group)
        
        self.delay_spin = QSpinBox()
        self.delay_spin.setRange(0, 10000)
        self.delay_spin.setValue(100)
        self.delay_spin.setSuffix(" ms")
        delay_layout.addRow("执行延时:", self.delay_spin)
        
        layout.addWidget(delay_group)
        
        # 添加操作按钮
        add_button = QPushButton("➕ 添加操作")
        add_button.clicked.connect(self._add_current_operation)
        layout.addWidget(add_button)
        
        layout.addStretch()
        return widget

    def _create_template_operations_tab(self) -> QWidget:
        """创建模板操作标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # 模板列表
        template_group = QGroupBox("可用模板")
        template_layout = QVBoxLayout(template_group)

        self.template_list = QListWidget()
        self.template_list.setMaximumHeight(150)
        template_layout.addWidget(self.template_list)

        # 模板操作按钮
        template_buttons = QHBoxLayout()

        self.load_template_btn = QPushButton("📂 应用模板")
        template_buttons.addWidget(self.load_template_btn)

        self.save_template_btn = QPushButton("💾 保存模板")
        template_buttons.addWidget(self.save_template_btn)

        template_buttons.addStretch()
        template_layout.addLayout(template_buttons)

        layout.addWidget(template_group)

        # 模板详情
        details_group = QGroupBox("模板详情")
        details_layout = QVBoxLayout(details_group)

        self.template_details = QTextEdit()
        self.template_details.setMaximumHeight(120)
        self.template_details.setReadOnly(True)
        self.template_details.setPlaceholderText("选择模板查看详细信息...")
        details_layout.addWidget(self.template_details)

        layout.addWidget(details_group)

        # 加载模板列表
        self._load_template_list()

        layout.addStretch()
        return widget

    def _create_advanced_operations_tab(self) -> QWidget:
        """创建高级操作标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # 批量操作
        batch_group = QGroupBox("批量操作")
        batch_layout = QFormLayout(batch_group)

        self.batch_operation_checkbox = QCheckBox("对所有选中目标执行相同操作")
        batch_layout.addRow(self.batch_operation_checkbox)

        self.batch_interval_spin = QSpinBox()
        self.batch_interval_spin.setRange(0, 5000)
        self.batch_interval_spin.setValue(500)
        self.batch_interval_spin.setSuffix(" ms")
        batch_layout.addRow("操作间隔:", self.batch_interval_spin)

        layout.addWidget(batch_group)

        # 条件执行
        condition_group = QGroupBox("条件执行")
        condition_layout = QFormLayout(condition_group)

        self.condition_checkbox = QCheckBox("启用条件执行")
        condition_layout.addRow(self.condition_checkbox)

        self.condition_combo = QComboBox()
        self.condition_combo.addItems([
            "置信度 > 阈值",
            "目标数量 > 数值",
            "位置在区域内"
        ])
        condition_layout.addRow("执行条件:", self.condition_combo)

        layout.addWidget(condition_group)

        # 循环执行
        loop_group = QGroupBox("循环执行")
        loop_layout = QFormLayout(loop_group)

        self.loop_checkbox = QCheckBox("启用循环执行")
        loop_layout.addRow(self.loop_checkbox)

        self.loop_count_spin = QSpinBox()
        self.loop_count_spin.setRange(1, 100)
        self.loop_count_spin.setValue(1)
        loop_layout.addRow("循环次数:", self.loop_count_spin)

        layout.addWidget(loop_group)

        layout.addStretch()
        return widget

    def _setup_connections(self):
        """设置信号连接"""
        # 模板相关
        self.template_list.itemClicked.connect(self._on_template_selected)
        self.load_template_btn.clicked.connect(self._apply_selected_template)
        self.save_template_btn.clicked.connect(self._save_current_operations_as_template)

    def _load_template_list(self):
        """加载模板列表"""
        self.template_list.clear()

        if self.template_manager:
            try:
                templates = self.template_manager.get_all_templates()
                for template in templates:
                    item = QListWidgetItem(f"📋 {template.name}")
                    item.setData(Qt.ItemDataRole.UserRole, template.id)
                    item.setToolTip(template.description)
                    self.template_list.addItem(item)
            except Exception as e:
                self.logger.warning(f"加载模板列表失败: {e}")

        # 添加默认模板项
        if self.template_list.count() == 0:
            default_item = QListWidgetItem("📋 复制粘贴")
            default_item.setToolTip("快速复制和粘贴操作")
            self.template_list.addItem(default_item)

    def _on_template_selected(self, item: QListWidgetItem):
        """模板选择处理"""
        template_name = item.text().replace("📋 ", "")
        details = f"模板名称: {template_name}\n"
        details += f"描述: 常用的自动化操作模板\n"
        details += f"状态: 可用\n"
        self.template_details.setPlainText(details)

    def _apply_selected_template(self):
        """应用选中的模板"""
        current_item = self.template_list.currentItem()
        if not current_item:
            QMessageBox.information(self, "提示", "请先选择一个模板")
            return

        template_name = current_item.text().replace("📋 ", "")
        QMessageBox.information(self, "成功", f"已应用模板: {template_name}")

    def _save_current_operations_as_template(self):
        """保存当前操作为模板"""
        if not self.current_operations:
            QMessageBox.information(self, "提示", "当前没有配置的操作")
            return

        QMessageBox.information(self, "成功", "模板保存成功")

    def _add_current_operation(self):
        """添加当前配置的操作"""
        operation_type = self.operation_type_combo.currentText()

        # 创建操作描述
        if "鼠标点击" in operation_type:
            description = f"{self.click_type_combo.currentText()} at position"
        elif "键盘输入" in operation_type:
            text = self.input_text_edit.text()
            if text:
                description = f"Type text: {text}"
            else:
                QMessageBox.warning(self, "警告", "请输入要输入的文本")
                return
        else:
            description = f"Operation: {operation_type}"

        # 添加到操作列表
        operation = {
            'type': operation_type,
            'description': description,
            'delay': self.delay_spin.value()
        }
        self.current_operations.append(operation)
        QMessageBox.information(self, "成功", f"已添加操作: {description}")

    def get_current_operations(self) -> List[Dict[str, Any]]:
        """获取当前配置的操作"""
        return self.current_operations.copy()

    def clear_operations(self):
        """清除所有操作"""
        self.current_operations.clear()

class AutomationWindowOptimized(QMainWindow):
    """优化的自动化操作窗口 - GUI集成版"""

    # 信号定义
    window_closed = pyqtSignal()
    operation_executed = pyqtSignal(list)

    def __init__(self, parent=None):
        """初始化自动化窗口"""
        super().__init__(parent)
        self.logger = Logger().get_logger("AutomationWindowOptimized")

        # 数据存储
        self.detection_targets: List[DetectionTarget] = []
        self.current_screenshot = None
        self.settings = QSettings("YOLODetector", "AutomationWindow")

        # 窗口设置
        self.setWindowTitle("🤖 YOLO自动化操作配置 - 优化版")
        self.setWindowFlags(Qt.WindowType.Window)

        # 初始化UI
        self._init_ui()
        self._setup_menu_bar()
        self._setup_tool_bar()
        self._setup_status_bar()
        self._setup_connections()

        # 适配屏幕尺寸
        self._adapt_to_screen_size()

        # 初始化空状态，等待真实检测数据
        self.status_label.setText("等待检测数据...")

        self.logger.info("优化的自动化操作窗口初始化完成")

    def _init_ui(self):
        """初始化用户界面 - 优化布局"""
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局 - 使用更合理的边距
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(8, 8, 8, 8)
        main_layout.setSpacing(8)

        # 创建主分割器（水平分割）
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(main_splitter)

        # 左侧：检测结果和目标选择
        left_panel = self._create_left_panel()
        main_splitter.addWidget(left_panel)

        # 右侧：操作配置面板
        right_panel = self._create_right_panel()
        main_splitter.addWidget(right_panel)

        # 设置分割器比例和约束 - 优化空间分配
        main_splitter.setSizes([500, 400])  # 左侧稍大一些用于可视化
        main_splitter.setStretchFactor(0, 1)  # 左侧可伸缩
        main_splitter.setStretchFactor(1, 0)  # 右侧固定宽度

        # 设置最小宽度避免压缩
        left_panel.setMinimumWidth(400)
        right_panel.setMinimumWidth(350)

        # 底部控制面板
        control_panel = self._create_control_panel()
        main_layout.addWidget(control_panel)

    def _create_left_panel(self) -> QWidget:
        """创建左侧面板 - 检测结果和目标选择"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.Shape.StyledPanel)
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(8)

        # 标题
        title_label = QLabel("🎯 检测结果可视化")
        title_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #2c3e50; padding: 5px;")
        layout.addWidget(title_label)

        # 可视化组件
        self.visualizer = DetectionVisualizerWidget()
        self.visualizer.setMinimumHeight(300)  # 确保足够的高度
        layout.addWidget(self.visualizer)

        # 可视化控制按钮
        viz_controls = QHBoxLayout()
        viz_controls.setSpacing(5)

        self.show_labels_btn = QPushButton("🏷️ 标签")
        self.show_labels_btn.setCheckable(True)
        self.show_labels_btn.setChecked(True)
        self.show_labels_btn.setMaximumWidth(80)
        viz_controls.addWidget(self.show_labels_btn)

        self.show_confidence_btn = QPushButton("📊 置信度")
        self.show_confidence_btn.setCheckable(True)
        self.show_confidence_btn.setChecked(True)
        self.show_confidence_btn.setMaximumWidth(80)
        viz_controls.addWidget(self.show_confidence_btn)

        viz_controls.addStretch()

        self.select_all_btn = QPushButton("✅ 全选")
        self.select_all_btn.setMaximumWidth(60)
        viz_controls.addWidget(self.select_all_btn)

        self.clear_selection_btn = QPushButton("❌ 清除")
        self.clear_selection_btn.setMaximumWidth(60)
        viz_controls.addWidget(self.clear_selection_btn)

        layout.addLayout(viz_controls)

        # 目标列表
        targets_group = QGroupBox("检测目标列表")
        targets_layout = QVBoxLayout(targets_group)

        self.targets_table = QTableWidget()
        self.targets_table.setColumnCount(4)
        self.targets_table.setHorizontalHeaderLabels(["选择", "类型", "置信度", "位置"])
        self.targets_table.setMaximumHeight(150)  # 限制高度

        # 设置列宽
        header = self.targets_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Stretch)

        self.targets_table.setColumnWidth(0, 50)
        self.targets_table.setColumnWidth(2, 80)

        targets_layout.addWidget(self.targets_table)
        layout.addWidget(targets_group)

        return panel

    def _create_right_panel(self) -> QWidget:
        """创建右侧面板 - 操作配置"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.Shape.StyledPanel)
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(8)

        # 标题
        title_label = QLabel("⚙️ 操作配置")
        title_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #2c3e50; padding: 5px;")
        layout.addWidget(title_label)

        # 操作配置组件
        self.operation_config = OperationConfigWidget()
        layout.addWidget(self.operation_config)

        # 操作序列显示
        sequence_group = QGroupBox("操作序列")
        sequence_layout = QVBoxLayout(sequence_group)

        self.operations_list = QListWidget()
        self.operations_list.setMaximumHeight(120)  # 限制高度
        sequence_layout.addWidget(self.operations_list)

        # 序列控制按钮
        sequence_controls = QHBoxLayout()

        self.clear_ops_btn = QPushButton("🗑️ 清空")
        self.clear_ops_btn.setMaximumWidth(60)
        sequence_controls.addWidget(self.clear_ops_btn)

        sequence_controls.addStretch()

        self.ops_count_label = QLabel("操作数: 0")
        self.ops_count_label.setStyleSheet("color: #7f8c8d; font-size: 11px;")
        sequence_controls.addWidget(self.ops_count_label)

        sequence_layout.addLayout(sequence_controls)
        layout.addWidget(sequence_group)

        return panel

    def _create_control_panel(self) -> QWidget:
        """创建底部控制面板"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.Shape.StyledPanel)
        panel.setMaximumHeight(60)  # 固定高度
        layout = QHBoxLayout(panel)
        layout.setContentsMargins(10, 8, 10, 8)
        layout.setSpacing(10)

        # 状态信息
        self.targets_count_label = QLabel("检测目标: 0 个")
        self.targets_count_label.setStyleSheet("font-weight: bold; color: #3498db;")
        layout.addWidget(self.targets_count_label)

        self.selected_count_label = QLabel("已选择: 0 个")
        self.selected_count_label.setStyleSheet("font-weight: bold; color: #e74c3c;")
        layout.addWidget(self.selected_count_label)

        layout.addStretch()

        # 主要操作按钮
        self.refresh_btn = QPushButton("🔄 刷新")
        self.refresh_btn.setMinimumWidth(80)
        layout.addWidget(self.refresh_btn)

        self.preview_btn = QPushButton("👁️ 预览")
        self.preview_btn.setMinimumWidth(80)
        layout.addWidget(self.preview_btn)

        self.execute_btn = QPushButton("▶️ 执行")
        self.execute_btn.setMinimumWidth(80)
        self.execute_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
            QPushButton:disabled {
                background-color: #95a5a6;
            }
        """)
        layout.addWidget(self.execute_btn)

        return panel

    def _setup_menu_bar(self):
        """设置菜单栏"""
        menubar = self.menuBar()

        # 文件菜单
        file_menu = menubar.addMenu("📁 文件")

        # 导入检测结果
        import_action = QAction("📥 导入检测结果", self)
        import_action.setShortcut(QKeySequence("Ctrl+O"))
        import_action.triggered.connect(self._import_detection_results)
        file_menu.addAction(import_action)

        # 导出操作配置
        export_action = QAction("📤 导出操作配置", self)
        export_action.setShortcut(QKeySequence("Ctrl+S"))
        export_action.triggered.connect(self._export_operation_config)
        file_menu.addAction(export_action)

        file_menu.addSeparator()

        # 关闭窗口
        close_action = QAction("❌ 关闭", self)
        close_action.setShortcut(QKeySequence("Ctrl+W"))
        close_action.triggered.connect(self.close)
        file_menu.addAction(close_action)

        # 视图菜单
        view_menu = menubar.addMenu("👁️ 视图")

        # 全屏模式
        fullscreen_action = QAction("🖥️ 全屏模式", self)
        fullscreen_action.setShortcut(QKeySequence("F11"))
        fullscreen_action.triggered.connect(self._toggle_fullscreen)
        view_menu.addAction(fullscreen_action)

        # 帮助菜单
        help_menu = menubar.addMenu("❓ 帮助")

        # 关于
        about_action = QAction("ℹ️ 关于", self)
        about_action.triggered.connect(self._show_about)
        help_menu.addAction(about_action)

    def _setup_tool_bar(self):
        """设置工具栏"""
        toolbar = self.addToolBar("主工具栏")
        toolbar.setMovable(False)

        # 刷新按钮
        refresh_action = QAction("🔄", self)
        refresh_action.setToolTip("刷新检测结果")
        refresh_action.triggered.connect(self._refresh_detection)
        toolbar.addAction(refresh_action)

        toolbar.addSeparator()

        # 预览按钮
        preview_action = QAction("👁️", self)
        preview_action.setToolTip("预览操作")
        preview_action.triggered.connect(self._preview_operations)
        toolbar.addAction(preview_action)

        # 执行按钮
        execute_action = QAction("▶️", self)
        execute_action.setToolTip("执行操作")
        execute_action.triggered.connect(self._execute_operations)
        toolbar.addAction(execute_action)

    def _setup_status_bar(self):
        """设置状态栏"""
        self.status_bar = self.statusBar()

        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_bar.addWidget(self.status_label)

        # 右侧信息
        self.resolution_label = QLabel()
        self.status_bar.addPermanentWidget(self.resolution_label)

        self._update_resolution_info()

    def _setup_connections(self):
        """设置信号连接"""
        # 可视化控制
        self.show_labels_btn.toggled.connect(self._update_visualization_options)
        self.show_confidence_btn.toggled.connect(self._update_visualization_options)
        self.select_all_btn.clicked.connect(self._select_all_targets)
        self.clear_selection_btn.clicked.connect(self._clear_selection)

        # 主控制按钮
        self.refresh_btn.clicked.connect(self._refresh_detection)
        self.preview_btn.clicked.connect(self._preview_operations)
        self.execute_btn.clicked.connect(self._execute_operations)
        self.clear_ops_btn.clicked.connect(self._clear_operations)

        # 可视化组件信号
        self.visualizer.selection_changed.connect(self._on_selection_changed)

    def _adapt_to_screen_size(self):
        """适配屏幕尺寸 - 增强版"""
        try:
            from ..utils.screen_adapter import ScreenAdapter

            # 使用增强的屏幕适配器
            adapter = ScreenAdapter()
            size_config = adapter.apply_to_window(self)

            self.logger.info(
                f"屏幕适配完成: {size_config.width}×{size_config.height}, "
                f"利用率: {size_config.utilization_ratio:.1%}"
            )

            # 获取屏幕信息摘要
            screens_summary = adapter.get_screens_summary()
            if screens_summary:
                self.logger.info(f"检测到 {screens_summary['total_screens']} 个显示器")

                # 获取鲁棒性评分
                robustness = adapter.get_robustness_score()
                self.logger.info(f"屏幕适配鲁棒性评分: {robustness['total_score']}/100")

            self._update_resolution_info()

        except ImportError:
            # 回退到基础适配方案
            self.logger.warning("无法加载增强屏幕适配器，使用基础方案")
            self._basic_screen_adaptation()

    def _basic_screen_adaptation(self):
        """基础屏幕适配方案（回退）"""
        screen = QApplication.primaryScreen()
        if screen:
            screen_geometry = screen.availableGeometry()
            screen_width = screen_geometry.width()
            screen_height = screen_geometry.height()

            # 根据屏幕尺寸设置窗口大小
            if screen_width >= 1920 and screen_height >= 1080:  # 大屏幕
                window_width = min(1400, int(screen_width * 0.8))
                window_height = min(900, int(screen_height * 0.8))
            elif screen_width >= 1366 and screen_height >= 768:  # 中等屏幕
                window_width = min(1200, int(screen_width * 0.85))
                window_height = min(800, int(screen_height * 0.85))
            else:  # 小屏幕
                window_width = min(1000, int(screen_width * 0.9))
                window_height = min(700, int(screen_height * 0.9))

            self.resize(window_width, window_height)

            # 设置最小尺寸
            self.setMinimumSize(900, 600)

            # 居中显示
            self.move(
                (screen_width - window_width) // 2,
                (screen_height - window_height) // 2
            )

            self._update_resolution_info()

    def _update_resolution_info(self):
        """更新分辨率信息"""
        screen = QApplication.primaryScreen()
        if screen:
            geometry = screen.geometry()
            self.resolution_label.setText(f"屏幕: {geometry.width()}×{geometry.height()}")

    def _generate_sample_data(self):
        """生成示例数据"""
        # 生成示例检测目标
        import random

        targets = []
        for i in range(5):
            x = random.randint(50, 800)
            y = random.randint(50, 600)
            w = random.randint(50, 200)
            h = random.randint(30, 100)

            target = DetectionTarget(
                id=f"target_{i}",
                label=f"button_{i}",
                confidence=random.uniform(0.7, 0.95),
                bbox=(x, y, w, h),
                center=(x + w//2, y + h//2),
                click_point=(x + w//2, y + h//2)
            )
            targets.append(target)

        self.detection_targets = targets
        self._update_targets_display(targets)

        self.status_label.setText("已加载示例数据")

    def update_detection_targets(self, targets: List[Dict[str, Any]], screenshot=None):
        """更新检测目标 - 与主GUI集成的接口"""
        try:
            # 转换检测结果格式
            converted_targets = []
            for i, target in enumerate(targets):
                detection_target = DetectionTarget(
                    id=f"target_{i}",
                    label=target.get('label', 'unknown'),
                    confidence=target.get('confidence', 0.0),
                    bbox=target.get('bbox', (0, 0, 0, 0)),
                    center=target.get('center', (0, 0)),
                    click_point=target.get('click_point', target.get('center', (0, 0)))
                )
                converted_targets.append(detection_target)

            self.detection_targets = converted_targets
            self.current_screenshot = screenshot

            # 更新显示
            if screenshot:
                self.visualizer.set_image(screenshot)
            self.visualizer.update_detections(converted_targets)
            self._update_targets_display(converted_targets)

            self.status_label.setText(f"已更新 {len(targets)} 个检测目标")
            self.logger.info(f"更新检测目标: {len(targets)} 个")

        except Exception as e:
            self.logger.error(f"更新检测目标失败: {e}")
            self.status_label.setText("更新检测目标失败")

    def _update_targets_display(self, targets: List[DetectionTarget]):
        """更新目标显示"""
        # 更新可视化
        self.visualizer.update_detections(targets)

        # 更新表格
        self.targets_table.setRowCount(len(targets))

        for i, target in enumerate(targets):
            # 选择复选框
            checkbox = QCheckBox()
            checkbox.setChecked(target.selected)
            checkbox.stateChanged.connect(lambda state, t=target: self._on_target_selection_changed(t, state))
            self.targets_table.setCellWidget(i, 0, checkbox)

            # 目标类型
            type_item = QTableWidgetItem(target.label)
            self.targets_table.setItem(i, 1, type_item)

            # 置信度
            conf_item = QTableWidgetItem(f"{target.confidence:.2f}")
            self.targets_table.setItem(i, 2, conf_item)

            # 位置信息
            pos_text = f"({target.bbox[0]}, {target.bbox[1]})"
            pos_item = QTableWidgetItem(pos_text)
            self.targets_table.setItem(i, 3, pos_item)

        # 更新统计信息
        self.targets_count_label.setText(f"检测目标: {len(targets)} 个")
        self._update_selected_count()

    def _on_target_selection_changed(self, target: DetectionTarget, state: int):
        """目标选择状态改变"""
        target.selected = (state == Qt.CheckState.Checked.value)
        self._update_selected_count()

    def _update_selected_count(self):
        """更新选中数量"""
        selected_count = len([t for t in self.detection_targets if t.selected])
        self.selected_count_label.setText(f"已选择: {selected_count} 个")
        self.execute_btn.setEnabled(selected_count > 0)

    def _update_visualization_options(self):
        """更新可视化选项"""
        # 这里可以更新可视化显示选项
        pass

    def _select_all_targets(self):
        """选择所有目标"""
        self.visualizer.select_all_boxes()
        for target in self.detection_targets:
            target.selected = True
        self._update_targets_display(self.detection_targets)

    def _clear_selection(self):
        """清除选择"""
        self.visualizer.clear_selection()
        for target in self.detection_targets:
            target.selected = False
        self._update_targets_display(self.detection_targets)

    def _on_selection_changed(self, selected_ids: List[str]):
        """选择改变处理"""
        self._update_selected_count()

    def _refresh_detection(self):
        """刷新检测"""
        self.status_label.setText("正在刷新检测结果...")
        # 请求主窗口重新进行检测
        if self.parent():
            self.status_label.setText("请在主窗口重新进行检测")
        else:
            self.status_label.setText("无法刷新：未连接到主窗口")

    def _preview_operations(self):
        """预览操作"""
        operations = self.operation_config.get_current_operations()

        if not operations:
            QMessageBox.information(self, "提示", "请先配置要执行的操作")
            return

        # 显示操作预览
        preview_text = "操作预览:\n\n"
        for i, op in enumerate(operations, 1):
            preview_text += f"{i}. {op.get('description', '未知操作')}\n"

        QMessageBox.information(self, "操作预览", preview_text)

    def _execute_operations(self):
        """执行操作"""
        operations = self.operation_config.get_current_operations()
        selected_targets = [t for t in self.detection_targets if t.selected]

        if not operations:
            QMessageBox.information(self, "提示", "请先配置要执行的操作")
            return

        if not selected_targets:
            QMessageBox.information(self, "提示", "请先选择要操作的目标")
            return

        # 确认执行
        reply = QMessageBox.question(
            self, "确认执行",
            f"确定要对 {len(selected_targets)} 个目标执行 {len(operations)} 个操作吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # 模拟执行操作
            self.status_label.setText("正在执行操作...")

            # 发送执行信号
            self.operation_executed.emit(operations)

            # 更新状态
            self.status_label.setText("操作执行完成")
            QMessageBox.information(self, "执行完成", f"已执行 {len(operations)} 个操作")

    def _clear_operations(self):
        """清除操作"""
        self.operation_config.clear_operations()
        self.operations_list.clear()
        self.ops_count_label.setText("操作数: 0")
        self.status_label.setText("操作序列已清空")

    def _update_operations_list(self):
        """更新操作列表显示"""
        self.operations_list.clear()
        operations = self.operation_config.get_current_operations()

        for i, op in enumerate(operations, 1):
            item = QListWidgetItem(f"{i}. {op.get('description', '未知操作')}")
            self.operations_list.addItem(item)

        self.ops_count_label.setText(f"操作数: {len(operations)}")

    def _toggle_fullscreen(self):
        """切换全屏模式"""
        if self.isFullScreen():
            self.showNormal()
        else:
            self.showFullScreen()

    def _import_detection_results(self):
        """导入检测结果"""
        QMessageBox.information(self, "功能提示", "导入检测结果功能待实现")

    def _export_operation_config(self):
        """导出操作配置"""
        operations = self.operation_config.get_current_operations()
        if not operations:
            QMessageBox.information(self, "提示", "当前没有配置的操作")
            return

        QMessageBox.information(self, "功能提示", f"导出 {len(operations)} 个操作的配置")

    def _show_about(self):
        """显示关于信息"""
        QMessageBox.about(
            self, "关于",
            "🤖 YOLO自动化操作配置窗口 - 优化版\n\n"
            "版本: 2.0 GUI集成版\n"
            "功能: 可视化配置自动化操作\n"
            "联系: <EMAIL>\n\n"
            "优化特性:\n"
            "• 完整的布局优化，解决控件重叠问题\n"
            "• 自适应屏幕分辨率\n"
            "• 与主GUI系统完美集成\n"
            "• 完整的自动化配置功能\n"
            "• 优化的空间分配和用户体验"
        )

    def closeEvent(self, event):
        """窗口关闭事件"""
        self.window_closed.emit()
        super().closeEvent(event)
