# -*- coding: utf-8 -*-
"""
统一日志记录系统
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

import sys
import logging
from pathlib import Path
from typing import Optional, Dict, Any
from loguru import logger
import time
from datetime import datetime

from .constants import LOGS_DIR, LogLevels


class Logger:
    """统一日志记录器"""
    
    _instance: Optional['Logger'] = None
    _initialized: bool = False
    
    def __new__(cls) -> 'Logger':
        """单例模式"""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        """初始化日志记录器"""
        if not self._initialized:
            self._setup_logger()
            self._initialized = True
    
    def _setup_logger(self) -> None:
        """设置日志记录器"""
        # 确保日志目录存在
        LOGS_DIR.mkdir(parents=True, exist_ok=True)
        
        # 移除默认的loguru处理器
        logger.remove()
        
        # 添加控制台处理器
        logger.add(
            sys.stderr,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
                   "<level>{level: <8}</level> | "
                   "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
                   "<level>{message}</level>",
            level="INFO",
            colorize=True
        )
        
        # 添加文件处理器
        log_file = LOGS_DIR / "app.log"
        logger.add(
            log_file,
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
            level="DEBUG",
            rotation="10 MB",
            retention="7 days",
            compression="zip",
            encoding="utf-8"
        )
        
        # 添加错误日志文件处理器
        error_log_file = LOGS_DIR / "error.log"
        logger.add(
            error_log_file,
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
            level="ERROR",
            rotation="5 MB",
            retention="30 days",
            compression="zip",
            encoding="utf-8"
        )
        
        # 添加性能日志文件处理器
        performance_log_file = LOGS_DIR / "performance.log"
        logger.add(
            performance_log_file,
            format="{time:YYYY-MM-DD HH:mm:ss} | {message}",
            level="INFO",
            rotation="5 MB",
            retention="7 days",
            filter=lambda record: "PERFORMANCE" in record["extra"],
            encoding="utf-8"
        )
    
    def configure(self, config: Dict[str, Any]) -> None:
        """
        根据配置更新日志设置
        
        Args:
            config: 日志配置字典
        """
        try:
            # 移除现有处理器
            logger.remove()
            
            # 获取配置参数
            level = config.get("level", "INFO")
            format_str = config.get("format", 
                "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}")
            rotation = config.get("rotation", "10 MB")
            retention = config.get("retention", "7 days")
            file_path = config.get("file_path", "logs/app.log")
            
            # 重新添加控制台处理器
            logger.add(
                sys.stderr,
                format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
                       "<level>{level: <8}</level> | "
                       "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
                       "<level>{message}</level>",
                level=level,
                colorize=True
            )
            
            # 重新添加文件处理器
            log_file = LOGS_DIR / file_path
            log_file.parent.mkdir(parents=True, exist_ok=True)
            
            logger.add(
                log_file,
                format=format_str,
                level="DEBUG",
                rotation=rotation,
                retention=retention,
                compression="zip",
                encoding="utf-8"
            )
            
            logger.info("日志配置已更新")
            
        except Exception as e:
            logger.error(f"日志配置更新失败: {e}")
    
    def get_logger(self, name: str) -> 'logger':
        """
        获取指定名称的日志记录器
        
        Args:
            name: 日志记录器名称
            
        Returns:
            logger: loguru日志记录器
        """
        return logger.bind(name=name)
    
    def debug(self, message: str, **kwargs) -> None:
        """记录调试信息"""
        logger.debug(message, **kwargs)
    
    def info(self, message: str, **kwargs) -> None:
        """记录信息"""
        logger.info(message, **kwargs)
    
    def warning(self, message: str, **kwargs) -> None:
        """记录警告"""
        logger.warning(message, **kwargs)
    
    def error(self, message: str, **kwargs) -> None:
        """记录错误"""
        logger.error(message, **kwargs)
    
    def critical(self, message: str, **kwargs) -> None:
        """记录严重错误"""
        logger.critical(message, **kwargs)
    
    def exception(self, message: str, **kwargs) -> None:
        """记录异常信息"""
        logger.exception(message, **kwargs)
    
    def performance(self, operation: str, duration: float, **kwargs) -> None:
        """
        记录性能信息
        
        Args:
            operation: 操作名称
            duration: 执行时间（秒）
            **kwargs: 其他性能指标
        """
        perf_data = {
            "operation": operation,
            "duration": duration,
            "timestamp": time.time(),
            **kwargs
        }
        
        logger.bind(PERFORMANCE=True).info(
            f"PERFORMANCE | {operation} | {duration:.4f}s | {perf_data}"
        )
    
    def log_function_call(self, func_name: str, args: tuple = (), kwargs: dict = None) -> None:
        """
        记录函数调用
        
        Args:
            func_name: 函数名称
            args: 位置参数
            kwargs: 关键字参数
        """
        kwargs = kwargs or {}
        logger.debug(f"调用函数: {func_name}(args={args}, kwargs={kwargs})")
    
    def log_detection_result(self, result_count: int, detection_time: float, source: str) -> None:
        """
        记录检测结果
        
        Args:
            result_count: 检测结果数量
            detection_time: 检测耗时
            source: 检测来源
        """
        logger.info(f"检测完成 | 来源: {source} | 结果数: {result_count} | 耗时: {detection_time:.4f}s")
    
    def log_error_with_context(self, error: Exception, context: Dict[str, Any]) -> None:
        """
        记录带上下文的错误信息
        
        Args:
            error: 异常对象
            context: 上下文信息
        """
        logger.error(f"错误发生: {type(error).__name__}: {error} | 上下文: {context}")
    
    def log_system_info(self, info: Dict[str, Any]) -> None:
        """
        记录系统信息
        
        Args:
            info: 系统信息字典
        """
        logger.info(f"系统信息: {info}")
    
    def log_config_change(self, section: str, key: str, old_value: Any, new_value: Any) -> None:
        """
        记录配置变更
        
        Args:
            section: 配置节
            key: 配置键
            old_value: 旧值
            new_value: 新值
        """
        logger.info(f"配置变更 | {section}.{key}: {old_value} -> {new_value}")


class PerformanceLogger:
    """性能日志记录器"""
    
    def __init__(self, logger_instance: Logger):
        """
        初始化性能日志记录器
        
        Args:
            logger_instance: 日志记录器实例
        """
        self.logger = logger_instance
        self.start_time: Optional[float] = None
        self.operation_name: str = ""
    
    def start(self, operation_name: str) -> None:
        """
        开始性能计时
        
        Args:
            operation_name: 操作名称
        """
        self.operation_name = operation_name
        self.start_time = time.time()
        self.logger.debug(f"开始执行: {operation_name}")
    
    def end(self, **kwargs) -> float:
        """
        结束性能计时并记录
        
        Args:
            **kwargs: 额外的性能指标
            
        Returns:
            float: 执行时间
        """
        if self.start_time is None:
            self.logger.warning("性能计时未开始")
            return 0.0
        
        duration = time.time() - self.start_time
        self.logger.performance(self.operation_name, duration, **kwargs)
        
        # 重置状态
        self.start_time = None
        self.operation_name = ""
        
        return duration
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        if self.start_time is not None:
            self.end()


def get_logger(name: str = __name__) -> Logger:
    """
    获取日志记录器实例
    
    Args:
        name: 日志记录器名称
        
    Returns:
        Logger: 日志记录器实例
    """
    logger_instance = Logger()
    return logger_instance.get_logger(name)


def performance_timer(operation_name: str):
    """
    性能计时装饰器
    
    Args:
        operation_name: 操作名称
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            logger_instance = Logger()
            perf_logger = PerformanceLogger(logger_instance)
            
            perf_logger.start(operation_name)
            try:
                result = func(*args, **kwargs)
                perf_logger.end(success=True)
                return result
            except Exception as e:
                perf_logger.end(success=False, error=str(e))
                raise
        
        return wrapper
    return decorator
