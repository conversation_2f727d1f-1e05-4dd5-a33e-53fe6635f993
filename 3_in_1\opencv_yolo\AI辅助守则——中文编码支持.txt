始终中文回复，包括提示词优化


# AI 守则：专门适用于 Augment Code 的中文编码规范

**核心理念**：在 AI 辅助代码生成环境中，建立全生命周期的**中文字符安全处理机制**，通过**统一编码标准**、**智能错误预防**、**自动化质量保证**与**合规监管**，确保生成的代码在中文环境下稳定、安全、可维护。

## 一、文件编码标准：最高优先级执行

### 1.1 强制编码规范
- **UTF-8 无 BOM 统一标准**：所有源文件、配置文件、文档必须以 UTF-8 无 BOM 格式保存[1][2]
- **AI 生成代码检验**：每次 AI 生成代码后，自动检测文件编码格式，发现非 UTF-8 立即报警并拒绝合并
- **Git 预提交钩子**：设置自动化检查，拦截包含 BOM 标记或非 UTF-8 编码的文件[1]

### 1.2 实施技术要点
```bash
# Git 预提交钩子示例
#!/bin/bash
git ls-files '*.py' '*.js' '*.java' '*.cpp' | xargs file | grep -v 'UTF-8'
if [ $? -eq 0 ]; then
    echo "错误：检测到非UTF-8编码文件，请修正后重新提交"
    exit 1
fi
```

## 二、AI 生成环境优化：智能编码配置

### 2.1 模型与输出格式优化
- **模型选择策略**：优先使用 Claude 3.5 Sonnet，其中文处理乱码率仅 6.2%，明显优于其他模型[1][3]
- **响应格式统一**：将 AI 响应格式设置为 Markdown，确保中文字符正确解析[2]
- **上下文管理**：在 `.augment-guidelines` 文件中明确指定中文编码处理规则[3]

### 2.2 提示词编码规范
```yaml
# .augment-guidelines 示例配置
encoding_rules:
  - "所有生成的代码文件必须使用UTF-8无BOM编码"
  - "中文注释和字符串literal需要进行编码安全检查"
  - "避免在代码中硬编码中文路径或文件名"
  - "生成的脚本必须显式声明字符编码环境"
```

## 三、安全审核机制：零容忍字符安全风险

### 3.1 多层次安全扫描
- **静态代码分析**：集成 SonarQube、CodeQL 等工具，专门检测中文字符处理相关的安全漏洞[4][5]
- **AI 代码审计**：使用 AiCodeAudit、CodeArgus 等基于大模型的审计工具，识别编码相关的逻辑漏洞[4][6]
- **人工终审机制**：所有涉及中文字符处理的关键代码必须经过人工安全审批[7]

### 3.2 编码安全检查点
```python
# 中文字符安全检查示例
def validate_chinese_encoding(code_content):
    """AI生成代码的中文编码安全检查"""
    security_checks = [
        check_hardcoded_chinese_strings(code_content),
        check_encoding_declaration(code_content),
        check_sql_injection_chinese(code_content),
        check_xss_chinese_vectors(code_content)
    ]
    return all(security_checks)
```

## 四、错误预防机制：主动防护中文字符问题

### 4.1 环境标准化配置
- **命令行环境**：所有批处理脚本必须以 `chcp 65001 >nul` 开头[1]
- **开发环境变量**：统一设置 `LANG=zh_CN.UTF-8` 和 `LC_ALL=zh_CN.UTF-8`[1]
- **IDE 配置标准化**：强制配置 VS Code、JetBrains 等 IDE 的默认编码为 UTF-8[8][9]

### 4.2 字体与显示规范
```json
// VS Code配置示例
{
  "files.encoding": "utf8",
  "files.autoGuessEncoding": true,
  "terminal.integrated.env.windows": {
    "PYTHONIOENCODING": "UTF-8"
  },
  "editor.fontFamily": "'Microsoft YaHei', Consolas, monospace"
}
```

## 五、质量保证体系：测试驱动的中文兼容性

### 5.1 测试覆盖策略
- **中文字符测试用例**：每个功能模块必须包含中文输入、输出、存储的测试场景[10]
- **编码转换测试**：验证 UTF-8、GBK、Big5 等编码之间的正确转换[11]
- **边界条件测试**：测试中文字符串的截断、拼接、搜索等操作[12]

### 5.2 自动化质量门禁
```python
# 中文字符处理测试示例
def test_chinese_character_handling():
    test_strings = [
        "测试中文字符串",
        "混合English和中文",
        "特殊符号：！@#￥%",
        "emoji测试：👨‍💻🚀"
    ]
    for test_str in test_strings:
        assert process_string(test_str).encoding == 'utf-8'
        assert len(test_str) == len(process_string(test_str))
```

## 六、工具配置统一：开发环境标准化

### 6.1 IDE 插件与配置管理
- **统一插件安装**：中文语言包、编码检测插件、格式化工具[8]
- **配置文件同步**：通过 `.vscode/settings.json` 或团队配置模板统一编码设置[2]
- **终端环境配置**：优先使用 Windows Terminal 或配置传统终端的中文支持[1]

### 6.2 AI 编码助手配置
```yaml
# 通义灵码配置示例
lingma_config:
  response_format: "utf-8"
  chinese_optimization: true
  code_review_rules:
    - "检查中文注释编码正确性"
    - "验证中文字符串处理安全性"
```

## 七、团队协作规范：一致性保障机制

### 7.1 协作流程标准化
- **编码规范文档**：维护团队共享的中文编码处理规范，定期更新[3]
- **代码审查清单**：在 PR 模板中加入中文编码专项检查项[13]
- **知识分享机制**：定期举办中文编码最佳实践培训[10]

### 7.2 版本控制集成
```bash
# .gitattributes 配置
*.py text eol=lf encoding=utf-8
*.js text eol=lf encoding=utf-8
*.java text eol=lf encoding=utf-8
*.md text eol=lf encoding=utf-8
```

## 八、合规要求：法规遵循与标识管理

### 8.1 AI 内容标识
- **生成内容标记**：按照《人工智能生成合成内容标识办法》要求，对 AI 生成的代码进行明确标识[14][15]
- **审计日志记录**：记录所有 AI 生成代码的来源、时间、审核状态等信息[7]
- **数据安全保护**：确保训练数据和生成内容符合《生成式人工智能服务安全基本要求》[7][16]

### 8.2 企业级安全管控
```python
# AI生成代码标识示例
"""
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-07-04 20:21 CST
AI模型: Claude-3.5-Sonnet
审核状态: 已通过人工安全审查
编码标准: UTF-8无BOM
"""
```
