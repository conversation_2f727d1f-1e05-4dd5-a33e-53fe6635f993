#!/usr/bin/env python3
"""
自动化操作执行模块
提供鼠标、键盘等自动化操作功能
"""

import time
import pyautogui
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass
from enum import Enum

# 配置pyautogui
pyautogui.FAILSAFE = True  # 启用安全模式
pyautogui.PAUSE = 0.1  # 操作间隔

class MouseAction(Enum):
    """鼠标操作类型"""
    LEFT_CLICK = "left_click"
    RIGHT_CLICK = "right_click"
    DOUBLE_CLICK = "double_click"
    DRAG = "drag"
    SCROLL = "scroll"
    HOVER = "hover"

class KeyboardAction(Enum):
    """键盘操作类型"""
    TYPE_TEXT = "type_text"
    PRESS_KEY = "press_key"
    HOTKEY = "hotkey"
    KEY_DOWN = "key_down"
    KEY_UP = "key_up"

@dataclass
class AutomationAction:
    """自动化操作数据类"""
    id: str
    action_type: str  # mouse, keyboard, composite
    target_position: Optional[Tuple[int, int]] = None
    parameters: Dict[str, Any] = None
    delay_before: float = 0.0  # 执行前延迟
    delay_after: float = 0.1   # 执行后延迟
    description: str = ""

class AutomationExecutor:
    """自动化操作执行器"""
    
    def __init__(self):
        """初始化执行器"""
        self.is_enabled = True
        self.safety_checks = True
        self.execution_log: List[Dict[str, Any]] = []
        
        # 安全区域设置（避免误操作系统关键区域）
        self.safe_zones = []
        self.forbidden_zones = [
            (0, 0, 100, 50),  # 左上角系统区域
        ]
    
    def execute_mouse_action(self, action: MouseAction, position: Tuple[int, int], 
                           **kwargs) -> bool:
        """
        执行鼠标操作
        
        Args:
            action: 鼠标操作类型
            position: 目标位置 (x, y)
            **kwargs: 额外参数
            
        Returns:
            操作是否成功
        """
        if not self.is_enabled:
            return False
            
        if not self._is_safe_position(position):
            print(f"警告: 位置 {position} 不在安全区域内")
            return False
        
        try:
            x, y = position
            
            if action == MouseAction.LEFT_CLICK:
                pyautogui.click(x, y, button='left')
                self._log_action("mouse_left_click", position)
                
            elif action == MouseAction.RIGHT_CLICK:
                pyautogui.click(x, y, button='right')
                self._log_action("mouse_right_click", position)
                
            elif action == MouseAction.DOUBLE_CLICK:
                pyautogui.doubleClick(x, y)
                self._log_action("mouse_double_click", position)
                
            elif action == MouseAction.DRAG:
                end_position = kwargs.get('end_position')
                if end_position:
                    pyautogui.drag(x, y, end_position[0], end_position[1], 
                                 duration=kwargs.get('duration', 0.5))
                    self._log_action("mouse_drag", position, end_position=end_position)
                
            elif action == MouseAction.SCROLL:
                scroll_amount = kwargs.get('scroll_amount', 3)
                pyautogui.scroll(scroll_amount, x=x, y=y)
                self._log_action("mouse_scroll", position, scroll_amount=scroll_amount)
                
            elif action == MouseAction.HOVER:
                pyautogui.moveTo(x, y)
                self._log_action("mouse_hover", position)
                
            return True
            
        except Exception as e:
            print(f"鼠标操作失败: {e}")
            return False
    
    def execute_keyboard_action(self, action: KeyboardAction, **kwargs) -> bool:
        """
        执行键盘操作
        
        Args:
            action: 键盘操作类型
            **kwargs: 操作参数
            
        Returns:
            操作是否成功
        """
        if not self.is_enabled:
            return False
            
        try:
            if action == KeyboardAction.TYPE_TEXT:
                text = kwargs.get('text', '')
                interval = kwargs.get('interval', 0.01)
                pyautogui.typewrite(text, interval=interval)
                self._log_action("keyboard_type", text=text)
                
            elif action == KeyboardAction.PRESS_KEY:
                key = kwargs.get('key')
                presses = kwargs.get('presses', 1)
                pyautogui.press(key, presses=presses)
                self._log_action("keyboard_press", key=key, presses=presses)
                
            elif action == KeyboardAction.HOTKEY:
                keys = kwargs.get('keys', [])
                pyautogui.hotkey(*keys)
                self._log_action("keyboard_hotkey", keys=keys)
                
            elif action == KeyboardAction.KEY_DOWN:
                key = kwargs.get('key')
                pyautogui.keyDown(key)
                self._log_action("keyboard_key_down", key=key)
                
            elif action == KeyboardAction.KEY_UP:
                key = kwargs.get('key')
                pyautogui.keyUp(key)
                self._log_action("keyboard_key_up", key=key)
                
            return True
            
        except Exception as e:
            print(f"键盘操作失败: {e}")
            return False
    
    def execute_composite_action(self, actions: List[AutomationAction]) -> bool:
        """
        执行复合操作序列
        
        Args:
            actions: 操作序列
            
        Returns:
            所有操作是否成功
        """
        success_count = 0
        
        for action in actions:
            # 执行前延迟
            if action.delay_before > 0:
                time.sleep(action.delay_before)
            
            success = False
            
            if action.action_type == "mouse":
                mouse_action = MouseAction(action.parameters.get('action'))
                success = self.execute_mouse_action(
                    mouse_action, 
                    action.target_position, 
                    **action.parameters
                )
                
            elif action.action_type == "keyboard":
                keyboard_action = KeyboardAction(action.parameters.get('action'))
                success = self.execute_keyboard_action(
                    keyboard_action, 
                    **action.parameters
                )
            
            if success:
                success_count += 1
            
            # 执行后延迟
            if action.delay_after > 0:
                time.sleep(action.delay_after)
        
        return success_count == len(actions)
    
    def create_click_action(self, position: Tuple[int, int], click_type: str = "left",
                          delay_before: float = 0.0, delay_after: float = 0.1) -> AutomationAction:
        """创建点击操作"""
        action_map = {
            "left": MouseAction.LEFT_CLICK,
            "right": MouseAction.RIGHT_CLICK,
            "double": MouseAction.DOUBLE_CLICK
        }
        
        return AutomationAction(
            id=f"click_{int(time.time())}",
            action_type="mouse",
            target_position=position,
            parameters={"action": action_map[click_type].value},
            delay_before=delay_before,
            delay_after=delay_after,
            description=f"{click_type.title()} click at {position}"
        )
    
    def create_type_action(self, text: str, delay_before: float = 0.0, 
                          delay_after: float = 0.1) -> AutomationAction:
        """创建文本输入操作"""
        return AutomationAction(
            id=f"type_{int(time.time())}",
            action_type="keyboard",
            parameters={
                "action": KeyboardAction.TYPE_TEXT.value,
                "text": text,
                "interval": 0.01
            },
            delay_before=delay_before,
            delay_after=delay_after,
            description=f"Type text: {text[:20]}..."
        )
    
    def create_hotkey_action(self, keys: List[str], delay_before: float = 0.0,
                           delay_after: float = 0.1) -> AutomationAction:
        """创建快捷键操作"""
        return AutomationAction(
            id=f"hotkey_{int(time.time())}",
            action_type="keyboard",
            parameters={
                "action": KeyboardAction.HOTKEY.value,
                "keys": keys
            },
            delay_before=delay_before,
            delay_after=delay_after,
            description=f"Hotkey: {'+'.join(keys)}"
        )
    
    def create_drag_action(self, start_pos: Tuple[int, int], end_pos: Tuple[int, int],
                          duration: float = 0.5, delay_before: float = 0.0,
                          delay_after: float = 0.1) -> AutomationAction:
        """创建拖拽操作"""
        return AutomationAction(
            id=f"drag_{int(time.time())}",
            action_type="mouse",
            target_position=start_pos,
            parameters={
                "action": MouseAction.DRAG.value,
                "end_position": end_pos,
                "duration": duration
            },
            delay_before=delay_before,
            delay_after=delay_after,
            description=f"Drag from {start_pos} to {end_pos}"
        )
    
    def _is_safe_position(self, position: Tuple[int, int]) -> bool:
        """检查位置是否安全"""
        if not self.safety_checks:
            return True
            
        x, y = position
        
        # 检查是否在禁止区域
        for zone in self.forbidden_zones:
            zone_x, zone_y, zone_w, zone_h = zone
            if (zone_x <= x <= zone_x + zone_w and 
                zone_y <= y <= zone_y + zone_h):
                return False
        
        # 检查屏幕边界
        screen_width, screen_height = pyautogui.size()
        if not (0 <= x <= screen_width and 0 <= y <= screen_height):
            return False
            
        return True
    
    def _log_action(self, action_type: str, position: Tuple[int, int] = None, **kwargs):
        """记录操作日志"""
        log_entry = {
            "timestamp": time.time(),
            "action_type": action_type,
            "position": position,
            "parameters": kwargs
        }
        self.execution_log.append(log_entry)
        
        # 保持日志大小
        if len(self.execution_log) > 1000:
            self.execution_log = self.execution_log[-500:]
    
    def get_execution_log(self, limit: int = 50) -> List[Dict[str, Any]]:
        """获取执行日志"""
        return self.execution_log[-limit:]
    
    def clear_log(self):
        """清除执行日志"""
        self.execution_log.clear()
    
    def set_safety_mode(self, enabled: bool):
        """设置安全模式"""
        self.safety_checks = enabled
    
    def add_forbidden_zone(self, zone: Tuple[int, int, int, int]):
        """添加禁止操作区域"""
        self.forbidden_zones.append(zone)
    
    def enable_execution(self, enabled: bool):
        """启用/禁用执行"""
        self.is_enabled = enabled
