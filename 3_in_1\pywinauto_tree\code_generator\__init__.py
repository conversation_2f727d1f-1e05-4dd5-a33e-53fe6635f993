#!/usr/bin/env python
# -*- coding:utf-8 -*-

from .generator_ui import CodeGeneratorDialog
from .window_locator import WindowLocatorStrategy, get_available_strategies as get_window_strategies
from .control_locator import ControlLocatorStrategy, get_available_strategies as get_control_strategies
from .code_snippet import CodeSnippetManager

__all__ = [
    'CodeGeneratorDialog',
    'WindowLocatorStrategy',
    'ControlLocatorStrategy',
    'CodeSnippetManager',
    'get_window_strategies',
    'get_control_strategies'
]
