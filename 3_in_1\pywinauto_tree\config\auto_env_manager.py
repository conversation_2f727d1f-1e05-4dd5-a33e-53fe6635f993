# -*- coding:utf-8 -*-
import os
import sys
import json
import time
import shutil
import logging
import psutil
from datetime import datetime
from pathlib import Path
from python_path_manager import PythonPathManager

class AutoEnvManager:
    def __init__(self):
        self.logger = self._setup_logging()
        self.python_manager = PythonPathManager()
        self.config_file = 'config/auto_env_config.json'
        self.config = self._load_config()
        
    def _setup_logging(self):
        logger = logging.getLogger('AutoEnvManager')
        logger.setLevel(logging.DEBUG)
        
        if not os.path.exists('logs'):
            os.makedirs('logs')
            
        fh = logging.FileHandler(
            f'logs/auto_env_manager_{datetime.now():%Y%m%d}.log',
            encoding='utf-8'
        )
        fh.setLevel(logging.DEBUG)
        
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        fh.setFormatter(formatter)
        logger.addHandler(fh)
        
        return logger
        
    def _load_config(self):
        """加载自动化配置"""
        default_config = {
            'auto_backup': {
                'enabled': True,
                'on_exit': True,
                'on_package_change': True,
                'interval_hours': 24,
                'min_changes': 5,  # 最小文件变更数触发备份
                'last_backup': None
            },
            'auto_clean': {
                'enabled': True,
                'max_backups_per_system': 3,
                'max_days_keep': 30,
                'min_free_space_gb': 10,
                'keep_first_backup': True  # 保留首次备份
            },
            'backup_importance': {
                'exit_backup': 1.0,
                'change_backup': 0.8,
                'periodic_backup': 0.6
            },
            'monitored_paths': [
                'venv/Lib/site-packages',
                'venv/Scripts',
                '.env_initialized'
            ]
        }
        
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    saved_config = json.load(f)
                    # 更新默认配置
                    default_config.update(saved_config)
            return default_config
        except Exception as e:
            self.logger.error(f"Failed to load config: {e}")
            return default_config
            
    def _save_config(self):
        """保存配置"""
        try:
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"Failed to save config: {e}")
            
    def _get_directory_hash(self, path):
        """获取目录的内容hash"""
        import hashlib
        
        if not os.path.exists(path):
            return None
            
        hash_md5 = hashlib.md5()
        
        if os.path.isfile(path):
            try:
                with open(path, "rb") as f:
                    for chunk in iter(lambda: f.read(4096), b""):
                        hash_md5.update(chunk)
                return hash_md5.hexdigest()
            except Exception:
                return None
                
        for root, dirs, files in os.walk(path):
            for names in files:
                filepath = os.path.join(root, names)
                try:
                    with open(filepath, "rb") as f:
                        for chunk in iter(lambda: f.read(4096), b""):
                            hash_md5.update(chunk)
                except Exception:
                    continue
                    
        return hash_md5.hexdigest()
        
    def _check_changes(self):
        """检查监控路径的变更"""
        changes = 0
        current_hashes = {}
        
        # 加载上次的hash记录
        hash_file = 'config/path_hashes.json'
        try:
            if os.path.exists(hash_file):
                with open(hash_file, 'r') as f:
                    last_hashes = json.load(f)
            else:
                last_hashes = {}
        except Exception:
            last_hashes = {}
            
        # 检查当前hash
        for path in self.config['monitored_paths']:
            current_hash = self._get_directory_hash(path)
            current_hashes[path] = current_hash
            
            if path not in last_hashes:
                changes += 1
            elif last_hashes[path] != current_hash:
                changes += 1
                
        # 保存当前hash
        try:
            with open(hash_file, 'w') as f:
                json.dump(current_hashes, f)
        except Exception as e:
            self.logger.error(f"Failed to save path hashes: {e}")
            
        return changes
        
    def _get_free_space_gb(self):
        """获取可用空间(GB)"""
        try:
            usage = shutil.disk_usage(os.path.abspath('backup'))
            return usage.free / (1024 * 1024 * 1024)  # 转换为GB
        except Exception:
            return float('inf')  # 如果无法获取，返回无限大
            
    def _calculate_backup_importance(self, backup_info):
        """计算备份的重要性分数"""
        score = 0.0
        
        # 基础分数(基于备份类型)
        backup_type = backup_info.get('backup_type', 'periodic')
        score += self.config['backup_importance'].get(f'{backup_type}_backup', 0.5)
        
        # 时间因素(越新越重要)
        try:
            backup_time = datetime.strptime(backup_info['timestamp'], '%Y%m%d_%H%M%S')
            days_old = (datetime.now() - backup_time).days
            time_score = max(0, 1 - (days_old / self.config['auto_clean']['max_days_keep']))
            score += time_score * 0.3
        except Exception:
            pass
            
        # 是否是首次备份
        if backup_info.get('is_first_backup'):
            score += 0.5
            
        # 包含重要更新
        if backup_info.get('important_changes'):
            score += 0.2
            
        return min(score, 1.0)  # 确保分数不超过1.0
        
    def check_and_backup(self):
        """检查是否需要备份并执行"""
        if not self.config['auto_backup']['enabled']:
            return False
            
        try:
            # 检查时间间隔
            last_backup = self.config['auto_backup']['last_backup']
            if last_backup:
                last_time = datetime.strptime(last_backup, '%Y-%m-%d %H:%M:%S')
                hours_passed = (datetime.now() - last_time).total_seconds() / 3600
                if hours_passed < self.config['auto_backup']['interval_hours']:
                    return False
                    
            # 检查变更数量
            changes = self._check_changes()
            if changes < self.config['auto_backup']['min_changes']:
                return False
                
            # 检查空间
            if self._get_free_space_gb() < self.config['auto_clean']['min_free_space_gb']:
                self.clean_old_backups()  # 尝试清理空间
                if self._get_free_space_gb() < self.config['auto_clean']['min_free_space_gb']:
                    self.logger.warning("Not enough free space for backup")
                    return False
                    
            # 执行备份
            success, backup_path = self.python_manager.backup_venv()
            if success:
                self.config['auto_backup']['last_backup'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                self._save_config()
                return True
                
        except Exception as e:
            self.logger.error(f"Backup failed: {e}")
            
        return False
        
    def clean_old_backups(self):
        """智能清理旧备份"""
        if not self.config['auto_clean']['enabled']:
            return
            
        try:
            # 获取所有备份
            all_backups = self.python_manager.list_backups()
            if not all_backups:
                return
                
            # 按系统分组
            system_backups = {}
            for backup in all_backups:
                sys_id = backup['system_id']
                if sys_id not in system_backups:
                    system_backups[sys_id] = []
                system_backups[sys_id].append(backup)
                
            # 处理每个系统的备份
            for sys_id, backups in system_backups.items():
                # 计算每个备份的重要性
                for backup in backups:
                    backup['importance'] = self._calculate_backup_importance(backup)
                    
                # 按重要性排序
                backups.sort(key=lambda x: x['importance'], reverse=True)
                
                # 确定要保留的备份
                max_keep = self.config['auto_clean']['max_backups_per_system']
                to_remove = backups[max_keep:]
                
                # 删除多余的备份
                for backup in to_remove:
                    try:
                        backup_path = backup['backup_path']
                        if os.path.exists(backup_path):
                            shutil.rmtree(backup_path)
                            self.logger.info(f"Removed backup: {backup_path}")
                    except Exception as e:
                        self.logger.error(f"Failed to remove backup {backup_path}: {e}")
                        
        except Exception as e:
            self.logger.error(f"Clean backups failed: {e}")
            
    def on_exit(self):
        """程序退出时的处理"""
        if self.config['auto_backup']['on_exit']:
            self.check_and_backup()
            
    def check_and_create_venv(self):
        """检查并创建虚拟环境"""
        if not os.path.exists('venv'):
            # 查找最佳Python版本
            system_id, _ = self.python_manager._get_system_identifier()
            python_path = self.python_manager.get_cached_python(system_id)
            
            if python_path:
                try:
                    # 使用缓存的Python创建虚拟环境
                    subprocess.run([python_path, '-m', 'venv', 'venv'], check=True)
                    return True
                except Exception:
                    pass
                    
            # 如果失败，使用start.bat的标准流程
            return False
            
        return True

def create_manager():
    """创建管理器单例"""
    if not hasattr(create_manager, 'instance'):
        create_manager.instance = AutoEnvManager()
    return create_manager.instance

# 注册退出处理
import atexit
manager = create_manager()
atexit.register(manager.on_exit)

if __name__ == '__main__':
    import argparse
    import subprocess
    
    parser = argparse.ArgumentParser(description='Automatic Environment Manager')
    parser.add_argument('--check-and-create', action='store_true', help='Check and create virtual environment')
    parser.add_argument('--force-backup', action='store_true', help='Force create backup')
    parser.add_argument('--clean-backups', action='store_true', help='Clean old backups')
    parser.add_argument('--list-backups', action='store_true', help='List all backups')
    
    args = parser.parse_args()
    
    manager = create_manager()
    
    if args.check_and_create:
        if manager.check_and_create_venv():
            print("[INFO] Virtual environment check/creation successful")
            sys.exit(0)
        else:
            print("[WARN] Failed to create virtual environment automatically")
            sys.exit(1)
    elif args.force_backup:
        if manager.check_and_backup():
            print("[INFO] Backup created successfully")
            sys.exit(0)
        else:
            print("[WARN] Failed to create backup")
            sys.exit(1)
    elif args.clean_backups:
        manager.clean_old_backups()
        print("[INFO] Cleaned old backups")
        sys.exit(0)
    elif args.list_backups:
        backups = manager.python_manager.list_backups()
        for backup in backups:
            print(f"Backup: {backup['timestamp']}")
            print(f"System: {backup['system_info']['computer_name']}")
            print(f"Path: {backup['backup_path']}")
            print("---")
        sys.exit(0)
    else:
        parser.print_help()
        sys.exit(1) 