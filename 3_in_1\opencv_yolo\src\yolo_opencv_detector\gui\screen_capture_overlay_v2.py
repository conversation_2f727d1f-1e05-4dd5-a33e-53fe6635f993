# -*- coding: utf-8 -*-
"""
重构的全屏截图区域选择覆盖层
作者: Cursor AI
创建时间: 2025-07-05
编码标准: UTF-8无BOM
"""

from PyQt6.QtWidgets import QWidget, QApplication
from PyQt6.QtCore import Qt, pyqtSignal, QRect, QTimer, QPoint
from PyQt6.QtGui import (
    QPainter, QPen, QColor, QBrush, QPixmap, QFont, 
    QCursor, QKeySequence, QShortcut
)

import cv2
import numpy as np
from ..utils.logger import Logger


class ScreenCaptureOverlayV2(QWidget):
    """重构的全屏截图区域选择覆盖层"""
    
    # 信号：区域选择完成 (x, y, width, height, screenshot_image)
    region_captured = pyqtSignal(int, int, int, int, object)
    # 信号：取消选择
    capture_cancelled = pyqtSignal()
    # 信号：全屏截图完成
    fullscreen_captured = pyqtSignal(object, str)  # (image, filepath)
    
    def __init__(self):
        super().__init__()
        self.logger = Logger()
        
        # 设置窗口属性
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint | 
            Qt.WindowType.WindowStaysOnTopHint |
            Qt.WindowType.Tool
        )
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setWindowState(Qt.WindowState.WindowFullScreen)
        
        # 选择状态
        self.selecting = False
        self.start_point = None
        self.end_point = None
        self.current_rect = QRect()
        
        # 截图数据
        self.screenshot = None
        self.screenshot_pixmap = None
        
        # 设置光标
        self.setCursor(QCursor(Qt.CursorShape.CrossCursor))
        
        # 设置快捷键
        self.escape_shortcut = QShortcut(QKeySequence(Qt.Key.Key_Escape), self)
        self.escape_shortcut.activated.connect(self._cancel_capture)
        
        # 初始化截图
        self._capture_screen()
        
        self.logger.info("屏幕截图覆盖层初始化完成")
    
    def _capture_screen(self):
        """截取屏幕"""
        try:
            from ..utils.screenshot_helper import get_screenshot_helper
            screenshot_helper = get_screenshot_helper()
            
            if screenshot_helper.is_available():
                # 截取全屏
                image, pixmap, filepath = screenshot_helper.take_screenshot_with_pixmap(save_to_file=True)
                
                if image is not None and pixmap is not None:
                    self.screenshot = image
                    self.screenshot_pixmap = pixmap
                    
                    # 发送全屏截图信号
                    self.fullscreen_captured.emit(image, filepath)
                    
                    self.logger.info("屏幕截图完成，等待区域选择")
                else:
                    self.logger.error("截图失败")
                    self._cancel_capture()
            else:
                self.logger.error("截图服务不可用")
                self._cancel_capture()
                
        except Exception as e:
            self.logger.error(f"截取屏幕失败: {e}")
            self._cancel_capture()
    
    def paintEvent(self, event):
        """绘制覆盖层"""
        painter = QPainter(self)
        
        # 绘制半透明遮罩
        painter.fillRect(self.rect(), QColor(0, 0, 0, 100))
        
        # 如果有选择区域，绘制选择框
        if self.start_point and self.end_point:
            # 计算选择矩形
            x1 = min(self.start_point.x(), self.end_point.x())
            y1 = min(self.start_point.y(), self.end_point.y())
            x2 = max(self.start_point.x(), self.end_point.x())
            y2 = max(self.start_point.y(), self.end_point.y())
            
            self.current_rect = QRect(x1, y1, x2 - x1, y2 - y1)
            
            # 清除选择区域的遮罩
            painter.setCompositionMode(QPainter.CompositionMode.CompositionMode_Clear)
            painter.fillRect(self.current_rect, QColor(0, 0, 0, 0))
            
            # 恢复绘制模式
            painter.setCompositionMode(QPainter.CompositionMode.CompositionMode_SourceOver)
            
            # 绘制选择框边框
            pen = QPen(QColor(255, 0, 0), 2)
            painter.setPen(pen)
            painter.drawRect(self.current_rect)
            
            # 绘制尺寸信息
            width = self.current_rect.width()
            height = self.current_rect.height()
            size_text = f"{width} × {height}"
            
            # 设置字体
            font = QFont("Arial", 12, QFont.Weight.Bold)
            painter.setFont(font)
            
            # 计算文本位置
            text_rect = painter.fontMetrics().boundingRect(size_text)
            text_x = self.current_rect.x() + 5
            text_y = self.current_rect.y() - 5
            
            # 确保文本在屏幕内
            if text_y < text_rect.height():
                text_y = self.current_rect.y() + text_rect.height() + 5
            
            # 绘制文本背景
            bg_rect = QRect(text_x - 3, text_y - text_rect.height() - 2, 
                           text_rect.width() + 6, text_rect.height() + 4)
            painter.fillRect(bg_rect, QColor(0, 0, 0, 180))
            
            # 绘制文本
            painter.setPen(QPen(QColor(255, 255, 255), 1))
            painter.drawText(text_x, text_y, size_text)
        
        # 绘制帮助信息
        self._draw_help_text(painter)
    
    def _draw_help_text(self, painter):
        """绘制帮助文本"""
        help_lines = [
            "🖱️ 拖拽选择区域",
            "⌨️ ESC 取消选择",
            "📏 实时显示尺寸"
        ]
        
        painter.setPen(QPen(QColor(255, 255, 255), 1))
        painter.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        
        # 在屏幕顶部中央显示帮助信息
        y_offset = 30
        for line in help_lines:
            text_rect = painter.fontMetrics().boundingRect(line)
            text_x = (self.width() - text_rect.width()) // 2
            text_y = y_offset
            
            # 绘制文本背景
            bg_rect = QRect(text_x - 10, text_y - text_rect.height() - 5, 
                           text_rect.width() + 20, text_rect.height() + 10)
            painter.fillRect(bg_rect, QColor(0, 0, 0, 180))
            
            # 绘制文本
            painter.drawText(text_x, text_y, line)
            y_offset += text_rect.height() + 15
    
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.selecting = True
            self.start_point = event.position().toPoint()
            self.end_point = self.start_point
            self.update()
    
    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if self.selecting:
            self.end_point = event.position().toPoint()
            self.update()
    
    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.MouseButton.LeftButton and self.selecting:
            self.selecting = False
            
            if self.start_point and self.end_point:
                # 计算选择区域
                x1 = min(self.start_point.x(), self.end_point.x())
                y1 = min(self.start_point.y(), self.end_point.y())
                x2 = max(self.start_point.x(), self.end_point.x())
                y2 = max(self.start_point.y(), self.end_point.y())
                
                width = x2 - x1
                height = y2 - y1
                
                # 检查选择区域是否有效
                if width > 10 and height > 10:
                    # 从截图中提取选择的区域
                    if self.screenshot is not None:
                        # 确保坐标在有效范围内
                        img_height, img_width = self.screenshot.shape[:2]
                        x1 = max(0, min(x1, img_width - 1))
                        y1 = max(0, min(y1, img_height - 1))
                        x2 = max(x1 + 1, min(x2, img_width))
                        y2 = max(y1 + 1, min(y2, img_height))
                        
                        # 提取区域图像
                        region_image = self.screenshot[y1:y2, x1:x2]
                        
                        # 发射信号
                        self.region_captured.emit(x1, y1, x2 - x1, y2 - y1, region_image)
                        self.close()
                        return
                
                # 如果区域太小，继续选择
                self.start_point = None
                self.end_point = None
                self.update()
    
    def keyPressEvent(self, event):
        """键盘事件"""
        if event.key() == Qt.Key.Key_Escape:
            self._cancel_capture()
        else:
            super().keyPressEvent(event)
    
    def _cancel_capture(self):
        """取消截图"""
        self.capture_cancelled.emit()
        self.close()
    
    def closeEvent(self, event):
        """关闭事件"""
        self.logger.info("屏幕截图覆盖层关闭")
        super().closeEvent(event)


def start_screen_capture_v2():
    """启动屏幕截图选择"""
    overlay = ScreenCaptureOverlayV2()
    overlay.show()
    return overlay
