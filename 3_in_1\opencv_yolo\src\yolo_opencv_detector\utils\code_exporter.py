#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代码导出器
为GUI检测功能生成的代码提供独立导出能力
支持导出完整可运行的示例代码包
"""

import os
import sys
import shutil
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import logging

class CodeExporter:
    """代码导出器"""
    
    def __init__(self):
        """初始化代码导出器"""
        self.logger = logging.getLogger(__name__)
        self.project_root = Path(__file__).parent.parent.parent.parent.parent
        self.export_base_dir = self.project_root / "exported_scripts"
        
    def export_standalone_script(self, 
                                code_content: str,
                                template_name: str = "检测脚本",
                                description: str = "YOLO目标检测自动化脚本") -> Tuple[bool, str]:
        """
        导出独立可运行的脚本包
        
        Args:
            code_content: 要导出的代码内容
            template_name: 模板名称
            description: 脚本描述
            
        Returns:
            (成功标志, 导出路径或错误信息)
        """
        try:
            # 创建导出目录
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            safe_template_name = self._sanitize_filename(template_name)
            export_dir_name = f"{safe_template_name}_{timestamp}"
            export_dir = self.export_base_dir / export_dir_name
            
            # 确保导出基础目录存在
            self.export_base_dir.mkdir(exist_ok=True)
            export_dir.mkdir(exist_ok=True)
            
            self.logger.info(f"开始导出脚本到: {export_dir}")
            
            # 1. 生成主要代码文件
            main_py_content = self._generate_main_script(code_content, template_name, description)
            main_py_path = export_dir / "main.py"
            with open(main_py_path, 'w', encoding='utf-8') as f:
                f.write(main_py_content)
            
            # 2. 生成依赖列表
            requirements_content = self._generate_requirements()
            requirements_path = export_dir / "requirements.txt"
            with open(requirements_path, 'w', encoding='utf-8') as f:
                f.write(requirements_content)
            
            # 3. 生成运行脚本
            run_bat_content = self._generate_run_script(safe_template_name)  # 使用清理后的名称
            run_bat_path = export_dir / "run.bat"
            with open(run_bat_path, 'w', encoding='utf-8') as f:  # 使用UTF-8编码
                f.write(run_bat_content)
            
            # 4. 生成配置目录和文件
            config_dir = export_dir / "config"
            config_dir.mkdir(exist_ok=True)
            
            config_content = self._generate_config_file(template_name, description)
            config_path = config_dir / "settings.json"
            with open(config_path, 'w', encoding='utf-8') as f:
                f.write(config_content)
            
            # 5. 生成README文档
            readme_content = self._generate_readme(template_name, description, export_dir_name)
            readme_path = export_dir / "README.md"
            with open(readme_path, 'w', encoding='utf-8') as f:
                f.write(readme_content)
            
            # 6. 复制必要的工具模块
            self._copy_utility_modules(export_dir)
            
            self.logger.info(f"脚本导出成功: {export_dir}")
            return True, str(export_dir)
            
        except Exception as e:
            error_msg = f"导出脚本失败: {e}"
            self.logger.error(error_msg)
            return False, error_msg
    
    def _sanitize_filename(self, filename: str) -> str:
        """清理文件名，移除不安全字符"""
        import re
        # 移除或替换不安全的字符
        safe_name = re.sub(r'[<>:"/\\|?*]', '_', filename)
        safe_name = re.sub(r'\s+', '_', safe_name)  # 空格替换为下划线
        safe_name = safe_name.strip('._')  # 移除开头结尾的点和下划线
        return safe_name[:50]  # 限制长度
    
    def _generate_main_script(self, code_content: str, template_name: str, description: str) -> str:
        """生成主要脚本文件"""
        return f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
{template_name} - 独立运行脚本
{description}

自动生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
运行环境: Python 3.7+
依赖包: 请先运行 pip install -r requirements.txt

使用方法:
1. 确保已安装所有依赖包
2. 运行 python main.py 或双击 run.bat
3. 按照提示进行操作
"""

import sys
import os
import cv2
import numpy as np
import json
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# 导入工具模块
try:
    from utils.chinese_text_renderer import put_chinese_text, ChineseTextRenderer
    CHINESE_RENDERER_AVAILABLE = True
except ImportError:
    CHINESE_RENDERER_AVAILABLE = False
    print("⚠️ 中文渲染器不可用，中文字符可能显示为问号")

try:
    from utils.detection_utils import DetectionResult, BoundingBox
    DETECTION_UTILS_AVAILABLE = True
except ImportError:
    DETECTION_UTILS_AVAILABLE = False
    print("⚠️ 检测工具不可用，使用基础功能")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('detection_log.txt', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class StandaloneDetector:
    """独立检测器"""
    
    def __init__(self):
        """初始化检测器"""
        self.logger = logger
        self.config = self._load_config()
        self.chinese_renderer = None
        
        if CHINESE_RENDERER_AVAILABLE:
            try:
                self.chinese_renderer = ChineseTextRenderer()
                self.logger.info("✅ 中文渲染器初始化成功")
            except Exception as e:
                self.logger.warning(f"中文渲染器初始化失败: {{e}}")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            config_path = Path("config/settings.json")
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            self.logger.warning(f"加载配置失败: {{e}}")
        
        # 返回默认配置
        return {{
            "template_name": "{template_name}",
            "description": "{description}",
            "confidence_threshold": 0.5,
            "nms_threshold": 0.4
        }}
    
    def render_text_on_image(self, image: np.ndarray, text: str, 
                           position: Tuple[int, int], 
                           color: Tuple[int, int, int] = (0, 255, 0)) -> np.ndarray:
        """在图像上渲染文字"""
        if self.chinese_renderer and CHINESE_RENDERER_AVAILABLE:
            try:
                return put_chinese_text(image, text, position, 
                                      font_size=16, color=color, background=True)
            except Exception as e:
                self.logger.warning(f"中文渲染失败: {{e}}")
        
        # 回退到OpenCV渲染
        cv2.putText(image, text, position, cv2.FONT_HERSHEY_SIMPLEX, 
                   0.6, color, 2, cv2.LINE_AA)
        return image
    
    def run_detection(self):
        """运行检测主程序"""
        self.logger.info("🚀 开始运行检测程序...")
        self.logger.info(f"📋 模板: {{self.config.get('template_name', 'Unknown')}}")
        self.logger.info(f"📝 描述: {{self.config.get('description', 'No description')}}")
        
        try:
            # 这里插入用户的检测代码
            {self._indent_code(code_content, 12)}
            
        except Exception as e:
            self.logger.error(f"检测程序执行失败: {{e}}")
            import traceback
            traceback.print_exc()

def main():
    """主函数"""
    print("=" * 60)
    print(f"🎯 {{'{template_name}'}}")
    print(f"📝 {{'{description}'}}")
    print("=" * 60)
    
    try:
        detector = StandaloneDetector()
        detector.run_detection()
        
        print("\\n✅ 程序执行完成")
        
    except KeyboardInterrupt:
        print("\\n⚠️ 程序被用户中断")
    except Exception as e:
        print(f"\\n❌ 程序执行失败: {{e}}")
        import traceback
        traceback.print_exc()
    
    # 等待用户输入（避免窗口立即关闭）
    input("\\n按回车键退出...")

if __name__ == "__main__":
    main()
'''
    
    def _indent_code(self, code: str, spaces: int) -> str:
        """为代码添加缩进"""
        indent = " " * spaces
        lines = code.split('\n')
        indented_lines = [indent + line if line.strip() else line for line in lines]
        return '\n'.join(indented_lines)
    
    def _generate_requirements(self) -> str:
        """生成依赖列表"""
        return '''# YOLO目标检测独立脚本依赖包
# 安装命令: pip install -r requirements.txt

# 核心依赖
opencv-python>=4.5.0
numpy>=1.19.0
Pillow>=8.0.0

# GUI相关（如果需要）
PyQt6>=6.0.0

# 机器学习相关
ultralytics>=8.0.0

# 工具库
pathlib2>=2.3.0
python-dateutil>=2.8.0

# 日志和配置
colorlog>=6.0.0

# 可选依赖（用于增强功能）
matplotlib>=3.3.0
scipy>=1.7.0
scikit-image>=0.18.0

# 注意事项:
# 1. 如果遇到安装问题，可以尝试使用国内镜像源:
#    pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
# 2. 某些包可能需要特定版本，请根据实际情况调整
# 3. 如果不需要GUI功能，可以注释掉PyQt6相关依赖
'''
    
    def _generate_run_script(self, export_dir_name: str) -> str:
        """生成Windows运行脚本"""
        # 获取当前Python环境路径
        python_exe = sys.executable
        
        return f'''@echo off
chcp 65001 >nul
title {export_dir_name} - YOLO Detection Script

echo ========================================
echo YOLO Detection Standalone Script
echo ========================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.7+ and try again
    pause
    exit /b 1
)

REM Check if virtual environment should be used
if exist "venv\\Scripts\\activate.bat" (
    echo Activating virtual environment...
    call venv\\Scripts\\activate.bat
)

REM Check if requirements are installed
echo Checking dependencies...
python -c "import cv2, numpy, PIL" >nul 2>&1
if errorlevel 1 (
    echo Installing required packages...
    python -m pip install -r requirements.txt
    if errorlevel 1 (
        echo Error: Failed to install dependencies
        echo Please check your internet connection and try again
        pause
        exit /b 1
    )
)

echo.
echo Starting detection script...
echo.

REM Run the main script
python main.py

REM Keep window open
echo.
echo Script execution completed.
pause
'''
    
    def _generate_config_file(self, template_name: str, description: str) -> str:
        """生成配置文件"""
        config = {
            "template_name": template_name,
            "description": description,
            "version": "1.0.0",
            "created_time": datetime.now().isoformat(),
            "detection_settings": {
                "confidence_threshold": 0.5,
                "nms_threshold": 0.4,
                "input_size": [640, 640],
                "max_detections": 100
            },
            "display_settings": {
                "show_confidence": True,
                "show_labels": True,
                "font_size": 16,
                "line_thickness": 2,
                "colors": {
                    "detection_box": [0, 255, 0],
                    "text_color": [255, 255, 255],
                    "background_color": [0, 0, 0]
                }
            },
            "output_settings": {
                "save_results": True,
                "output_format": "png",
                "create_log": True,
                "log_level": "INFO"
            }
        }
        
        return json.dumps(config, ensure_ascii=False, indent=2)
    
    def _copy_utility_modules(self, export_dir: Path):
        """复制必要的工具模块"""
        try:
            utils_dir = export_dir / "utils"
            utils_dir.mkdir(exist_ok=True)
            
            # 复制中文渲染器
            source_renderer = Path(__file__).parent / "chinese_text_renderer.py"
            if source_renderer.exists():
                target_renderer = utils_dir / "chinese_text_renderer.py"
                shutil.copy2(source_renderer, target_renderer)
                self.logger.info("✅ 复制中文渲染器模块")
            
            # 创建检测工具模块
            detection_utils_content = self._generate_detection_utils()
            detection_utils_path = utils_dir / "detection_utils.py"
            with open(detection_utils_path, 'w', encoding='utf-8') as f:
                f.write(detection_utils_content)
            
            # 创建__init__.py文件
            init_content = '''"""
工具模块包
包含检测和渲染相关的工具函数
"""

__version__ = "1.0.0"
'''
            init_path = utils_dir / "__init__.py"
            with open(init_path, 'w', encoding='utf-8') as f:
                f.write(init_content)
            
            self.logger.info("✅ 工具模块复制完成")
            
        except Exception as e:
            self.logger.warning(f"复制工具模块失败: {e}")
    
    def _generate_detection_utils(self) -> str:
        """生成检测工具模块"""
        return '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检测工具模块
提供检测结果处理和边界框操作的工具函数
"""

from typing import List, Tuple, Optional, Dict, Any
from dataclasses import dataclass
import numpy as np

@dataclass
class BoundingBox:
    """边界框数据类"""
    x: int
    y: int
    width: int
    height: int
    confidence: float = 0.0
    class_id: int = -1
    class_name: str = ""
    
    @property
    def x1(self) -> int:
        return self.x
    
    @property
    def y1(self) -> int:
        return self.y
    
    @property
    def x2(self) -> int:
        return self.x + self.width
    
    @property
    def y2(self) -> int:
        return self.y + self.height
    
    @property
    def center(self) -> Tuple[int, int]:
        return (self.x + self.width // 2, self.y + self.height // 2)
    
    @property
    def area(self) -> int:
        return self.width * self.height

@dataclass
class DetectionResult:
    """检测结果数据类"""
    bbox: BoundingBox
    confidence: float
    class_id: int
    class_name: str
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "bbox": {
                "x": self.bbox.x,
                "y": self.bbox.y,
                "width": self.bbox.width,
                "height": self.bbox.height
            },
            "confidence": self.confidence,
            "class_id": self.class_id,
            "class_name": self.class_name
        }

def calculate_iou(box1: BoundingBox, box2: BoundingBox) -> float:
    """计算两个边界框的IoU"""
    # 计算交集区域
    x1 = max(box1.x1, box2.x1)
    y1 = max(box1.y1, box2.y1)
    x2 = min(box1.x2, box2.x2)
    y2 = min(box1.y2, box2.y2)
    
    if x2 <= x1 or y2 <= y1:
        return 0.0
    
    intersection = (x2 - x1) * (y2 - y1)
    union = box1.area + box2.area - intersection
    
    return intersection / union if union > 0 else 0.0

def filter_detections_by_confidence(detections: List[DetectionResult], 
                                  threshold: float = 0.5) -> List[DetectionResult]:
    """根据置信度过滤检测结果"""
    return [det for det in detections if det.confidence >= threshold]

def sort_detections_by_confidence(detections: List[DetectionResult],
                                reverse: bool = True) -> List[DetectionResult]:
    """根据置信度排序检测结果"""
    return sorted(detections, key=lambda x: x.confidence, reverse=reverse)
'''

    def _generate_readme(self, template_name: str, description: str, export_dir_name: str) -> str:
        """生成README文档"""
        return f'''# {template_name}

{description}

## 📋 项目信息

- **项目名称**: {template_name}
- **生成时间**: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
- **导出目录**: {export_dir_name}
- **Python版本**: 3.7+

## 🚀 快速开始

### 方法1：使用批处理文件（推荐）
1. 双击 `run.bat` 文件
2. 脚本会自动检查并安装依赖
3. 按照提示进行操作

### 方法2：手动运行
1. 安装依赖包：
   ```bash
   pip install -r requirements.txt
   ```

2. 运行主脚本：
   ```bash
   python main.py
   ```

## 📁 文件结构

```
{export_dir_name}/
├── main.py                 # 主要检测代码
├── requirements.txt        # 依赖列表
├── run.bat                # Windows运行脚本
├── README.md              # 使用说明（本文件）
├── config/                # 配置文件目录
│   └── settings.json      # 检测参数配置
└── utils/                 # 工具模块
    ├── __init__.py
    ├── chinese_text_renderer.py  # 中文字符渲染
    └── detection_utils.py        # 检测工具函数
```

## ⚙️ 配置说明

### 检测参数配置 (config/settings.json)

```json
{{
  "detection_settings": {{
    "confidence_threshold": 0.5,    // 置信度阈值
    "nms_threshold": 0.4,          // NMS阈值
    "input_size": [640, 640],      // 输入图像尺寸
    "max_detections": 100          // 最大检测数量
  }},
  "display_settings": {{
    "show_confidence": true,       // 显示置信度
    "show_labels": true,          // 显示标签
    "font_size": 16,              // 字体大小
    "line_thickness": 2           // 线条粗细
  }}
}}
```

## 🔧 依赖包说明

### 核心依赖
- **opencv-python**: 图像处理和计算机视觉
- **numpy**: 数值计算
- **Pillow**: 图像处理
- **ultralytics**: YOLO模型支持

### 可选依赖
- **PyQt6**: GUI界面（如果需要）
- **matplotlib**: 图像显示和绘图
- **scipy**: 科学计算

## 🎯 功能特性

### ✅ 已实现功能
- 🔍 YOLO目标检测
- 🖼️ 图像处理和显示
- 📝 中文字符渲染支持
- 📊 检测结果可视化
- 📋 详细日志记录
- ⚙️ 配置文件支持

### 🔄 自定义配置
1. **修改检测参数**: 编辑 `config/settings.json`
2. **调整显示效果**: 修改 `display_settings` 部分
3. **更改输出设置**: 配置 `output_settings` 部分

## 🐛 故障排除

### 常见问题

#### 1. 依赖安装失败
**问题**: `pip install` 命令执行失败
**解决方案**:
```bash
# 使用国内镜像源
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 或者升级pip
python -m pip install --upgrade pip
```

#### 2. OpenCV导入错误
**问题**: `ImportError: No module named 'cv2'`
**解决方案**:
```bash
pip uninstall opencv-python
pip install opencv-python
```

#### 3. 中文字符显示问题
**问题**: 中文字符显示为问号
**解决方案**:
- 确保系统已安装中文字体
- Windows系统通常自带，Linux可能需要安装：
  ```bash
  sudo apt-get install fonts-wqy-microhei
  ```

#### 4. 权限错误
**问题**: 文件读写权限不足
**解决方案**:
- 确保脚本有读写当前目录的权限
- Windows: 右键以管理员身份运行
- Linux/Mac: 使用 `chmod +x` 添加执行权限

#### 5. Python版本不兼容
**问题**: 语法错误或模块不兼容
**解决方案**:
- 确保使用Python 3.7或更高版本
- 检查Python版本：`python --version`

### 性能优化建议

1. **GPU加速**: 如果有NVIDIA GPU，安装CUDA版本的依赖
2. **内存优化**: 处理大图像时适当调整输入尺寸
3. **批处理**: 处理多个图像时使用批处理模式

## 📞 技术支持

如果遇到问题，请检查：
1. Python版本是否为3.7+
2. 所有依赖包是否正确安装
3. 配置文件格式是否正确
4. 输入数据是否符合要求

## 📄 许可证

本脚本基于原YOLO项目生成，请遵循相应的开源许可证。

## 🔄 更新日志

- **v1.0.0** ({datetime.now().strftime("%Y-%m-%d")}): 初始版本
  - 基础检测功能
  - 中文字符支持
  - 配置文件支持
  - 完整文档

---

*自动生成于 {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}*
'''
