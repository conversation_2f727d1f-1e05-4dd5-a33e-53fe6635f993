#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试实时检测场景下的错误修复
模拟实际的实时检测流程，验证AttributeError修复的有效性
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

from yolo_opencv_detector.utils.data_structures import DetectionResult, BoundingBox, DetectionSource
from yolo_opencv_detector.gui.widgets.screenshot_widget import ScreenshotLabel
# 注释掉不需要的导入，只保留必要的测试组件
from PyQt6.QtWidgets import QApplication
from PyQt6.QtGui import QPixmap
import numpy as np
import time

def simulate_yolo_detector_v1_output():
    """模拟YOLODetector (v1) 的输出 - 返回DetectionResult对象列表"""
    bbox1 = BoundingBox(x=100, y=100, width=200, height=150)
    bbox2 = BoundingBox(x=300, y=200, width=180, height=120)
    
    return [
        DetectionResult(
            bbox=bbox1,
            confidence=0.85,
            class_id=0,
            class_name="person",
            source=DetectionSource.YOLO
        ),
        DetectionResult(
            bbox=bbox2,
            confidence=0.92,
            class_id=1,
            class_name="car",
            source=DetectionSource.YOLO
        )
    ]

def simulate_yolo_detector_v2_output():
    """模拟YOLODetectorV2 的输出 - 返回字典列表"""
    return [
        {
            'source': 'yolo',
            'bbox': [150, 120, 180, 140],  # [x, y, w, h]
            'confidence': 0.78,
            'class_id': 2,
            'class_name': 'bicycle'
        },
        {
            'source': 'yolo',
            'bbox': [400, 300, 160, 100],  # [x, y, w, h]
            'confidence': 0.89,
            'class_id': 3,
            'class_name': 'motorcycle'
        }
    ]

def simulate_smart_detection_manager_output():
    """模拟智能检测管理器的输出 - 混合格式"""
    # 模拟智能检测管理器可能返回的混合数据
    bbox = BoundingBox(x=250, y=180, width=220, height=160)
    detection_result = DetectionResult(
        bbox=bbox,
        confidence=0.95,
        class_id=0,
        class_name="person",
        source=DetectionSource.TEMPLATE
    )
    
    dict_result = {
        'source': 'template',
        'bbox': {'x': 500, 'y': 400, 'width': 140, 'height': 90},
        'confidence': 0.82,
        'class_id': 4,
        'class_name': 'bus'
    }
    
    return [detection_result, dict_result]

def test_realtime_detection_scenario():
    """测试实时检测场景"""
    print("开始测试实时检测场景...")
    
    app = QApplication(sys.argv)
    
    try:
        # 创建截图标签组件
        screenshot_label = ScreenshotLabel()
        
        # 创建测试图像
        test_pixmap = QPixmap(1920, 1080)
        test_pixmap.fill()
        screenshot_label.set_screenshot(test_pixmap)
        
        print("\n模拟实时检测循环...")
        
        # 模拟实时检测循环 - 每秒更新一次检测结果
        for cycle in range(5):
            print(f"\n--- 检测循环 {cycle + 1} ---")
            
            if cycle % 3 == 0:
                # 模拟YOLODetector (v1) 输出
                print("模拟YOLODetector (v1) 输出 - DetectionResult对象")
                detections = simulate_yolo_detector_v1_output()
            elif cycle % 3 == 1:
                # 模拟YOLODetectorV2 输出
                print("模拟YOLODetectorV2 输出 - 字典列表")
                detections = simulate_yolo_detector_v2_output()
            else:
                # 模拟智能检测管理器输出
                print("模拟智能检测管理器输出 - 混合格式")
                detections = simulate_smart_detection_manager_output()
            
            try:
                # 这里是关键测试点 - 之前会出现AttributeError的地方
                screenshot_label.set_detection_results(detections)
                print(f"✅ 检测循环 {cycle + 1} 成功处理 {len(detections)} 个检测结果")
                
                # 模拟实时更新的延迟
                time.sleep(0.1)
                
            except AttributeError as e:
                if "'list' object has no attribute 'get'" in str(e):
                    print(f"❌ 检测循环 {cycle + 1} 出现原始错误: {e}")
                    return False
                else:
                    print(f"❌ 检测循环 {cycle + 1} 出现其他AttributeError: {e}")
                    return False
            except Exception as e:
                print(f"❌ 检测循环 {cycle + 1} 出现意外错误: {e}")
                return False
        
        print("\n✅ 所有实时检测循环都成功完成，没有出现AttributeError!")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        app.quit()

def test_edge_cases():
    """测试边缘情况"""
    print("\n开始测试边缘情况...")
    
    app = QApplication(sys.argv)
    
    try:
        screenshot_label = ScreenshotLabel()
        test_pixmap = QPixmap(800, 600)
        test_pixmap.fill()
        screenshot_label.set_screenshot(test_pixmap)
        
        # 测试空列表
        print("测试空检测结果列表...")
        screenshot_label.set_detection_results([])
        print("✅ 空列表处理成功")
        
        # 测试None值
        print("测试包含None的列表...")
        screenshot_label.set_detection_results([None, None])
        print("✅ None值处理成功")
        
        # 测试混合无效数据
        print("测试混合无效数据...")
        invalid_data = [
            "invalid_string",
            123,
            [],
            {},
            None,
            simulate_yolo_detector_v1_output()[0]  # 一个有效的DetectionResult
        ]
        screenshot_label.set_detection_results(invalid_data)
        print("✅ 混合无效数据处理成功")
        
        # 测试错误的bbox格式
        print("测试错误的bbox格式...")
        bad_bbox_data = [
            {"bbox": "invalid", "confidence": 0.5, "class_name": "test"},
            {"bbox": [], "confidence": 0.5, "class_name": "test"},
            {"bbox": [1, 2], "confidence": 0.5, "class_name": "test"},  # 不足4个元素
            {"confidence": 0.5, "class_name": "test"}  # 缺少bbox
        ]
        screenshot_label.set_detection_results(bad_bbox_data)
        print("✅ 错误bbox格式处理成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 边缘情况测试失败: {e}")
        return False
    
    finally:
        app.quit()

if __name__ == "__main__":
    print("=" * 70)
    print("YOLO检测工具实时检测AttributeError错误修复验证")
    print("=" * 70)
    
    success = True
    
    # 测试实时检测场景
    if not test_realtime_detection_scenario():
        success = False
    
    # 测试边缘情况
    if not test_edge_cases():
        success = False
    
    print("\n" + "=" * 70)
    if success:
        print("🎉 所有测试通过！AttributeError错误已成功修复！")
        print("✅ 实时检测功能现在可以稳定运行，不会出现重复错误")
    else:
        print("❌ 部分测试失败，需要进一步检查修复")
    print("=" * 70)
