#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
应用图标测试和展示
测试生成的图标是否正常工作，并展示图标效果
"""

import sys
import os
from pathlib import Path

def setup_project_path():
    """设置项目路径"""
    project_root = Path(__file__).parent
    src_path = project_root / "src"
    if str(src_path) not in sys.path:
        sys.path.insert(0, str(src_path))

def test_icon_files():
    """测试图标文件"""
    print("🔍 测试图标文件...")
    
    icons_dir = Path("icons")
    if not icons_dir.exists():
        print("❌ 图标目录不存在")
        return False
    
    # 检查必要的图标文件
    required_files = [
        "yolo_detector_app.ico",
        "yolo_detector_taskbar.ico",
        "app_icon_256.png",
        "app_icon_128.png",
        "app_icon_64.png",
        "app_icon_32.png",
        "taskbar_icon_32.png",
        "taskbar_icon_24.png",
        "taskbar_icon_16.png"
    ]
    
    missing_files = []
    existing_files = []
    
    for file_name in required_files:
        file_path = icons_dir / file_name
        if file_path.exists():
            existing_files.append(file_name)
            # 检查文件大小
            file_size = file_path.stat().st_size
            print(f"  ✅ {file_name} ({file_size} bytes)")
        else:
            missing_files.append(file_name)
            print(f"  ❌ {file_name} - 文件不存在")
    
    if missing_files:
        print(f"\n⚠️ 缺少 {len(missing_files)} 个图标文件")
        return False
    else:
        print(f"\n✅ 所有 {len(existing_files)} 个图标文件都存在")
        return True

def test_icon_integration():
    """测试图标集成"""
    print("\n🔧 测试图标集成...")
    
    setup_project_path()
    
    # 测试主窗口图标集成
    main_window_file = Path("src/yolo_opencv_detector/gui/main_window_v2.py")
    if main_window_file.exists():
        with open(main_window_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if "setup_application_icon" in content and "setWindowIcon" in content:
            print("  ✅ 主窗口图标集成正常")
        else:
            print("  ❌ 主窗口图标集成缺失")
            return False
    else:
        print("  ❌ 主窗口文件不存在")
        return False
    
    # 测试源代码对话框图标集成
    dialog_file = Path("src/yolo_opencv_detector/gui/dialogs/source_code_dialog.py")
    if dialog_file.exists():
        with open(dialog_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if "setup_dialog_icon" in content:
            print("  ✅ 源代码对话框图标集成正常")
        else:
            print("  ❌ 源代码对话框图标集成缺失")
            return False
    else:
        print("  ❌ 源代码对话框文件不存在")
        return False
    
    print("  ✅ 图标集成测试通过")
    return True

def create_icon_preview():
    """创建图标预览"""
    print("\n🖼️ 创建图标预览...")
    
    try:
        from PIL import Image, ImageDraw, ImageFont
        
        # 创建预览画布
        preview_width = 800
        preview_height = 600
        preview = Image.new('RGB', (preview_width, preview_height), (240, 240, 240))
        draw = ImageDraw.Draw(preview)
        
        # 标题
        try:
            title_font = ImageFont.truetype("arial.ttf", 24)
            subtitle_font = ImageFont.truetype("arial.ttf", 16)
        except:
            title_font = ImageFont.load_default()
            subtitle_font = ImageFont.load_default()
        
        # 绘制标题
        title = "YOLO OpenCV检测器 - 应用图标预览"
        title_bbox = draw.textbbox((0, 0), title, font=title_font)
        title_width = title_bbox[2] - title_bbox[0]
        draw.text(((preview_width - title_width) // 2, 20), title, fill=(50, 50, 50), font=title_font)
        
        # 加载和显示图标
        icons_to_show = [
            ("app_icon_256.png", "主应用图标 (256x256)", 100, 80),
            ("app_icon_128.png", "应用图标 (128x128)", 400, 80),
            ("app_icon_64.png", "应用图标 (64x64)", 600, 80),
            ("taskbar_icon_32.png", "任务栏图标 (32x32)", 100, 300),
            ("taskbar_icon_24.png", "任务栏图标 (24x24)", 250, 300),
            ("taskbar_icon_16.png", "任务栏图标 (16x16)", 400, 300),
            ("simple_icon_48.png", "简化图标 (48x48)", 550, 300),
        ]
        
        icons_dir = Path("icons")
        
        for icon_file, description, x, y in icons_to_show:
            icon_path = icons_dir / icon_file
            if icon_path.exists():
                try:
                    # 加载图标
                    icon = Image.open(icon_path)
                    
                    # 如果图标有透明度，创建白色背景
                    if icon.mode == 'RGBA':
                        background = Image.new('RGB', icon.size, (255, 255, 255))
                        background.paste(icon, mask=icon.split()[-1])
                        icon = background
                    
                    # 粘贴图标
                    preview.paste(icon, (x, y))
                    
                    # 添加描述文字
                    desc_y = y + icon.height + 10
                    draw.text((x, desc_y), description, fill=(80, 80, 80), font=subtitle_font)
                    
                except Exception as e:
                    print(f"  ⚠️ 无法加载图标 {icon_file}: {e}")
        
        # 保存预览
        preview_path = icons_dir / "图标预览.png"
        preview.save(preview_path)
        print(f"  ✅ 图标预览已保存: {preview_path}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 创建图标预览失败: {e}")
        return False

def show_icon_summary():
    """显示图标总结信息"""
    print("\n📊 图标总结信息")
    print("=" * 50)
    
    # 图标设计信息
    print("🎨 设计特色:")
    print("  - 现代扁平化设计风格")
    print("  - YOLO检测框 + 自动化光标元素")
    print("  - 蓝色科技感 + 橙色活力配色")
    print("  - 多尺寸优化，小图标依然清晰")
    
    print("\n📁 文件结构:")
    print("  - yolo_detector_app.ico (主应用图标)")
    print("  - yolo_detector_taskbar.ico (任务栏图标)")
    print("  - 9个PNG文件 (不同尺寸和用途)")
    
    print("\n💻 集成状态:")
    print("  - ✅ 主窗口图标集成")
    print("  - ✅ 源代码对话框图标集成")
    print("  - ✅ 任务栏图标优化")
    
    print("\n🚀 使用效果:")
    print("  - 窗口标题栏显示应用图标")
    print("  - 任务栏显示优化的小图标")
    print("  - Alt+Tab切换时显示图标")
    print("  - 系统托盘支持（如果启用）")

def main():
    """主函数"""
    print("🎨 YOLO OpenCV检测器图标测试")
    print("=" * 60)
    print("测试和展示应用程序图标")
    print("=" * 60)
    
    success_count = 0
    total_tests = 4
    
    # 1. 测试图标文件
    if test_icon_files():
        success_count += 1
    
    # 2. 测试图标集成
    if test_icon_integration():
        success_count += 1
    
    # 3. 创建图标预览
    if create_icon_preview():
        success_count += 1
    
    # 4. 显示总结信息
    show_icon_summary()
    success_count += 1
    
    print("\n" + "=" * 60)
    print("📋 测试结果总结:")
    print("=" * 60)
    print(f"✅ 通过测试: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 图标系统完全正常！")
        print("\n💡 下一步:")
        print("  1. 重新启动应用程序查看图标效果")
        print("  2. 检查窗口标题栏和任务栏图标")
        print("  3. 查看 icons/图标预览.png 了解图标外观")
        print("  4. 阅读 icons/图标使用指南.md 了解详细信息")
        return 0
    else:
        print("⚠️ 部分测试失败，请检查错误信息")
        return 1

if __name__ == "__main__":
    sys.exit(main())
