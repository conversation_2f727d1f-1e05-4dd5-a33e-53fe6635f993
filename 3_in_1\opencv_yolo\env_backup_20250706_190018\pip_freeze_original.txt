aiobotocore @ file:///C:/b/abs_3cwz1w13nn/croot/aiobotocore_1701291550158/work
aiofiles==24.1.0
aiohappyeyeballs==2.4.3
aiohttp==3.9.1
aioitertools @ file:///tmp/build/80754af9/aioitertools_1607109665762/work
aiosignal @ file:///tmp/build/80754af9/aiosignal_1637843061372/work
alabaster @ file:///home/<USER>/src/ci/alabaster_1611921544520/work
alembic==1.13.2
altair @ file:///C:/b/abs_27reu1igbg/croot/altair_1687526066495/work
altgraph==0.17.4
anaconda-anon-usage @ file:///C:/b/abs_95v3x0wy8p/croot/anaconda-anon-usage_1697038984188/work
anaconda-catalogs @ file:///C:/b/abs_8btyy0o8s8/croot/anaconda-catalogs_1685727315626/work
anaconda-client @ file:///C:/b/abs_34txutm0ue/croot/anaconda-client_1708640705294/work
anaconda-cloud-auth @ file:///C:/b/abs_410afndtyf/croot/anaconda-cloud-auth_1697462767853/work
anaconda-navigator @ file:///C:/b/abs_cfvv8k_j21/croot/anaconda-navigator_1704813334508/work
anaconda-project @ file:///C:/ci_311/anaconda-project_1676458365912/work
anndata==0.11.3
annotated-types==0.7.0
anthropic==0.34.2
anyio==4.9.0
appdirs==1.4.4
APScheduler==3.10.4
archspec @ file:///croot/archspec_1697725767277/work
argon2-cffi==23.1.0
argon2-cffi-bindings @ file:///C:/ci_311/argon2-cffi-bindings_1676424443321/work
array_api_compat==1.10.0
arrow @ file:///C:/ci_311/arrow_1678249767083/work
asgiref==3.8.1
astor==0.8.1
astroid @ file:///C:/ci_311/astroid_1678740610167/work
astropy @ file:///C:/b/abs_2fb3x_tapx/croot/astropy_1697468987983/work
asttokens @ file:///opt/conda/conda-bld/asttokens_1646925590279/work
async-lru @ file:///C:/b/abs_e0hjkvwwb5/croot/async-lru_1699554572212/work
atomicwrites==1.4.0
attrs @ file:///C:/b/abs_35n0jusce8/croot/attrs_1695717880170/work
Authlib==1.3.2
auto-py-to-exe==2.44.1
Automat @ file:///tmp/build/80754af9/automat_1600298431173/work
autopep8 @ file:///opt/conda/conda-bld/autopep8_1650463822033/work
av==12.3.0
Babel @ file:///C:/ci_311/babel_1676427169844/work
backoff==2.2.1
backports.functools-lru-cache @ file:///tmp/build/80754af9/backports.functools_lru_cache_1618170165463/work
backports.tarfile==1.2.0
backports.tempfile @ file:///home/<USER>/recipes/ci/backports.tempfile_1610991236607/work
backports.weakref==1.0.post1
bcrypt==4.2.0
beautifulsoup4 @ file:///C:/b/abs_0agyz1wsr4/croot/beautifulsoup4-split_1681493048687/work
bidict==0.23.1
binaryornot @ file:///tmp/build/80754af9/binaryornot_1617751525010/work
bitarray==2.9.2
black==24.8.0
bleach @ file:///opt/conda/conda-bld/bleach_1641577558959/work
blinker @ file:///C:/b/abs_d9y2dm7cw2/croot/blinker_1696539752170/work
bokeh @ file:///C:/b/abs_74ungdyhwc/croot/bokeh_1706912192007/work
boltons @ file:///C:/ci_311/boltons_1677729932371/work
boto3==1.35.0
botocore==1.35.0
bottle==0.12.25
bottle-websocket==0.2.9
Bottleneck @ file:///C:/b/abs_f05kqh7yvj/croot/bottleneck_1707864273291/work
Brotli @ file:///C:/ci_311/brotli-split_1676435766766/work
build==1.2.2
cachetools @ file:///tmp/build/80754af9/cachetools_1619597386817/work
cairocffi==1.7.1
CairoSVG==2.7.1
certifi==2025.4.26
cffi==1.17.1
chardet @ file:///C:/ci_311/chardet_1676436134885/work
charset-normalizer==3.4.2
chroma-hnswlib==0.7.6
chromadb==0.5.5
click==8.1.8
clicknium==0.2.4
cloudpickle @ file:///C:/b/abs_3796yxesic/croot/cloudpickle_1683040098851/work
clr_loader==0.2.7.post0
clyent==1.2.1
colbert-ai==0.2.21
colorama==0.4.6
colorcet @ file:///C:/ci_311/colorcet_1676440389947/work
colorclass==2.2.2
coloredlogs==15.0.1
colorlog==6.9.0
comfyui_frontend_package==1.20.7
comm @ file:///C:/ci_311/comm_1678376562840/work
compressed-rtf==1.0.6
comtypes==1.4.10
conda @ file:///C:/b/abs_89vd8hj61u/croot/conda_1708369170790/work
conda-build @ file:///C:/b/abs_3ed9gavxgz/croot/conda-build_1708025907525/work
conda-content-trust @ file:///C:/b/abs_e3bcpyv7sw/croot/conda-content-trust_1693490654398/work
conda-libmamba-solver @ file:///croot/conda-libmamba-solver_1706733287605/work/src
conda-pack @ file:///tmp/build/80754af9/conda-pack_1611163042455/work
conda-package-handling @ file:///C:/b/abs_b9wp3lr1gn/croot/conda-package-handling_1691008700066/work
conda-repo-cli==1.0.75
conda-token @ file:///Users/<USER>/miniconda3/envs/c3i/conda-bld/conda-token_1662660369760/work
conda-verify==3.4.2
conda_index @ file:///croot/conda-index_1706633791028/work
conda_package_streaming @ file:///C:/b/abs_6c28n38aaj/croot/conda-package-streaming_1690988019210/work
constantly @ file:///C:/b/abs_cbuavw4443/croot/constantly_1703165617403/work
contourpy @ file:///C:/b/abs_853rfy8zse/croot/contourpy_1700583617587/work
controlnet_aux==0.0.7
cookiecutter @ file:///C:/b/abs_3d1730toam/croot/cookiecutter_1700677089156/work
cryptography @ file:///C:/b/abs_531eqmhgsd/croot/cryptography_1707523768330/work
cssselect @ file:///C:/b/abs_71gnjab7b0/croot/cssselect_1707339955530/work
cssselect2==0.7.0
cssutils==2.11.1
ctranslate2==4.4.0
cupy-cuda12x==13.3.0
customtkinter==5.2.2
cx_Freeze==7.2.0
cx_Logging==3.2.0
cycler @ file:///tmp/build/80754af9/cycler_1637851556182/work
cytoolz @ file:///C:/b/abs_d43s8lnb60/croot/cytoolz_1701723636699/work
darkdetect==0.8.0
dask @ file:///C:/b/abs_1899k8plyj/croot/dask-core_1701396135885/work
dataclasses-json==0.6.7
datasets==3.2.0
datashader @ file:///C:/b/abs_cb5s63ty8z/croot/datashader_1699544282143/work
debugpy @ file:///C:/b/abs_c0y1fjipt2/croot/debugpy_1690906864587/work
decorator @ file:///opt/conda/conda-bld/decorator_1643638310831/work
deepdiff==8.0.1
defusedxml @ file:///tmp/build/80754af9/defusedxml_1615228127516/work
Deprecated==1.2.14
diff-match-patch @ file:///Users/<USER>/demo/mc3/conda-bld/diff-match-patch_1630511840874/work
# Editable install with no version control (diffsynth==1.0.0)
-e c:\users\<USER>\appdata\roaming\python\python311\site-packages
dill==0.3.8
distributed @ file:///C:/b/abs_5eren88ku4/croot/distributed_1701398076011/work
distro @ file:///C:/b/abs_a3uni_yez3/croot/distro_1701455052240/work
dnspython==2.6.1
docker==7.1.0
docopt==0.6.2
docstring-to-markdown @ file:///C:/ci_311/docstring-to-markdown_1677742566583/work
docutils @ file:///C:/ci_311/docutils_1676428078664/work
docx2pdf==0.1.8
docx2txt==0.8
docxcompose==1.4.0
docxtpl==0.19.1
duckduckgo_search==6.2.13
durationpy==0.8
easygui==0.98.3
easyocr==1.7.2
ebcdic==1.1.1
ecdsa==0.19.0
Eel==0.17.0
einops==0.8.0
email_validator==2.2.0
emoji==2.13.2
entrypoints @ file:///C:/ci_311/entrypoints_1676423328987/work
environs==9.5.0
et_xmlfile==2.0.0
executing @ file:///opt/conda/conda-bld/executing_1646925071911/work
extract-msg==0.49.0
faiss-cpu==1.10.0
fake-useragent==1.5.1
fastapi==0.115.12
fastapi-cli==0.0.5
faster-whisper==1.0.3
fastjsonschema @ file:///C:/ci_311/python-fastjsonschema_1679500568724/work
fastrlock==0.8.2
ffmpeg-python==0.2.0
ffmpy==0.5.0
filelock==3.18.0
filetype==1.2.0
flake8 @ file:///C:/ci_311/flake8_1678376624746/work
Flask==3.0.3
Flask-Cors==5.0.0
flatbuffers==25.2.10
fonttools==4.54.1
forestplot==0.4.1
fpdf2==2.7.9
frozenlist @ file:///C:/b/abs_d8e__s1ys3/croot/frozenlist_1698702612014/work
fsspec==2025.5.1
fst-pso==1.8.1
ftfy==6.3.1
future @ file:///C:/ci_311_rebuilds/future_1678998246262/work
FuzzyTM==2.0.9
gallery_dl==1.29.7
gensim @ file:///C:/ci_311/gensim_1677743037820/work
gevent==24.2.1
gevent-websocket==0.10.1
git-python==1.0.3
gitdb @ file:///tmp/build/80754af9/gitdb_1617117951232/work
GitPython @ file:///C:/b/abs_e1lwow9h41/croot/gitpython_1696937027832/work
gmpy2 @ file:///C:/ci_311/gmpy2_1677743390134/work
google-ai-generativelanguage==0.6.6
google-api-core==2.20.0
google-api-python-client==2.147.0
google-auth==2.35.0
google-auth-httplib2==0.2.0
google-generativeai==0.7.2
googleapis-common-protos==1.65.0
GPUtil==1.4.0
gradio==5.31.0
gradio_client==1.10.1
gradio_rangeslider==0.0.8
greenlet @ file:///C:/b/abs_a6c75ie0bc/croot/greenlet_1702060012174/work
groovy==0.1.2
grpcio==1.66.2
grpcio-status==1.62.3
h11==0.16.0
h5py @ file:///C:/b/abs_17fav01gwy/croot/h5py_1691589733413/work
HeapDict @ file:///Users/<USER>/demo/mc3/conda-bld/heapdict_1630598515714/work
holoviews @ file:///C:/b/abs_704uucojt7/croot/holoviews_1707836477070/work
httpcore==1.0.9
httplib2==0.22.0
httptools==0.6.1
httpx==0.28.1
huggingface-hub==0.32.0
humanfriendly==10.0
hvplot @ file:///C:/b/abs_3627uzd5h0/croot/hvplot_1706712443782/work
hyperlink @ file:///tmp/build/80754af9/hyperlink_1610130746837/work
idna==3.10
igraph==0.11.8
imagecodecs @ file:///C:/b/abs_e2g5zbs1q0/croot/imagecodecs_1695065012000/work
imageio @ file:///C:/b/abs_aeqerw_nps/croot/imageio_1707247365204/work
imageio-ffmpeg==0.5.1
imagesize @ file:///C:/ci_311/imagesize_1676431905616/work
imbalanced-learn @ file:///C:/b/abs_87es3kd5fi/croot/imbalanced-learn_1700648276799/work
importlib-metadata @ file:///C:/b/abs_c1egths604/croot/importlib_metadata-suite_1704813568388/work
importlib_resources==6.4.4
incremental @ file:///croot/incremental_1708639938299/work
inflection==0.5.1
iniconfig @ file:///home/<USER>/recipes/ci/iniconfig_1610983019677/work
intake @ file:///C:/ci_311_rebuilds/intake_1678999914269/work
intel-openmp==2021.4.0
intervaltree @ file:///Users/<USER>/demo/mc3/conda-bld/intervaltree_1630511889664/work
ipykernel @ file:///C:/b/abs_c2u94kxcy6/croot/ipykernel_1705933907920/work
ipython @ file:///C:/b/abs_b6pfgmrqnd/croot/ipython_1704833422163/work
ipython-genutils @ file:///tmp/build/80754af9/ipython_genutils_1606773439826/work
ipywidgets @ file:///croot/ipywidgets_1701289330913/work
isort @ file:///tmp/build/80754af9/isort_1628603791788/work
itemadapter @ file:///tmp/build/80754af9/itemadapter_1626442940632/work
itemloaders @ file:///C:/b/abs_5e3azgv25z/croot/itemloaders_1708639993442/work
itsdangerous==2.2.0
jaraco.classes @ file:///tmp/build/80754af9/jaraco.classes_1620983179379/work
jedi @ file:///C:/ci_311/jedi_1679427407646/work
jellyfish @ file:///C:/b/abs_50kgvtnrbj/croot/jellyfish_1695193564091/work
jieba==0.42.1
Jinja2==3.1.6
jiter==0.5.0
jmespath @ file:///C:/b/abs_59jpuaows7/croot/jmespath_1700144635019/work
joblib==1.4.2
json5 @ file:///tmp/build/80754af9/json5_1624432770122/work
jsonpatch==1.33
jsonpath-python==1.0.6
jsonpointer==2.1
jsonschema @ file:///C:/b/abs_d1c4sm8drk/croot/jsonschema_1699041668863/work
jsonschema-specifications @ file:///C:/b/abs_0brvm6vryw/croot/jsonschema-specifications_1699032417323/work
jupyter @ file:///C:/b/abs_4e102rc6e5/croot/jupyter_1707947170513/work
jupyter-console @ file:///C:/b/abs_82xaa6i2y4/croot/jupyter_console_1680000189372/work
jupyter-events @ file:///C:/b/abs_17ajfqnlz0/croot/jupyter_events_1699282519713/work
jupyter-lsp @ file:///C:/b/abs_ecle3em9d4/croot/jupyter-lsp-meta_1699978291372/work
jupyter_client @ file:///C:/b/abs_a6h3c8hfdq/croot/jupyter_client_1699455939372/work
jupyter_core @ file:///C:/b/abs_c769pbqg9b/croot/jupyter_core_1698937367513/work
jupyter_server @ file:///C:/b/abs_7esjvdakg9/croot/jupyter_server_1699466495151/work
jupyter_server_terminals @ file:///C:/b/abs_ec0dq4b50j/croot/jupyter_server_terminals_1686870763512/work
jupyterlab @ file:///C:/b/abs_43venm28fu/croot/jupyterlab_1706802651134/work
jupyterlab-pygments @ file:///tmp/build/80754af9/jupyterlab_pygments_1601490720602/work
jupyterlab-widgets @ file:///C:/b/abs_adrrqr26no/croot/jupyterlab_widgets_1700169018974/work
jupyterlab_server @ file:///C:/b/abs_e08i7qn9m8/croot/jupyterlab_server_1699555481806/work
keyboard==0.13.5
keyring @ file:///C:/b/abs_dbjc7g0dh2/croot/keyring_1678999228878/work
kiwisolver @ file:///C:/ci_311/kiwisolver_1676431979301/work
kubernetes==31.0.0
langchain==0.2.15
langchain-chroma==0.1.2
langchain-community==0.2.12
langchain-core==0.2.41
langchain-openai==0.1.25
langchain-text-splitters==0.2.4
langdetect==1.0.9
langfuse==2.44.0
langsmith==0.1.129
lark==1.1.9
lazy-object-proxy @ file:///C:/ci_311/lazy-object-proxy_1676432050939/work
lazy_loader @ file:///C:/b/abs_3bn4_r4g42/croot/lazy_loader_1695850158046/work
lckr_jupyterlab_variableinspector @ file:///C:/b/abs_b5yb2mprx2/croot/jupyterlab-variableinspector_1701096592545/work
legacy-api-wrap==1.4.1
leidenalg==0.10.2
Levenshtein==0.26.1
libarchive-c @ file:///tmp/build/80754af9/python-libarchive-c_1617780486945/work
libmambapy @ file:///C:/b/abs_2euls_1a38/croot/mamba-split_1704219444888/work/libmambapy
lief==0.14.1
linkify-it-py @ file:///C:/ci_311/linkify-it-py_1676474436187/work
llvmlite @ file:///C:/b/abs_da15r8vkf8/croot/llvmlite_1706910779994/work
lmdb @ file:///C:/b/abs_556ronuvb2/croot/python-lmdb_1682522366268/work
locket @ file:///C:/ci_311/locket_1676428325082/work
loguru==0.7.3
lxml @ file:///C:/b/abs_9e7tpg2vv9/croot/lxml_1695058219431/work
lz4 @ file:///C:/b/abs_064u6aszy3/croot/lz4_1686057967376/work
Mako==1.3.5
Markdown==3.7
markdown-it-py==3.0.0
MarkupSafe==3.0.2
marshmallow==3.22.0
matplotlib==3.10.0
matplotlib-inline==0.1.3
mccabe @ file:///opt/conda/conda-bld/mccabe_1644221741721/work
mdit-py-plugins @ file:///C:/ci_311/mdit-py-plugins_1676481827414/work
mdurl==0.1.2
# Editable install with no version control (medical-record-automation==0.1.0)
-e c:\users\<USER>\documents\1_1
menuinst @ file:///C:/b/abs_099kybla52/croot/menuinst_1706732987063/work
miniful==0.0.6
mistune @ file:///C:/ci_311/mistune_1676425111783/work
mkl==2021.4.0
mkl-fft @ file:///C:/b/abs_19i1y8ykas/croot/mkl_fft_1695058226480/work
mkl-random @ file:///C:/b/abs_edwkj1_o69/croot/mkl_random_1695059866750/work
mkl-service==2.4.0
mmh3==5.0.1
modelscope==1.19.0
monotonic==1.6
more-itertools @ file:///C:/b/abs_36p38zj5jx/croot/more-itertools_1700662194485/work
mouse==0.7.1
MouseInfo==0.1.3
mpmath==1.3.0
msgpack @ file:///C:/ci_311/msgpack-python_1676427482892/work
msoffcrypto-tool==5.4.2
mss==10.0.0
multidict @ file:///C:/b/abs_44ido987fv/croot/multidict_1701097803486/work
multipledispatch @ file:///C:/ci_311/multipledispatch_1676442767760/work
multiprocess==0.70.16
munkres==1.1.4
mypy @ file:///C:/b/abs_3880czibje/croot/mypy-split_1708366584048/work
mypy-extensions @ file:///C:/b/abs_8f7xiidjya/croot/mypy_extensions_1695131051147/work
mysql-connector-python==9.3.0
natsort==8.4.0
navigator-updater @ file:///C:/b/abs_895otdwmo9/croot/navigator-updater_1695210220239/work
nbclient @ file:///C:/b/abs_cal0q5fyju/croot/nbclient_1698934263135/work
nbconvert @ file:///C:/b/abs_17p29f_rx4/croot/nbconvert_1699022793097/work
nbformat @ file:///C:/b/abs_5a2nea1iu2/croot/nbformat_1694616866197/work
nest-asyncio @ file:///C:/b/abs_65d6lblmoi/croot/nest-asyncio_1708532721305/work
networkx @ file:///C:/b/abs_e6gi1go5op/croot/networkx_1690562046966/work
ninja==********
nltk==3.9.1
notebook @ file:///C:/b/abs_65xjlnf9q4/croot/notebook_1708029957105/work
notebook_shim @ file:///C:/b/abs_a5xysln3lb/croot/notebook-shim_1699455926920/work
Nuitka==2.4.5
numba @ file:///C:/b/abs_3e3co1qfvo/croot/numba_1707085143481/work
numexpr @ file:///C:/b/abs_5fucrty5dc/croot/numexpr_1696515448831/work
numpy==1.26.4
numpydoc @ file:///C:/ci_311/numpydoc_1676453412027/work
oauthlib==3.2.2
olefile==0.47
oletools==0.60.2
onnx==1.17.0
onnxruntime==1.20.1
open-webui==0.3.30
openai==1.90.0
opencv-contrib-python==4.9.0.80
opencv-python==4.11.0.86
openpyxl==3.1.5
opentelemetry-api==1.27.0
opentelemetry-exporter-otlp-proto-common==1.27.0
opentelemetry-exporter-otlp-proto-grpc==1.27.0
opentelemetry-instrumentation==0.48b0
opentelemetry-instrumentation-asgi==0.48b0
opentelemetry-instrumentation-fastapi==0.48b0
opentelemetry-proto==1.27.0
opentelemetry-sdk==1.27.0
opentelemetry-semantic-conventions==0.48b0
opentelemetry-util-http==0.48b0
opt-einsum==3.3.0
ordered-set==4.1.0
orderly-set==5.2.2
orjson==3.10.18
overrides @ file:///C:/b/abs_cfh89c8yf4/croot/overrides_1699371165349/work
packaging==24.2
paddleocr==3.0.2
paddlepaddle==3.0.0
paddlex==3.0.2
pandas==2.2.3
pandocfilters @ file:///opt/conda/conda-bld/pandocfilters_1643405455980/work
panel @ file:///C:/b/abs_abnm_ot327/croot/panel_1706539613212/work
param @ file:///C:/b/abs_39ncjvb7lu/croot/param_1705937833389/work
paramiko @ file:///opt/conda/conda-bld/paramiko_1640109032755/work
parsel @ file:///C:/b/abs_ebc3tzm_c4/croot/parsel_1707503517596/work
parso @ file:///opt/conda/conda-bld/parso_1641458642106/work
partd @ file:///C:/b/abs_46awex0fd7/croot/partd_1698702622970/work
passlib==1.7.4
pathlib @ file:///Users/<USER>/demo/mc3/conda-bld/pathlib_1629713961906/work
pathspec @ file:///C:/ci_311/pathspec_1679427644142/work
patsy==0.5.3
pcodedmp==1.2.6
pdfminer.six==20231228
pdfplumber==0.11.5
peewee==3.17.6
peewee-migrate==1.12.2
pefile==2023.2.7
pexpect @ file:///tmp/build/80754af9/pexpect_1605563209008/work
PhenoGraph==1.5.7
pickleshare @ file:///tmp/build/80754af9/pickleshare_1606932040724/work
pillow==11.2.1
pkce @ file:///C:/b/abs_d0z4444tb0/croot/pkce_1690384879799/work
pkginfo @ file:///C:/b/abs_d18srtr68x/croot/pkginfo_1679431192239/work
platformdirs @ file:///C:/b/abs_b6z_yqw_ii/croot/platformdirs_1692205479426/work
plotly @ file:///C:/ci_311/plotly_1676443558683/work
pluggy==1.5.0
ply==3.11
posthog==3.6.6
premailer==3.10.0
prettytable==3.15.1
primp==0.6.3
prometheus-client @ file:///C:/ci_311/prometheus_client_1679591942558/work
prompt-toolkit @ file:///C:/b/abs_68uwr58ed1/croot/prompt-toolkit_1704404394082/work
Protego @ file:///tmp/build/80754af9/protego_1598657180827/work
proto-plus==1.24.0
protobuf==6.31.0
psutil==7.0.0
psycopg2==2.9.9
psycopg2-binary==2.9.9
ptyprocess @ file:///tmp/build/80754af9/ptyprocess_1609355006118/work/dist/ptyprocess-0.7.0-py2.py3-none-any.whl
pure-eval @ file:///opt/conda/conda-bld/pure_eval_1646925070566/work
py-cpuinfo @ file:///C:/b/abs_9ej7u6shci/croot/py-cpuinfo_1698068121579/work
pyarrow==17.0.0
pyasn1 @ file:///Users/<USER>/demo/mc3/conda-bld/pyasn1_1629708007385/work
pyasn1-modules==0.2.8
PyAutoGUI==0.9.54
pyclipper==1.3.0.post5
pycodestyle @ file:///C:/ci_311/pycodestyle_1678376707834/work
pycosat @ file:///C:/b/abs_31zywn1be3/croot/pycosat_1696537126223/work
pycparser @ file:///tmp/build/80754af9/pycparser_1636541352034/work
pyct @ file:///C:/ci_311/pyct_1676438538057/work
pycurl==7.45.2
pydantic==2.11.5
pydantic-settings==2.10.1
pydantic_core==2.33.2
pydeck @ file:///C:/b/abs_ad9p880wi1/croot/pydeck_1706194121328/work
PyDispatcher==2.0.5
pydocstyle @ file:///C:/ci_311/pydocstyle_1678402028085/work
pydub==0.25.1
pyerfa @ file:///C:/ci_311/pyerfa_1676503994641/work
pyflakes @ file:///C:/ci_311/pyflakes_1678402101687/work
pyFUME==0.3.4
PyGetWindow==0.0.9
Pygments==2.19.1
pyinstaller==6.3.0
pyinstaller-hooks-contrib==2024.8
PyJWT==2.9.0
pykwalify==1.8.0
pylint @ file:///C:/ci_311/pylint_1678740302984/work
pylint-venv @ file:///C:/ci_311/pylint-venv_1678402170638/work
pyls-spyder==0.4.0
pymilvus==2.4.6
pymongo==4.10.1
PyMsgBox==1.0.9
PyMuPDF==1.25.3
PyMySQL==1.1.1
PyNaCl @ file:///C:/ci_311/pynacl_1676445861112/work
pynndescent==0.5.13
pynput==1.7.7
pyodbc @ file:///C:/b/abs_90kly0uuwz/croot/pyodbc_1705431396548/work
pyOpenSSL @ file:///C:/b/abs_baj0aupznq/croot/pyopenssl_1708380486701/work
pypandoc==1.13
pyparsing @ file:///C:/ci_311/pyparsing_1678502182533/work
pypdf==4.3.1
PyPDF2==3.0.1
pypdfium2==4.30.1
pyperclip==1.9.0
PyPika==0.48.9
pyproject_hooks==1.2.0
PyQt5==5.15.11
pyqt5-plugins==********.3
PyQt5-Qt5==5.15.2
pyqt5-tools==********.3
PyQt5_sip==12.17.0
PyQt6==6.9.1
PyQt6-Qt6==6.9.1
PyQt6_sip==13.10.2
PyQtWebEngine==5.15.6
pyradiomics==3.0.1
pyreadline3==3.5.4
PyRect==0.2.0
PyScreeze==0.1.30
PySide6==*******
PySide6_Addons==*******
PySide6_Essentials==*******
PySocks @ file:///C:/ci_311/pysocks_1676425991111/work
pytesseract==0.3.10
pytest==8.2.2
pytest-aiohttp==1.0.5
pytest-docker==3.1.1
pytest-qt==4.2.0
pytest-timeout==2.3.1
python-bidi==0.6.3
python-dateutil==2.9.0.post0
python-docx==1.1.2
python-dotenv==1.0.1
python-engineio==4.9.1
python-iso639==2024.4.27
python-jose==3.3.0
python-json-logger @ file:///C:/b/abs_cblnsm6puj/croot/python-json-logger_1683824130469/work
python-Levenshtein==0.26.1
python-lsp-black @ file:///C:/ci_311/python-lsp-black_1678721855627/work
python-lsp-jsonrpc==1.0.0
python-lsp-server @ file:///C:/b/abs_catecj7fv1/croot/python-lsp-server_1681930405912/work
python-magic==0.4.27
python-multipart==0.0.20
python-oxmsg==0.0.1
python-pptx==1.0.0
python-slugify @ file:///tmp/build/80754af9/python-slugify_1620405669636/work
python-snappy @ file:///C:/ci_311/python-snappy_1676446060182/work
python-socketio==5.11.3
pythonnet==3.0.5
pytoolconfig @ file:///C:/b/abs_f2j_xsvrpn/croot/pytoolconfig_1701728751207/work
pyttrree @ file:///C:/Users/<USER>/Documents/pyttrree/dist/pyttrree-0.1.0-py3-none-any.whl#sha256=6f8ce59778b0a31013a9dff1f85788544c51ed1701b41b4eeb75ac31c054449f
pytube==15.0.0
pytweening==1.2.0
pytz==2025.2
pyviz_comms @ file:///C:/b/abs_31r9afnand/croot/pyviz_comms_1701728067143/work
pywavelets @ file:///C:/b/abs_7est386xsb/croot/pywavelets_1705049855879/work
pywin32==306
pywin32-ctypes==0.2.2
pywinauto==0.6.9
pyWinhook==1.6.2
pywinpty @ file:///C:/ci_311/pywinpty_1677707791185/work/target/wheels/pywinpty-2.0.10-cp311-none-win_amd64.whl
pyxlsb==1.0.10
PyYAML==6.0.2
pyzmq @ file:///C:/b/abs_89aq69t0up/croot/pyzmq_1705605705281/work
qasync==0.27.1
QDarkStyle @ file:///tmp/build/80754af9/qdarkstyle_1617386714626/work
QScintilla==2.14.1
qstylizer @ file:///C:/ci_311/qstylizer_1678502012152/work/dist/qstylizer-0.2.2-py2.py3-none-any.whl
qt5-applications==5.15.2.2.3
qt5-tools==5.15.2.1.3
QtAwesome==1.3.0
qtconsole @ file:///C:/b/abs_eb4u9jg07y/croot/qtconsole_1681402843494/work
QtPy @ file:///C:/b/abs_derqu__3p8/croot/qtpy_1700144907661/work
queuelib @ file:///C:/b/abs_563lpxcne9/croot/queuelib_1696951148213/work
rank-bm25==0.2.2
RapidFuzz==3.10.0
rapidocr-onnxruntime==1.3.24
red-black-tree-mod==1.20
redis==5.1.0
referencing @ file:///C:/b/abs_09f4hj6adf/croot/referencing_1699012097448/work
regex @ file:///C:/b/abs_d5e2e5uqmr/croot/regex_1696515472506/work
requests==2.32.3
requests-file @ file:///Users/<USER>/demo/mc3/conda-bld/requests-file_1629455781986/work
requests-mock==1.12.1
requests-oauthlib==2.0.0
requests-toolbelt @ file:///C:/b/abs_2fsmts66wp/croot/requests-toolbelt_1690874051210/work
rfc3339-validator @ file:///C:/b/abs_ddfmseb_vm/croot/rfc3339-validator_1683077054906/work
rfc3986-validator @ file:///C:/b/abs_6e9azihr8o/croot/rfc3986-validator_1683059049737/work
rich==14.0.0
rope @ file:///C:/ci_311/rope_1678402524346/work
rpds-py @ file:///C:/b/abs_76j4g4la23/croot/rpds-py_1698947348047/work
rsa==4.9
RTFDE==0.1.2
Rtree @ file:///C:/ci_311/rtree_1676455758391/work
ruamel-yaml-conda @ file:///C:/ci_311/ruamel_yaml_1676455799258/work
ruamel.yaml @ file:///C:/ci_311/ruamel.yaml_1676439214109/work
ruff==0.11.11
s3fs @ file:///C:/b/abs_24vbfcawyu/croot/s3fs_1701294224436/work
s3transfer==0.10.2
safehttpx==0.1.6
safetensors==0.4.4
scanpy==1.11.0
schedule==1.2.2
scikit-image @ file:///C:/b/abs_f7z1pjjn6f/croot/scikit-image_1707346180040/work
scikit-learn==1.6.1
scipy==1.15.2
Scrapy @ file:///C:/ci_311/scrapy_1678502587780/work
seaborn==0.13.2
semantic-version==2.10.0
semver @ file:///tmp/build/80754af9/semver_1603822362442/work
Send2Trash @ file:///C:/b/abs_08dh49ew26/croot/send2trash_1699371173324/work
sentence-transformers==3.0.1
sentencepiece==0.2.0
service-identity @ file:///Users/<USER>/demo/mc3/conda-bld/service_identity_1629460757137/work
session-info2==0.1.2
shapely==2.0.6
shellingham==1.5.4
shiboken6==*******
simpful==2.12.0
simple-websocket==1.0.0
SimpleITK==2.4.1
sip @ file:///C:/b/abs_edevan3fce/croot/sip_1698675983372/work
six==1.17.0
smart-open @ file:///C:/ci_311/smart_open_1676439339434/work
smmap @ file:///tmp/build/80754af9/smmap_1611694433573/work
sniffio==1.3.1
snowballstemmer @ file:///tmp/build/80754af9/snowballstemmer_1637937080595/work
sortedcontainers @ file:///tmp/build/80754af9/sortedcontainers_1623949099177/work
soupsieve @ file:///C:/b/abs_bbsvy9t4pl/croot/soupsieve_1696347611357/work
Sphinx @ file:///C:/ci_311/sphinx_1676434546244/work
sphinxcontrib-applehelp @ file:///home/<USER>/src/ci/sphinxcontrib-applehelp_1611920841464/work
sphinxcontrib-devhelp @ file:///home/<USER>/src/ci/sphinxcontrib-devhelp_1611920923094/work
sphinxcontrib-htmlhelp @ file:///tmp/build/80754af9/sphinxcontrib-htmlhelp_1623945626792/work
sphinxcontrib-jsmath @ file:///home/<USER>/src/ci/sphinxcontrib-jsmath_1611920942228/work
sphinxcontrib-qthelp @ file:///home/<USER>/src/ci/sphinxcontrib-qthelp_1611921055322/work
sphinxcontrib-serializinghtml @ file:///tmp/build/80754af9/sphinxcontrib-serializinghtml_1624451540180/work
spyder @ file:///C:/b/abs_e99kl7d8t0/croot/spyder_1681934304813/work
spyder-kernels @ file:///C:/b/abs_e788a8_4y9/croot/spyder-kernels_1691599588437/work
SQLAlchemy==2.0.38
stack-data @ file:///opt/conda/conda-bld/stack_data_1646927590127/work
starlette==0.46.2
statsmodels @ file:///C:/b/abs_7bth810rna/croot/statsmodels_1689937298619/work
streamlit @ file:///C:/b/abs_ba5je7xxy7/croot/streamlit_1706200559831/work
streamlit-drawable-canvas==0.9.3
supervision==0.23.0
sympy==1.14.0
tables @ file:///C:/b/abs_411740ajo7/croot/pytables_1705614883108/work
tabulate @ file:///C:/b/abs_21rf8iibnh/croot/tabulate_1701354830521/work
tbb==2021.13.1
tblib @ file:///Users/<USER>/demo/mc3/conda-bld/tblib_1629402031467/work
tenacity==8.5.0
terminado @ file:///C:/ci_311/terminado_1678228513830/work
text-unidecode @ file:///Users/<USER>/demo/mc3/conda-bld/text-unidecode_1629401354553/work
textdistance @ file:///tmp/build/80754af9/textdistance_1612461398012/work
texttable==1.7.0
threadpoolctl==3.6.0
three-merge @ file:///tmp/build/80754af9/three-merge_1607553261110/work
tifffile @ file:///C:/b/abs_45o5chuqwt/croot/tifffile_1695107511025/work
tiktoken==0.7.0
timm==1.0.15
tinycss2 @ file:///C:/ci_311/tinycss2_1676425376744/work
tk==0.1.0
tldextract @ file:///opt/conda/conda-bld/tldextract_1646638314385/work
tokenizers==0.21.0
toml @ file:///tmp/build/80754af9/toml_1616166611790/work
tomlkit==0.13.2
toolz @ file:///C:/ci_311/toolz_1676431406517/work
torch==2.7.1
torchdiffeq==0.2.5
torchvision==0.22.1
tornado @ file:///C:/b/abs_0cbrstidzg/croot/tornado_1696937003724/work
tqdm==4.67.1
traitlets @ file:///C:/ci_311/traitlets_1676423290727/work
transformers==4.49.0
truststore @ file:///C:/b/abs_55z7b3r045/croot/truststore_1695245455435/work
ttkbootstrap==1.10.1
Twisted @ file:///C:/b/abs_e7yqd811in/croot/twisted_1708702883769/work
twisted-iocpsupport @ file:///C:/ci_311/twisted-iocpsupport_1676447612160/work
typer==0.15.4
typing==3.7.4.3
typing-inspect==0.9.0
typing-inspection==0.4.1
typing_extensions==4.13.2
tzdata==2025.2
tzlocal==5.2
uc-micro-py @ file:///C:/ci_311/uc-micro-py_1676457695423/work
ujson @ file:///C:/ci_311/ujson_1676434714224/work
ultralytics==8.3.158
ultralytics-thop==2.0.14
umap-learn==0.5.7
Unidecode @ file:///tmp/build/80754af9/unidecode_1614712377438/work
unstructured==0.15.9
unstructured-client==0.25.9
uritemplate==4.1.1
urllib3==2.4.0
uv==0.5.11
uvicorn==0.34.2
validators==0.33.0
w3lib @ file:///C:/b/abs_957begrwnl/croot/w3lib_1708640020760/work
wakepy==0.10.2.post1
watchdog==2.1.9
watchfiles==0.24.0
wcwidth @ file:///Users/<USER>/demo/mc3/conda-bld/wcwidth_1629357192024/work
webencodings==0.5.1
websocket-client @ file:///C:/ci_311/websocket-client_1676426063281/work
websockets==15.0.1
Werkzeug==3.0.4
whatthepatch @ file:///C:/ci_311/whatthepatch_1678402578113/work
whichcraft==0.6.1
widgetsnbextension @ file:///C:/b/abs_derxhz1biv/croot/widgetsnbextension_1701273671518/work
win-inet-pton @ file:///C:/ci_311/win_inet_pton_1676425458225/work
win-unicode-console==0.5
win32_setctime==1.2.0
wrapt @ file:///C:/ci_311/wrapt_1676432805090/work
wsproto==1.2.0
xarray @ file:///C:/b/abs_5bkjiynp4e/croot/xarray_1689041498548/work
xlrd==2.0.1
XlsxWriter==3.2.0
xlwings @ file:///C:/ci_311_rebuilds/xlwings_1679013429160/work
xlwt==1.3.0
xxhash==3.5.0
xyzservices @ file:///C:/ci_311/xyzservices_1676434829315/work
yapf @ file:///tmp/build/80754af9/yapf_1615749224965/work
yarl @ file:///C:/b/abs_8bxwdyhjvp/croot/yarl_1701105248152/work
youtube-transcript-api==0.6.2
zict @ file:///C:/b/abs_780gyydtbp/croot/zict_1695832899404/work
zipp @ file:///C:/b/abs_b0beoc27oa/croot/zipp_1704206963359/work
zope.event==5.0
zope.interface @ file:///C:/ci_311/zope.interface_1676439868776/work
zstandard==0.19.0
