# -*- coding:utf-8 -*-
import json
import os
import sys
import time
import subprocess
import hashlib
import shutil
from datetime import datetime
import logging
import platform
import re

class PythonPathManager:
    def __init__(self, cache_file='config/python_paths.json'):
        self.cache_file = cache_file
        self.cache_dir = os.path.dirname(cache_file)
        if not os.path.exists(self.cache_dir):
            os.makedirs(self.cache_dir)
        
        self.logger = self._setup_logging()
        self.cache = self._load_cache()
        
        # 虚拟环境信息文件
        self.venv_info_file = os.path.join(self.cache_dir, 'venv_info.json')
        self.venv_info = self._load_venv_info()
        
        # 备份配置
        self.backup_config_file = os.path.join(self.cache_dir, 'backup_config.json')
        self.backup_config = self._load_backup_config()

    def _setup_logging(self):
        logger = logging.getLogger('PythonPathManager')
        logger.setLevel(logging.DEBUG)
        
        if not os.path.exists('logs'):
            os.makedirs('logs')
            
        fh = logging.FileHandler(
            f'logs/python_path_manager_{datetime.now():%Y%m%d}.log',
            encoding='utf-8'
        )
        fh.setLevel(logging.DEBUG)
        
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        fh.setFormatter(formatter)
        logger.addHandler(fh)
        
        return logger

    def _load_cache(self):
        """加载缓存文件"""
        try:
            if os.path.exists(self.cache_file):
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            self.logger.error(f"Failed to load cache: {e}")
            return {}

    def _save_cache(self):
        """保存缓存文件"""
        try:
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(self.cache, f, indent=4, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"Failed to save cache: {e}")

    def _get_python_info(self, python_path):
        """获取Python安装信息"""
        try:
            # 获取Python版本
            cmd = [python_path, '-c', 
                  'import sys, platform; '
                  'print(sys.version_info[0], sys.version_info[1], sys.version_info[2], '
                  'sys.maxsize > 2**32, '
                  'hasattr(sys, "windows_store_version"), '
                  'platform.python_implementation(), '
                  'sys.executable)']
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                ver_major, ver_minor, ver_micro, is_64bit, is_store, impl, real_path = result.stdout.strip().split()
                
                return {
                    'version': f"{ver_major}.{ver_minor}.{ver_micro}",
                    'is_64bit': is_64bit == 'True',
                    'is_store': is_store == 'True',
                    'implementation': impl,
                    'real_path': real_path,
                    'last_check': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
            return None
        except Exception as e:
            self.logger.error(f"Failed to get Python info: {e}")
            return None

    def _verify_python(self, python_path):
        """验证Python是否可用且符合要求"""
        try:
            info = self._get_python_info(python_path)
            if info:
                ver_parts = [int(x) for x in info['version'].split('.')]
                return (
                    ver_parts[0] == 3 and
                    8 <= ver_parts[1] <= 12 and
                    info['is_64bit'] and
                    not info['is_store']
                )
            return False
        except Exception as e:
            self.logger.error(f"Failed to verify Python: {e}")
            return False

    def get_cached_python(self, system_uuid):
        """获取缓存的Python路径"""
        try:
            # 处理环境变量格式的UUID
            if system_uuid.startswith('%') and system_uuid.endswith('%'):
                # 尝试从环境变量获取实际的UUID
                env_var = system_uuid[1:-1]  # 去掉%号
                actual_uuid = os.environ.get(env_var)
                if actual_uuid:
                    system_uuid = actual_uuid
                else:
                    self.logger.error(f"Environment variable {env_var} not found")
                    return None

            if system_uuid in self.cache:
                entry = self.cache[system_uuid]
                python_path = entry['path']
                
                # 验证Python是否仍然可用
                if os.path.exists(python_path) and self._verify_python(python_path):
                    # 更新信息
                    entry.update(self._get_python_info(python_path))
                    entry['last_used'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    self._save_cache()
                    return python_path
                    
                # 如果验证失败，从缓存中移除
                del self.cache[system_uuid]
                self._save_cache()
                
            return None
            
        except Exception as e:
            self.logger.error(f"Failed to get cached Python: {e}")
            return None

    def add_python_path(self, system_uuid, python_path):
        """添加新的Python路径到缓存"""
        try:
            # 处理环境变量
            if system_uuid.startswith('%') and system_uuid.endswith('%'):
                # 如果是环境变量格式，跳过添加
                return False
                
            info = self._get_python_info(python_path)
            if info and self._verify_python(python_path):
                self.cache[system_uuid] = {
                    'path': python_path,
                    **info,
                    'computer_name': os.environ.get('COMPUTERNAME', 'Unknown'),
                    'last_used': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
                
                self._save_cache()
                return True
                
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to add Python path: {e}")
            return False

    def clean_old_entries(self, days=90):
        """清理超过指定天数未使用的缓存条目"""
        try:
            now = datetime.now()
            to_remove = []
            
            for uuid, entry in self.cache.items():
                last_used = datetime.strptime(
                    entry['last_used'],
                    '%Y-%m-%d %H:%M:%S'
                )
                if (now - last_used).days > days:
                    to_remove.append(uuid)
                    
            for uuid in to_remove:
                del self.cache[uuid]
                
            if to_remove:
                self._save_cache()
                
        except Exception as e:
            self.logger.error(f"Failed to clean old entries: {e}")

    def _load_venv_info(self):
        """加载虚拟环境信息"""
        try:
            if os.path.exists(self.venv_info_file):
                with open(self.venv_info_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {
                'created_by': None,
                'python_version': None,
                'packages': {},
                'last_validated': None
            }
        except Exception as e:
            self.logger.error(f"Failed to load venv info: {e}")
            return {}

    def _save_venv_info(self):
        """保存虚拟环境信息"""
        try:
            with open(self.venv_info_file, 'w', encoding='utf-8') as f:
                json.dump(self.venv_info, f, indent=4, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"Failed to save venv info: {e}")

    def _get_venv_python_version(self, venv_path):
        """获取虚拟环境的Python版本"""
        try:
            if os.path.exists(venv_path):
                venv_python = os.path.join(venv_path, 'Scripts', 'python.exe')
                if os.path.exists(venv_python):
                    result = subprocess.run(
                        [venv_python, '-c', 'import sys; print(".".join(map(str, sys.version_info[:3])))'],
                        capture_output=True,
                        text=True
                    )
                    if result.returncode == 0:
                        return result.stdout.strip()
            return None
        except Exception as e:
            self.logger.error(f"Failed to get venv Python version: {e}")
            return None

    def _get_installed_packages(self, venv_path):
        """获取已安装的包信息"""
        try:
            venv_pip = os.path.join(venv_path, 'Scripts', 'pip.exe')
            if os.path.exists(venv_pip):
                result = subprocess.run(
                    [venv_pip, 'freeze'],
                    capture_output=True,
                    text=True
                )
                if result.returncode == 0:
                    packages = {}
                    for line in result.stdout.splitlines():
                        if '==' in line:
                            name, version = line.split('==')
                            packages[name.lower()] = version
                    return packages
            return {}
        except Exception as e:
            self.logger.error(f"Failed to get installed packages: {e}")
            return {}

    def check_venv_compatibility(self, system_python_path, venv_path='venv'):
        """检查虚拟环境与系统Python的兼容性"""
        try:
            # 获取系统Python版本
            result = subprocess.run(
                [system_python_path, '-c', 'import sys; print(".".join(map(str, sys.version_info[:3])))'],
                capture_output=True,
                text=True
            )
            if result.returncode != 0:
                return False, "Failed to get system Python version"
                
            system_version = result.stdout.strip()
            
            # 获取虚拟环境信息
            venv_version = self._get_venv_python_version(venv_path)
            if not venv_version:
                return False, "Failed to get venv Python version"
                
            # 检查主版本号是否匹配
            sys_major_minor = '.'.join(system_version.split('.')[:2])
            venv_major_minor = '.'.join(venv_version.split('.')[:2])
            
            if sys_major_minor != venv_major_minor:
                return False, f"Python version mismatch: system={system_version}, venv={venv_version}"
                
            return True, "Compatible"
            
        except Exception as e:
            self.logger.error(f"Failed to check venv compatibility: {e}")
            return False, str(e)

    def validate_venv(self, venv_path='venv'):
        """验证虚拟环境的完整性"""
        try:
            # 检查必要的文件和目录
            required_files = [
                os.path.join(venv_path, 'Scripts', 'python.exe'),
                os.path.join(venv_path, 'Scripts', 'pip.exe'),
                os.path.join(venv_path, 'pyvenv.cfg')
            ]
            
            for file in required_files:
                if not os.path.exists(file):
                    return False, f"Missing required file: {file}"
            
            # 验证Python可以正常运行
            venv_python = os.path.join(venv_path, 'Scripts', 'python.exe')
            result = subprocess.run(
                [venv_python, '-c', 'print("test")'],
                capture_output=True,
                text=True
            )
            if result.returncode != 0:
                return False, "Python validation failed"
            
            # 获取当前包信息
            current_packages = self._get_installed_packages(venv_path)
            
            # 比较与记录的包信息
            if self.venv_info['packages']:
                missing_packages = []
                version_mismatch = []
                
                for pkg, version in self.venv_info['packages'].items():
                    if pkg not in current_packages:
                        missing_packages.append(pkg)
                    elif current_packages[pkg] != version:
                        version_mismatch.append(f"{pkg}: expected={version}, found={current_packages[pkg]}")
                
                if missing_packages or version_mismatch:
                    return False, f"Package inconsistency found: missing={missing_packages}, version_mismatch={version_mismatch}"
            
            # 更新验证时间
            self.venv_info['last_validated'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            self._save_venv_info()
            
            return True, "Validation successful"
            
        except Exception as e:
            self.logger.error(f"Failed to validate venv: {e}")
            return False, str(e)

    def update_venv_info(self, system_python_path, venv_path='venv'):
        """更新虚拟环境信息"""
        try:
            self.venv_info['created_by'] = system_python_path
            self.venv_info['python_version'] = self._get_venv_python_version(venv_path)
            self.venv_info['packages'] = self._get_installed_packages(venv_path)
            self.venv_info['last_validated'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            self._save_venv_info()
            return True
        except Exception as e:
            self.logger.error(f"Failed to update venv info: {e}")
            return False

    def _load_backup_config(self):
        """加载备份配置"""
        try:
            if os.path.exists(self.backup_config_file):
                with open(self.backup_config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {
                'max_backups_per_system': 3,  # 每个系统保留的最大备份数
                'systems': {},  # 记录每个系统的备份信息
                'global_backups': []  # 全局备份列表
            }
        except Exception as e:
            self.logger.error(f"Failed to load backup config: {e}")
            return {
                'max_backups_per_system': 3,
                'systems': {},
                'global_backups': []
            }

    def _save_backup_config(self):
        """保存备份配置"""
        try:
            with open(self.backup_config_file, 'w', encoding='utf-8') as f:
                json.dump(self.backup_config, f, indent=4, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"Failed to save backup config: {e}")

    def _get_system_identifier(self):
        """获取系统标识符"""
        try:
            # 获取系统信息
            system_info = {
                'uuid': os.environ.get('SYSTEM_UUID', ''),
                'computer_name': os.environ.get('COMPUTERNAME', ''),
                'python_version': platform.python_version(),
                'os_version': platform.platform()
            }
            
            # 创建唯一标识符
            identifier = f"{system_info['computer_name']}_{system_info['uuid'][:8]}"
            return identifier, system_info
        except Exception as e:
            self.logger.error(f"Failed to get system identifier: {e}")
            return None, None

    def backup_venv(self, venv_path='venv', backup_dir='backup', system_uuid=None):
        """备份虚拟环境"""
        try:
            # 获取系统标识
            system_id, system_info = self._get_system_identifier()
            if not system_id:
                return False, "Failed to get system identifier"
                
            # 创建系统特定的备份目录
            system_backup_dir = os.path.join(backup_dir, system_id)
            if not os.path.exists(system_backup_dir):
                os.makedirs(system_backup_dir)
                
            # 生成备份名称
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_name = f"venv_backup_{timestamp}"
            backup_path = os.path.join(system_backup_dir, backup_name)
            
            # 复制虚拟环境
            shutil.copytree(venv_path, backup_path)
            
            # 准备备份信息
            backup_info = {
                'timestamp': timestamp,
                'system_id': system_id,
                'system_info': system_info,
                'venv_info': self.venv_info,
                'backup_path': backup_path,
                'backup_type': 'system_specific'  # 标记为系统特定的备份
            }
            
            # 确保系统记录存在
            if system_id not in self.backup_config['systems']:
                self.backup_config['systems'][system_id] = {
                    'info': system_info,
                    'backups': []
                }
            
            # 添加新备份记录
            self.backup_config['systems'][system_id]['backups'].append(backup_info)
            
            # 保存配置
            self._save_backup_config()
            
            # 保存备份详细信息
            with open(os.path.join(backup_path, 'backup_info.json'), 'w', encoding='utf-8') as f:
                json.dump(backup_info, f, indent=4, ensure_ascii=False)
                
            return True, backup_path
            
        except Exception as e:
            self.logger.error(f"Failed to backup venv: {e}")
            return False, str(e)

    def list_backups(self, system_id=None):
        """列出备份"""
        try:
            if system_id:
                # 列出特定系统的备份
                if system_id in self.backup_config['systems']:
                    return self.backup_config['systems'][system_id]['backups']
                return []
            else:
                # 列出所有备份
                all_backups = []
                for sys_id, sys_info in self.backup_config['systems'].items():
                    all_backups.extend(sys_info['backups'])
                return sorted(all_backups, key=lambda x: x['timestamp'], reverse=True)
        except Exception as e:
            self.logger.error(f"Failed to list backups: {e}")
            return []

    def restore_backup(self, backup_path, venv_path='venv'):
        """恢复备份"""
        try:
            # 检查备份是否存在
            if not os.path.exists(backup_path):
                return False, "Backup does not exist"
                
            # 读取备份信息
            info_file = os.path.join(backup_path, 'backup_info.json')
            if not os.path.exists(info_file):
                return False, "Backup info file not found"
                
            with open(info_file, 'r', encoding='utf-8') as f:
                backup_info = json.load(f)
            
            # 获取当前系统标识
            current_system_id, _ = self._get_system_identifier()
            
            # 检查系统兼容性
            if backup_info['system_id'] != current_system_id:
                return False, f"Backup was created on different system: {backup_info['system_info']['computer_name']}"
            
            # 如果目标虚拟环境存在，先删除
            if os.path.exists(venv_path):
                shutil.rmtree(venv_path)
            
            # 复制备份到目标位置
            shutil.copytree(backup_path, venv_path)
            
            # 更新虚拟环境信息
            self.venv_info = backup_info['venv_info']
            self._save_venv_info()
            
            return True, "Backup restored successfully"
            
        except Exception as e:
            self.logger.error(f"Failed to restore backup: {e}")
            return False, str(e)

    def clean_old_backups(self, days=30):
        """清理旧备份"""
        try:
            now = datetime.now()
            cleaned = 0
            
            for system_id in list(self.backup_config['systems'].keys()):
                backups = self.backup_config['systems'][system_id]['backups']
                valid_backups = []
                
                for backup in backups:
                    backup_time = datetime.strptime(
                        backup['timestamp'],
                        '%Y%m%d_%H%M%S'
                    )
                    if (now - backup_time).days <= days:
                        valid_backups.append(backup)
                    else:
                        # 删除旧备份
                        backup_path = backup['backup_path']
                        if os.path.exists(backup_path):
                            try:
                                shutil.rmtree(backup_path)
                                cleaned += 1
                                self.logger.info(f"Removed old backup: {backup_path}")
                            except Exception as e:
                                self.logger.error(f"Failed to remove backup {backup_path}: {e}")
                
                if valid_backups:
                    self.backup_config['systems'][system_id]['backups'] = valid_backups
                else:
                    # 如果系统没有有效备份，删除系统记录
                    del self.backup_config['systems'][system_id]
            
            self._save_backup_config()
            return cleaned
            
        except Exception as e:
            self.logger.error(f"Failed to clean old backups: {e}")
            return 0

    def list_pythons(self):
        """列出所有缓存的Python安装"""
        try:
            # 创建一个集合来存储已经显示的路径
            shown_paths = set()
            results = []
            
            for uuid, entry in self.cache.items():
                # 如果是环境变量格式的UUID，跳过
                if uuid.startswith('%') and uuid.endswith('%'):
                    continue
                    
                path = entry['path']
                # 如果这个路径已经显示过，跳过
                if path in shown_paths:
                    continue
                    
                shown_paths.add(path)
                
                # 创建结果字典
                result = {
                    'system': entry.get('computer_name', 'Unknown'),
                    'uuid': uuid,
                    'path': path,
                    'version': entry.get('version', 'Unknown'),
                    'implementation': entry.get('implementation', 'Unknown'),
                    'is_64bit': entry.get('is_64bit', False),
                    'last_check': entry.get('last_check', None)
                }
                results.append(result)
                
            return results
                
        except Exception as e:
            self.logger.error(f"Failed to list Pythons: {e}")
            return []

if __name__ == '__main__':
    import argparse
    parser = argparse.ArgumentParser(description='Python Path Manager')
    parser.add_argument('--get', action='store_true', help='Get Python path for system UUID')
    parser.add_argument('--add', action='store_true', help='Add Python path for system UUID')
    parser.add_argument('--clean', action='store_true', help='Clean old entries')
    parser.add_argument('--list', action='store_true', help='List all cached Pythons')
    parser.add_argument('--uuid', help='System UUID')
    parser.add_argument('--path', help='Python path to add')
    parser.add_argument('--check-venv', help='Check virtual environment compatibility')
    parser.add_argument('--validate-venv', action='store_true', help='Validate virtual environment')
    parser.add_argument('--update-venv', help='Update virtual environment info')
    parser.add_argument('--backup-venv', action='store_true', help='Backup virtual environment')
    parser.add_argument('--list-backups', action='store_true', help='List all backups')
    parser.add_argument('--restore-backup', help='Restore specified backup')
    parser.add_argument('--clean-backups', action='store_true', help='Clean old backups')
    parser.add_argument('--system-id', help='System identifier for backup operations')
    parser.add_argument('--json', action='store_true', help='Output in JSON format')
    
    args = parser.parse_args()
    manager = PythonPathManager()
    
    if args.clean:
        manager.clean_old_entries()
    elif args.list:
        pythons = manager.list_pythons()
        if args.json:
            import json
            print(json.dumps(pythons, indent=2))
        else:
            for python in pythons:
                print(f"System: {python['system']} ({python['uuid']})")
                print(f"Path: {python['path']}")
                print(f"Version: {python['version']} ({python['implementation']})")
                print(f"64-bit: {python['is_64bit']}")
                if python['last_check']:
                    print(f"Last check: {python['last_check']}")
                print("---")
    elif args.get and args.uuid:
        path = manager.get_cached_python(args.uuid)
        if path:
            print(path)
            sys.exit(0)
        sys.exit(1)
    elif args.add and args.uuid and args.path:
        success = manager.add_python_path(args.uuid, args.path)
        sys.exit(0 if success else 1)
    elif args.check_venv:
        compatible, message = manager.check_venv_compatibility(args.check_venv)
        print(message)
        sys.exit(0 if compatible else 1)
    elif args.validate_venv:
        valid, message = manager.validate_venv()
        print(message)
        sys.exit(0 if valid else 1)
    elif args.update_venv:
        success = manager.update_venv_info(args.update_venv)
        sys.exit(0 if success else 1)
    elif args.backup_venv:
        success, result = manager.backup_venv()
        if success:
            print(f"Backup created at: {result}")
        else:
            print(f"Backup failed: {result}")
        sys.exit(0 if success else 1)
    elif args.list_backups:
        backups = manager.list_backups(args.system_id)
        for backup in backups:
            print(f"Backup: {backup['timestamp']}")
            print(f"System: {backup['system_info']['computer_name']}")
            print(f"Path: {backup['backup_path']}")
            print("---")
    elif args.restore_backup:
        success, message = manager.restore_backup(args.restore_backup)
        print(message)
        sys.exit(0 if success else 1)
    elif args.clean_backups:
        cleaned = manager.clean_old_backups()
        print(f"Cleaned {cleaned} old backups")
    else:
        parser.print_help()
        sys.exit(1) 