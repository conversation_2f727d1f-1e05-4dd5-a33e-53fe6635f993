#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试检测脚本 - 独立运行脚本
用于测试代码导出功能的示例脚本

自动生成时间: 2025-07-09 20:43:33
运行环境: Python 3.7+
依赖包: 请先运行 pip install -r requirements.txt

使用方法:
1. 确保已安装所有依赖包
2. 运行 python main.py 或双击 run.bat
3. 按照提示进行操作
"""

import sys
import os
import cv2
import numpy as np
import json
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# 导入工具模块
try:
    from utils.chinese_text_renderer import put_chinese_text, ChineseTextRenderer
    CHINESE_RENDERER_AVAILABLE = True
except ImportError:
    CHINESE_RENDERER_AVAILABLE = False
    print("⚠️ 中文渲染器不可用，中文字符可能显示为问号")

try:
    from utils.detection_utils import DetectionResult, BoundingBox
    DETECTION_UTILS_AVAILABLE = True
except ImportError:
    DETECTION_UTILS_AVAILABLE = False
    print("⚠️ 检测工具不可用，使用基础功能")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('detection_log.txt', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class StandaloneDetector:
    """独立检测器"""
    
    def __init__(self):
        """初始化检测器"""
        self.logger = logger
        self.config = self._load_config()
        self.chinese_renderer = None
        
        if CHINESE_RENDERER_AVAILABLE:
            try:
                self.chinese_renderer = ChineseTextRenderer()
                self.logger.info("✅ 中文渲染器初始化成功")
            except Exception as e:
                self.logger.warning(f"中文渲染器初始化失败: {e}")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            config_path = Path("config/settings.json")
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            self.logger.warning(f"加载配置失败: {e}")
        
        # 返回默认配置
        return {
            "template_name": "测试检测脚本",
            "description": "用于测试代码导出功能的示例脚本",
            "confidence_threshold": 0.5,
            "nms_threshold": 0.4
        }
    
    def render_text_on_image(self, image: np.ndarray, text: str, 
                           position: Tuple[int, int], 
                           color: Tuple[int, int, int] = (0, 255, 0)) -> np.ndarray:
        """在图像上渲染文字"""
        if self.chinese_renderer and CHINESE_RENDERER_AVAILABLE:
            try:
                return put_chinese_text(image, text, position, 
                                      font_size=16, color=color, background=True)
            except Exception as e:
                self.logger.warning(f"中文渲染失败: {e}")
        
        # 回退到OpenCV渲染
        cv2.putText(image, text, position, cv2.FONT_HERSHEY_SIMPLEX, 
                   0.6, color, 2, cv2.LINE_AA)
        return image
    
    def run_detection(self):
        """运行检测主程序"""
        self.logger.info("🚀 开始运行检测程序...")
        self.logger.info(f"📋 模板: {self.config.get('template_name', 'Unknown')}")
        self.logger.info(f"📝 描述: {self.config.get('description', 'No description')}")
        
        try:
            # 这里插入用户的检测代码
            
            # 测试检测代码
            import cv2
            import numpy as np

            def test_detection():
                """测试检测函数"""
                print("🔍 开始检测...")
    
                # 模拟检测逻辑
                image = np.zeros((480, 640, 3), dtype=np.uint8)
    
                # 绘制测试框
                cv2.rectangle(image, (100, 100), (200, 200), (0, 255, 0), 2)
    
                # 添加中文标签
                if CHINESE_RENDERER_AVAILABLE:
                    image = self.render_text_on_image(image, "测试目标", (110, 90))
                else:
                    cv2.putText(image, "Test Target", (110, 90), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
    
                # 保存结果
                cv2.imwrite("detection_result.png", image)
                print("✅ 检测完成，结果已保存")

            # 运行检测
            test_detection()

            
        except Exception as e:
            self.logger.error(f"检测程序执行失败: {e}")
            import traceback
            traceback.print_exc()

def main():
    """主函数"""
    print("=" * 60)
    print(f"🎯 {'测试检测脚本'}")
    print(f"📝 {'用于测试代码导出功能的示例脚本'}")
    print("=" * 60)
    
    try:
        detector = StandaloneDetector()
        detector.run_detection()
        
        print("\n✅ 程序执行完成")
        
    except KeyboardInterrupt:
        print("\n⚠️ 程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 等待用户输入（避免窗口立即关闭）
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
