# -*- coding: utf-8 -*-
"""
用户反馈收集系统
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

import json
import time
import uuid
import platform
import psutil
from pathlib import Path
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
import requests
import threading

from .logger import Logger
from .config_manager import ConfigManager


@dataclass
class FeedbackData:
    """反馈数据类"""
    feedback_id: str
    timestamp: float
    user_id: str
    feedback_type: str  # "bug", "feature", "improvement", "question"
    title: str
    description: str
    severity: str  # "low", "medium", "high", "critical"
    category: str
    system_info: Dict[str, Any]
    app_info: Dict[str, Any]
    logs: List[str]
    screenshots: List[str]
    contact_info: Optional[str]
    status: str  # "pending", "submitted", "acknowledged"


class FeedbackCollector:
    """用户反馈收集器类"""
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化反馈收集器
        
        Args:
            config_manager: 配置管理器
        """
        self.logger = Logger().get_logger(__name__)
        self.config_manager = config_manager
        
        # 配置
        self.feedback_server_url = "https://api.your-domain.com/feedback"
        self.enable_auto_submit = False
        self.enable_crash_reports = True
        self.enable_usage_analytics = False
        self.max_log_lines = 1000
        
        # 存储
        self.feedback_dir = Path("feedback")
        self.feedback_dir.mkdir(exist_ok=True)
        
        # 用户标识
        self.user_id = self._get_or_create_user_id()
        
        # 反馈队列
        self.pending_feedback: List[FeedbackData] = []
        self.submission_thread: Optional[threading.Thread] = None
        
        # 统计信息
        self.total_feedback_count = 0
        self.submitted_feedback_count = 0
        self.failed_submission_count = 0
        
        # 加载待提交的反馈
        self._load_pending_feedback()
        
        self.logger.info("用户反馈收集器初始化完成")
    
    def _get_or_create_user_id(self) -> str:
        """获取或创建用户ID"""
        try:
            user_id_file = self.feedback_dir / "user_id.txt"
            
            if user_id_file.exists():
                with open(user_id_file, 'r') as f:
                    user_id = f.read().strip()
                if user_id:
                    return user_id
            
            # 创建新的用户ID
            user_id = str(uuid.uuid4())
            with open(user_id_file, 'w') as f:
                f.write(user_id)
            
            return user_id
            
        except Exception as e:
            self.logger.error(f"获取用户ID失败: {e}")
            return str(uuid.uuid4())
    
    def collect_system_info(self) -> Dict[str, Any]:
        """收集系统信息"""
        try:
            system_info = {
                "platform": platform.platform(),
                "system": platform.system(),
                "release": platform.release(),
                "version": platform.version(),
                "machine": platform.machine(),
                "processor": platform.processor(),
                "python_version": platform.python_version(),
                "cpu_count": psutil.cpu_count(),
                "memory_total": psutil.virtual_memory().total,
                "disk_usage": {}
            }
            
            # 磁盘使用情况
            for disk in psutil.disk_partitions():
                try:
                    usage = psutil.disk_usage(disk.mountpoint)
                    system_info["disk_usage"][disk.device] = {
                        "total": usage.total,
                        "used": usage.used,
                        "free": usage.free
                    }
                except:
                    pass
            
            return system_info
            
        except Exception as e:
            self.logger.error(f"收集系统信息失败: {e}")
            return {}
    
    def collect_app_info(self) -> Dict[str, Any]:
        """收集应用信息"""
        try:
            app_info = {
                "version": "1.0.0",
                "build_date": "2025-01-27",
                "config": {},
                "performance_stats": {},
                "recent_errors": []
            }
            
            # 配置信息（脱敏）
            if self.config_manager:
                config_dict = self.config_manager.to_dict()
                # 移除敏感信息
                safe_config = self._sanitize_config(config_dict)
                app_info["config"] = safe_config
            
            return app_info
            
        except Exception as e:
            self.logger.error(f"收集应用信息失败: {e}")
            return {}
    
    def _sanitize_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """脱敏配置信息"""
        try:
            sanitized = {}
            
            for key, value in config.items():
                if isinstance(value, dict):
                    sanitized[key] = self._sanitize_config(value)
                elif key.lower() in ['password', 'token', 'key', 'secret', 'api_key']:
                    sanitized[key] = "***"
                elif isinstance(value, str) and len(value) > 100:
                    sanitized[key] = value[:100] + "..."
                else:
                    sanitized[key] = value
            
            return sanitized
            
        except Exception as e:
            self.logger.error(f"配置脱敏失败: {e}")
            return {}
    
    def collect_recent_logs(self, max_lines: int = None) -> List[str]:
        """收集最近的日志"""
        try:
            if max_lines is None:
                max_lines = self.max_log_lines
            
            # 这里应该从日志系统获取最近的日志
            # 暂时返回空列表
            return []
            
        except Exception as e:
            self.logger.error(f"收集日志失败: {e}")
            return []
    
    def create_feedback(self, 
                       feedback_type: str,
                       title: str,
                       description: str,
                       severity: str = "medium",
                       category: str = "general",
                       contact_info: Optional[str] = None,
                       include_system_info: bool = True,
                       include_logs: bool = True) -> str:
        """
        创建反馈
        
        Args:
            feedback_type: 反馈类型
            title: 标题
            description: 描述
            severity: 严重程度
            category: 类别
            contact_info: 联系信息
            include_system_info: 是否包含系统信息
            include_logs: 是否包含日志
            
        Returns:
            str: 反馈ID
        """
        try:
            feedback_id = str(uuid.uuid4())
            
            # 收集系统信息
            system_info = self.collect_system_info() if include_system_info else {}
            app_info = self.collect_app_info() if include_system_info else {}
            logs = self.collect_recent_logs() if include_logs else []
            
            # 创建反馈数据
            feedback = FeedbackData(
                feedback_id=feedback_id,
                timestamp=time.time(),
                user_id=self.user_id,
                feedback_type=feedback_type,
                title=title,
                description=description,
                severity=severity,
                category=category,
                system_info=system_info,
                app_info=app_info,
                logs=logs,
                screenshots=[],
                contact_info=contact_info,
                status="pending"
            )
            
            # 添加到队列
            self.pending_feedback.append(feedback)
            self.total_feedback_count += 1
            
            # 保存到本地
            self._save_feedback(feedback)
            
            # 自动提交
            if self.enable_auto_submit:
                self._start_submission_thread()
            
            self.logger.info(f"反馈已创建: {feedback_id}")
            return feedback_id
            
        except Exception as e:
            self.logger.error(f"创建反馈失败: {e}")
            return ""
    
    def add_screenshot(self, feedback_id: str, screenshot_path: str) -> bool:
        """
        添加截图到反馈
        
        Args:
            feedback_id: 反馈ID
            screenshot_path: 截图路径
            
        Returns:
            bool: 是否成功添加
        """
        try:
            for feedback in self.pending_feedback:
                if feedback.feedback_id == feedback_id:
                    feedback.screenshots.append(screenshot_path)
                    self._save_feedback(feedback)
                    self.logger.info(f"截图已添加到反馈 {feedback_id}")
                    return True
            
            self.logger.warning(f"未找到反馈: {feedback_id}")
            return False
            
        except Exception as e:
            self.logger.error(f"添加截图失败: {e}")
            return False
    
    def submit_feedback(self, feedback_id: str) -> bool:
        """
        提交反馈
        
        Args:
            feedback_id: 反馈ID
            
        Returns:
            bool: 是否成功提交
        """
        try:
            feedback = None
            for fb in self.pending_feedback:
                if fb.feedback_id == feedback_id:
                    feedback = fb
                    break
            
            if not feedback:
                self.logger.error(f"未找到反馈: {feedback_id}")
                return False
            
            # 提交到服务器
            success = self._submit_to_server(feedback)
            
            if success:
                feedback.status = "submitted"
                self.submitted_feedback_count += 1
                self.pending_feedback.remove(feedback)
                
                # 删除本地文件
                feedback_file = self.feedback_dir / f"{feedback_id}.json"
                if feedback_file.exists():
                    feedback_file.unlink()
                
                self.logger.info(f"反馈提交成功: {feedback_id}")
            else:
                self.failed_submission_count += 1
                self.logger.error(f"反馈提交失败: {feedback_id}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"提交反馈失败: {e}")
            return False
    
    def _submit_to_server(self, feedback: FeedbackData) -> bool:
        """提交反馈到服务器"""
        try:
            # 准备提交数据
            submit_data = asdict(feedback)
            
            # 处理截图
            files = {}
            for i, screenshot_path in enumerate(feedback.screenshots):
                screenshot_file = Path(screenshot_path)
                if screenshot_file.exists():
                    files[f"screenshot_{i}"] = open(screenshot_file, 'rb')
            
            try:
                # 发送POST请求
                response = requests.post(
                    self.feedback_server_url,
                    data={"feedback": json.dumps(submit_data)},
                    files=files,
                    timeout=30
                )
                
                response.raise_for_status()
                
                # 检查响应
                result = response.json()
                if result.get("success", False):
                    return True
                else:
                    self.logger.error(f"服务器返回错误: {result.get('error', 'Unknown error')}")
                    return False
                    
            finally:
                # 关闭文件
                for file_obj in files.values():
                    file_obj.close()
            
        except requests.RequestException as e:
            self.logger.error(f"网络请求失败: {e}")
            return False
        except Exception as e:
            self.logger.error(f"提交到服务器失败: {e}")
            return False
    
    def _save_feedback(self, feedback: FeedbackData) -> None:
        """保存反馈到本地"""
        try:
            feedback_file = self.feedback_dir / f"{feedback.feedback_id}.json"
            
            with open(feedback_file, 'w', encoding='utf-8') as f:
                json.dump(asdict(feedback), f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            self.logger.error(f"保存反馈失败: {e}")
    
    def _load_pending_feedback(self) -> None:
        """加载待提交的反馈"""
        try:
            for feedback_file in self.feedback_dir.glob("*.json"):
                try:
                    with open(feedback_file, 'r', encoding='utf-8') as f:
                        feedback_data = json.load(f)
                    
                    feedback = FeedbackData(**feedback_data)
                    if feedback.status == "pending":
                        self.pending_feedback.append(feedback)
                        
                except Exception as e:
                    self.logger.error(f"加载反馈文件失败: {feedback_file}, {e}")
            
            self.logger.info(f"加载了 {len(self.pending_feedback)} 个待提交反馈")
            
        except Exception as e:
            self.logger.error(f"加载待提交反馈失败: {e}")
    
    def _start_submission_thread(self) -> None:
        """启动提交线程"""
        if self.submission_thread and self.submission_thread.is_alive():
            return
        
        def submit_all():
            for feedback in self.pending_feedback.copy():
                try:
                    self.submit_feedback(feedback.feedback_id)
                    time.sleep(1)  # 避免频繁请求
                except Exception as e:
                    self.logger.error(f"批量提交反馈失败: {e}")
        
        self.submission_thread = threading.Thread(target=submit_all, daemon=True)
        self.submission_thread.start()
    
    def submit_all_pending(self) -> Dict[str, int]:
        """提交所有待提交的反馈"""
        try:
            success_count = 0
            failed_count = 0
            
            for feedback in self.pending_feedback.copy():
                if self.submit_feedback(feedback.feedback_id):
                    success_count += 1
                else:
                    failed_count += 1
            
            self.logger.info(f"批量提交完成: 成功 {success_count}, 失败 {failed_count}")
            
            return {
                "success": success_count,
                "failed": failed_count
            }
            
        except Exception as e:
            self.logger.error(f"批量提交失败: {e}")
            return {"success": 0, "failed": len(self.pending_feedback)}
    
    def create_crash_report(self, 
                           exception: Exception,
                           traceback_str: str,
                           context: Dict[str, Any] = None) -> str:
        """
        创建崩溃报告
        
        Args:
            exception: 异常对象
            traceback_str: 异常堆栈
            context: 上下文信息
            
        Returns:
            str: 反馈ID
        """
        try:
            if not self.enable_crash_reports:
                return ""
            
            title = f"应用崩溃: {type(exception).__name__}"
            description = f"""
应用发生崩溃，详细信息如下：

异常类型: {type(exception).__name__}
异常消息: {str(exception)}

堆栈跟踪:
{traceback_str}

上下文信息:
{json.dumps(context or {}, ensure_ascii=False, indent=2)}
"""
            
            return self.create_feedback(
                feedback_type="bug",
                title=title,
                description=description,
                severity="critical",
                category="crash",
                include_system_info=True,
                include_logs=True
            )
            
        except Exception as e:
            self.logger.error(f"创建崩溃报告失败: {e}")
            return ""
    
    def get_feedback_stats(self) -> Dict[str, Any]:
        """获取反馈统计"""
        return {
            "total_feedback_count": self.total_feedback_count,
            "submitted_feedback_count": self.submitted_feedback_count,
            "failed_submission_count": self.failed_submission_count,
            "pending_feedback_count": len(self.pending_feedback),
            "user_id": self.user_id,
            "auto_submit_enabled": self.enable_auto_submit,
            "crash_reports_enabled": self.enable_crash_reports
        }
    
    def configure_collector(self, 
                           server_url: Optional[str] = None,
                           auto_submit: Optional[bool] = None,
                           crash_reports: Optional[bool] = None,
                           usage_analytics: Optional[bool] = None) -> None:
        """
        配置反馈收集器
        
        Args:
            server_url: 服务器URL
            auto_submit: 是否自动提交
            crash_reports: 是否启用崩溃报告
            usage_analytics: 是否启用使用分析
        """
        if server_url:
            self.feedback_server_url = server_url
        
        if auto_submit is not None:
            self.enable_auto_submit = auto_submit
        
        if crash_reports is not None:
            self.enable_crash_reports = crash_reports
        
        if usage_analytics is not None:
            self.enable_usage_analytics = usage_analytics
        
        self.logger.info("反馈收集器配置已更新")
    
    def clear_pending_feedback(self) -> None:
        """清除所有待提交反馈"""
        try:
            for feedback in self.pending_feedback:
                feedback_file = self.feedback_dir / f"{feedback.feedback_id}.json"
                if feedback_file.exists():
                    feedback_file.unlink()
            
            self.pending_feedback.clear()
            self.logger.info("所有待提交反馈已清除")
            
        except Exception as e:
            self.logger.error(f"清除待提交反馈失败: {e}")
