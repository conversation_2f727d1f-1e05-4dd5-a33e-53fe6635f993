# -*- coding: utf-8 -*-
"""
NMS算法和IOU计算模块
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

import numpy as np
from typing import List, Tuple, Optional, Dict, Any
from enum import Enum

from ..utils.logger import Logger
from ..utils.data_structures import DetectionResult, BoundingBox


class NMSMethod(Enum):
    """NMS方法枚举"""
    STANDARD = "standard"
    SOFT = "soft"
    GAUSSIAN = "gaussian"
    DIOU = "diou"
    CIOU = "ciou"


class IOUCalculator:
    """IOU计算器类"""
    
    def __init__(self):
        """初始化IOU计算器"""
        self.logger = Logger().get_logger(__name__)
    
    def calculate_iou(self, bbox1: BoundingBox, bbox2: BoundingBox) -> float:
        """
        计算标准IoU
        
        Args:
            bbox1: 第一个边界框
            bbox2: 第二个边界框
            
        Returns:
            float: IoU值
        """
        try:
            return bbox1.iou(bbox2)
        except Exception as e:
            self.logger.error(f"IoU计算失败: {e}")
            return 0.0
    
    def calculate_giou(self, bbox1: BoundingBox, bbox2: BoundingBox) -> float:
        """
        计算Generalized IoU (GIoU)
        
        Args:
            bbox1: 第一个边界框
            bbox2: 第二个边界框
            
        Returns:
            float: GIoU值
        """
        try:
            # 计算标准IoU
            iou = self.calculate_iou(bbox1, bbox2)
            
            # 计算最小外接矩形
            x1_min = min(bbox1.x, bbox2.x)
            y1_min = min(bbox1.y, bbox2.y)
            x2_max = max(bbox1.x2, bbox2.x2)
            y2_max = max(bbox1.y2, bbox2.y2)
            
            # 外接矩形面积
            enclosing_area = (x2_max - x1_min) * (y2_max - y1_min)
            
            if enclosing_area == 0:
                return iou
            
            # 计算并集面积
            union_area = bbox1.area + bbox2.area - (iou * min(bbox1.area, bbox2.area))
            
            # GIoU公式
            giou = iou - (enclosing_area - union_area) / enclosing_area
            
            return giou
            
        except Exception as e:
            self.logger.error(f"GIoU计算失败: {e}")
            return 0.0
    
    def calculate_diou(self, bbox1: BoundingBox, bbox2: BoundingBox) -> float:
        """
        计算Distance IoU (DIoU)
        
        Args:
            bbox1: 第一个边界框
            bbox2: 第二个边界框
            
        Returns:
            float: DIoU值
        """
        try:
            # 计算标准IoU
            iou = self.calculate_iou(bbox1, bbox2)
            
            # 计算中心点距离
            center1_x, center1_y = bbox1.center_x, bbox1.center_y
            center2_x, center2_y = bbox2.center_x, bbox2.center_y
            center_distance_sq = (center1_x - center2_x) ** 2 + (center1_y - center2_y) ** 2
            
            # 计算最小外接矩形的对角线距离
            x1_min = min(bbox1.x, bbox2.x)
            y1_min = min(bbox1.y, bbox2.y)
            x2_max = max(bbox1.x2, bbox2.x2)
            y2_max = max(bbox1.y2, bbox2.y2)
            diagonal_distance_sq = (x2_max - x1_min) ** 2 + (y2_max - y1_min) ** 2
            
            if diagonal_distance_sq == 0:
                return iou
            
            # DIoU公式
            diou = iou - center_distance_sq / diagonal_distance_sq
            
            return diou
            
        except Exception as e:
            self.logger.error(f"DIoU计算失败: {e}")
            return 0.0
    
    def calculate_ciou(self, bbox1: BoundingBox, bbox2: BoundingBox) -> float:
        """
        计算Complete IoU (CIoU)
        
        Args:
            bbox1: 第一个边界框
            bbox2: 第二个边界框
            
        Returns:
            float: CIoU值
        """
        try:
            # 计算DIoU
            diou = self.calculate_diou(bbox1, bbox2)
            
            # 计算宽高比一致性
            w1, h1 = bbox1.width, bbox1.height
            w2, h2 = bbox2.width, bbox2.height
            
            if h1 == 0 or h2 == 0:
                return diou
            
            # 计算宽高比差异
            v = (4 / (np.pi ** 2)) * ((np.arctan(w2 / h2) - np.arctan(w1 / h1)) ** 2)
            
            # 计算IoU
            iou = self.calculate_iou(bbox1, bbox2)
            
            # 计算alpha参数
            if iou > 0:
                alpha = v / (1 - iou + v)
            else:
                alpha = 0
            
            # CIoU公式
            ciou = diou - alpha * v
            
            return ciou
            
        except Exception as e:
            self.logger.error(f"CIoU计算失败: {e}")
            return 0.0


class NMSProcessor:
    """NMS处理器类"""
    
    def __init__(self, iou_calculator: Optional[IOUCalculator] = None):
        """
        初始化NMS处理器
        
        Args:
            iou_calculator: IoU计算器，None时自动创建
        """
        self.logger = Logger().get_logger(__name__)
        self.iou_calculator = iou_calculator or IOUCalculator()
    
    def standard_nms(self, 
                    results: List[DetectionResult],
                    iou_threshold: float = 0.5,
                    score_threshold: float = 0.0) -> List[DetectionResult]:
        """
        标准NMS算法
        
        Args:
            results: 检测结果列表
            iou_threshold: IoU阈值
            score_threshold: 置信度阈值
            
        Returns:
            List[DetectionResult]: 处理后的结果
        """
        try:
            if not results:
                return []
            
            # 过滤低置信度结果
            filtered_results = [r for r in results if r.confidence >= score_threshold]
            
            if not filtered_results:
                return []
            
            # 按置信度排序
            sorted_results = sorted(filtered_results, key=lambda x: x.confidence, reverse=True)
            
            keep_results = []
            
            for current in sorted_results:
                should_keep = True
                
                for kept in keep_results:
                    iou = self.iou_calculator.calculate_iou(current.bbox, kept.bbox)
                    
                    if iou > iou_threshold:
                        should_keep = False
                        break
                
                if should_keep:
                    keep_results.append(current)
            
            return keep_results
            
        except Exception as e:
            self.logger.error(f"标准NMS处理失败: {e}")
            return results
    
    def soft_nms(self,
                results: List[DetectionResult],
                iou_threshold: float = 0.5,
                sigma: float = 0.5,
                score_threshold: float = 0.001) -> List[DetectionResult]:
        """
        Soft NMS算法
        
        Args:
            results: 检测结果列表
            iou_threshold: IoU阈值
            sigma: 高斯函数参数
            score_threshold: 最终置信度阈值
            
        Returns:
            List[DetectionResult]: 处理后的结果
        """
        try:
            if not results:
                return []
            
            # 复制结果以避免修改原始数据
            working_results = []
            for result in results:
                new_result = DetectionResult(
                    bbox=result.bbox,
                    confidence=result.confidence,
                    class_id=result.class_id,
                    class_name=result.class_name,
                    template_id=result.template_id,
                    source=result.source,
                    timestamp=result.timestamp,
                    metadata=result.metadata.copy()
                )
                working_results.append(new_result)
            
            # 按置信度排序
            working_results.sort(key=lambda x: x.confidence, reverse=True)
            
            keep_results = []
            
            while working_results:
                # 取置信度最高的
                current = working_results.pop(0)
                keep_results.append(current)
                
                # 更新剩余结果的置信度
                for other in working_results:
                    iou = self.iou_calculator.calculate_iou(current.bbox, other.bbox)
                    
                    if iou > iou_threshold:
                        # 使用高斯衰减
                        weight = np.exp(-(iou ** 2) / sigma)
                        other.confidence *= weight
                
                # 移除置信度过低的结果
                working_results = [r for r in working_results if r.confidence >= score_threshold]
                working_results.sort(key=lambda x: x.confidence, reverse=True)
            
            return keep_results
            
        except Exception as e:
            self.logger.error(f"Soft NMS处理失败: {e}")
            return results
    
    def diou_nms(self,
                results: List[DetectionResult],
                iou_threshold: float = 0.5,
                score_threshold: float = 0.0) -> List[DetectionResult]:
        """
        DIoU NMS算法
        
        Args:
            results: 检测结果列表
            iou_threshold: DIoU阈值
            score_threshold: 置信度阈值
            
        Returns:
            List[DetectionResult]: 处理后的结果
        """
        try:
            if not results:
                return []
            
            # 过滤低置信度结果
            filtered_results = [r for r in results if r.confidence >= score_threshold]
            
            if not filtered_results:
                return []
            
            # 按置信度排序
            sorted_results = sorted(filtered_results, key=lambda x: x.confidence, reverse=True)
            
            keep_results = []
            
            for current in sorted_results:
                should_keep = True
                
                for kept in keep_results:
                    diou = self.iou_calculator.calculate_diou(current.bbox, kept.bbox)
                    
                    if diou > iou_threshold:
                        should_keep = False
                        break
                
                if should_keep:
                    keep_results.append(current)
            
            return keep_results
            
        except Exception as e:
            self.logger.error(f"DIoU NMS处理失败: {e}")
            return results
    
    def apply_nms(self,
                 results: List[DetectionResult],
                 method: NMSMethod = NMSMethod.STANDARD,
                 iou_threshold: float = 0.5,
                 **kwargs) -> List[DetectionResult]:
        """
        应用指定的NMS方法
        
        Args:
            results: 检测结果列表
            method: NMS方法
            iou_threshold: IoU阈值
            **kwargs: 其他参数
            
        Returns:
            List[DetectionResult]: 处理后的结果
        """
        try:
            if method == NMSMethod.STANDARD:
                return self.standard_nms(results, iou_threshold, **kwargs)
            elif method == NMSMethod.SOFT:
                return self.soft_nms(results, iou_threshold, **kwargs)
            elif method == NMSMethod.DIOU:
                return self.diou_nms(results, iou_threshold, **kwargs)
            else:
                self.logger.warning(f"不支持的NMS方法: {method}，使用标准NMS")
                return self.standard_nms(results, iou_threshold, **kwargs)
                
        except Exception as e:
            self.logger.error(f"NMS处理失败: {e}")
            return results
    
    def class_aware_nms(self,
                       results: List[DetectionResult],
                       method: NMSMethod = NMSMethod.STANDARD,
                       iou_threshold: float = 0.5,
                       **kwargs) -> List[DetectionResult]:
        """
        类别感知的NMS
        
        Args:
            results: 检测结果列表
            method: NMS方法
            iou_threshold: IoU阈值
            **kwargs: 其他参数
            
        Returns:
            List[DetectionResult]: 处理后的结果
        """
        try:
            if not results:
                return []
            
            # 按类别分组
            class_groups = {}
            for result in results:
                class_key = result.class_name or "unknown"
                if class_key not in class_groups:
                    class_groups[class_key] = []
                class_groups[class_key].append(result)
            
            # 对每个类别单独应用NMS
            final_results = []
            for class_name, class_results in class_groups.items():
                nms_results = self.apply_nms(class_results, method, iou_threshold, **kwargs)
                final_results.extend(nms_results)
            
            # 按置信度排序
            final_results.sort(key=lambda x: x.confidence, reverse=True)
            
            return final_results
            
        except Exception as e:
            self.logger.error(f"类别感知NMS处理失败: {e}")
            return results
    
    def get_nms_stats(self, 
                     original_results: List[DetectionResult],
                     nms_results: List[DetectionResult]) -> Dict[str, Any]:
        """
        获取NMS处理统计信息
        
        Args:
            original_results: 原始结果
            nms_results: NMS处理后的结果
            
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            original_count = len(original_results)
            final_count = len(nms_results)
            suppressed_count = original_count - final_count
            
            # 计算置信度统计
            if original_results:
                original_confidences = [r.confidence for r in original_results]
                avg_original_confidence = np.mean(original_confidences)
                max_original_confidence = np.max(original_confidences)
                min_original_confidence = np.min(original_confidences)
            else:
                avg_original_confidence = 0.0
                max_original_confidence = 0.0
                min_original_confidence = 0.0
            
            if nms_results:
                final_confidences = [r.confidence for r in nms_results]
                avg_final_confidence = np.mean(final_confidences)
                max_final_confidence = np.max(final_confidences)
                min_final_confidence = np.min(final_confidences)
            else:
                avg_final_confidence = 0.0
                max_final_confidence = 0.0
                min_final_confidence = 0.0
            
            return {
                "original_count": original_count,
                "final_count": final_count,
                "suppressed_count": suppressed_count,
                "suppression_rate": suppressed_count / original_count if original_count > 0 else 0.0,
                "confidence_stats": {
                    "original": {
                        "avg": avg_original_confidence,
                        "max": max_original_confidence,
                        "min": min_original_confidence
                    },
                    "final": {
                        "avg": avg_final_confidence,
                        "max": max_final_confidence,
                        "min": min_final_confidence
                    }
                }
            }
            
        except Exception as e:
            self.logger.error(f"NMS统计计算失败: {e}")
            return {}
