{"name": "5555代码_20250126_192207", "code": "from pywinauto import Desktop, Application\nimport win32gui, win32con, win32process\nimport psutil, logging, time\n\n# 配置日志输出\nlogger = logging.getLogger('test_run')\n\ntry:\n    # 窗口定位代码\n    logger.info('正在通过句柄 1509328 定位窗口')\n    window = Desktop(backend='uia').window(handle=1509328)\n    logger.info('窗口定位成功')\n    logger.debug('尝试通过句柄定位窗口')\n\n    # 等待窗口就绪\n    if not window.exists():\n        logger.error('未找到目标窗口')\n        raise TimeoutError('窗口定位超时')\n    window.wait('ready', timeout=10)\n    logger.debug('窗口已就绪')\n\n    # 控件定位代码\n    logger.info('正在通过文本 \"帮助\" 定位控件')\n    control = window.child_window(title=r'帮助')\n    logger.info('控件定位成功')\n    logger.debug('尝试通过文本定位控件')\n\n    # 验证控件是否存在\n    if not control.exists():\n        logger.error('未找到目标控件')\n        raise TimeoutError('控件定位超时')\n\n    # 打印控件信息\n    logger.info(f'找到控件: {control.window_text()}')\n    logger.debug(f'控件类名: {control.element_info.class_name}')\n    logger.debug(f'控件位置: {control.rectangle()}')\n\n    # 在这里添加控件操作代码\n    # control.click_input()  # 点击控件\n    # control.type_keys('text')  # 输入文本", "category": "控件定位代码", "description": "控件类型: , 文本: 帮助", "created_at": "2025-01-26 19:22:40", "updated_at": "2025-01-26 19:22:40", "version": 1}