# -*- coding: utf-8 -*-
"""
UI自动化测试示例
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

import sys
import time
import json
from pathlib import Path
from typing import List, Dict, Any

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from yolo_opencv_detector.core.detection_engine import DetectionEngine
from yolo_opencv_detector.utils.config_manager import ConfigManager
from yolo_opencv_detector.utils.logger import Logger


class UITestAutomation:
    """UI自动化测试类"""
    
    def __init__(self):
        """初始化UI测试自动化"""
        self.logger = Logger().get_logger(__name__)
        self.config_manager = ConfigManager()
        self.detection_engine = DetectionEngine(self.config_manager)
        
        # 测试配置
        self.test_results = []
        self.ui_elements = {}
        self.test_scenarios = []
        
        self.logger.info("UI测试自动化初始化完成")
    
    def load_ui_elements(self, elements_config: Dict[str, Any]):
        """加载UI元素配置"""
        try:
            self.ui_elements = elements_config
            self.logger.info(f"加载了 {len(elements_config)} 个UI元素配置")
            
        except Exception as e:
            self.logger.error(f"加载UI元素配置失败: {e}")
    
    def detect_ui_elements(self) -> Dict[str, Any]:
        """检测UI元素"""
        try:
            # 执行检测
            results = self.detection_engine.detect_once()
            
            # 分析检测结果
            detected_elements = {
                "buttons": [],
                "text_fields": [],
                "labels": [],
                "images": [],
                "windows": [],
                "menus": []
            }
            
            for result in results:
                element_info = {
                    "position": (result.bbox.center_x, result.bbox.center_y),
                    "size": (result.bbox.width, result.bbox.height),
                    "confidence": result.confidence,
                    "class_name": result.class_name,
                    "template_id": result.template_id
                }
                
                # 根据类别分类UI元素
                if "button" in result.class_name.lower():
                    detected_elements["buttons"].append(element_info)
                elif "text" in result.class_name.lower():
                    detected_elements["text_fields"].append(element_info)
                elif "label" in result.class_name.lower():
                    detected_elements["labels"].append(element_info)
                elif "image" in result.class_name.lower():
                    detected_elements["images"].append(element_info)
                elif "window" in result.class_name.lower():
                    detected_elements["windows"].append(element_info)
                elif "menu" in result.class_name.lower():
                    detected_elements["menus"].append(element_info)
            
            return detected_elements
            
        except Exception as e:
            self.logger.error(f"检测UI元素失败: {e}")
            return {}
    
    def verify_element_presence(self, element_name: str, expected_count: int = 1) -> bool:
        """验证元素存在性"""
        try:
            detected_elements = self.detect_ui_elements()
            
            # 查找指定元素
            found_count = 0
            for category, elements in detected_elements.items():
                for element in elements:
                    if (element_name.lower() in element.get("class_name", "").lower() or
                        element_name.lower() in element.get("template_id", "").lower()):
                        found_count += 1
            
            success = found_count == expected_count
            
            test_result = {
                "test_type": "element_presence",
                "element_name": element_name,
                "expected_count": expected_count,
                "found_count": found_count,
                "success": success,
                "timestamp": time.time()
            }
            
            self.test_results.append(test_result)
            
            if success:
                self.logger.info(f"✓ 元素存在性验证通过: {element_name} (找到 {found_count} 个)")
            else:
                self.logger.warning(f"✗ 元素存在性验证失败: {element_name} (期望 {expected_count} 个，找到 {found_count} 个)")
            
            return success
            
        except Exception as e:
            self.logger.error(f"验证元素存在性失败: {e}")
            return False
    
    def verify_element_position(self, element_name: str, expected_region: tuple) -> bool:
        """验证元素位置"""
        try:
            detected_elements = self.detect_ui_elements()
            
            # 查找指定元素
            element_found = False
            element_in_region = False
            
            for category, elements in detected_elements.items():
                for element in elements:
                    if (element_name.lower() in element.get("class_name", "").lower() or
                        element_name.lower() in element.get("template_id", "").lower()):
                        element_found = True
                        
                        x, y = element["position"]
                        x1, y1, x2, y2 = expected_region
                        
                        if x1 <= x <= x2 and y1 <= y <= y2:
                            element_in_region = True
                            break
                
                if element_in_region:
                    break
            
            success = element_found and element_in_region
            
            test_result = {
                "test_type": "element_position",
                "element_name": element_name,
                "expected_region": expected_region,
                "element_found": element_found,
                "element_in_region": element_in_region,
                "success": success,
                "timestamp": time.time()
            }
            
            self.test_results.append(test_result)
            
            if success:
                self.logger.info(f"✓ 元素位置验证通过: {element_name}")
            else:
                self.logger.warning(f"✗ 元素位置验证失败: {element_name}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"验证元素位置失败: {e}")
            return False
    
    def verify_ui_layout(self, layout_config: Dict[str, Any]) -> bool:
        """验证UI布局"""
        try:
            detected_elements = self.detect_ui_elements()
            layout_checks = []
            
            # 检查各种布局要求
            for check_name, check_config in layout_config.items():
                check_type = check_config.get("type")
                
                if check_type == "element_count":
                    # 检查元素数量
                    category = check_config.get("category")
                    expected_count = check_config.get("expected_count")
                    actual_count = len(detected_elements.get(category, []))
                    
                    check_result = {
                        "check_name": check_name,
                        "type": check_type,
                        "expected": expected_count,
                        "actual": actual_count,
                        "success": actual_count == expected_count
                    }
                    
                elif check_type == "relative_position":
                    # 检查相对位置
                    element1 = check_config.get("element1")
                    element2 = check_config.get("element2")
                    relation = check_config.get("relation")  # "above", "below", "left", "right"
                    
                    # 这里需要实现相对位置检查逻辑
                    check_result = {
                        "check_name": check_name,
                        "type": check_type,
                        "success": True  # 简化实现
                    }
                
                else:
                    check_result = {
                        "check_name": check_name,
                        "type": "unknown",
                        "success": False
                    }
                
                layout_checks.append(check_result)
            
            # 计算总体成功率
            success_count = sum(1 for check in layout_checks if check["success"])
            total_checks = len(layout_checks)
            success_rate = success_count / total_checks if total_checks > 0 else 0
            
            overall_success = success_rate >= 0.8  # 80%通过率
            
            test_result = {
                "test_type": "ui_layout",
                "layout_checks": layout_checks,
                "success_rate": success_rate,
                "success": overall_success,
                "timestamp": time.time()
            }
            
            self.test_results.append(test_result)
            
            if overall_success:
                self.logger.info(f"✓ UI布局验证通过 (成功率: {success_rate:.1%})")
            else:
                self.logger.warning(f"✗ UI布局验证失败 (成功率: {success_rate:.1%})")
            
            return overall_success
            
        except Exception as e:
            self.logger.error(f"验证UI布局失败: {e}")
            return False
    
    def run_test_scenario(self, scenario_name: str, test_steps: List[Dict[str, Any]]) -> bool:
        """运行测试场景"""
        try:
            self.logger.info(f"开始运行测试场景: {scenario_name}")
            
            scenario_results = []
            overall_success = True
            
            for i, step in enumerate(test_steps):
                step_name = step.get("name", f"步骤{i+1}")
                step_type = step.get("type")
                
                self.logger.info(f"执行测试步骤: {step_name}")
                
                step_success = False
                
                if step_type == "verify_element":
                    element_name = step.get("element_name")
                    expected_count = step.get("expected_count", 1)
                    step_success = self.verify_element_presence(element_name, expected_count)
                
                elif step_type == "verify_position":
                    element_name = step.get("element_name")
                    expected_region = step.get("expected_region")
                    step_success = self.verify_element_position(element_name, expected_region)
                
                elif step_type == "verify_layout":
                    layout_config = step.get("layout_config")
                    step_success = self.verify_ui_layout(layout_config)
                
                elif step_type == "wait":
                    wait_time = step.get("wait_time", 1)
                    time.sleep(wait_time)
                    step_success = True
                
                else:
                    self.logger.warning(f"未知的测试步骤类型: {step_type}")
                    step_success = False
                
                scenario_results.append({
                    "step_name": step_name,
                    "step_type": step_type,
                    "success": step_success
                })
                
                if not step_success:
                    overall_success = False
                    if step.get("critical", False):
                        self.logger.error(f"关键步骤失败，停止测试场景: {step_name}")
                        break
            
            # 记录场景结果
            scenario_result = {
                "test_type": "test_scenario",
                "scenario_name": scenario_name,
                "steps": scenario_results,
                "success": overall_success,
                "timestamp": time.time()
            }
            
            self.test_results.append(scenario_result)
            
            if overall_success:
                self.logger.info(f"✓ 测试场景完成: {scenario_name}")
            else:
                self.logger.warning(f"✗ 测试场景失败: {scenario_name}")
            
            return overall_success
            
        except Exception as e:
            self.logger.error(f"运行测试场景失败: {e}")
            return False
    
    def generate_test_report(self, output_file: Path = None) -> Dict[str, Any]:
        """生成测试报告"""
        try:
            if not self.test_results:
                self.logger.warning("没有测试结果可生成报告")
                return {}
            
            # 统计测试结果
            total_tests = len(self.test_results)
            passed_tests = sum(1 for result in self.test_results if result["success"])
            failed_tests = total_tests - passed_tests
            pass_rate = passed_tests / total_tests if total_tests > 0 else 0
            
            # 按类型分组统计
            test_types = {}
            for result in self.test_results:
                test_type = result["test_type"]
                if test_type not in test_types:
                    test_types[test_type] = {"total": 0, "passed": 0}
                
                test_types[test_type]["total"] += 1
                if result["success"]:
                    test_types[test_type]["passed"] += 1
            
            # 生成报告
            report = {
                "summary": {
                    "total_tests": total_tests,
                    "passed_tests": passed_tests,
                    "failed_tests": failed_tests,
                    "pass_rate": pass_rate,
                    "test_date": time.strftime("%Y-%m-%d %H:%M:%S")
                },
                "test_types": test_types,
                "detailed_results": self.test_results
            }
            
            # 保存报告到文件
            if output_file:
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(report, f, ensure_ascii=False, indent=2)
                self.logger.info(f"测试报告已保存到: {output_file}")
            
            # 打印摘要
            print(f"\n{'='*60}")
            print("UI自动化测试报告")
            print(f"{'='*60}")
            print(f"总测试数: {total_tests}")
            print(f"通过测试: {passed_tests}")
            print(f"失败测试: {failed_tests}")
            print(f"通过率: {pass_rate:.1%}")
            print(f"{'='*60}")
            
            return report
            
        except Exception as e:
            self.logger.error(f"生成测试报告失败: {e}")
            return {}


def main():
    """主函数"""
    try:
        # 创建UI测试自动化实例
        ui_tester = UITestAutomation()
        
        # 示例测试场景
        test_scenario = [
            {
                "name": "验证主窗口存在",
                "type": "verify_element",
                "element_name": "window",
                "expected_count": 1,
                "critical": True
            },
            {
                "name": "验证按钮数量",
                "type": "verify_element",
                "element_name": "button",
                "expected_count": 3
            },
            {
                "name": "等待界面加载",
                "type": "wait",
                "wait_time": 2
            },
            {
                "name": "验证布局",
                "type": "verify_layout",
                "layout_config": {
                    "button_count": {
                        "type": "element_count",
                        "category": "buttons",
                        "expected_count": 3
                    }
                }
            }
        ]
        
        # 运行测试场景
        success = ui_tester.run_test_scenario("基本UI测试", test_scenario)
        
        # 生成测试报告
        report_file = Path(__file__).parent / "ui_test_report.json"
        ui_tester.generate_test_report(report_file)
        
        return 0 if success else 1
        
    except Exception as e:
        print(f"UI自动化测试失败: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
