#!/usr/bin/env python3
"""
检测结果后处理模块
将YOLO检测结果转换为可操作的坐标信息和自动化操作
"""

import numpy as np
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass
from enum import Enum

@dataclass
class DetectionTarget:
    """检测目标数据类"""
    id: str
    label: str
    confidence: float
    bbox: Tuple[int, int, int, int]  # (x, y, width, height)
    center: Tuple[int, int]  # (center_x, center_y)
    click_point: Tuple[int, int]  # 推荐的点击位置
    segmentation_mask: Optional[np.ndarray] = None  # 分割掩码（如果有）
    
class ClickStrategy(Enum):
    """点击策略枚举"""
    CENTER = "center"  # 中心点击
    TOP_LEFT = "top_left"  # 左上角点击
    CUSTOM_OFFSET = "custom_offset"  # 自定义偏移
    MASK_CENTROID = "mask_centroid"  # 分割掩码质心

class DetectionProcessor:
    """检测结果处理器"""
    
    def __init__(self):
        """初始化处理器"""
        self.targets: List[DetectionTarget] = []
        self.screen_scale_factor = 1.0  # DPI缩放因子
        self.coordinate_offset = (0, 0)  # 全局坐标偏移
        
    def process_yolo_results(self, results: Any, image_shape: Tuple[int, int]) -> List[DetectionTarget]:
        """
        处理YOLO检测结果
        
        Args:
            results: YOLO检测结果
            image_shape: 图像尺寸 (height, width)
            
        Returns:
            检测目标列表
        """
        targets = []
        
        try:
            # 处理检测框结果
            if hasattr(results, 'boxes') and results.boxes is not None:
                boxes = results.boxes
                
                for i in range(len(boxes)):
                    # 获取边界框坐标
                    bbox = boxes.xyxy[i].cpu().numpy()  # [x1, y1, x2, y2]
                    confidence = float(boxes.conf[i].cpu().numpy())
                    class_id = int(boxes.cls[i].cpu().numpy())
                    
                    # 转换为 (x, y, width, height) 格式
                    x, y, x2, y2 = bbox
                    width = x2 - x
                    height = y2 - y
                    
                    # 计算中心点和推荐点击位置
                    center_x = int(x + width / 2)
                    center_y = int(y + height / 2)
                    
                    # 创建检测目标
                    target = DetectionTarget(
                        id=f"target_{i}",
                        label=f"object_{class_id}",
                        confidence=confidence,
                        bbox=(int(x), int(y), int(width), int(height)),
                        center=(center_x, center_y),
                        click_point=(center_x, center_y)
                    )
                    
                    targets.append(target)
            
            # 处理分割结果（如果有）
            if hasattr(results, 'masks') and results.masks is not None:
                self._process_segmentation_masks(targets, results.masks)
                
        except Exception as e:
            print(f"处理YOLO结果失败: {e}")
            
        self.targets = targets
        return targets
    
    def _process_segmentation_masks(self, targets: List[DetectionTarget], masks: Any) -> None:
        """处理分割掩码"""
        try:
            for i, target in enumerate(targets):
                if i < len(masks.data):
                    mask = masks.data[i].cpu().numpy()
                    target.segmentation_mask = mask
                    
                    # 基于分割掩码计算更精确的点击位置
                    if mask.sum() > 0:
                        # 计算掩码质心
                        y_coords, x_coords = np.where(mask > 0.5)
                        if len(x_coords) > 0 and len(y_coords) > 0:
                            centroid_x = int(np.mean(x_coords))
                            centroid_y = int(np.mean(y_coords))
                            target.click_point = (centroid_x, centroid_y)
                            
        except Exception as e:
            print(f"处理分割掩码失败: {e}")
    
    def adjust_coordinates_for_screen(self, screenshot_region: Tuple[int, int, int, int] = None) -> None:
        """
        调整坐标以适应屏幕坐标系
        
        Args:
            screenshot_region: 截图区域 (x, y, width, height)，如果为None则为全屏
        """
        if screenshot_region:
            offset_x, offset_y = screenshot_region[0], screenshot_region[1]
            self.coordinate_offset = (offset_x, offset_y)
            
            # 更新所有目标的坐标
            for target in self.targets:
                # 更新边界框
                x, y, w, h = target.bbox
                target.bbox = (x + offset_x, y + offset_y, w, h)
                
                # 更新中心点
                center_x, center_y = target.center
                target.center = (center_x + offset_x, center_y + offset_y)
                
                # 更新点击位置
                click_x, click_y = target.click_point
                target.click_point = (click_x + offset_x, click_y + offset_y)
    
    def apply_dpi_scaling(self, scale_factor: float) -> None:
        """应用DPI缩放"""
        self.screen_scale_factor = scale_factor
        
        for target in self.targets:
            # 缩放边界框
            x, y, w, h = target.bbox
            target.bbox = (
                int(x * scale_factor),
                int(y * scale_factor),
                int(w * scale_factor),
                int(h * scale_factor)
            )
            
            # 缩放中心点
            center_x, center_y = target.center
            target.center = (
                int(center_x * scale_factor),
                int(center_y * scale_factor)
            )
            
            # 缩放点击位置
            click_x, click_y = target.click_point
            target.click_point = (
                int(click_x * scale_factor),
                int(click_y * scale_factor)
            )
    
    def get_target_by_id(self, target_id: str) -> Optional[DetectionTarget]:
        """根据ID获取目标"""
        for target in self.targets:
            if target.id == target_id:
                return target
        return None
    
    def get_targets_by_confidence(self, min_confidence: float = 0.5) -> List[DetectionTarget]:
        """根据置信度筛选目标"""
        return [target for target in self.targets if target.confidence >= min_confidence]
    
    def get_targets_in_region(self, region: Tuple[int, int, int, int]) -> List[DetectionTarget]:
        """获取指定区域内的目标"""
        region_x, region_y, region_w, region_h = region
        targets_in_region = []
        
        for target in self.targets:
            target_x, target_y = target.center
            if (region_x <= target_x <= region_x + region_w and 
                region_y <= target_y <= region_y + region_h):
                targets_in_region.append(target)
                
        return targets_in_region
    
    def calculate_click_point(self, target: DetectionTarget, strategy: ClickStrategy, 
                            custom_offset: Tuple[int, int] = (0, 0)) -> Tuple[int, int]:
        """
        计算点击位置
        
        Args:
            target: 检测目标
            strategy: 点击策略
            custom_offset: 自定义偏移量
            
        Returns:
            点击坐标 (x, y)
        """
        if strategy == ClickStrategy.CENTER:
            return target.center
        
        elif strategy == ClickStrategy.TOP_LEFT:
            x, y, _, _ = target.bbox
            return (x + 5, y + 5)  # 稍微偏移避免边界
        
        elif strategy == ClickStrategy.CUSTOM_OFFSET:
            center_x, center_y = target.center
            offset_x, offset_y = custom_offset
            return (center_x + offset_x, center_y + offset_y)
        
        elif strategy == ClickStrategy.MASK_CENTROID:
            if target.segmentation_mask is not None:
                return target.click_point  # 已经基于掩码计算
            else:
                return target.center  # 回退到中心点
        
        else:
            return target.center
    
    def get_detection_summary(self) -> Dict[str, Any]:
        """获取检测结果摘要"""
        if not self.targets:
            return {"total": 0, "high_confidence": 0, "avg_confidence": 0.0}
        
        total = len(self.targets)
        high_confidence = len([t for t in self.targets if t.confidence >= 0.8])
        avg_confidence = sum(t.confidence for t in self.targets) / total
        
        return {
            "total": total,
            "high_confidence": high_confidence,
            "avg_confidence": round(avg_confidence, 3),
            "confidence_range": (
                min(t.confidence for t in self.targets),
                max(t.confidence for t in self.targets)
            )
        }
