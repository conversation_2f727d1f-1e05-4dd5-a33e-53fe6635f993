# -*- coding: utf-8 -*-
"""
屏幕截取服务
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

import time
import threading
from typing import List, Optional, Dict, Any
import numpy as np
from PIL import Image, ImageGrab
from collections import deque

try:
    import cv2
    CV2_AVAILABLE = True
except ImportError:
    CV2_AVAILABLE = False

try:
    import mss
    MSS_AVAILABLE = True
except ImportError:
    MSS_AVAILABLE = False

try:
    import win32gui
    import win32con
    import win32api
    WIN32_AVAILABLE = True
except ImportError:
    WIN32_AVAILABLE = False

from ..utils.logger import Logger, performance_timer
from ..utils.data_structures import ScreenInfo
from ..utils.constants import DEFAULT_CACHE_SIZE


class ScreenCaptureService:
    """屏幕截取服务类"""
    
    def __init__(self,
                 cache_size: int = DEFAULT_CACHE_SIZE,
                 enable_cache: bool = True,
                 capture_method: str = "auto"):
        """
        初始化屏幕截取服务
        
        Args:
            cache_size: 缓存大小
            enable_cache: 是否启用缓存
            capture_method: 截图方法 ("auto", "pil", "mss", "win32")
        """
        self.logger = Logger().get_logger(__name__)
        
        # 配置参数
        self.cache_size = cache_size
        self.enable_cache = enable_cache
        self.capture_method = self._resolve_capture_method(capture_method)
        
        # 缓存系统
        self.screenshot_cache = deque(maxlen=cache_size) if enable_cache else None
        self.cache_lock = threading.Lock()
        
        # 屏幕信息
        self.monitors: List[ScreenInfo] = []
        self.primary_monitor: Optional[ScreenInfo] = None
        
        # 性能统计
        self.capture_times: List[float] = []
        self.total_captures = 0
        
        # 初始化
        self._detect_monitors()
        
        self.logger.info(f"屏幕截取服务初始化完成 - 方法: {self.capture_method}, 监视器数量: {len(self.monitors)}")
    
    def _resolve_capture_method(self, method: str) -> str:
        """解析截图方法"""
        if method == "auto":
            # 自动选择最佳方法
            if MSS_AVAILABLE:
                return "mss"
            elif WIN32_AVAILABLE:
                return "win32"
            else:
                return "pil"
        elif method == "mss" and not MSS_AVAILABLE:
            self.logger.warning("MSS不可用，回退到PIL")
            return "pil"
        elif method == "win32" and not WIN32_AVAILABLE:
            self.logger.warning("Win32不可用，回退到PIL")
            return "pil"
        else:
            return method
    
    def _detect_monitors(self) -> None:
        """检测显示器"""
        try:
            self.monitors.clear()
            
            if WIN32_AVAILABLE:
                self._detect_monitors_win32()
            else:
                self._detect_monitors_pil()
            
            # 找到主显示器
            for monitor in self.monitors:
                if monitor.is_primary:
                    self.primary_monitor = monitor
                    break
            
            if not self.primary_monitor and self.monitors:
                self.primary_monitor = self.monitors[0]
                self.primary_monitor.is_primary = True
            
            self.logger.info(f"检测到 {len(self.monitors)} 个显示器")
            
        except Exception as e:
            self.logger.error(f"显示器检测失败: {e}")
            # 创建默认显示器
            self._create_default_monitor()
    
    def _detect_monitors_win32(self) -> None:
        """使用Win32 API检测显示器"""
        def enum_callback(hmonitor, hdc, rect, data):
            # 忽略未使用的参数
            _ = hdc, rect, data
            monitor_info = win32api.GetMonitorInfo(hmonitor)
            monitor_rect = monitor_info['Monitor']
            
            screen_info = ScreenInfo(
                monitor_id=len(self.monitors),
                x=monitor_rect[0],
                y=monitor_rect[1],
                width=monitor_rect[2] - monitor_rect[0],
                height=monitor_rect[3] - monitor_rect[1],
                is_primary=monitor_info['Flags'] & win32con.MONITORINFOF_PRIMARY != 0,
                name=monitor_info.get('Device', f'Monitor {len(self.monitors)}')
            )
            
            self.monitors.append(screen_info)
            return True
        
        win32api.EnumDisplayMonitors(None, None, enum_callback, None)
    
    def _detect_monitors_pil(self) -> None:
        """使用PIL检测显示器（简单方法）"""
        # PIL只能获取主显示器信息
        try:
            with ImageGrab.grab() as img:
                screen_info = ScreenInfo(
                    monitor_id=0,
                    x=0,
                    y=0,
                    width=img.width,
                    height=img.height,
                    is_primary=True,
                    name="Primary Monitor"
                )
                self.monitors.append(screen_info)
        except Exception as e:
            self.logger.error(f"PIL显示器检测失败: {e}")
            self._create_default_monitor()
    
    def _create_default_monitor(self) -> None:
        """创建默认显示器"""
        default_monitor = ScreenInfo(
            monitor_id=0,
            x=0,
            y=0,
            width=1920,
            height=1080,
            is_primary=True,
            name="Default Monitor"
        )
        self.monitors = [default_monitor]
        self.primary_monitor = default_monitor
        self.logger.warning("使用默认显示器配置")
    
    @performance_timer("屏幕截图")
    def grab_fullscreen(self, monitor_id: Optional[int] = None) -> Optional[np.ndarray]:
        """
        全屏截图
        
        Args:
            monitor_id: 显示器ID，None表示主显示器
            
        Returns:
            Optional[np.ndarray]: 截图图像，失败返回None
        """
        try:
            start_time = time.time()
            
            # 选择显示器
            if monitor_id is None:
                monitor = self.primary_monitor
            else:
                monitor = self.get_monitor(monitor_id)
            
            if monitor is None:
                self.logger.error("无效的显示器")
                return None
            
            # 执行截图
            image = self._capture_screen(monitor.x, monitor.y, monitor.width, monitor.height)
            
            if image is not None:
                # 更新统计
                capture_time = time.time() - start_time
                self.capture_times.append(capture_time)
                self.total_captures += 1
                
                # 添加到缓存
                if self.enable_cache:
                    self._add_to_cache(image, monitor_id, "fullscreen")
                
                self.logger.debug(f"全屏截图完成 - 监视器: {monitor_id}, 耗时: {capture_time:.4f}s")
            
            return image
            
        except Exception as e:
            self.logger.error(f"全屏截图失败: {e}")
            return None
    
    @performance_timer("区域截图")
    def grab_region(self, x: int, y: int, width: int, height: int) -> Optional[np.ndarray]:
        """
        区域截图
        
        Args:
            x: 左上角x坐标
            y: 左上角y坐标
            width: 宽度
            height: 高度
            
        Returns:
            Optional[np.ndarray]: 截图图像，失败返回None
        """
        try:
            start_time = time.time()
            
            # 验证坐标
            if width <= 0 or height <= 0:
                self.logger.error("无效的区域尺寸")
                return None
            
            # 执行截图
            image = self._capture_screen(x, y, width, height)
            
            if image is not None:
                # 更新统计
                capture_time = time.time() - start_time
                self.capture_times.append(capture_time)
                self.total_captures += 1
                
                # 添加到缓存
                if self.enable_cache:
                    self._add_to_cache(image, None, f"region_{x}_{y}_{width}_{height}")
                
                self.logger.debug(f"区域截图完成 - 区域: ({x},{y},{width},{height}), 耗时: {capture_time:.4f}s")
            
            return image
            
        except Exception as e:
            self.logger.error(f"区域截图失败: {e}")
            return None
    
    def _capture_screen(self, x: int, y: int, width: int, height: int) -> Optional[np.ndarray]:
        """执行屏幕截图"""
        try:
            if self.capture_method == "mss":
                return self._capture_with_mss(x, y, width, height)
            elif self.capture_method == "win32":
                return self._capture_with_win32(x, y, width, height)
            else:
                return self._capture_with_pil(x, y, width, height)
                
        except Exception as e:
            self.logger.error(f"截图执行失败: {e}")
            return None
    
    def _capture_with_mss(self, x: int, y: int, width: int, height: int) -> Optional[np.ndarray]:
        """使用MSS截图"""
        try:
            with mss.mss() as sct:
                monitor = {"top": y, "left": x, "width": width, "height": height}
                screenshot = sct.grab(monitor)
                
                # 转换为numpy数组
                image = np.array(screenshot)
                # MSS返回BGRA格式，转换为BGR
                if CV2_AVAILABLE:
                    image = cv2.cvtColor(image, cv2.COLOR_BGRA2BGR)
                else:
                    # 手动转换BGRA到BGR
                    image = image[:, :, [2, 1, 0]]  # BGR
                
                return image
                
        except Exception as e:
            self.logger.error(f"MSS截图失败: {e}")
            return None
    
    def _capture_with_win32(self, x: int, y: int, width: int, height: int) -> Optional[np.ndarray]:
        """使用Win32 API截图"""
        try:
            import win32ui
            import win32gui
            
            # 获取桌面设备上下文
            hdesktop = win32gui.GetDesktopWindow()
            desktop_dc = win32gui.GetWindowDC(hdesktop)
            img_dc = win32ui.CreateDCFromHandle(desktop_dc)
            mem_dc = img_dc.CreateCompatibleDC()
            
            # 创建位图
            screenshot = win32ui.CreateBitmap()
            screenshot.CreateCompatibleBitmap(img_dc, width, height)
            mem_dc.SelectObject(screenshot)
            
            # 复制屏幕内容
            mem_dc.BitBlt((0, 0), (width, height), img_dc, (x, y), win32con.SRCCOPY)
            
            # 获取位图数据
            bmpstr = screenshot.GetBitmapBits(True)

            # 转换为numpy数组
            image = np.frombuffer(bmpstr, dtype=np.uint8)
            image = image.reshape((height, width, 4))  # BGRA格式
            if CV2_AVAILABLE:
                image = cv2.cvtColor(image, cv2.COLOR_BGRA2BGR)
            else:
                # 手动转换BGRA到BGR
                image = image[:, :, [2, 1, 0]]  # BGR
            
            # 清理资源
            mem_dc.DeleteDC()
            img_dc.DeleteDC()
            win32gui.ReleaseDC(hdesktop, desktop_dc)
            win32gui.DeleteObject(screenshot.GetHandle())
            
            return image
            
        except Exception as e:
            self.logger.error(f"Win32截图失败: {e}")
            return None
    
    def _capture_with_pil(self, x: int, y: int, width: int, height: int) -> Optional[np.ndarray]:
        """使用PIL截图"""
        try:
            # PIL截图
            bbox = (x, y, x + width, y + height)
            with ImageGrab.grab(bbox=bbox) as pil_image:
                # 转换为numpy数组
                image = np.array(pil_image)
                
                # 如果是RGBA，转换为BGR
                if image.shape[2] == 4:
                    if CV2_AVAILABLE:
                        image = cv2.cvtColor(image, cv2.COLOR_RGBA2BGR)
                    else:
                        # 手动转换RGBA到BGR
                        image = image[:, :, [2, 1, 0]]  # BGR，忽略Alpha通道
                elif image.shape[2] == 3:
                    if CV2_AVAILABLE:
                        image = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
                    else:
                        # 手动转换RGB到BGR
                        image = image[:, :, [2, 1, 0]]  # BGR
                
                return image
                
        except Exception as e:
            self.logger.error(f"PIL截图失败: {e}")
            return None

    def _add_to_cache(self, image: np.ndarray, monitor_id: Optional[int], cache_key: str) -> None:
        """添加截图到缓存"""
        if not self.enable_cache or self.screenshot_cache is None:
            return

        try:
            with self.cache_lock:
                cache_entry = {
                    "image": image.copy(),
                    "monitor_id": monitor_id,
                    "key": cache_key,
                    "timestamp": time.time()
                }
                self.screenshot_cache.append(cache_entry)

        except Exception as e:
            self.logger.error(f"添加缓存失败: {e}")

    def get_cached_screenshot(self, cache_key: str, max_age: float = 1.0) -> Optional[np.ndarray]:
        """
        获取缓存的截图

        Args:
            cache_key: 缓存键
            max_age: 最大缓存时间（秒）

        Returns:
            Optional[np.ndarray]: 缓存的图像，不存在或过期返回None
        """
        if not self.enable_cache or self.screenshot_cache is None:
            return None

        try:
            current_time = time.time()

            with self.cache_lock:
                for entry in reversed(self.screenshot_cache):
                    if (entry["key"] == cache_key and
                        current_time - entry["timestamp"] <= max_age):
                        return entry["image"].copy()

            return None

        except Exception as e:
            self.logger.error(f"获取缓存失败: {e}")
            return None

    def clear_cache(self) -> None:
        """清空缓存"""
        if self.screenshot_cache is not None:
            with self.cache_lock:
                self.screenshot_cache.clear()
            self.logger.info("截图缓存已清空")

    def get_monitors(self) -> List[ScreenInfo]:
        """
        获取所有显示器信息

        Returns:
            List[ScreenInfo]: 显示器列表
        """
        return self.monitors.copy()

    def get_monitor(self, monitor_id: int) -> Optional[ScreenInfo]:
        """
        获取指定显示器信息

        Args:
            monitor_id: 显示器ID

        Returns:
            Optional[ScreenInfo]: 显示器信息，不存在返回None
        """
        for monitor in self.monitors:
            if monitor.monitor_id == monitor_id:
                return monitor
        return None

    def get_primary_monitor(self) -> Optional[ScreenInfo]:
        """
        获取主显示器信息

        Returns:
            Optional[ScreenInfo]: 主显示器信息
        """
        return self.primary_monitor

    def refresh_monitors(self) -> bool:
        """
        刷新显示器信息

        Returns:
            bool: 刷新是否成功
        """
        try:
            old_count = len(self.monitors)
            self._detect_monitors()
            new_count = len(self.monitors)

            self.logger.info(f"显示器信息已刷新: {old_count} -> {new_count}")
            return True

        except Exception as e:
            self.logger.error(f"刷新显示器信息失败: {e}")
            return False

    def grab_window(self, window_title: str) -> Optional[np.ndarray]:
        """
        截取指定窗口

        Args:
            window_title: 窗口标题

        Returns:
            Optional[np.ndarray]: 窗口截图，失败返回None
        """
        if not WIN32_AVAILABLE:
            self.logger.error("Win32不可用，无法截取窗口")
            return None

        try:
            # 查找窗口
            hwnd = win32gui.FindWindow(None, window_title)
            if hwnd == 0:
                self.logger.error(f"未找到窗口: {window_title}")
                return None

            # 获取窗口位置和大小
            rect = win32gui.GetWindowRect(hwnd)
            x, y, x2, y2 = rect
            width = x2 - x
            height = y2 - y

            if width <= 0 or height <= 0:
                self.logger.error("窗口尺寸无效")
                return None

            # 截取窗口
            return self.grab_region(x, y, width, height)

        except Exception as e:
            self.logger.error(f"窗口截图失败: {e}")
            return None

    def grab_active_window(self) -> Optional[np.ndarray]:
        """
        截取当前活动窗口

        Returns:
            Optional[np.ndarray]: 活动窗口截图，失败返回None
        """
        if not WIN32_AVAILABLE:
            self.logger.error("Win32不可用，无法截取活动窗口")
            return None

        try:
            # 获取活动窗口
            hwnd = win32gui.GetForegroundWindow()
            if hwnd == 0:
                self.logger.error("未找到活动窗口")
                return None

            # 获取窗口位置和大小
            rect = win32gui.GetWindowRect(hwnd)
            x, y, x2, y2 = rect
            width = x2 - x
            height = y2 - y

            if width <= 0 or height <= 0:
                self.logger.error("活动窗口尺寸无效")
                return None

            # 截取窗口
            return self.grab_region(x, y, width, height)

        except Exception as e:
            self.logger.error(f"活动窗口截图失败: {e}")
            return None

    def save_screenshot(self, image: np.ndarray, file_path: str, quality: int = 95) -> bool:
        """
        保存截图

        Args:
            image: 图像数据
            file_path: 保存路径
            quality: 图像质量（1-100）

        Returns:
            bool: 保存是否成功
        """
        try:
            from pathlib import Path

            file_path = Path(file_path)
            file_path.parent.mkdir(parents=True, exist_ok=True)

            # 转换颜色格式
            if image.shape[2] == 3:
                # BGR转RGB
                if CV2_AVAILABLE:
                    image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                else:
                    # 手动转换BGR到RGB
                    image_rgb = image[:, :, [2, 1, 0]]  # RGB
            else:
                image_rgb = image

            # 保存图像
            pil_image = Image.fromarray(image_rgb)

            if file_path.suffix.lower() in ['.jpg', '.jpeg']:
                pil_image.save(file_path, 'JPEG', quality=quality)
            else:
                pil_image.save(file_path, 'PNG')

            self.logger.info(f"截图保存成功: {file_path}")
            return True

        except Exception as e:
            self.logger.error(f"保存截图失败: {e}")
            return False

    def get_performance_stats(self) -> Dict[str, Any]:
        """
        获取性能统计信息

        Returns:
            Dict[str, Any]: 性能统计
        """
        if not self.capture_times:
            return {}

        return {
            "total_captures": len(self.capture_times),
            "avg_capture_time": np.mean(self.capture_times),
            "min_capture_time": np.min(self.capture_times),
            "max_capture_time": np.max(self.capture_times),
            "avg_fps": 1.0 / np.mean(self.capture_times) if self.capture_times else 0,
            "cache_size": len(self.screenshot_cache) if self.screenshot_cache else 0,
            "cache_enabled": self.enable_cache,
            "capture_method": self.capture_method,
            "monitors_count": len(self.monitors)
        }

    def reset_stats(self) -> None:
        """重置性能统计"""
        self.capture_times.clear()
        self.total_captures = 0
        self.logger.info("截图性能统计已重置")

    def update_config(self, **kwargs) -> None:
        """
        更新配置

        Args:
            **kwargs: 配置参数
        """
        if "cache_size" in kwargs:
            self.cache_size = kwargs["cache_size"]
            if self.screenshot_cache is not None:
                # 重新创建缓存
                with self.cache_lock:
                    old_cache = list(self.screenshot_cache)
                    self.screenshot_cache = deque(maxlen=self.cache_size)
                    # 保留最新的缓存项
                    for item in old_cache[-self.cache_size:]:
                        self.screenshot_cache.append(item)

        if "enable_cache" in kwargs:
            self.enable_cache = kwargs["enable_cache"]
            if not self.enable_cache:
                self.clear_cache()
                self.screenshot_cache = None
            elif self.screenshot_cache is None:
                self.screenshot_cache = deque(maxlen=self.cache_size)

        if "capture_method" in kwargs:
            new_method = self._resolve_capture_method(kwargs["capture_method"])
            if new_method != self.capture_method:
                self.capture_method = new_method
                self.logger.info(f"截图方法已更新: {self.capture_method}")

        self.logger.info(f"截图服务配置已更新: {kwargs}")

    def __del__(self):
        """析构函数"""
        if hasattr(self, 'screenshot_cache') and self.screenshot_cache is not None:
            self.clear_cache()
