# -*- coding: utf-8 -*-
"""
截图辅助工具 - 提供截图和图像转换功能
作者: Cursor AI
创建时间: 2025-07-05
编码标准: UTF-8无BOM
"""

import time
from pathlib import Path
from typing import Optional, <PERSON>ple
import numpy as np

from .logger import Logger


class ScreenshotHelper:
    """截图辅助工具类"""
    
    def __init__(self):
        self.logger = Logger()
        self._screen_capture = None
        self._init_screen_capture()
    
    def _init_screen_capture(self):
        """初始化屏幕截图服务"""
        try:
            from ..core.screen_capture_v2 import ScreenCaptureServiceV2
            self._screen_capture = ScreenCaptureServiceV2()
            self.logger.info("截图辅助工具初始化完成")
        except Exception as e:
            self.logger.error(f"初始化截图服务失败: {e}")
    
    def take_screenshot(self, save_to_file: bool = True) -> Tuple[Optional[np.ndarray], Optional[str]]:
        """
        截取屏幕
        
        Args:
            save_to_file: 是否保存到文件
            
        Returns:
            (图像数组, 文件路径) 元组，失败时返回 (None, None)
        """
        try:
            if not self._screen_capture:
                self.logger.error("屏幕截图服务未初始化")
                return None, None
            
            # 截取屏幕
            image = self._screen_capture.capture_fullscreen()
            
            if image is None:
                self.logger.error("截取屏幕失败")
                return None, None
            
            filepath = None
            if save_to_file:
                # 生成文件名
                timestamp = int(time.time())
                filename = f"screenshot_{timestamp}.png"
                
                # 确保目录存在
                screenshots_dir = Path("screenshots")
                screenshots_dir.mkdir(exist_ok=True)
                
                filepath = screenshots_dir / filename
                
                # 保存截图
                if self._screen_capture.save_screenshot(image, str(filepath)):
                    self.logger.info(f"截图已保存: {filepath}")
                    filepath = str(filepath)
                else:
                    self.logger.error("保存截图失败")
                    filepath = None
            
            return image, filepath
            
        except Exception as e:
            self.logger.error(f"截图失败: {e}")
            return None, None
    
    def numpy_to_qpixmap(self, image: np.ndarray):
        """
        将numpy数组转换为QPixmap
        
        Args:
            image: BGR格式的图像数组
            
        Returns:
            QPixmap对象，失败时返回None
        """
        try:
            import cv2
            from PyQt6.QtGui import QImage, QPixmap
            
            # 转换BGR到RGB
            rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            height, width, channel = rgb_image.shape
            bytes_per_line = 3 * width

            # 确保内存连续性，避免显示问题
            rgb_image = np.ascontiguousarray(rgb_image)

            # 创建QImage
            q_image = QImage(
                rgb_image.data,
                width,
                height,
                bytes_per_line,
                QImage.Format.Format_RGB888
            )
            
            # 转换为QPixmap
            pixmap = QPixmap.fromImage(q_image)
            
            return pixmap
            
        except Exception as e:
            self.logger.error(f"图像转换失败: {e}")
            return None
    
    def take_screenshot_with_pixmap(self, save_to_file: bool = True) -> Tuple[Optional[np.ndarray], Optional[object], Optional[str]]:
        """
        截取屏幕并转换为QPixmap
        
        Args:
            save_to_file: 是否保存到文件
            
        Returns:
            (图像数组, QPixmap, 文件路径) 元组
        """
        try:
            # 截取屏幕
            image, filepath = self.take_screenshot(save_to_file)
            
            if image is None:
                return None, None, None
            
            # 转换为QPixmap
            pixmap = self.numpy_to_qpixmap(image)
            
            return image, pixmap, filepath
            
        except Exception as e:
            self.logger.error(f"截图并转换失败: {e}")
            return None, None, None
    
    def get_screen_size(self) -> Tuple[int, int]:
        """
        获取屏幕尺寸
        
        Returns:
            (宽度, 高度) 元组
        """
        try:
            if self._screen_capture:
                return self._screen_capture.get_screen_size()
            else:
                # 备用方法
                import tkinter as tk
                root = tk.Tk()
                width = root.winfo_screenwidth()
                height = root.winfo_screenheight()
                root.destroy()
                return width, height
                
        except Exception as e:
            self.logger.error(f"获取屏幕尺寸失败: {e}")
            return 1920, 1080  # 默认尺寸
    
    def capture_region(self, x: int, y: int, width: int, height: int, 
                      save_to_file: bool = True) -> Tuple[Optional[np.ndarray], Optional[str]]:
        """
        截取指定区域
        
        Args:
            x, y: 区域左上角坐标
            width, height: 区域宽高
            save_to_file: 是否保存到文件
            
        Returns:
            (图像数组, 文件路径) 元组
        """
        try:
            if not self._screen_capture:
                self.logger.error("屏幕截图服务未初始化")
                return None, None
            
            # 截取指定区域
            image = self._screen_capture.capture_region(x, y, width, height)
            
            if image is None:
                self.logger.error("截取区域失败")
                return None, None
            
            filepath = None
            if save_to_file:
                # 生成文件名
                timestamp = int(time.time())
                filename = f"region_{timestamp}_{x}_{y}_{width}x{height}.png"
                
                # 确保目录存在
                screenshots_dir = Path("screenshots")
                screenshots_dir.mkdir(exist_ok=True)
                
                filepath = screenshots_dir / filename
                
                # 保存截图
                if self._screen_capture.save_screenshot(image, str(filepath)):
                    self.logger.info(f"区域截图已保存: {filepath}")
                    filepath = str(filepath)
                else:
                    self.logger.error("保存区域截图失败")
                    filepath = None
            
            return image, filepath
            
        except Exception as e:
            self.logger.error(f"区域截图失败: {e}")
            return None, None
    
    def is_available(self) -> bool:
        """检查截图服务是否可用"""
        return self._screen_capture is not None
    
    def get_available_methods(self) -> list:
        """获取可用的截图方法"""
        try:
            if self._screen_capture:
                return self._screen_capture.available_methods
            else:
                return []
        except:
            return []


# 全局截图辅助实例
_screenshot_helper = None

def get_screenshot_helper() -> ScreenshotHelper:
    """获取全局截图辅助实例"""
    global _screenshot_helper
    if _screenshot_helper is None:
        _screenshot_helper = ScreenshotHelper()
    return _screenshot_helper
