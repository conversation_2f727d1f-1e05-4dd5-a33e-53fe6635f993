# -*- coding: utf-8 -*-
"""
可执行文件构建脚本
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
import argparse

def check_pyinstaller():
    """检查PyInstaller是否已安装"""
    try:
        import PyInstaller
        print(f"✓ PyInstaller 已安装 (版本: {PyInstaller.__version__})")
        return True
    except ImportError:
        print("✗ PyInstaller 未安装")
        print("请运行: pip install pyinstaller")
        return False

def create_spec_file(project_root: Path, output_dir: Path):
    """创建PyInstaller spec文件"""
    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

import sys
from pathlib import Path

# 项目路径
project_root = Path(r"{project_root}")
src_path = project_root / "src"

# 添加源码路径
sys.path.insert(0, str(src_path))

block_cipher = None

# 数据文件
datas = [
    (str(project_root / "configs"), "configs"),
    (str(project_root / "models"), "models"),
    (str(project_root / "src" / "yolo_opencv_detector" / "gui" / "icons"), "gui/icons"),
]

# 隐藏导入
hiddenimports = [
    'PyQt6.QtCore',
    'PyQt6.QtGui', 
    'PyQt6.QtWidgets',
    'ultralytics',
    'cv2',
    'numpy',
    'loguru',
    'yaml',
    'psutil',
    'mss',
    'PIL',
]

a = Analysis(
    [str(src_path / "yolo_opencv_detector" / "main.py")],
    pathex=[str(src_path)],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='YOLO_OpenCV_Detector',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=str(project_root / "src" / "yolo_opencv_detector" / "gui" / "icons" / "app_icon.ico") if (project_root / "src" / "yolo_opencv_detector" / "gui" / "icons" / "app_icon.ico").exists() else None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='YOLO_OpenCV_Detector',
)
'''
    
    spec_file = output_dir / "yolo_detector.spec"
    with open(spec_file, 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print(f"✓ 创建spec文件: {spec_file}")
    return spec_file

def build_executable(spec_file: Path, output_dir: Path, clean: bool = True):
    """构建可执行文件"""
    print("开始构建可执行文件...")
    
    # PyInstaller命令
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--distpath", str(output_dir / "dist"),
        "--workpath", str(output_dir / "build"),
        "--specpath", str(output_dir),
    ]
    
    if clean:
        cmd.append("--clean")
    
    cmd.append(str(spec_file))
    
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✓ 构建成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ 构建失败: {e}")
        print("标准输出:", e.stdout)
        print("标准错误:", e.stderr)
        return False

def create_installer_script(project_root: Path, output_dir: Path):
    """创建安装脚本"""
    installer_script = f'''@echo off
echo YOLO OpenCV 屏幕识别工具 - 安装程序
echo =====================================

set INSTALL_DIR=%PROGRAMFILES%\\YOLO_OpenCV_Detector
set DESKTOP_SHORTCUT=%USERPROFILE%\\Desktop\\YOLO OpenCV 检测器.lnk

echo 正在安装到: %INSTALL_DIR%

REM 创建安装目录
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

REM 复制文件
echo 复制程序文件...
xcopy /E /I /Y "YOLO_OpenCV_Detector" "%INSTALL_DIR%"

REM 创建桌面快捷方式
echo 创建桌面快捷方式...
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%DESKTOP_SHORTCUT%'); $Shortcut.TargetPath = '%INSTALL_DIR%\\YOLO_OpenCV_Detector.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Save()"

REM 创建开始菜单快捷方式
set START_MENU=%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs
echo 创建开始菜单快捷方式...
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%START_MENU%\\YOLO OpenCV 检测器.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\\YOLO_OpenCV_Detector.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Save()"

echo.
echo 安装完成！
echo 程序已安装到: %INSTALL_DIR%
echo 桌面快捷方式已创建
echo.
pause
'''
    
    installer_file = output_dir / "install.bat"
    with open(installer_file, 'w', encoding='gbk') as f:  # 使用GBK编码以支持中文
        f.write(installer_script)
    
    print(f"✓ 创建安装脚本: {installer_file}")
    return installer_file

def create_uninstaller_script(output_dir: Path):
    """创建卸载脚本"""
    uninstaller_script = '''@echo off
echo YOLO OpenCV 屏幕识别工具 - 卸载程序
echo =====================================

set INSTALL_DIR=%PROGRAMFILES%\\YOLO_OpenCV_Detector
set DESKTOP_SHORTCUT=%USERPROFILE%\\Desktop\\YOLO OpenCV 检测器.lnk
set START_MENU_SHORTCUT=%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs\\YOLO OpenCV 检测器.lnk

echo 正在卸载...

REM 删除桌面快捷方式
if exist "%DESKTOP_SHORTCUT%" del "%DESKTOP_SHORTCUT%"

REM 删除开始菜单快捷方式
if exist "%START_MENU_SHORTCUT%" del "%START_MENU_SHORTCUT%"

REM 删除安装目录
if exist "%INSTALL_DIR%" (
    echo 删除程序文件...
    rmdir /S /Q "%INSTALL_DIR%"
)

echo.
echo 卸载完成！
echo.
pause
'''
    
    uninstaller_file = output_dir / "dist" / "YOLO_OpenCV_Detector" / "uninstall.bat"
    uninstaller_file.parent.mkdir(parents=True, exist_ok=True)
    
    with open(uninstaller_file, 'w', encoding='gbk') as f:
        f.write(uninstaller_script)
    
    print(f"✓ 创建卸载脚本: {uninstaller_file}")
    return uninstaller_file

def copy_additional_files(project_root: Path, dist_dir: Path):
    """复制额外的文件"""
    print("复制额外文件...")
    
    # 复制README
    readme_src = project_root / "README.md"
    if readme_src.exists():
        shutil.copy2(readme_src, dist_dir / "README.txt")
        print("✓ 复制 README.md")
    
    # 复制许可证
    license_src = project_root / "LICENSE"
    if license_src.exists():
        shutil.copy2(license_src, dist_dir / "LICENSE.txt")
        print("✓ 复制 LICENSE")
    
    # 创建版本信息文件
    version_info = f"""YOLO OpenCV 屏幕识别工具
版本: 1.0.0
构建时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Python版本: {sys.version}

使用说明:
1. 双击 YOLO_OpenCV_Detector.exe 启动程序
2. 首次运行会自动下载YOLO模型文件
3. 在界面中配置检测参数
4. 点击"开始检测"开始屏幕识别

技术支持:
- 项目主页: https://github.com/your-username/yolo-opencv-detector
- 问题反馈: https://github.com/your-username/yolo-opencv-detector/issues

卸载程序:
运行 uninstall.bat 可以完全卸载本程序
"""
    
    version_file = dist_dir / "版本信息.txt"
    with open(version_file, 'w', encoding='utf-8') as f:
        f.write(version_info)
    
    print("✓ 创建版本信息文件")

def create_portable_package(output_dir: Path):
    """创建便携版压缩包"""
    try:
        import zipfile
        
        dist_dir = output_dir / "dist" / "YOLO_OpenCV_Detector"
        if not dist_dir.exists():
            print("✗ 找不到构建输出目录")
            return False
        
        zip_file = output_dir / "YOLO_OpenCV_Detector_Portable.zip"
        
        print("创建便携版压缩包...")
        with zipfile.ZipFile(zip_file, 'w', zipfile.ZIP_DEFLATED) as zf:
            for file_path in dist_dir.rglob('*'):
                if file_path.is_file():
                    arc_name = file_path.relative_to(dist_dir.parent)
                    zf.write(file_path, arc_name)
        
        print(f"✓ 便携版创建完成: {zip_file}")
        return True
        
    except ImportError:
        print("✗ zipfile模块不可用，跳过便携版创建")
        return False
    except Exception as e:
        print(f"✗ 创建便携版失败: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="YOLO OpenCV检测器可执行文件构建工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python build_executable.py                    # 构建可执行文件
  python build_executable.py --clean            # 清理后重新构建
  python build_executable.py --portable         # 同时创建便携版
  python build_executable.py --output ./build   # 指定输出目录
        """
    )
    
    parser.add_argument(
        "--output", "-o",
        type=str,
        default="./build",
        help="输出目录"
    )
    
    parser.add_argument(
        "--clean", "-c",
        action="store_true",
        help="清理后重新构建"
    )
    
    parser.add_argument(
        "--portable", "-p",
        action="store_true",
        help="创建便携版压缩包"
    )
    
    parser.add_argument(
        "--no-installer",
        action="store_true",
        help="不创建安装脚本"
    )
    
    args = parser.parse_args()
    
    # 检查依赖
    if not check_pyinstaller():
        return 1
    
    # 确定路径
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    output_dir = Path(args.output).resolve()
    
    print(f"项目根目录: {project_root}")
    print(f"输出目录: {output_dir}")
    
    # 创建输出目录
    output_dir.mkdir(parents=True, exist_ok=True)
    
    try:
        # 创建spec文件
        spec_file = create_spec_file(project_root, output_dir)
        
        # 构建可执行文件
        if not build_executable(spec_file, output_dir, args.clean):
            return 1
        
        # 复制额外文件
        dist_dir = output_dir / "dist" / "YOLO_OpenCV_Detector"
        copy_additional_files(project_root, dist_dir)
        
        # 创建卸载脚本
        create_uninstaller_script(output_dir)
        
        # 创建安装脚本
        if not args.no_installer:
            create_installer_script(project_root, output_dir / "dist")
        
        # 创建便携版
        if args.portable:
            create_portable_package(output_dir)
        
        print(f"\n{'='*60}")
        print("构建完成！")
        print(f"{'='*60}")
        print(f"可执行文件: {dist_dir / 'YOLO_OpenCV_Detector.exe'}")
        if not args.no_installer:
            print(f"安装程序: {output_dir / 'dist' / 'install.bat'}")
        if args.portable:
            print(f"便携版: {output_dir / 'YOLO_OpenCV_Detector_Portable.zip'}")
        print(f"{'='*60}")
        
        return 0
        
    except Exception as e:
        print(f"构建失败: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
