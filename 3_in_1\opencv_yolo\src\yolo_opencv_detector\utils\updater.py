# -*- coding: utf-8 -*-
"""
自动更新系统
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

import os
import sys
import json
import time
import hashlib
import requests
import subprocess
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple
from packaging import version
import threading

from .logger import Logger
from .config_manager import ConfigManager


class UpdateChecker:
    """更新检查器类"""
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化更新检查器
        
        Args:
            config_manager: 配置管理器
        """
        self.logger = Logger().get_logger(__name__)
        self.config_manager = config_manager
        
        # 版本信息
        self.current_version = "1.0.0"
        self.latest_version = None
        self.version_info = {}
        
        # 更新配置
        self.update_server_url = "https://api.github.com/repos/your-username/yolo-opencv-detector"
        self.check_interval = 24 * 3600  # 24小时检查一次
        self.auto_check_enabled = True
        self.auto_download_enabled = False
        self.beta_updates_enabled = False
        
        # 更新状态
        self.is_checking = False
        self.is_downloading = False
        self.last_check_time = 0
        self.download_progress = 0
        
        # 文件路径
        self.app_dir = Path(sys.executable).parent if getattr(sys, 'frozen', False) else Path(__file__).parent.parent.parent
        self.temp_dir = self.app_dir / "temp"
        self.backup_dir = self.app_dir / "backup"
        
        # 创建必要目录
        self.temp_dir.mkdir(exist_ok=True)
        self.backup_dir.mkdir(exist_ok=True)
        
        self.logger.info("更新检查器初始化完成")
    
    def check_for_updates(self, force: bool = False) -> Optional[Dict[str, Any]]:
        """
        检查更新
        
        Args:
            force: 是否强制检查
            
        Returns:
            Optional[Dict[str, Any]]: 更新信息
        """
        try:
            current_time = time.time()
            
            # 检查是否需要更新检查
            if not force and (current_time - self.last_check_time) < self.check_interval:
                self.logger.debug("距离上次检查时间过短，跳过检查")
                return None
            
            if self.is_checking:
                self.logger.debug("正在检查更新中")
                return None
            
            self.is_checking = True
            self.last_check_time = current_time
            
            try:
                # 获取最新版本信息
                version_info = self._fetch_latest_version()
                
                if version_info:
                    self.latest_version = version_info.get("tag_name", "").lstrip("v")
                    self.version_info = version_info
                    
                    # 比较版本
                    if self._is_newer_version(self.latest_version, self.current_version):
                        self.logger.info(f"发现新版本: {self.latest_version} (当前: {self.current_version})")
                        return version_info
                    else:
                        self.logger.info("当前已是最新版本")
                        return None
                
            finally:
                self.is_checking = False
            
            return None
            
        except Exception as e:
            self.logger.error(f"检查更新失败: {e}")
            self.is_checking = False
            return None
    
    def _fetch_latest_version(self) -> Optional[Dict[str, Any]]:
        """获取最新版本信息"""
        try:
            # 从GitHub API获取最新发布信息
            api_url = f"{self.update_server_url}/releases/latest"
            
            headers = {
                "Accept": "application/vnd.github.v3+json",
                "User-Agent": "YOLO-OpenCV-Detector-Updater"
            }
            
            response = requests.get(api_url, headers=headers, timeout=30)
            response.raise_for_status()
            
            release_info = response.json()
            
            # 检查是否为预发布版本
            if release_info.get("prerelease", False) and not self.beta_updates_enabled:
                self.logger.debug("跳过预发布版本")
                return None
            
            return release_info
            
        except requests.RequestException as e:
            self.logger.error(f"获取版本信息失败: {e}")
            return None
        except Exception as e:
            self.logger.error(f"解析版本信息失败: {e}")
            return None
    
    def _is_newer_version(self, latest: str, current: str) -> bool:
        """比较版本号"""
        try:
            return version.parse(latest) > version.parse(current)
        except Exception as e:
            self.logger.error(f"版本比较失败: {e}")
            return False
    
    def download_update(self, 
                       version_info: Dict[str, Any],
                       progress_callback: Optional[callable] = None) -> Optional[str]:
        """
        下载更新
        
        Args:
            version_info: 版本信息
            progress_callback: 进度回调函数
            
        Returns:
            Optional[str]: 下载文件路径
        """
        try:
            if self.is_downloading:
                self.logger.warning("正在下载更新中")
                return None
            
            self.is_downloading = True
            self.download_progress = 0
            
            try:
                # 查找适合的下载资源
                download_url = self._find_download_asset(version_info)
                if not download_url:
                    self.logger.error("未找到适合的下载资源")
                    return None
                
                # 下载文件
                filename = download_url.split("/")[-1]
                download_path = self.temp_dir / filename
                
                self.logger.info(f"开始下载更新: {download_url}")
                
                response = requests.get(download_url, stream=True, timeout=60)
                response.raise_for_status()
                
                total_size = int(response.headers.get('content-length', 0))
                downloaded_size = 0
                
                with open(download_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                            downloaded_size += len(chunk)
                            
                            # 更新进度
                            if total_size > 0:
                                self.download_progress = (downloaded_size / total_size) * 100
                                if progress_callback:
                                    progress_callback(self.download_progress)
                
                # 验证下载文件
                if self._verify_download(download_path, version_info):
                    self.logger.info(f"更新下载完成: {download_path}")
                    return str(download_path)
                else:
                    self.logger.error("下载文件验证失败")
                    download_path.unlink()
                    return None
                
            finally:
                self.is_downloading = False
                self.download_progress = 0
            
        except Exception as e:
            self.logger.error(f"下载更新失败: {e}")
            self.is_downloading = False
            return None
    
    def _find_download_asset(self, version_info: Dict[str, Any]) -> Optional[str]:
        """查找下载资源"""
        try:
            assets = version_info.get("assets", [])
            
            # 根据平台选择合适的资源
            platform = sys.platform
            architecture = "x64" if sys.maxsize > 2**32 else "x86"
            
            # 优先级列表
            preferred_patterns = []
            
            if platform == "win32":
                preferred_patterns = [
                    f"windows_{architecture}.exe",
                    f"win_{architecture}.exe",
                    "windows.exe",
                    "win.exe",
                    ".exe"
                ]
            elif platform == "darwin":
                preferred_patterns = [
                    "macos.dmg",
                    "mac.dmg",
                    "darwin.dmg",
                    ".dmg"
                ]
            elif platform.startswith("linux"):
                preferred_patterns = [
                    f"linux_{architecture}.tar.gz",
                    "linux.tar.gz",
                    ".tar.gz",
                    ".deb",
                    ".rpm"
                ]
            
            # 查找匹配的资源
            for pattern in preferred_patterns:
                for asset in assets:
                    asset_name = asset.get("name", "").lower()
                    if pattern.lower() in asset_name:
                        return asset.get("browser_download_url")
            
            # 如果没有找到特定平台的资源，返回第一个
            if assets:
                return assets[0].get("browser_download_url")
            
            return None
            
        except Exception as e:
            self.logger.error(f"查找下载资源失败: {e}")
            return None
    
    def _verify_download(self, file_path: Path, version_info: Dict[str, Any]) -> bool:
        """验证下载文件"""
        try:
            # 检查文件大小
            if not file_path.exists() or file_path.stat().st_size == 0:
                return False
            
            # 如果有校验和信息，进行验证
            assets = version_info.get("assets", [])
            filename = file_path.name
            
            for asset in assets:
                if asset.get("name") == filename:
                    # 这里可以添加SHA256校验逻辑
                    # expected_hash = asset.get("sha256")
                    # if expected_hash:
                    #     return self._verify_file_hash(file_path, expected_hash)
                    break
            
            # 基本验证通过
            return True
            
        except Exception as e:
            self.logger.error(f"验证下载文件失败: {e}")
            return False
    
    def _verify_file_hash(self, file_path: Path, expected_hash: str) -> bool:
        """验证文件哈希"""
        try:
            sha256_hash = hashlib.sha256()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    sha256_hash.update(chunk)
            
            actual_hash = sha256_hash.hexdigest()
            return actual_hash.lower() == expected_hash.lower()
            
        except Exception as e:
            self.logger.error(f"计算文件哈希失败: {e}")
            return False
    
    def install_update(self, update_file: str) -> bool:
        """
        安装更新
        
        Args:
            update_file: 更新文件路径
            
        Returns:
            bool: 是否成功安装
        """
        try:
            update_path = Path(update_file)
            if not update_path.exists():
                self.logger.error(f"更新文件不存在: {update_file}")
                return False
            
            # 创建备份
            if not self._create_backup():
                self.logger.error("创建备份失败")
                return False
            
            # 根据文件类型执行不同的安装逻辑
            if update_path.suffix.lower() == ".exe":
                return self._install_exe_update(update_path)
            elif update_path.suffix.lower() in [".tar.gz", ".zip"]:
                return self._install_archive_update(update_path)
            else:
                self.logger.error(f"不支持的更新文件格式: {update_path.suffix}")
                return False
                
        except Exception as e:
            self.logger.error(f"安装更新失败: {e}")
            return False
    
    def _create_backup(self) -> bool:
        """创建当前版本备份"""
        try:
            import shutil
            
            backup_name = f"backup_{self.current_version}_{int(time.time())}"
            backup_path = self.backup_dir / backup_name
            
            # 备份主要文件
            if getattr(sys, 'frozen', False):
                # 可执行文件模式
                exe_path = Path(sys.executable)
                shutil.copy2(exe_path, backup_path.with_suffix('.exe'))
            else:
                # 源码模式
                src_dir = Path(__file__).parent.parent
                shutil.copytree(src_dir, backup_path, ignore=shutil.ignore_patterns('__pycache__', '*.pyc'))
            
            self.logger.info(f"备份创建完成: {backup_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"创建备份失败: {e}")
            return False
    
    def _install_exe_update(self, update_path: Path) -> bool:
        """安装EXE更新"""
        try:
            # 创建更新脚本
            script_content = f'''
@echo off
echo 正在安装更新...
timeout /t 3 /nobreak > nul
taskkill /f /im "{Path(sys.executable).name}" > nul 2>&1
timeout /t 2 /nobreak > nul
copy /y "{update_path}" "{sys.executable}"
echo 更新安装完成
start "" "{sys.executable}"
del "%~f0"
'''
            
            script_path = self.temp_dir / "update_install.bat"
            with open(script_path, 'w', encoding='gbk') as f:
                f.write(script_content)
            
            # 执行更新脚本
            subprocess.Popen([str(script_path)], shell=True)
            
            self.logger.info("更新安装脚本已启动，程序将重启")
            return True
            
        except Exception as e:
            self.logger.error(f"安装EXE更新失败: {e}")
            return False
    
    def _install_archive_update(self, update_path: Path) -> bool:
        """安装压缩包更新"""
        try:
            import zipfile
            import tarfile
            import shutil
            
            # 解压到临时目录
            extract_dir = self.temp_dir / "extract"
            if extract_dir.exists():
                shutil.rmtree(extract_dir)
            extract_dir.mkdir()
            
            if update_path.suffix.lower() == ".zip":
                with zipfile.ZipFile(update_path, 'r') as zip_ref:
                    zip_ref.extractall(extract_dir)
            elif update_path.suffix.lower() in [".tar.gz", ".tgz"]:
                with tarfile.open(update_path, 'r:gz') as tar_ref:
                    tar_ref.extractall(extract_dir)
            
            # 复制文件到应用目录
            for item in extract_dir.rglob('*'):
                if item.is_file():
                    relative_path = item.relative_to(extract_dir)
                    target_path = self.app_dir / relative_path
                    target_path.parent.mkdir(parents=True, exist_ok=True)
                    shutil.copy2(item, target_path)
            
            self.logger.info("压缩包更新安装完成")
            return True
            
        except Exception as e:
            self.logger.error(f"安装压缩包更新失败: {e}")
            return False
    
    def rollback_update(self) -> bool:
        """回滚更新"""
        try:
            # 查找最新的备份
            backup_files = list(self.backup_dir.glob("backup_*"))
            if not backup_files:
                self.logger.error("没有找到备份文件")
                return False
            
            # 按时间排序，选择最新的备份
            latest_backup = max(backup_files, key=lambda x: x.stat().st_mtime)
            
            # 恢复备份
            if latest_backup.is_file() and latest_backup.suffix == '.exe':
                # 可执行文件备份
                import shutil
                shutil.copy2(latest_backup, sys.executable)
            elif latest_backup.is_dir():
                # 目录备份
                import shutil
                src_dir = Path(__file__).parent.parent
                if src_dir.exists():
                    shutil.rmtree(src_dir)
                shutil.copytree(latest_backup, src_dir)
            
            self.logger.info(f"已回滚到备份: {latest_backup}")
            return True
            
        except Exception as e:
            self.logger.error(f"回滚更新失败: {e}")
            return False
    
    def start_auto_check(self) -> None:
        """启动自动检查"""
        if not self.auto_check_enabled:
            return
        
        def check_loop():
            while self.auto_check_enabled:
                try:
                    update_info = self.check_for_updates()
                    if update_info and self.auto_download_enabled:
                        # 自动下载更新
                        download_path = self.download_update(update_info)
                        if download_path:
                            self.logger.info(f"更新已自动下载: {download_path}")
                    
                    # 等待下次检查
                    time.sleep(self.check_interval)
                    
                except Exception as e:
                    self.logger.error(f"自动检查更新出错: {e}")
                    time.sleep(3600)  # 出错后等待1小时再试
        
        check_thread = threading.Thread(target=check_loop, daemon=True)
        check_thread.start()
        
        self.logger.info("自动更新检查已启动")
    
    def stop_auto_check(self) -> None:
        """停止自动检查"""
        self.auto_check_enabled = False
        self.logger.info("自动更新检查已停止")
    
    def get_update_status(self) -> Dict[str, Any]:
        """获取更新状态"""
        return {
            "current_version": self.current_version,
            "latest_version": self.latest_version,
            "is_checking": self.is_checking,
            "is_downloading": self.is_downloading,
            "download_progress": self.download_progress,
            "last_check_time": self.last_check_time,
            "auto_check_enabled": self.auto_check_enabled,
            "auto_download_enabled": self.auto_download_enabled,
            "beta_updates_enabled": self.beta_updates_enabled
        }
    
    def configure_updater(self, 
                         auto_check: Optional[bool] = None,
                         auto_download: Optional[bool] = None,
                         beta_updates: Optional[bool] = None,
                         check_interval: Optional[int] = None) -> None:
        """
        配置更新器
        
        Args:
            auto_check: 是否自动检查
            auto_download: 是否自动下载
            beta_updates: 是否包含测试版本
            check_interval: 检查间隔（秒）
        """
        if auto_check is not None:
            self.auto_check_enabled = auto_check
        
        if auto_download is not None:
            self.auto_download_enabled = auto_download
        
        if beta_updates is not None:
            self.beta_updates_enabled = beta_updates
        
        if check_interval is not None:
            self.check_interval = check_interval
        
        self.logger.info("更新器配置已更新")
