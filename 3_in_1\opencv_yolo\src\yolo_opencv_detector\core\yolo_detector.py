# -*- coding: utf-8 -*-
"""
YOLO检测器模块
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

import torch
import numpy as np
from pathlib import Path
from typing import List, Optional, Union, Tuple, Dict, Any
import cv2
from PIL import Image
import time

try:
    from ultralytics import YOLO
except ImportError:
    YOLO = None

from ..utils.logger import Logger, performance_timer
from ..utils.data_structures import DetectionResult, BoundingBox, DetectionSource
from ..utils.constants import (
    MODELS_DIR, DEFAULT_CONFIDENCE_THRESHOLD, DEFAULT_NMS_THRESHOLD,
    DEFAULT_INPUT_SIZE, MAX_DETECTIONS, YoloModels
)


class YoloDetector:
    """YOLO检测器类"""
    
    def __init__(self, 
                 model_path: Optional[Union[str, Path]] = None,
                 device: str = "auto",
                 confidence_threshold: float = DEFAULT_CONFIDENCE_THRESHOLD,
                 nms_threshold: float = DEFAULT_NMS_THRESHOLD,
                 input_size: Tuple[int, int] = DEFAULT_INPUT_SIZE,
                 max_detections: int = MAX_DETECTIONS):
        """
        初始化YOLO检测器
        
        Args:
            model_path: 模型文件路径，如果为None则使用默认模型
            device: 设备类型 ("auto", "cpu", "cuda:0", etc.)
            confidence_threshold: 置信度阈值
            nms_threshold: NMS阈值
            input_size: 输入图像尺寸
            max_detections: 最大检测数量
        """
        self.logger = Logger().get_logger(__name__)
        
        # 检查ultralytics是否可用
        if YOLO is None:
            raise ImportError("ultralytics包未安装，请运行: pip install ultralytics")
        
        # 参数设置
        self.model_path = self._resolve_model_path(model_path)
        self.device = self._resolve_device(device)
        self.confidence_threshold = confidence_threshold
        self.nms_threshold = nms_threshold
        self.input_size = input_size
        self.max_detections = max_detections
        
        # 模型相关
        self.model: Optional[YOLO] = None
        self.is_loaded = False
        self.model_info: Dict[str, Any] = {}
        
        # 性能统计
        self.inference_times: List[float] = []
        self.total_detections = 0
        
        self.logger.info(f"YOLO检测器初始化完成 - 模型: {self.model_path}, 设备: {self.device}")
    
    def _resolve_model_path(self, model_path: Optional[Union[str, Path]]) -> Path:
        """解析模型路径"""
        if model_path is None:
            # 使用默认模型
            model_path = MODELS_DIR / YoloModels.NANO.value
        else:
            model_path = Path(model_path)
            
            # 如果是相对路径，相对于models目录
            if not model_path.is_absolute():
                model_path = MODELS_DIR / model_path
        
        return model_path
    
    def _resolve_device(self, device: str) -> str:
        """解析设备类型"""
        if device == "auto":
            if torch.cuda.is_available():
                device = "cuda:0"
                self.logger.info("检测到CUDA，使用GPU加速")
            else:
                device = "cpu"
                self.logger.info("未检测到CUDA，使用CPU")
        
        return device
    
    @performance_timer("YOLO模型加载")
    def load_model(self) -> bool:
        """
        加载YOLO模型
        
        Returns:
            bool: 加载是否成功
        """
        try:
            if self.is_loaded:
                self.logger.warning("模型已经加载")
                return True
            
            self.logger.info(f"开始加载YOLO模型: {self.model_path}")
            
            # 检查模型文件是否存在
            if not self.model_path.exists():
                self.logger.warning(f"模型文件不存在，将自动下载: {self.model_path}")
                # ultralytics会自动下载模型
            
            # 加载模型
            self.model = YOLO(str(self.model_path))
            
            # 设置设备
            if self.device != "cpu":
                self.model.to(self.device)
            
            # 获取模型信息
            self.model_info = {
                "model_path": str(self.model_path),
                "device": self.device,
                "input_size": self.input_size,
                "num_classes": len(self.model.names) if hasattr(self.model, 'names') else 0,
                "class_names": self.model.names if hasattr(self.model, 'names') else {}
            }
            
            self.is_loaded = True
            self.logger.info(f"YOLO模型加载成功 - 类别数: {self.model_info['num_classes']}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"YOLO模型加载失败: {e}")
            self.is_loaded = False
            return False
    
    def unload_model(self) -> None:
        """卸载模型，释放内存"""
        if self.model is not None:
            del self.model
            self.model = None
            
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            
        self.is_loaded = False
        self.logger.info("YOLO模型已卸载")
    
    @performance_timer("YOLO检测")
    def detect(self, 
               image: Union[np.ndarray, Image.Image, str, Path],
               return_raw: bool = False) -> List[DetectionResult]:
        """
        执行目标检测
        
        Args:
            image: 输入图像（numpy数组、PIL图像或文件路径）
            return_raw: 是否返回原始检测结果
            
        Returns:
            List[DetectionResult]: 检测结果列表
        """
        if not self.is_loaded:
            if not self.load_model():
                self.logger.error("模型未加载，无法执行检测")
                return []
        
        try:
            start_time = time.time()
            
            # 预处理图像
            processed_image = self._preprocess_image(image)
            if processed_image is None:
                return []
            
            # 执行推理
            results = self.model(
                processed_image,
                conf=self.confidence_threshold,
                iou=self.nms_threshold,
                imgsz=self.input_size,
                max_det=self.max_detections,
                device=self.device,
                verbose=False
            )
            
            # 后处理结果
            detections = self._postprocess_results(results, processed_image.shape[:2])
            
            # 记录性能统计
            inference_time = time.time() - start_time
            self.inference_times.append(inference_time)
            self.total_detections += len(detections)
            
            self.logger.debug(f"YOLO检测完成 - 检测到 {len(detections)} 个目标，耗时 {inference_time:.4f}s")
            
            if return_raw:
                return results, detections
            else:
                return detections
                
        except Exception as e:
            self.logger.error(f"YOLO检测失败: {e}")
            return []
    
    def _preprocess_image(self, image: Union[np.ndarray, Image.Image, str, Path]) -> Optional[np.ndarray]:
        """
        预处理输入图像
        
        Args:
            image: 输入图像
            
        Returns:
            Optional[np.ndarray]: 预处理后的图像
        """
        try:
            # 处理不同类型的输入
            if isinstance(image, (str, Path)):
                # 文件路径
                image_path = Path(image)
                if not image_path.exists():
                    self.logger.error(f"图像文件不存在: {image_path}")
                    return None
                image = cv2.imread(str(image_path))
                if image is None:
                    self.logger.error(f"无法读取图像文件: {image_path}")
                    return None
                image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                
            elif isinstance(image, Image.Image):
                # PIL图像
                image = np.array(image)
                if image.ndim == 3 and image.shape[2] == 4:
                    # RGBA转RGB
                    image = image[:, :, :3]
                    
            elif isinstance(image, np.ndarray):
                # numpy数组
                if image.ndim == 3:
                    if image.shape[2] == 4:
                        # RGBA转RGB
                        image = image[:, :, :3]
                    elif image.shape[2] == 3:
                        # 检查是否是BGR格式，如果是则转换为RGB
                        # 这里假设输入是RGB格式，如果需要BGR转RGB，可以取消注释下一行
                        # image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                        pass
                elif image.ndim == 2:
                    # 灰度图转RGB
                    image = cv2.cvtColor(image, cv2.COLOR_GRAY2RGB)
                else:
                    self.logger.error(f"不支持的图像维度: {image.ndim}")
                    return None
            else:
                self.logger.error(f"不支持的图像类型: {type(image)}")
                return None
            
            # 验证图像
            if image.size == 0:
                self.logger.error("图像为空")
                return None
                
            return image
            
        except Exception as e:
            self.logger.error(f"图像预处理失败: {e}")
            return None
    
    def _postprocess_results(self, results, image_shape: Tuple[int, int]) -> List[DetectionResult]:
        """
        后处理检测结果
        
        Args:
            results: YOLO原始检测结果
            image_shape: 图像尺寸 (height, width)
            
        Returns:
            List[DetectionResult]: 处理后的检测结果
        """
        detections = []
        
        try:
            if not results or len(results) == 0:
                return detections
            
            # 获取第一个结果（通常只有一个）
            result = results[0]
            
            if result.boxes is None or len(result.boxes) == 0:
                return detections
            
            # 提取检测框信息
            boxes = result.boxes.xyxy.cpu().numpy()  # [x1, y1, x2, y2]
            confidences = result.boxes.conf.cpu().numpy()
            class_ids = result.boxes.cls.cpu().numpy().astype(int)
            
            # 转换为DetectionResult格式
            for i, (box, conf, class_id) in enumerate(zip(boxes, confidences, class_ids)):
                x1, y1, x2, y2 = box
                
                # 创建边界框
                bbox = BoundingBox(
                    x=int(x1),
                    y=int(y1),
                    width=int(x2 - x1),
                    height=int(y2 - y1)
                )
                
                # 获取类别名称
                class_name = None
                if hasattr(self.model, 'names') and class_id in self.model.names:
                    class_name = self.model.names[class_id]
                
                # 创建检测结果
                detection = DetectionResult(
                    bbox=bbox,
                    confidence=float(conf),
                    class_id=class_id,
                    class_name=class_name,
                    source=DetectionSource.YOLO,
                    metadata={
                        "model_path": str(self.model_path),
                        "device": self.device,
                        "image_shape": image_shape
                    }
                )
                
                detections.append(detection)
            
            # 按置信度排序
            detections.sort(key=lambda x: x.confidence, reverse=True)
            
        except Exception as e:
            self.logger.error(f"结果后处理失败: {e}")
        
        return detections
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """
        获取性能统计信息
        
        Returns:
            Dict[str, Any]: 性能统计
        """
        if not self.inference_times:
            return {}
        
        return {
            "total_inferences": len(self.inference_times),
            "total_detections": self.total_detections,
            "avg_inference_time": np.mean(self.inference_times),
            "min_inference_time": np.min(self.inference_times),
            "max_inference_time": np.max(self.inference_times),
            "avg_fps": 1.0 / np.mean(self.inference_times) if self.inference_times else 0,
            "avg_detections_per_image": self.total_detections / len(self.inference_times)
        }
    
    def reset_stats(self) -> None:
        """重置性能统计"""
        self.inference_times.clear()
        self.total_detections = 0
        self.logger.info("性能统计已重置")
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        获取模型信息
        
        Returns:
            Dict[str, Any]: 模型信息
        """
        return self.model_info.copy()
    
    def update_config(self, **kwargs) -> None:
        """
        更新检测器配置
        
        Args:
            **kwargs: 配置参数
        """
        if "confidence_threshold" in kwargs:
            self.confidence_threshold = kwargs["confidence_threshold"]
            
        if "nms_threshold" in kwargs:
            self.nms_threshold = kwargs["nms_threshold"]
            
        if "input_size" in kwargs:
            self.input_size = kwargs["input_size"]
            
        if "max_detections" in kwargs:
            self.max_detections = kwargs["max_detections"]
        
        self.logger.info(f"检测器配置已更新: {kwargs}")
    
    def __del__(self):
        """析构函数"""
        if hasattr(self, 'model') and self.model is not None:
            self.unload_model()
