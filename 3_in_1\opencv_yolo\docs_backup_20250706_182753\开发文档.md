# 详细技术设计文档及开发任务分解

为了将“基于YOLO深度学习检测结合OpenCV模板匹配的Windows屏幕识别工具”项目落地实施，以下给出**模块级技术设计**和**迭代式开发任务分解**。文档分为四大部分：架构概览、模块设计、数据流与接口、详细任务分解计划。

## 一、系统架构概览

1. **总体架构**  
   -  **截图层**：屏幕截取、坐标映射、多显示器支持  
   -  **检测层**：YOLO 检测服务、模板匹配服务  
   -  **融合层**：结果融合与后处理  
   -  **交互层**：PyQt6 GUI、自动化操作脚本生成  
   -  **管理层**：配置管理、日志与监控  

2. **技术栈**  
   - 语言：Python 3.9+  
   - 框架：PyQt6（GUI），Ultralytics YOLOv8（检测），OpenCV 4.x  
   - 加速：CUDA/cuDNN（GPU 推理），多线程／asyncio  
   - 自动化：PyAutoGUI、Win32 API  
   - 存储：SQLite（模板库、日志、配置）

## 二、核心模块设计

### 1. 截图与坐标管理模块 (V2重构版本)
   - **ScreenCaptureServiceV2**
     -  方法：take_screenshot(), take_screenshot_with_pixmap(), get_available_methods()
     -  多显示器：自动检测屏幕布局并映射全局坐标
     -  中文路径支持：统一使用 Unicode 文件名接口
     -  多种截图方法：MSS、PIL、PyAutoGUI自动选择最佳方法

   - **CoordinateMapper**
     -  将截图坐标映射到物理屏幕／UI 操作坐标

### 2. YOLO 检测模块 (V2重构版本)
   - **YOLODetectorV2**
     -  初始化：load_model(model_path, device)
     -  推理：detect(image) → [ {bbox, confidence, class_id} ]
     -  参数：置信度阈值、NMS 阈值、输入尺寸
     -  性能优化：GPU加速、批处理支持

### 3. 模板匹配模块 (V2重构版本)
   - **TemplateMatcherV2**
     -  方法：match_template(image, template, threshold) → [ {bbox, score, template_id} ]
     -  多尺度匹配：支持缩放范围和步长配置
     -  预处理：灰度化、直方图均衡、光照补偿
     -  性能统计：匹配时间和成功率统计

### 4. 结果融合模块  
   - **FusionEngine**  
     -  算法：IOU 计算、置信度加权、非极大值抑制融合  
     -  输出接口：merge(detections_yolo, detections_tm) → final_detections  

### 5. 自动化脚本生成模块  
   - **ScriptGenerator**  
     -  支持语言：Python(PyAutoGUI)、C#(Win32 API)  
     -  方法：generate_click(x,y), generate_drag(start,end), generate_sequence(detections)  

### 6. GUI 交互模块  
   - **MainWindow**（PyQt6）  
     -  子组件：CapturePanel、ResultView、TemplateLibraryView、SettingsDialog  
     -  核心交互：区域选择、实时可视化、模板创建／管理  

### 7. 日志与监控模块  
   - **Logger**：操作日志、错误日志  
   - **PerformanceMonitor**：推理时间、内存／GPU 占用统计  

## 三、数据流与接口规范

1. 截图 → ScreenCaptureService → PIL／NumPy 图像  
2. 图像送入 YoloDetector（异步）  
3. 图像与模板库并行送入 TemplateMatcher  
4. 检测结果汇集至 FusionEngine → 标准化 JSON 结构  
5. GUI 订阅事件，绘制边界框与标签  
6. 用户触发“生成脚本” → ScriptGenerator → 文本输出／剪贴板  

**数据结构示例**  
```json
{
  "detections": [
    {
      "source": "yolo|template",
      "bbox": [x, y, w, h],
      "confidence": 0.87,
      "class_id": 3,
      "template_id": "btn_ok"
    }
  ]
}
```

## 四、迭代式开发任务分解

### 版本 1.0：核心功能实现（1–6 周）

| 周次 | 任务                                                         | 输出                                                  | 负责人       |
|---|------------------------------------------------------------|-----------------------------------------------------|-----------|
| 1 | 环境搭建、依赖配置                                         | 项目脚手架，CI/CD 初版                               | 技术负责人    |
| 2 | ScreenCaptureService 与 CoordinateMapper 开发           | 截图 demo，多屏测试用例                              | 系统开发      |
| 3–4 | YOLODetector 模块：模型加载、推理、单元测试               | 检测服务接口文档，准确率基准报告                      | 算法工程师    |
| 5–6 | TemplateManager + TemplateMatcher：模板存储与匹配功能   | 模板管理 UI，有效匹配示例                            | 视觉算法工程师 |

### 版本 1.1：结果融合与基本 GUI（7–10 周）

| 周次 | 任务                                                        | 输出                                                   | 负责人         |
|---|-----------------------------------------------------------|------------------------------------------------------|-------------|
| 7 | FusionEngine：融合算法、IOU 与 NMS 实现                     | 融合模块单元测试，融合效果对比报告                     | 算法工程师      |
| 8–9 | PyQt6 界面雏形：CapturePanel、ResultView                  | GUI 初版，区域选择与结果展示功能                       | 前端开发        |
| 10 | 日志与监控模块：Logger + PerformanceMonitor             | 日志文件样例，性能统计面板                             | 系统开发        |

### 版本 1.2：自动化脚本生成与多线程优化（11–13 周）

| 周次 | 任务                                                        | 输出                                                   | 负责人         |
|---|-----------------------------------------------------------|------------------------------------------------------|-------------|
| 11 | ScriptGenerator：Python/C# 模板、API 设计与实现              | 脚本示例，文档                                       | 后端开发        |
| 12 | 多线程／asyncio 改造检测与融合流程                           | 并行性能基准报告，响应时间达标                         | 算法＋系统开发 |
| 13 | GPU 加速与内存优化：CUDA 推理集成 + 缓存机制               | 性能测试报告（响应<500ms，内存<200MB）                | 算法工程师      |

### 版本 1.3：质量保障与交付（14–16 周）

| 周次 | 任务                                                         | 输出                                                    | 负责人       |
|---|------------------------------------------------------------|-------------------------------------------------------|-----------|
| 14 | 集成测试：功能覆盖、跨平台验证                               | 集成测试报告，Bug 列表                                 | 测试工程师    |
| 15 | 文档编写：开发文档、用户手册、API 参考                         | 完整技术文档包                                        | 技术文档      |
| 16 | 内部验收与优化：性能微调，UI 细节完善                         | 版本发布 Candidate                                   | 全体团队      |

**备注**：每周需举行一次迭代评审，评估进度与质量，调整下周计划。上述计划可根据团队规模与实际资源灵活调整。