# -*- coding: utf-8 -*-
"""
系统常量定义
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

from enum import Enum
from pathlib import Path

# 应用程序信息
APP_NAME = "YOLO OpenCV 屏幕识别工具"
APP_VERSION = "1.0.0"
APP_AUTHOR = "AI Assistant"

# 文件路径常量
PROJECT_ROOT = Path(__file__).parent.parent.parent.parent
SRC_DIR = PROJECT_ROOT / "src"
CONFIG_DIR = PROJECT_ROOT / "configs"
MODELS_DIR = PROJECT_ROOT / "models"
TEMPLATES_DIR = PROJECT_ROOT / "templates"
LOGS_DIR = PROJECT_ROOT / "logs"
DATA_DIR = PROJECT_ROOT / "data"

# 默认文件名
DEFAULT_CONFIG_FILE = "default_config.yaml"
USER_CONFIG_FILE = "user_config.yaml"
TEMPLATES_DB_FILE = "templates.db"
LOG_FILE = "app.log"

# YOLO相关常量
class YoloModels(Enum):
    """YOLO模型枚举"""
    NANO = "yolov8n.pt"
    SMALL = "yolov8s.pt"
    MEDIUM = "yolov8m.pt"
    LARGE = "yolov8l.pt"
    EXTRA_LARGE = "yolov8x.pt"

# 检测相关常量
DEFAULT_CONFIDENCE_THRESHOLD = 0.5
DEFAULT_NMS_THRESHOLD = 0.4
DEFAULT_INPUT_SIZE = (640, 640)
MAX_DETECTIONS = 100

# 模板匹配常量
class TemplateMatchMethods(Enum):
    """OpenCV模板匹配方法"""
    CCOEFF = "cv2.TM_CCOEFF"
    CCOEFF_NORMED = "cv2.TM_CCOEFF_NORMED"
    CCORR = "cv2.TM_CCORR"
    CCORR_NORMED = "cv2.TM_CCORR_NORMED"
    SQDIFF = "cv2.TM_SQDIFF"
    SQDIFF_NORMED = "cv2.TM_SQDIFF_NORMED"

DEFAULT_TEMPLATE_THRESHOLD = 0.8
DEFAULT_SCALE_RANGE = (0.5, 2.0)
DEFAULT_SCALE_STEPS = 10
DEFAULT_ANGLE_RANGE = (-15, 15)
DEFAULT_ANGLE_STEPS = 7

# 屏幕截图常量
class ImageFormats(Enum):
    """图像格式枚举"""
    RGB = "RGB"
    RGBA = "RGBA"
    BGR = "BGR"
    BGRA = "BGRA"
    GRAY = "L"

DEFAULT_IMAGE_FORMAT = ImageFormats.RGB
DEFAULT_IMAGE_QUALITY = 95
DEFAULT_CACHE_SIZE = 10

# 融合算法常量
DEFAULT_IOU_THRESHOLD = 0.5
DEFAULT_CONFIDENCE_WEIGHT = 0.7
DEFAULT_TEMPLATE_WEIGHT = 0.3
MAX_FUSION_RESULTS = 50

# GUI相关常量
DEFAULT_WINDOW_SIZE = (1400, 900)  # 增大默认窗口尺寸
MIN_WINDOW_SIZE = (1200, 800)      # 进一步增大最小窗口尺寸

class Themes(Enum):
    """GUI主题枚举"""
    DEFAULT = "default"
    DARK = "dark"
    LIGHT = "light"

class Languages(Enum):
    """支持的语言"""
    CHINESE = "zh_CN"
    ENGLISH = "en_US"

# 脚本生成常量
class ScriptLanguages(Enum):
    """支持的脚本语言"""
    PYTHON = "python"
    CSHARP = "csharp"

DEFAULT_CLICK_DELAY = 0.1
DEFAULT_DRAG_DURATION = 0.5

# 性能监控常量
DEFAULT_LOG_INTERVAL = 5.0
DEFAULT_MEMORY_THRESHOLD = 200  # MB
DEFAULT_CPU_THRESHOLD = 80      # %
DEFAULT_GPU_THRESHOLD = 90      # %

# 数据库常量
DEFAULT_BACKUP_INTERVAL = 3600  # seconds
MAX_BACKUP_FILES = 5

# 日志级别
class LogLevels(Enum):
    """日志级别枚举"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

# 错误代码
class ErrorCodes(Enum):
    """错误代码枚举"""
    SUCCESS = 0
    CONFIG_ERROR = 1001
    MODEL_LOAD_ERROR = 1002
    DETECTION_ERROR = 1003
    TEMPLATE_ERROR = 1004
    SCREEN_CAPTURE_ERROR = 1005
    FUSION_ERROR = 1006
    GUI_ERROR = 1007
    SCRIPT_GENERATION_ERROR = 1008
    DATABASE_ERROR = 1009
    UNKNOWN_ERROR = 9999

# 文件扩展名
SUPPORTED_IMAGE_EXTENSIONS = {".png", ".jpg", ".jpeg", ".bmp", ".tiff"}
SUPPORTED_MODEL_EXTENSIONS = {".pt", ".onnx", ".engine"}
SUPPORTED_CONFIG_EXTENSIONS = {".yaml", ".yml", ".json"}

# 网络相关常量
DEFAULT_TIMEOUT = 30.0
MAX_RETRY_ATTEMPTS = 3

# 缓存相关常量
DEFAULT_CACHE_TTL = 300  # seconds
MAX_CACHE_SIZE = 100     # items
