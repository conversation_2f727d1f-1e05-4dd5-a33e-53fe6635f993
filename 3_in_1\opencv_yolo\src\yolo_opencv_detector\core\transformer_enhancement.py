# -*- coding: utf-8 -*-
"""
基于Transformer的检测增强模块

集成Vision Transformer、DETR等先进架构，提升检测精度和鲁棒性。

Created: 2025-07-13
Author: Augment Agent
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
import logging
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class TransformerConfig:
    """Transformer配置"""
    d_model: int = 256
    nhead: int = 8
    num_encoder_layers: int = 6
    num_decoder_layers: int = 6
    dim_feedforward: int = 2048
    dropout: float = 0.1
    activation: str = "relu"
    normalize_before: bool = False


class PositionalEncoding(nn.Module):
    """位置编码"""
    
    def __init__(self, d_model: int, max_len: int = 5000):
        super().__init__()
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-np.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        self.register_buffer('pe', pe)
    
    def forward(self, x):
        return x + self.pe[:x.size(0), :]


class MultiScaleFeatureExtractor(nn.Module):
    """多尺度特征提取器"""
    
    def __init__(self, input_dim: int, output_dim: int):
        super().__init__()
        
        # 多尺度卷积分支
        self.scale_1 = nn.Conv2d(input_dim, output_dim // 4, 1)
        self.scale_3 = nn.Conv2d(input_dim, output_dim // 4, 3, padding=1)
        self.scale_5 = nn.Conv2d(input_dim, output_dim // 4, 5, padding=2)
        self.scale_7 = nn.Conv2d(input_dim, output_dim // 4, 7, padding=3)
        
        # 特征融合
        self.fusion = nn.Conv2d(output_dim, output_dim, 1)
        self.norm = nn.BatchNorm2d(output_dim)
        self.activation = nn.ReLU(inplace=True)
    
    def forward(self, x):
        # 多尺度特征提取
        feat_1 = self.scale_1(x)
        feat_3 = self.scale_3(x)
        feat_5 = self.scale_5(x)
        feat_7 = self.scale_7(x)
        
        # 特征拼接
        multi_scale_feat = torch.cat([feat_1, feat_3, feat_5, feat_7], dim=1)
        
        # 特征融合
        fused_feat = self.fusion(multi_scale_feat)
        fused_feat = self.norm(fused_feat)
        fused_feat = self.activation(fused_feat)
        
        return fused_feat


class AttentionFusionModule(nn.Module):
    """注意力融合模块"""
    
    def __init__(self, feature_dim: int, num_sources: int):
        super().__init__()
        
        self.feature_dim = feature_dim
        self.num_sources = num_sources
        
        # 注意力权重计算
        self.attention_weights = nn.Sequential(
            nn.Linear(feature_dim * num_sources, feature_dim),
            nn.ReLU(),
            nn.Linear(feature_dim, num_sources),
            nn.Softmax(dim=-1)
        )
        
        # 特征变换
        self.feature_transform = nn.ModuleList([
            nn.Linear(feature_dim, feature_dim) for _ in range(num_sources)
        ])
        
        # 输出投影
        self.output_proj = nn.Linear(feature_dim, feature_dim)
    
    def forward(self, features: List[torch.Tensor]) -> torch.Tensor:
        """
        融合多源特征
        
        Args:
            features: 多源特征列表
            
        Returns:
            融合后的特征
        """
        batch_size = features[0].size(0)
        
        # 特征变换
        transformed_features = []
        for i, feat in enumerate(features):
            transformed = self.feature_transform[i](feat)
            transformed_features.append(transformed)
        
        # 计算注意力权重
        concat_features = torch.cat(transformed_features, dim=-1)
        attention_weights = self.attention_weights(concat_features)
        
        # 加权融合
        fused_feature = torch.zeros_like(transformed_features[0])
        for i, feat in enumerate(transformed_features):
            weight = attention_weights[:, i:i+1]
            fused_feature += weight * feat
        
        # 输出投影
        output = self.output_proj(fused_feature)
        
        return output


class TransformerDetectionHead(nn.Module):
    """基于Transformer的检测头"""
    
    def __init__(self, config: TransformerConfig, num_classes: int):
        super().__init__()
        
        self.config = config
        self.num_classes = num_classes
        
        # Transformer编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=config.d_model,
            nhead=config.nhead,
            dim_feedforward=config.dim_feedforward,
            dropout=config.dropout,
            activation=config.activation,
            normalize_before=config.normalize_before
        )
        self.transformer_encoder = nn.TransformerEncoder(
            encoder_layer, num_layers=config.num_encoder_layers
        )
        
        # 位置编码
        self.pos_encoding = PositionalEncoding(config.d_model)
        
        # 分类头
        self.class_head = nn.Sequential(
            nn.Linear(config.d_model, config.d_model),
            nn.ReLU(),
            nn.Dropout(config.dropout),
            nn.Linear(config.d_model, num_classes)
        )
        
        # 回归头
        self.bbox_head = nn.Sequential(
            nn.Linear(config.d_model, config.d_model),
            nn.ReLU(),
            nn.Dropout(config.dropout),
            nn.Linear(config.d_model, 4)  # x, y, w, h
        )
        
        # 置信度头
        self.confidence_head = nn.Sequential(
            nn.Linear(config.d_model, config.d_model // 2),
            nn.ReLU(),
            nn.Dropout(config.dropout),
            nn.Linear(config.d_model // 2, 1),
            nn.Sigmoid()
        )
    
    def forward(self, features: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        前向传播
        
        Args:
            features: 输入特征 [seq_len, batch_size, d_model]
            
        Returns:
            检测结果字典
        """
        # 添加位置编码
        features = self.pos_encoding(features)
        
        # Transformer编码
        encoded_features = self.transformer_encoder(features)
        
        # 多头预测
        class_logits = self.class_head(encoded_features)
        bbox_pred = self.bbox_head(encoded_features)
        confidence = self.confidence_head(encoded_features)
        
        return {
            "class_logits": class_logits,
            "bbox_pred": bbox_pred,
            "confidence": confidence,
            "features": encoded_features
        }


class DiffusionEnhancedDetector(nn.Module):
    """基于扩散模型的检测增强器"""
    
    def __init__(self, feature_dim: int, timesteps: int = 1000):
        super().__init__()
        
        self.feature_dim = feature_dim
        self.timesteps = timesteps
        
        # 噪声预测网络
        self.noise_predictor = nn.Sequential(
            nn.Linear(feature_dim + 1, feature_dim * 2),  # +1 for timestep
            nn.ReLU(),
            nn.Linear(feature_dim * 2, feature_dim * 2),
            nn.ReLU(),
            nn.Linear(feature_dim * 2, feature_dim)
        )
        
        # 时间步嵌入
        self.time_embedding = nn.Embedding(timesteps, feature_dim)
        
        # 特征增强网络
        self.feature_enhancer = nn.Sequential(
            nn.Linear(feature_dim, feature_dim * 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(feature_dim * 2, feature_dim)
        )
    
    def add_noise(self, features: torch.Tensor, timestep: int) -> torch.Tensor:
        """添加噪声"""
        noise = torch.randn_like(features)
        alpha = self._get_alpha(timestep)
        noisy_features = torch.sqrt(alpha) * features + torch.sqrt(1 - alpha) * noise
        return noisy_features, noise
    
    def _get_alpha(self, timestep: int) -> float:
        """获取噪声调度参数"""
        # 简化的线性调度
        return 1.0 - (timestep / self.timesteps)
    
    def denoise_step(self, noisy_features: torch.Tensor, timestep: int) -> torch.Tensor:
        """去噪步骤"""
        # 时间步嵌入
        t_emb = self.time_embedding(torch.tensor([timestep], device=noisy_features.device))
        t_emb = t_emb.expand(noisy_features.size(0), -1)
        
        # 预测噪声
        input_features = torch.cat([noisy_features, t_emb], dim=-1)
        predicted_noise = self.noise_predictor(input_features)
        
        # 去噪
        alpha = self._get_alpha(timestep)
        denoised_features = (noisy_features - torch.sqrt(1 - alpha) * predicted_noise) / torch.sqrt(alpha)
        
        return denoised_features
    
    def enhance_features(self, features: torch.Tensor, num_steps: int = 10) -> torch.Tensor:
        """特征增强"""
        # 添加噪声
        timestep = np.random.randint(0, self.timesteps)
        noisy_features, _ = self.add_noise(features, timestep)
        
        # 多步去噪
        enhanced_features = noisy_features
        for step in range(num_steps):
            current_timestep = max(0, timestep - step * (timestep // num_steps))
            enhanced_features = self.denoise_step(enhanced_features, current_timestep)
        
        # 特征增强
        enhanced_features = self.feature_enhancer(enhanced_features)
        
        return enhanced_features


class TransformerEnhancementPipeline:
    """Transformer增强管道"""
    
    def __init__(self, config: TransformerConfig, num_classes: int, device: str = "cuda"):
        self.config = config
        self.num_classes = num_classes
        self.device = device
        
        # 初始化模块
        self.feature_extractor = MultiScaleFeatureExtractor(3, config.d_model).to(device)
        self.attention_fusion = AttentionFusionModule(config.d_model, 3).to(device)  # 3个特征源
        self.detection_head = TransformerDetectionHead(config, num_classes).to(device)
        self.diffusion_enhancer = DiffusionEnhancedDetector(config.d_model).to(device)
        
        # 优化器
        self.optimizer = torch.optim.AdamW(
            list(self.feature_extractor.parameters()) +
            list(self.attention_fusion.parameters()) +
            list(self.detection_head.parameters()) +
            list(self.diffusion_enhancer.parameters()),
            lr=1e-4, weight_decay=1e-4
        )
    
    def process_image(self, image: np.ndarray, 
                     yolo_features: Optional[torch.Tensor] = None,
                     template_features: Optional[torch.Tensor] = None) -> Dict[str, Any]:
        """处理图像并返回增强的检测结果"""
        try:
            # 转换图像格式
            if isinstance(image, np.ndarray):
                image_tensor = torch.from_numpy(image).permute(2, 0, 1).float().unsqueeze(0).to(self.device)
            else:
                image_tensor = image
            
            # 多尺度特征提取
            multi_scale_features = self.feature_extractor(image_tensor)
            
            # 准备融合特征
            features_to_fuse = [multi_scale_features.flatten(2).transpose(1, 2)]  # [B, HW, C]
            
            if yolo_features is not None:
                features_to_fuse.append(yolo_features)
            if template_features is not None:
                features_to_fuse.append(template_features)
            
            # 注意力融合
            if len(features_to_fuse) > 1:
                fused_features = self.attention_fusion(features_to_fuse)
            else:
                fused_features = features_to_fuse[0]
            
            # 扩散增强
            enhanced_features = self.diffusion_enhancer.enhance_features(fused_features)
            
            # Transformer检测
            detection_results = self.detection_head(enhanced_features.transpose(0, 1))
            
            return {
                "detections": detection_results,
                "enhanced_features": enhanced_features,
                "multi_scale_features": multi_scale_features
            }
            
        except Exception as e:
            logger.error(f"Transformer增强处理失败: {e}")
            return {"detections": {}, "enhanced_features": None, "multi_scale_features": None}
    
    def train_step(self, batch_data: Dict[str, torch.Tensor]) -> Dict[str, float]:
        """训练步骤"""
        self.optimizer.zero_grad()
        
        # 前向传播
        results = self.process_image(
            batch_data["images"],
            batch_data.get("yolo_features"),
            batch_data.get("template_features")
        )
        
        # 计算损失
        losses = self._compute_losses(results["detections"], batch_data["targets"])
        total_loss = sum(losses.values())
        
        # 反向传播
        total_loss.backward()
        self.optimizer.step()
        
        return {k: v.item() for k, v in losses.items()}
    
    def _compute_losses(self, predictions: Dict[str, torch.Tensor], 
                       targets: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """计算损失"""
        losses = {}
        
        # 分类损失
        if "class_logits" in predictions and "class_labels" in targets:
            losses["class_loss"] = F.cross_entropy(
                predictions["class_logits"].view(-1, self.num_classes),
                targets["class_labels"].view(-1)
            )
        
        # 回归损失
        if "bbox_pred" in predictions and "bbox_targets" in targets:
            losses["bbox_loss"] = F.smooth_l1_loss(
                predictions["bbox_pred"],
                targets["bbox_targets"]
            )
        
        # 置信度损失
        if "confidence" in predictions and "confidence_targets" in targets:
            losses["conf_loss"] = F.binary_cross_entropy(
                predictions["confidence"],
                targets["confidence_targets"]
            )
        
        return losses
