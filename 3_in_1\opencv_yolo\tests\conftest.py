# -*- coding: utf-8 -*-
"""
pytest配置文件
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

import pytest
import sys
import os
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

@pytest.fixture(scope="session")
def test_data_dir():
    """测试数据目录"""
    return Path(__file__).parent / "test_data"

@pytest.fixture(scope="session") 
def temp_dir(tmp_path_factory):
    """临时目录"""
    return tmp_path_factory.mktemp("yolo_opencv_test")

@pytest.fixture
def sample_image_path(test_data_dir):
    """示例图片路径"""
    return test_data_dir / "sample_screenshot.png"

@pytest.fixture
def sample_template_path(test_data_dir):
    """示例模板路径"""
    return test_data_dir / "sample_template.png"
