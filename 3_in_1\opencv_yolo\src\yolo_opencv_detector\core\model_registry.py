# -*- coding: utf-8 -*-
"""
现代化模型注册和管理系统

提供模型版本控制、自动下载、缓存管理、性能监控等功能，
支持多种深度学习框架和模型格式。

Created: 2025-07-13
Author: Augment Agent
"""

import json
import hashlib
import shutil
import requests
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Callable
from dataclasses import dataclass, asdict
from datetime import datetime
import logging
import threading
from urllib.parse import urlparse
import tempfile

logger = logging.getLogger(__name__)


@dataclass
class ModelMetadata:
    """模型元数据"""
    name: str
    version: str
    framework: str  # pytorch, onnx, tensorrt, etc.
    task_type: str  # detection, classification, segmentation
    input_shape: List[int]
    output_shape: List[int]
    model_size: int  # bytes
    accuracy_metrics: Dict[str, float]
    download_url: str
    checksum: str
    created_at: str
    updated_at: str
    description: str = ""
    tags: List[str] = None
    dependencies: List[str] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []
        if self.dependencies is None:
            self.dependencies = []


@dataclass
class ModelConfig:
    """模型配置"""
    device: str = "auto"
    batch_size: int = 1
    precision: str = "fp32"  # fp32, fp16, int8
    optimization_level: str = "O1"
    max_memory_usage: float = 0.8
    enable_tensorrt: bool = False
    enable_quantization: bool = False
    custom_params: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.custom_params is None:
            self.custom_params = {}


class ModelDownloader:
    """模型下载器"""
    
    def __init__(self, cache_dir: Path):
        self.cache_dir = cache_dir
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.download_lock = threading.Lock()
        
    def download_model(self, metadata: ModelMetadata, 
                      progress_callback: Optional[Callable] = None) -> Path:
        """下载模型"""
        model_path = self.cache_dir / f"{metadata.name}_{metadata.version}"
        
        # 检查是否已存在且校验和正确
        if model_path.exists() and self._verify_checksum(model_path, metadata.checksum):
            logger.info(f"模型已存在: {model_path}")
            return model_path
        
        with self.download_lock:
            # 双重检查
            if model_path.exists() and self._verify_checksum(model_path, metadata.checksum):
                return model_path
            
            logger.info(f"开始下载模型: {metadata.name} v{metadata.version}")
            
            try:
                # 下载到临时文件
                with tempfile.NamedTemporaryFile(delete=False) as temp_file:
                    temp_path = Path(temp_file.name)
                    
                    response = requests.get(metadata.download_url, stream=True)
                    response.raise_for_status()
                    
                    total_size = int(response.headers.get('content-length', 0))
                    downloaded_size = 0
                    
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            temp_file.write(chunk)
                            downloaded_size += len(chunk)
                            
                            if progress_callback and total_size > 0:
                                progress = downloaded_size / total_size
                                progress_callback(progress)
                
                # 验证校验和
                if not self._verify_checksum(temp_path, metadata.checksum):
                    temp_path.unlink()
                    raise ValueError("模型文件校验和不匹配")
                
                # 移动到最终位置
                shutil.move(str(temp_path), str(model_path))
                
                logger.info(f"模型下载完成: {model_path}")
                return model_path
                
            except Exception as e:
                logger.error(f"模型下载失败: {e}")
                if temp_path.exists():
                    temp_path.unlink()
                raise
    
    def _verify_checksum(self, file_path: Path, expected_checksum: str) -> bool:
        """验证文件校验和"""
        try:
            sha256_hash = hashlib.sha256()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    sha256_hash.update(chunk)
            
            actual_checksum = sha256_hash.hexdigest()
            return actual_checksum == expected_checksum
            
        except Exception as e:
            logger.error(f"校验和验证失败: {e}")
            return False


class ModelCache:
    """模型缓存管理器"""
    
    def __init__(self, cache_dir: Path, max_cache_size: int = 10 * 1024 * 1024 * 1024):  # 10GB
        self.cache_dir = cache_dir
        self.max_cache_size = max_cache_size
        self.cache_index_file = cache_dir / "cache_index.json"
        self.cache_index = self._load_cache_index()
        
    def _load_cache_index(self) -> Dict[str, Dict]:
        """加载缓存索引"""
        if self.cache_index_file.exists():
            try:
                with open(self.cache_index_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"加载缓存索引失败: {e}")
        
        return {}
    
    def _save_cache_index(self):
        """保存缓存索引"""
        try:
            with open(self.cache_index_file, 'w', encoding='utf-8') as f:
                json.dump(self.cache_index, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"保存缓存索引失败: {e}")
    
    def add_to_cache(self, model_key: str, model_path: Path, metadata: ModelMetadata):
        """添加模型到缓存"""
        self.cache_index[model_key] = {
            'path': str(model_path),
            'metadata': asdict(metadata),
            'access_time': datetime.now().isoformat(),
            'access_count': 1
        }
        self._save_cache_index()
        self._cleanup_cache()
    
    def get_from_cache(self, model_key: str) -> Optional[Path]:
        """从缓存获取模型"""
        if model_key in self.cache_index:
            cache_entry = self.cache_index[model_key]
            model_path = Path(cache_entry['path'])
            
            if model_path.exists():
                # 更新访问信息
                cache_entry['access_time'] = datetime.now().isoformat()
                cache_entry['access_count'] += 1
                self._save_cache_index()
                
                return model_path
            else:
                # 文件不存在，从缓存中移除
                del self.cache_index[model_key]
                self._save_cache_index()
        
        return None
    
    def _cleanup_cache(self):
        """清理缓存"""
        total_size = self._calculate_cache_size()
        
        if total_size <= self.max_cache_size:
            return
        
        # 按访问时间排序，删除最旧的
        sorted_entries = sorted(
            self.cache_index.items(),
            key=lambda x: (x[1]['access_count'], x[1]['access_time'])
        )
        
        for model_key, cache_entry in sorted_entries:
            model_path = Path(cache_entry['path'])
            if model_path.exists():
                model_size = model_path.stat().st_size
                model_path.unlink()
                total_size -= model_size
                
                del self.cache_index[model_key]
                
                if total_size <= self.max_cache_size:
                    break
        
        self._save_cache_index()
    
    def _calculate_cache_size(self) -> int:
        """计算缓存总大小"""
        total_size = 0
        for cache_entry in self.cache_index.values():
            model_path = Path(cache_entry['path'])
            if model_path.exists():
                total_size += model_path.stat().st_size
        
        return total_size
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        total_size = self._calculate_cache_size()
        
        return {
            'total_models': len(self.cache_index),
            'total_size': total_size,
            'max_size': self.max_cache_size,
            'usage_ratio': total_size / self.max_cache_size,
            'models': list(self.cache_index.keys())
        }


class ModelRegistry:
    """模型注册表"""
    
    def __init__(self, registry_file: Path, cache_dir: Path):
        self.registry_file = registry_file
        self.cache_dir = cache_dir
        self.models: Dict[str, ModelMetadata] = {}
        self.downloader = ModelDownloader(cache_dir)
        self.cache = ModelCache(cache_dir)
        
        self._load_registry()
    
    def _load_registry(self):
        """加载模型注册表"""
        if self.registry_file.exists():
            try:
                with open(self.registry_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                for model_data in data.get('models', []):
                    metadata = ModelMetadata(**model_data)
                    model_key = f"{metadata.name}:{metadata.version}"
                    self.models[model_key] = metadata
                    
                logger.info(f"加载了 {len(self.models)} 个模型定义")
                
            except Exception as e:
                logger.error(f"加载模型注册表失败: {e}")
    
    def _save_registry(self):
        """保存模型注册表"""
        try:
            data = {
                'version': '1.0',
                'updated_at': datetime.now().isoformat(),
                'models': [asdict(metadata) for metadata in self.models.values()]
            }
            
            with open(self.registry_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"保存模型注册表失败: {e}")
    
    def register_model(self, metadata: ModelMetadata):
        """注册模型"""
        model_key = f"{metadata.name}:{metadata.version}"
        self.models[model_key] = metadata
        self._save_registry()
        
        logger.info(f"模型已注册: {model_key}")
    
    def get_model(self, name: str, version: str = "latest") -> Optional[ModelMetadata]:
        """获取模型元数据"""
        if version == "latest":
            # 查找最新版本
            matching_models = [
                (key, metadata) for key, metadata in self.models.items()
                if metadata.name == name
            ]
            
            if not matching_models:
                return None
            
            # 按版本排序（简单字符串排序）
            latest_model = max(matching_models, key=lambda x: x[1].version)
            return latest_model[1]
        else:
            model_key = f"{name}:{version}"
            return self.models.get(model_key)
    
    def download_model(self, name: str, version: str = "latest",
                      progress_callback: Optional[Callable] = None) -> Optional[Path]:
        """下载模型"""
        metadata = self.get_model(name, version)
        if not metadata:
            logger.error(f"未找到模型: {name}:{version}")
            return None
        
        model_key = f"{metadata.name}:{metadata.version}"
        
        # 检查缓存
        cached_path = self.cache.get_from_cache(model_key)
        if cached_path:
            return cached_path
        
        # 下载模型
        try:
            model_path = self.downloader.download_model(metadata, progress_callback)
            self.cache.add_to_cache(model_key, model_path, metadata)
            return model_path
            
        except Exception as e:
            logger.error(f"下载模型失败: {e}")
            return None
    
    def list_models(self, task_type: Optional[str] = None,
                   framework: Optional[str] = None) -> List[ModelMetadata]:
        """列出模型"""
        models = list(self.models.values())
        
        if task_type:
            models = [m for m in models if m.task_type == task_type]
        
        if framework:
            models = [m for m in models if m.framework == framework]
        
        return models
    
    def search_models(self, query: str) -> List[ModelMetadata]:
        """搜索模型"""
        query_lower = query.lower()
        results = []
        
        for metadata in self.models.values():
            if (query_lower in metadata.name.lower() or
                query_lower in metadata.description.lower() or
                any(query_lower in tag.lower() for tag in metadata.tags)):
                results.append(metadata)
        
        return results
    
    def get_registry_stats(self) -> Dict[str, Any]:
        """获取注册表统计"""
        frameworks = {}
        task_types = {}
        
        for metadata in self.models.values():
            frameworks[metadata.framework] = frameworks.get(metadata.framework, 0) + 1
            task_types[metadata.task_type] = task_types.get(metadata.task_type, 0) + 1
        
        return {
            'total_models': len(self.models),
            'frameworks': frameworks,
            'task_types': task_types,
            'cache_stats': self.cache.get_cache_stats()
        }


# 全局模型注册表
_global_registry: Optional[ModelRegistry] = None


def get_model_registry(registry_file: Optional[Path] = None,
                      cache_dir: Optional[Path] = None) -> ModelRegistry:
    """获取全局模型注册表"""
    global _global_registry
    
    if _global_registry is None:
        if registry_file is None:
            registry_file = Path("models/registry.json")
        if cache_dir is None:
            cache_dir = Path("models/cache")
        
        registry_file.parent.mkdir(parents=True, exist_ok=True)
        cache_dir.mkdir(parents=True, exist_ok=True)
        
        _global_registry = ModelRegistry(registry_file, cache_dir)
    
    return _global_registry


def register_default_models():
    """注册默认模型"""
    registry = get_model_registry()
    
    # YOLO模型
    yolo_models = [
        ModelMetadata(
            name="yolov8n",
            version="1.0.0",
            framework="pytorch",
            task_type="detection",
            input_shape=[3, 640, 640],
            output_shape=[25200, 85],
            model_size=6_200_000,
            accuracy_metrics={"mAP50": 0.375, "mAP50-95": 0.237},
            download_url="https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8n.pt",
            checksum="",  # 需要实际计算
            created_at=datetime.now().isoformat(),
            updated_at=datetime.now().isoformat(),
            description="YOLOv8 Nano - 最轻量级的YOLO模型",
            tags=["yolo", "detection", "lightweight"],
            dependencies=["ultralytics", "torch", "torchvision"]
        ),
        # 可以添加更多模型...
    ]
    
    for model in yolo_models:
        registry.register_model(model)
