# -*- coding: utf-8 -*-
"""
内存优化器模块
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

import gc
import sys
import time
import psutil
import threading
from typing import Dict, Any, List, Optional, Callable
from collections import deque
import numpy as np
import weakref

from ..utils.logger import Logger


class MemoryPool:
    """内存池类"""
    
    def __init__(self, max_size: int = 100):
        """
        初始化内存池
        
        Args:
            max_size: 最大缓存数量
        """
        self.max_size = max_size
        self.pool = deque(maxlen=max_size)
        self.lock = threading.Lock()
        self.hit_count = 0
        self.miss_count = 0
    
    def get_array(self, shape: tuple, dtype=np.uint8) -> np.ndarray:
        """
        从内存池获取数组
        
        Args:
            shape: 数组形状
            dtype: 数据类型
            
        Returns:
            np.ndarray: 数组对象
        """
        with self.lock:
            # 查找匹配的数组
            for i, (cached_shape, cached_dtype, array) in enumerate(self.pool):
                if cached_shape == shape and cached_dtype == dtype:
                    # 移除并返回
                    self.pool.remove((cached_shape, cached_dtype, array))
                    self.hit_count += 1
                    return array
            
            # 未找到匹配的，创建新数组
            self.miss_count += 1
            return np.empty(shape, dtype=dtype)
    
    def return_array(self, array: np.ndarray) -> None:
        """
        将数组返回到内存池
        
        Args:
            array: 要返回的数组
        """
        with self.lock:
            if len(self.pool) < self.max_size:
                self.pool.append((array.shape, array.dtype, array))
    
    def clear(self) -> None:
        """清空内存池"""
        with self.lock:
            self.pool.clear()
            gc.collect()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取内存池统计信息"""
        total_requests = self.hit_count + self.miss_count
        hit_rate = self.hit_count / total_requests if total_requests > 0 else 0
        
        return {
            "pool_size": len(self.pool),
            "max_size": self.max_size,
            "hit_count": self.hit_count,
            "miss_count": self.miss_count,
            "hit_rate": hit_rate
        }


class MemoryOptimizer:
    """内存优化器类"""
    
    def __init__(self):
        """初始化内存优化器"""
        self.logger = Logger().get_logger(__name__)
        
        # 内存监控
        self.process = psutil.Process()
        self.memory_threshold = 0.8  # 内存使用阈值
        self.monitoring_enabled = False
        self.monitor_thread = None
        
        # 内存池
        self.image_pool = MemoryPool(max_size=50)
        self.result_pool = MemoryPool(max_size=100)
        
        # 弱引用缓存
        self.weak_cache = weakref.WeakValueDictionary()
        
        # 内存统计
        self.peak_memory = 0
        self.gc_count = 0
        self.cleanup_count = 0
        
        # 配置
        self.auto_gc_enabled = True
        self.gc_threshold = 1000  # 对象数量阈值
        
        self.logger.info("内存优化器初始化完成")
    
    def start_monitoring(self, interval: float = 5.0) -> None:
        """
        开始内存监控
        
        Args:
            interval: 监控间隔（秒）
        """
        if self.monitoring_enabled:
            return
        
        self.monitoring_enabled = True
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop,
            args=(interval,),
            daemon=True
        )
        self.monitor_thread.start()
        
        self.logger.info(f"内存监控已启动，间隔: {interval}秒")
    
    def stop_monitoring(self) -> None:
        """停止内存监控"""
        self.monitoring_enabled = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5.0)
        
        self.logger.info("内存监控已停止")
    
    def _monitor_loop(self, interval: float) -> None:
        """内存监控循环"""
        while self.monitoring_enabled:
            try:
                # 获取内存使用情况
                memory_info = self.get_memory_usage()
                current_memory = memory_info["memory_percent"]
                
                # 更新峰值内存
                if current_memory > self.peak_memory:
                    self.peak_memory = current_memory
                
                # 检查是否需要清理
                if current_memory > self.memory_threshold:
                    self.logger.warning(f"内存使用率过高: {current_memory:.1%}")
                    self.force_cleanup()
                
                # 自动垃圾回收
                if self.auto_gc_enabled:
                    self._auto_gc_check()
                
                time.sleep(interval)
                
            except Exception as e:
                self.logger.error(f"内存监控出错: {e}")
                time.sleep(interval)
    
    def get_memory_usage(self) -> Dict[str, Any]:
        """获取内存使用情况"""
        try:
            # 进程内存信息
            memory_info = self.process.memory_info()
            memory_percent = self.process.memory_percent()
            
            # 系统内存信息
            system_memory = psutil.virtual_memory()
            
            return {
                "rss": memory_info.rss,  # 物理内存
                "vms": memory_info.vms,  # 虚拟内存
                "memory_percent": memory_percent / 100.0,  # 进程内存占用百分比
                "system_memory_total": system_memory.total,
                "system_memory_available": system_memory.available,
                "system_memory_percent": system_memory.percent / 100.0,
                "rss_mb": memory_info.rss / 1024 / 1024,
                "vms_mb": memory_info.vms / 1024 / 1024
            }
            
        except Exception as e:
            self.logger.error(f"获取内存使用情况失败: {e}")
            return {}
    
    def optimize_image_processing(self, image: np.ndarray) -> np.ndarray:
        """
        优化图像处理内存使用
        
        Args:
            image: 输入图像
            
        Returns:
            np.ndarray: 优化后的图像
        """
        try:
            # 从内存池获取数组
            optimized_image = self.image_pool.get_array(image.shape, image.dtype)
            
            # 复制数据
            np.copyto(optimized_image, image)
            
            return optimized_image
            
        except Exception as e:
            self.logger.error(f"图像处理内存优化失败: {e}")
            return image.copy()
    
    def release_image(self, image: np.ndarray) -> None:
        """
        释放图像到内存池
        
        Args:
            image: 要释放的图像
        """
        try:
            self.image_pool.return_array(image)
        except Exception as e:
            self.logger.error(f"释放图像失败: {e}")
    
    def cache_result(self, key: str, result: Any) -> None:
        """
        缓存结果（使用弱引用）
        
        Args:
            key: 缓存键
            result: 要缓存的结果
        """
        try:
            self.weak_cache[key] = result
        except Exception as e:
            self.logger.error(f"缓存结果失败: {e}")
    
    def get_cached_result(self, key: str) -> Optional[Any]:
        """
        获取缓存的结果
        
        Args:
            key: 缓存键
            
        Returns:
            Optional[Any]: 缓存的结果
        """
        try:
            return self.weak_cache.get(key)
        except Exception as e:
            self.logger.error(f"获取缓存结果失败: {e}")
            return None
    
    def clear_cache(self) -> None:
        """清空缓存"""
        try:
            self.weak_cache.clear()
            self.image_pool.clear()
            self.result_pool.clear()
            
            self.logger.info("缓存已清空")
            
        except Exception as e:
            self.logger.error(f"清空缓存失败: {e}")
    
    def force_cleanup(self) -> None:
        """强制内存清理"""
        try:
            # 清空缓存
            self.clear_cache()
            
            # 强制垃圾回收
            collected = gc.collect()
            self.gc_count += 1
            self.cleanup_count += 1
            
            # 获取清理后的内存使用
            memory_after = self.get_memory_usage()
            
            self.logger.info(f"强制内存清理完成，回收对象: {collected}, "
                           f"当前内存使用: {memory_after.get('memory_percent', 0):.1%}")
            
        except Exception as e:
            self.logger.error(f"强制内存清理失败: {e}")
    
    def _auto_gc_check(self) -> None:
        """自动垃圾回收检查"""
        try:
            # 获取垃圾回收统计
            gc_stats = gc.get_stats()
            
            # 检查是否需要垃圾回收
            for generation, stats in enumerate(gc_stats):
                if stats['collections'] > self.gc_threshold:
                    collected = gc.collect(generation)
                    if collected > 0:
                        self.gc_count += 1
                        self.logger.debug(f"自动垃圾回收 generation {generation}: {collected} 对象")
                    break
                    
        except Exception as e:
            self.logger.error(f"自动垃圾回收检查失败: {e}")
    
    def optimize_numpy_arrays(self, arrays: List[np.ndarray]) -> List[np.ndarray]:
        """
        优化NumPy数组内存使用
        
        Args:
            arrays: 数组列表
            
        Returns:
            List[np.ndarray]: 优化后的数组列表
        """
        try:
            optimized_arrays = []
            
            for array in arrays:
                # 检查是否可以使用更小的数据类型
                if array.dtype == np.float64:
                    # 尝试转换为float32
                    if np.allclose(array, array.astype(np.float32)):
                        array = array.astype(np.float32)
                
                elif array.dtype == np.int64:
                    # 尝试转换为int32
                    if np.all(array >= np.iinfo(np.int32).min) and np.all(array <= np.iinfo(np.int32).max):
                        array = array.astype(np.int32)
                
                # 检查是否可以压缩
                if not array.flags.c_contiguous:
                    array = np.ascontiguousarray(array)
                
                optimized_arrays.append(array)
            
            return optimized_arrays
            
        except Exception as e:
            self.logger.error(f"NumPy数组优化失败: {e}")
            return arrays
    
    def set_memory_limit(self, limit_mb: int) -> None:
        """
        设置内存限制
        
        Args:
            limit_mb: 内存限制（MB）
        """
        try:
            # 计算阈值
            system_memory = psutil.virtual_memory()
            limit_bytes = limit_mb * 1024 * 1024
            self.memory_threshold = limit_bytes / system_memory.total
            
            self.logger.info(f"内存限制已设置: {limit_mb}MB ({self.memory_threshold:.1%})")
            
        except Exception as e:
            self.logger.error(f"设置内存限制失败: {e}")
    
    def get_optimization_stats(self) -> Dict[str, Any]:
        """获取优化统计信息"""
        try:
            memory_usage = self.get_memory_usage()
            
            return {
                "memory_usage": memory_usage,
                "peak_memory_percent": self.peak_memory,
                "gc_count": self.gc_count,
                "cleanup_count": self.cleanup_count,
                "image_pool_stats": self.image_pool.get_stats(),
                "result_pool_stats": self.result_pool.get_stats(),
                "weak_cache_size": len(self.weak_cache),
                "monitoring_enabled": self.monitoring_enabled
            }
            
        except Exception as e:
            self.logger.error(f"获取优化统计失败: {e}")
            return {}
    
    def configure_gc(self, 
                    auto_enabled: bool = True,
                    threshold: int = 1000,
                    generation_thresholds: Optional[tuple] = None) -> None:
        """
        配置垃圾回收
        
        Args:
            auto_enabled: 是否启用自动垃圾回收
            threshold: 垃圾回收阈值
            generation_thresholds: 分代垃圾回收阈值
        """
        try:
            self.auto_gc_enabled = auto_enabled
            self.gc_threshold = threshold
            
            if generation_thresholds:
                gc.set_threshold(*generation_thresholds)
            
            self.logger.info(f"垃圾回收配置已更新: auto={auto_enabled}, threshold={threshold}")
            
        except Exception as e:
            self.logger.error(f"配置垃圾回收失败: {e}")
    
    def memory_profiler(self, func: Callable) -> Callable:
        """
        内存分析装饰器
        
        Args:
            func: 要分析的函数
            
        Returns:
            Callable: 装饰后的函数
        """
        def wrapper(*args, **kwargs):
            # 记录开始内存
            start_memory = self.get_memory_usage()
            start_time = time.time()
            
            try:
                # 执行函数
                result = func(*args, **kwargs)
                
                # 记录结束内存
                end_memory = self.get_memory_usage()
                end_time = time.time()
                
                # 计算内存增长
                memory_delta = end_memory.get('rss_mb', 0) - start_memory.get('rss_mb', 0)
                execution_time = end_time - start_time
                
                self.logger.info(f"函数 {func.__name__} 内存分析: "
                               f"内存增长={memory_delta:.2f}MB, "
                               f"执行时间={execution_time:.3f}s")
                
                return result
                
            except Exception as e:
                self.logger.error(f"函数 {func.__name__} 执行失败: {e}")
                raise
        
        return wrapper
    
    def suggest_optimizations(self) -> List[str]:
        """建议优化措施"""
        suggestions = []
        
        try:
            stats = self.get_optimization_stats()
            memory_usage = stats.get('memory_usage', {})
            
            # 检查内存使用率
            memory_percent = memory_usage.get('memory_percent', 0)
            if memory_percent > 0.7:
                suggestions.append("内存使用率过高，建议增加内存或优化算法")
            
            # 检查缓存命中率
            image_pool_stats = stats.get('image_pool_stats', {})
            hit_rate = image_pool_stats.get('hit_rate', 0)
            if hit_rate < 0.5:
                suggestions.append("内存池命中率较低，建议增加池大小或优化使用模式")
            
            # 检查垃圾回收频率
            if self.gc_count > 100:
                suggestions.append("垃圾回收频率过高，建议优化对象生命周期管理")
            
            # 检查峰值内存
            if self.peak_memory > 0.9:
                suggestions.append("峰值内存使用过高，建议实施内存限制")
            
            if not suggestions:
                suggestions.append("内存使用状况良好，无需特殊优化")
            
        except Exception as e:
            self.logger.error(f"生成优化建议失败: {e}")
            suggestions.append("无法分析内存状况")
        
        return suggestions
