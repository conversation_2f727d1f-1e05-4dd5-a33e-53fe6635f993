#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
源代码对话框 - 修复版本
显示与GUI完全相同的检测源代码
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTextEdit, QPushButton,
    QLabel, QTabWidget, QWidget, QMessageBox, QApplication,
    QPlainTextEdit, QComboBox, QFileDialog, QSplitter
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
from PyQt6.QtGui import QFont, QTextCursor
import subprocess
import sys
import os
import time
import tempfile
from pathlib import Path

class CodeExecutionThread(QThread):
    """Code execution thread with proper UTF-8 encoding support"""
    output_signal = pyqtSignal(str)
    finished_signal = pyqtSignal(int)

    def __init__(self, code: str):
        super().__init__()
        self.code = code

    def run(self):
        """Execute code with proper UTF-8 encoding handling"""
        temp_file = None
        try:
            # 检查代码中的依赖库
            missing_deps = self.check_dependencies(self.code)
            if missing_deps:
                self.output_signal.emit(f"⚠️ 检测到缺失的依赖库: {', '.join(missing_deps)}")
                self.output_signal.emit("💡 正在尝试自动安装...")

                # 尝试自动安装缺失的依赖
                install_success = self.auto_install_dependencies(missing_deps)
                if not install_success:
                    self.output_signal.emit("❌ 自动安装失败，请手动安装依赖库")
                    self.output_signal.emit(f"   pip install {' '.join(missing_deps)}")
                    self.finished_signal.emit(1)
                    return
                else:
                    self.output_signal.emit("✅ 依赖库安装成功")

            # Ensure the code has proper UTF-8 encoding declaration
            if not self.code.startswith('#!/usr/bin/env python3'):
                code_with_encoding = '#!/usr/bin/env python3\n# -*- coding: utf-8 -*-\n' + self.code
            else:
                code_with_encoding = self.code

            # Create temporary file with explicit UTF-8 encoding
            import tempfile
            temp_fd, temp_file = tempfile.mkstemp(suffix='.py', text=True)

            try:
                # Write with explicit UTF-8 encoding
                with os.fdopen(temp_fd, 'w', encoding='utf-8', newline='\n') as f:
                    f.write(code_with_encoding)
                    f.flush()
                    os.fsync(f.fileno())  # Ensure data is written to disk
            except Exception as write_error:
                self.output_signal.emit(f"❌ Failed to write temporary file: {write_error}")
                self.finished_signal.emit(1)
                return

            # Set environment variables for proper UTF-8 handling
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'
            env['PYTHONLEGACYWINDOWSFSENCODING'] = '0'
            env['PYTHONUTF8'] = '1'

            # 确保使用当前环境的Python解释器
            python_executable = sys.executable

            # 检查是否在虚拟环境中
            if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
                # 在虚拟环境中，确保使用虚拟环境的Python
                venv_python = Path(sys.prefix) / "Scripts" / "python.exe"
                if venv_python.exists():
                    python_executable = str(venv_python)

            # 添加项目路径到环境变量
            project_root = str(Path(__file__).parent.parent.parent.parent)
            if 'PYTHONPATH' in env:
                env['PYTHONPATH'] = f"{project_root};{env['PYTHONPATH']}"
            else:
                env['PYTHONPATH'] = project_root

            # Execute with proper encoding settings
            process = subprocess.Popen(
                [python_executable, '-u', temp_file],  # -u for unbuffered output
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                encoding='utf-8',
                errors='replace',  # Replace invalid characters instead of failing
                env=env,
                cwd=os.getcwd(),
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )

            # Read output with proper encoding handling
            while True:
                try:
                    output = process.stdout.readline()
                    if output == '' and process.poll() is not None:
                        break
                    if output:
                        # Ensure output is properly decoded
                        clean_output = output.strip()
                        if clean_output:
                            self.output_signal.emit(clean_output)
                except UnicodeDecodeError as decode_error:
                    self.output_signal.emit(f"⚠️ Encoding warning: {decode_error}")
                    continue

            return_code = process.poll()
            self.finished_signal.emit(return_code)

        except Exception as e:
            error_msg = f"❌ 执行失败: {str(e)}"
            try:
                self.output_signal.emit(error_msg)
            except:
                # Fallback if even error emission fails
                print(error_msg)
            self.finished_signal.emit(1)
        finally:
            # Clean up temporary file
            if temp_file and os.path.exists(temp_file):
                try:
                    os.unlink(temp_file)
                except:
                    pass

    def check_dependencies(self, code: str) -> list:
        """检查代码中的依赖库"""
        import re

        # 常见的依赖库映射
        dependency_map = {
            'pyautogui': 'pyautogui',
            'cv2': 'opencv-python',
            'PIL': 'pillow',
            'numpy': 'numpy',
            'pandas': 'pandas',
            'matplotlib': 'matplotlib',
            'requests': 'requests',
            'selenium': 'selenium',
        }

        # 提取import语句
        import_pattern = r'(?:from\s+(\w+)|import\s+(\w+))'
        imports = re.findall(import_pattern, code)

        # 检查缺失的依赖
        missing_deps = []
        for from_import, direct_import in imports:
            module = from_import or direct_import
            if module in dependency_map:
                try:
                    __import__(module)
                except ImportError:
                    package_name = dependency_map[module]
                    if package_name not in missing_deps:
                        missing_deps.append(package_name)

        return missing_deps

    def auto_install_dependencies(self, packages: list) -> bool:
        """自动安装依赖库"""
        try:
            for package in packages:
                self.output_signal.emit(f"📥 安装 {package}...")
                result = subprocess.run(
                    [sys.executable, '-m', 'pip', 'install', package],
                    capture_output=True,
                    text=True,
                    timeout=60  # 60秒超时
                )

                if result.returncode == 0:
                    self.output_signal.emit(f"✅ {package} 安装成功")
                else:
                    self.output_signal.emit(f"❌ {package} 安装失败: {result.stderr}")
                    return False

            return True

        except subprocess.TimeoutExpired:
            self.output_signal.emit("❌ 安装超时")
            return False
        except Exception as e:
            self.output_signal.emit(f"❌ 安装异常: {e}")
            return False

class SourceCodeDialog(QDialog):
    """源代码对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)

        # 设置对话框图标
        self.setup_dialog_icon()
        self.parent_window = parent
        self.execution_thread = None
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("📄 源代码编辑器")
        self.setGeometry(100, 100, 1200, 800)
        
        layout = QVBoxLayout()
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        
        # GUI检测方法复制标签页
        self.create_gui_detector_tab()
        
        # 使用示例标签页
        self.create_usage_example_tab()
        
        layout.addWidget(self.tab_widget)
        
        # 按钮布局
        button_layout = QHBoxLayout()
        
        self.run_button = QPushButton("▶️ 运行代码")
        self.run_button.clicked.connect(self.run_current_code)
        
        self.copy_button = QPushButton("📋 复制代码")
        self.copy_button.clicked.connect(self.copy_current_code)
        
        self.save_button = QPushButton("💾 保存代码")
        self.save_button.clicked.connect(self.save_current_code)

        self.export_button = QPushButton("📦 导出为独立脚本")
        self.export_button.clicked.connect(self.export_as_standalone_script)
        self.export_button.setToolTip("导出完整可运行的独立脚本包")

        button_layout.addWidget(self.run_button)
        button_layout.addWidget(self.copy_button)
        button_layout.addWidget(self.save_button)
        button_layout.addWidget(self.export_button)
        button_layout.addStretch()
        
        layout.addLayout(button_layout)
        
        # 输出面板
        self.output_text = QPlainTextEdit()
        self.output_text.setMaximumHeight(200)
        self.output_text.setPlainText("📋 输出面板 - 代码执行结果将显示在这里")
        
        layout.addWidget(QLabel("📋 执行输出:"))
        layout.addWidget(self.output_text)
        
        self.setLayout(layout)

    def export_as_standalone_script(self):
        """导出为独立脚本"""
        try:
            from PyQt6.QtWidgets import QInputDialog, QMessageBox, QProgressDialog
            from PyQt6.QtCore import QThread, pyqtSignal
            from ...utils.code_exporter import CodeExporter

            # 获取当前代码内容
            current_code = self.get_current_code()
            if not current_code.strip():
                QMessageBox.warning(self, "警告", "当前没有可导出的代码内容")
                return

            # 获取模板名称
            template_name, ok = QInputDialog.getText(
                self, "导出设置", "请输入脚本名称:", text="YOLO检测脚本"
            )
            if not ok or not template_name.strip():
                return

            # 获取描述
            description, ok = QInputDialog.getText(
                self, "导出设置", "请输入脚本描述:",
                text="基于YOLO的目标检测自动化脚本"
            )
            if not ok:
                description = "YOLO目标检测脚本"

            # 创建进度对话框
            progress = QProgressDialog("正在导出脚本...", "取消", 0, 100, self)
            progress.setWindowTitle("导出进度")
            progress.setModal(True)
            progress.show()

            # 执行导出
            exporter = CodeExporter()

            # 更新进度
            progress.setValue(20)
            self.output_text.appendPlainText("📦 开始导出独立脚本...")

            progress.setValue(50)
            self.output_text.appendPlainText(f"📝 脚本名称: {template_name}")
            self.output_text.appendPlainText(f"📋 脚本描述: {description}")

            progress.setValue(80)

            # 执行导出
            success, result = exporter.export_standalone_script(
                current_code, template_name, description
            )

            progress.setValue(100)
            progress.close()

            if success:
                self.output_text.appendPlainText(f"✅ 导出成功！")
                self.output_text.appendPlainText(f"📁 导出路径: {result}")

                # 询问是否打开导出目录
                reply = QMessageBox.question(
                    self, "导出成功",
                    f"脚本已成功导出到:\n{result}\n\n是否打开导出目录？",
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
                )

                if reply == QMessageBox.StandardButton.Yes:
                    import os
                    import subprocess
                    import platform

                    try:
                        if platform.system() == "Windows":
                            os.startfile(result)
                        elif platform.system() == "Darwin":  # macOS
                            subprocess.run(["open", result])
                        else:  # Linux
                            subprocess.run(["xdg-open", result])
                    except Exception as e:
                        QMessageBox.information(
                            self, "提示",
                            f"无法自动打开目录，请手动访问:\n{result}"
                        )
            else:
                self.output_text.appendPlainText(f"❌ 导出失败: {result}")
                QMessageBox.critical(self, "导出失败", f"导出过程中出现错误:\n{result}")

        except Exception as e:
            self.output_text.appendPlainText(f"❌ 导出异常: {e}")
            QMessageBox.critical(self, "错误", f"导出功能出现异常:\n{e}")

    def get_current_code(self) -> str:
        """获取当前标签页的代码内容"""
        try:
            current_index = self.tab_widget.currentIndex()
            current_widget = self.tab_widget.currentWidget()

            # 查找当前标签页中的代码编辑器
            code_editor = None
            for child in current_widget.findChildren(QTextEdit):
                if hasattr(child, 'toPlainText'):
                    code_editor = child
                    break

            if code_editor:
                return code_editor.toPlainText()
            else:
                return ""

        except Exception as e:
            self.output_text.appendPlainText(f"⚠️ 获取代码内容失败: {e}")
            return ""

    def setup_dialog_icon(self):
        """设置对话框图标"""
        try:
            from PyQt6.QtGui import QIcon

            # 图标文件路径
            icon_path = Path(__file__).parent.parent.parent.parent / "icons" / "yolo_detector_app.ico"

            if icon_path.exists():
                icon = QIcon(str(icon_path))
                self.setWindowIcon(icon)
                print(f"✅ 源代码对话框图标已设置")
            else:
                print(f"⚠️ 图标文件不存在: {icon_path}")

        except Exception as e:
            print(f"❌ 设置对话框图标失败: {e}")

    def create_gui_detector_tab(self):
        """创建GUI检测器标签页"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        code_editor = QTextEdit()
        code_editor.setFont(QFont("Consolas", 10))
        code_editor.setPlainText(self.get_gui_detector_code())
        
        layout.addWidget(QLabel("🎯 GUI检测方法完全复制版本:"))
        layout.addWidget(code_editor)
        
        tab.setLayout(layout)
        self.tab_widget.addTab(tab, "🎯 GUI检测复制")
    
    def create_usage_example_tab(self):
        """创建使用示例标签页 - 优化版"""
        tab = QWidget()
        main_layout = QVBoxLayout()

        # 标题和说明区域
        header_layout = QVBoxLayout()

        # 标题
        title_label = QLabel("📚 完整自动化操作使用示例")
        title_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #2c3e50; margin-bottom: 5px;")
        header_layout.addWidget(title_label)

        # 功能说明
        info_label = QLabel(
            "本示例包含4个完整的自动化操作场景，支持代码编辑、保存和独立运行：\n"
            "🏢 办公软件自动化 - 智能点击和操作  🎯 多目标操作 - 复杂拖拽和协调\n"
            "🔍 目标选择策略 - 各种选择方法  🛡️ 错误处理机制 - 安全和重试机制"
        )
        info_label.setStyleSheet(
            "color: #34495e; padding: 8px; background-color: #ecf0f1; "
            "border-radius: 5px; margin-bottom: 10px; font-size: 11px;"
        )
        header_layout.addWidget(info_label)
        main_layout.addLayout(header_layout)

        # 工具栏区域
        toolbar_layout = QHBoxLayout()

        # 模板选择
        template_label = QLabel("📋 代码模板:")
        toolbar_layout.addWidget(template_label)

        self.template_combo = QComboBox()
        self.template_combo.addItems([
            "完整示例 (4个场景)",
            "办公软件自动化",
            "多目标操作",
            "目标选择策略",
            "错误处理机制",
            "独立运行版本"
        ])
        self.template_combo.currentTextChanged.connect(self.on_template_changed)
        toolbar_layout.addWidget(self.template_combo)

        toolbar_layout.addStretch()

        # 状态指示器
        self.status_label = QLabel("📄 原始代码")
        self.status_label.setStyleSheet("color: #7f8c8d; font-size: 10px;")
        toolbar_layout.addWidget(self.status_label)

        main_layout.addLayout(toolbar_layout)

        # 代码编辑器区域
        editor_layout = QVBoxLayout()

        # 编辑器标签和按钮
        editor_header = QHBoxLayout()
        editor_label = QLabel("💻 示例代码编辑器:")
        editor_header.addWidget(editor_label)

        editor_header.addStretch()

        # 编辑器工具按钮
        self.save_edit_btn = QPushButton("💾 保存编辑")
        self.save_edit_btn.setToolTip("保存当前编辑的代码到临时文件")
        self.save_edit_btn.clicked.connect(self.save_code_edits)
        editor_header.addWidget(self.save_edit_btn)

        self.reset_btn = QPushButton("🔄 恢复原始")
        self.reset_btn.setToolTip("恢复到原始示例代码")
        self.reset_btn.clicked.connect(self.reset_to_original)
        editor_header.addWidget(self.reset_btn)

        self.export_btn = QPushButton("📤 导出文件")
        self.export_btn.setToolTip("导出代码到外部Python文件")
        self.export_btn.clicked.connect(self.export_code_to_file)
        editor_header.addWidget(self.export_btn)

        editor_layout.addLayout(editor_header)

        # 代码编辑器
        self.example_code_editor = QTextEdit()
        self.example_code_editor.setFont(QFont("Consolas", 10))
        self.example_code_editor.setPlainText(self.get_usage_example_code())
        self.example_code_editor.textChanged.connect(self.on_code_changed)

        # 设置语法高亮（简单版）
        self.setup_syntax_highlighting()

        editor_layout.addWidget(self.example_code_editor)
        main_layout.addLayout(editor_layout)

        # 运行结果显示区域
        result_layout = QVBoxLayout()
        result_header = QHBoxLayout()

        result_label = QLabel("📊 运行结果:")
        result_header.addWidget(result_label)

        result_header.addStretch()

        self.clear_result_btn = QPushButton("🗑️ 清空结果")
        self.clear_result_btn.clicked.connect(self.clear_run_results)
        result_header.addWidget(self.clear_result_btn)

        result_layout.addLayout(result_header)

        self.result_display = QTextEdit()
        self.result_display.setFont(QFont("Consolas", 9))
        self.result_display.setMaximumHeight(120)
        self.result_display.setReadOnly(True)
        self.result_display.setStyleSheet(
            "background-color: #2c3e50; color: #ecf0f1; "
            "border: 1px solid #34495e; border-radius: 3px;"
        )
        result_layout.addWidget(self.result_display)
        main_layout.addLayout(result_layout)

        # 使用提示
        tip_label = QLabel(
            "💡 使用提示：• 选择代码模板快速切换  • 编辑代码后点击'保存编辑'  • 使用'导出文件'创建独立脚本  • 查看运行结果了解执行状态"
        )
        tip_label.setStyleSheet(
            "color: #27ae60; padding: 6px; background-color: #d5f4e6; "
            "border-radius: 3px; font-size: 10px; margin-top: 5px;"
        )
        main_layout.addWidget(tip_label)

        # 初始化状态
        self.code_modified = False
        self.original_code = self.get_usage_example_code()
        self.saved_edits = {}  # 保存不同模板的编辑内容

        tab.setLayout(main_layout)
        self.tab_widget.addTab(tab, "📚 完整使用示例")
    
    def get_gui_detector_code(self) -> str:
        """Get GUI detector code with English comments to avoid UTF-8 issues"""
        return """#!/usr/bin/env python3
# -*- coding: utf-8 -*-
\"\"\"
GUI Detection Method Complete Copy
This code 100% replicates GUI detection calls to ensure identical results
\"\"\"

import sys
import os
import json
from typing import List, Dict, Any, Optional
from pathlib import Path

# Robust project path detection for temporary file execution
def setup_project_path():
    \"\"\"Setup project path for module imports\"\"\"
    # Method 1: Try to find project root by looking for src/yolo_opencv_detector
    current_dir = Path.cwd()

    # Check current directory and parent directories
    for path in [current_dir] + list(current_dir.parents):
        src_path = path / "src"
        yolo_path = src_path / "yolo_opencv_detector"

        if yolo_path.exists() and yolo_path.is_dir():
            print(f"信息: 找到项目根目录: {path}")
            if str(src_path) not in sys.path:
                sys.path.insert(0, str(src_path))
                print(f"信息: 已添加到sys.path: {src_path}")
            return True

    # Method 2: Try common project locations
    common_paths = [
        Path.cwd(),
        Path.cwd().parent,
        Path.cwd() / "yolo_opencv_run",
        Path.home() / "Documents" / "【看见上海】" / "yolo_opencv_run",
        Path("C:/Users") / os.environ.get("USERNAME", "") / "Documents" / "【看见上海】" / "yolo_opencv_run"
    ]

    for base_path in common_paths:
        if base_path.exists():
            src_path = base_path / "src"
            yolo_path = src_path / "yolo_opencv_detector"

            if yolo_path.exists() and yolo_path.is_dir():
                print(f"信息: 在常用位置找到项目: {base_path}")
                if str(src_path) not in sys.path:
                    sys.path.insert(0, str(src_path))
                    print(f"信息: 已添加到sys.path: {src_path}")
                return True

    # Method 3: Manual path specification (fallback)
    print("警告: 无法自动检测项目路径")
    print("信息: 请确保从项目目录运行")
    print("信息: 当前工作目录:", Path.cwd())
    print("信息: 当前sys.path:", sys.path[:3], "...")
    return False

# Setup project path
setup_project_path()

# Configuration parameters identical to GUI
CONFIDENCE_THRESHOLD = 0.5  # GUI default confidence threshold
NMS_THRESHOLD = 0.4         # GUI default NMS threshold

class GUIDetectorCopy:
    \"\"\"Class that completely replicates GUI detection methods\"\"\"

    def __init__(self):
        self.yolo_detector = None
        self.screen_capture = None
        self.config_manager = None
        self.smart_detection_manager = None
        self.detection_count = 0
        self.current_fps = 0.0

        self._init_gui_services()
        print("成功: GUI检测服务已初始化")

    def _init_gui_services(self):
        \"\"\"Initialize GUI services with enhanced error handling\"\"\"
        print("信息: 正在初始化GUI服务...")
        print(f"信息: 当前工作目录: {os.getcwd()}")
        print(f"信息: Python路径条目数: {len(sys.path)}")

        # Debug: Show relevant sys.path entries
        for i, path in enumerate(sys.path[:5]):
            print(f"信息: sys.path[{i}]: {path}")

        try:
            print("信息: 尝试导入yolo_opencv_detector模块...")

            # Import with detailed error reporting
            try:
                from yolo_opencv_detector.utils.config_manager import ConfigManager
                print("成功: ConfigManager导入成功")
            except ImportError as e:
                print(f"错误: ConfigManager导入失败: {e}")
                print("调试: 检查yolo_opencv_detector包是否可访问...")
                try:
                    import yolo_opencv_detector
                    print(f"调试: yolo_opencv_detector包位置: {yolo_opencv_detector.__file__}")
                except ImportError:
                    print("调试: 在sys.path中未找到yolo_opencv_detector包")
                raise

            try:
                from yolo_opencv_detector.core.yolo_detector_v2 import YOLODetectorV2
                print("成功: YOLODetectorV2导入成功")
            except ImportError as e:
                print(f"错误: YOLODetectorV2导入失败: {e}")
                raise

            try:
                from yolo_opencv_detector.core.screen_capture_v2 import ScreenCaptureServiceV2
                print("成功: ScreenCaptureServiceV2导入成功")
            except ImportError as e:
                print(f"错误: ScreenCaptureServiceV2导入失败: {e}")
                raise

            try:
                from yolo_opencv_detector.core.smart_detection_manager import SmartDetectionManager
                print("成功: SmartDetectionManager导入成功")
            except ImportError as e:
                print(f"错误: SmartDetectionManager导入失败: {e}")
                raise

            # Initialize services
            print("信息: 所有导入成功，正在初始化服务...")

            self.config_manager = ConfigManager()
            print("成功: ConfigManager已初始化")

            self.yolo_detector = YOLODetectorV2(self.config_manager)
            print("成功: YOLODetectorV2已初始化")

            self.screen_capture = ScreenCaptureServiceV2()
            print("成功: ScreenCaptureServiceV2已初始化")

            self.smart_detection_manager = SmartDetectionManager()
            print("成功: SmartDetectionManager已初始化")

        except ImportError as e:
            print(f"错误: GUI服务导入失败: {e}")
            print("解决方案: 请确保从项目根目录运行")
            print("解决方案: 项目结构应为: project_root/src/yolo_opencv_detector/")
            print("当前: 请尝试从包含'src'文件夹的目录运行")
            raise
        except Exception as e:
            print(f"错误: GUI服务初始化失败: {e}")
            import traceback
            traceback.print_exc()
            raise

    def gui_detect_screen(self) -> List[Dict[str, Any]]:
        \"\"\"Complete copy of GUI screen detection method with 模式 detection\"\"\"
        print("信息: 开始屏幕检测（GUI方法复制）...")

        try:
            # Step 1: Screenshot (identical to GUI)
            image = self.screen_capture.capture_fullscreen()
            if image is None:
                print("错误: 截图失败")
                return []

            print(f"成功: 截图已捕获: {image.shape}")

            # Step 2: Detect current GUI detection 模式
            detection_模式 = self._detect_gui_模式()
            print(f"信息: 检测到GUI模式: {detection_模式}")

            # Step 3: Configure smart detection manager based on GUI 模式
            self._configure_detection_模式(detection_模式)

            # Step 4: Execute detection (identical to GUI)
            detections = []
            if self.yolo_detector:
                # Get current GUI parameters
                gui_params = self._get_gui_detection_params()
                confidence = gui_params.get('confidence_threshold', CONFIDENCE_THRESHOLD)
                nms_threshold = gui_params.get('nms_threshold', NMS_THRESHOLD)

                print(f"信息: 使用GUI参数 - 置信度: {confidence}, NMS: {nms_threshold}")

                detections = self.yolo_detector.detect(
                    image,
                    confidence=confidence,
                    nms_threshold=nms_threshold
                )

                self.detection_count += 1
                stats = self.yolo_detector.get_performance_stats()
                self.current_fps = stats.get('fps', 0.0)

                print(f"成功: YOLO检测完成: {len(detections)} 个目标")
                print(f"统计: 性能: FPS={self.current_fps:.2f}")

            # Step 5: Smart detection processing (identical to GUI)
            if self.smart_detection_manager:
                detection_result = self.smart_detection_manager.process_detections(image, detections)
                actual_detections = detection_result.get("detections", [])
                actual_模式 = detection_result.get("detection_模式", "yolo_detection")

                print(f"成功: 智能检测处理完成: {len(actual_detections)} 个目标 ({actual_模式})")

                return self._convert_detections(actual_detections, actual_模式)
            else:
                return self._convert_detections(detections, "yolo_detection")

        except Exception as e:
            print(f"错误: GUI检测方法执行失败: {e}")
            import traceback
            traceback.print_exc()
            return []

    def _detect_gui_模式(self) -> str:
        \"\"\"Detect current GUI detection 模式 by checking GUI state\"\"\"
        try:
            # Method 1: Try to read GUI current template selection from config
            current_template = self._get_gui_current_template()
            if current_template:
                print(f"信息: GUI已选择模板: {current_template}")
                return "template_matching"

            # Method 2: Check if there are template files and try to detect recent selection
            templates_dir = Path("templates")
            if templates_dir.exists():
                template_files = list(templates_dir.glob("*.png")) + list(templates_dir.glob("*.jpg"))
                if template_files:
                    print(f"信息: Found {len(template_files)} template files")

                    # Try to detect the most recently used template
                    recent_template = self._detect_recent_template_usage()
                    if recent_template:
                        print(f"信息: Detected recent template usage: {recent_template}")
                        return "template_matching"

                    # If templates exist but no recent usage, check for GUI state files
                    gui_state = self._check_gui_state_files()
                    if gui_state and gui_state.get('template_模式'):
                        return "template_matching"

            # Default to YOLO detection
            print("信息: No template selection detected, using YOLO 模式")
            return "yolo_detection"

        except Exception as e:
            print(f"警告: Failed to detect GUI 模式: {e}")
            return "yolo_detection"

    def _configure_detection_模式(self, 模式: str):
        \"\"\"Configure detection 模式 to match GUI\"\"\"
        try:
            if 模式 == "template_matching":
                # Step 1: Try to get GUI current selected template
                gui_template_name = self._get_gui_current_template()
                template_data = None

                if gui_template_name:
                    print(f"信息: GUI selected template: {gui_template_name}")
                    # Try to load the specific template that GUI is using
                    template_data = self._load_specific_template(gui_template_name)

                if not template_data:
                    print("警告: Could not load GUI selected template, trying available templates...")
                    # Fallback: load any available template
                    template_data = self._load_available_template()

                if template_data:
                    self.smart_detection_manager.set_template_matching(True, template_data)
                    print(f"信息: 已启用模板匹配，使用模板: {template_data['name']}")
                    if gui_template_name:
                        # Enhanced template matching verification
                        is_match = self._verify_template_match(gui_template_name, template_data['name'])

                        if is_match:
                            print("✅ 成功: 使用与GUI相同的模板 (verified match)")
                        else:
                            print("⚠️ 警告: 使用与GUI不同的模板")
                    else:
                        print("⚠️ 警告: 使用与GUI不同的模板")
                else:
                    print("警告: Template matching 模式 detected but no valid template found")
                    print("信息: Falling back to YOLO detection")
                    self.smart_detection_manager.set_template_matching(False)
            else:
                # Use YOLO detection
                self.smart_detection_manager.set_template_matching(False)
                print("信息: Using YOLO detection 模式")

        except Exception as e:
            print(f"错误: Failed to configure detection 模式: {e}")
            # Fallback to YOLO detection
            self.smart_detection_manager.set_template_matching(False)

    def _load_specific_template(self, template_name: str) -> Optional[Dict[str, Any]]:
        \"\"\"Load a specific template by name with fuzzy matching\"\"\"
        try:
            templates_dir = Path("templates")
            if not templates_dir.exists():
                print(f"警告: 未找到模板目录")
                return None

            # Get all template files
            template_files = list(templates_dir.glob("*.png")) + list(templates_dir.glob("*.jpg")) + list(templates_dir.glob("*.jpeg")) + list(templates_dir.glob("*.bmp"))

            if not template_files:
                print(f"警告: No template files found in {templates_dir}")
                return None

            # Method 1: Exact match
            possible_extensions = ['.png', '.jpg', '.jpeg', '.bmp']
            for ext in possible_extensions:
                candidate = templates_dir / f"{template_name}{ext}"
                if candidate.exists():
                    template_file = candidate
                    break
            else:
                template_file = None

            # Method 2: Enhanced fuzzy matching if exact match fails
            if not template_file:
                print(f"信息: 精确匹配失败: '{template_name}', 尝试增强匹配...")

                template_file = self._find_template_with_enhanced_matching(template_name, template_files)

                if template_file:
                    print(f"信息: 找到增强匹配: {template_file.stem}")
                else:
                    print(f"警告: 未找到增强匹配: {template_name}")

            if not template_file:
                print(f"警告: 未找到匹配的模板文件: {template_name}")
                print(f"可用模板: {[f.stem for f in template_files]}")
                return None

            # Load template image
            import cv2
            template_image = cv2.imread(str(template_file))
            if template_image is None:
                print(f"错误: 无法加载模板图像: {template_file}")
                return None

            actual_name = template_file.stem
            print(f"成功: 已加载指定模板: {actual_name} (requested: {template_name})")
            return {
                'name': actual_name,  # Use actual file name
                'image': template_image,
                'threshold': 0.8,  # Default threshold
                'path': str(template_file)
            }

        except Exception as e:
            print(f"错误: Failed to load specific template {template_name}: {e}")
            return None

    def _load_available_template(self) -> Optional[Dict[str, Any]]:
        \"\"\"Load any available template for template matching (fallback)\"\"\"
        try:
            templates_dir = Path("templates")
            if not templates_dir.exists():
                return None

            # Look for template files
            template_files = list(templates_dir.glob("*.png")) + list(templates_dir.glob("*.jpg"))
            if not template_files:
                return None

            # Sort by modification time (most recent first) to get likely current template
            template_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)

            # Try to load templates in order of recency
            for template_file in template_files:
                try:
                    # Load template image
                    import cv2
                    template_image = cv2.imread(str(template_file))
                    if template_image is not None:
                        print(f"成功: 已加载备用模板: {template_file.stem}")
                        return {
                            'name': template_file.stem,
                            'image': template_image,
                            'threshold': 0.8,  # Default threshold
                            'path': str(template_file)
                        }
                except Exception as e:
                    print(f"警告: Could not load template {template_file}: {e}")
                    continue

            print("错误: No valid template files found")
            return None

        except Exception as e:
            print(f"错误: Failed to load available template: {e}")
            return None

    def _get_gui_detection_params(self) -> Dict[str, Any]:
        \"\"\"Get current GUI detection parameters\"\"\"
        # This is a simplified version - in real implementation,
        # this would read from the actual GUI state
        return {
            'confidence_threshold': CONFIDENCE_THRESHOLD,
            'nms_threshold': NMS_THRESHOLD,
            'detection_interval': 1.0
        }

    def _get_gui_current_template(self) -> Optional[str]:
        \"\"\"Get GUI current selected template name\"\"\"
        try:
            # Method 1: Try to read from config files
            config_file = Path("configs/user_config.yaml")
            if config_file.exists():
                try:
                    import yaml
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config = yaml.safe_load(f)

                    # Check for current template in config
                    current_template = config.get('current_template')
                    if current_template:
                        print(f"信息: 在配置中找到当前模板: {current_template}")
                        return current_template
                except Exception as e:
                    print(f"调试: Could not read config file: {e}")

            # Method 2: Try to detect from recent log files
            recent_template = self._parse_recent_logs_for_template()
            if recent_template:
                return recent_template

            # Method 3: Try to detect from GUI state (if available)
            gui_template = self._detect_gui_template_from_runtime()
            if gui_template:
                return gui_template

            return None

        except Exception as e:
            print(f"警告: Failed to get GUI current template: {e}")
            return None

    def _parse_recent_logs_for_template(self) -> Optional[str]:
        \"\"\"Parse recent log files to find current template selection\"\"\"
        try:
            # Look for recent log files
            log_patterns = [
                "logs/*.log",
                "*.log",
                "yolo_opencv_detector.log"
            ]

            recent_logs = []
            for pattern in log_patterns:
                log_files = list(Path(".").glob(pattern))
                recent_logs.extend(log_files)

            if not recent_logs:
                return None

            # Sort by modification time, get most recent
            recent_logs.sort(key=lambda x: x.stat().st_mtime, reverse=True)

            # Parse the most recent log file
            for log_file in recent_logs[:3]:  # Check up to 3 most recent logs
                try:
                    with open(log_file, 'r', encoding='utf-8') as f:
                        lines = f.readlines()

                    # Look for template selection in recent lines (last 100 lines)
                    for line in reversed(lines[-100:]):
                        if "选择模板:" in line:
                            # Extract template name from log line
                            template_name = line.split("选择模板:")[-1].strip()
                            if template_name and template_name != "unknown":
                                print(f"信息: 在日志中找到最近的模板选择: {template_name}")
                                return template_name
                        elif "template_selected" in line or "Template selected" in line:
                            # Alternative log format
                            parts = line.split()
                            for i, part in enumerate(parts):
                                if "template" in part.lower() and i + 1 < len(parts):
                                    template_name = parts[i + 1].strip()
                                    if template_name:
                                        print(f"信息: Found template selection: {template_name}")
                                        return template_name
                except Exception as e:
                    print(f"调试: Could not parse log file {log_file}: {e}")
                    continue

            return None

        except Exception as e:
            print(f"警告: Failed to parse recent logs: {e}")
            return None

    def _detect_recent_template_usage(self) -> Optional[str]:
        \"\"\"Detect recent template usage from various sources\"\"\"
        try:
            # Check for recent template usage indicators
            templates_dir = Path("templates")
            if not templates_dir.exists():
                return None

            # Method 1: Check file access times
            template_files = list(templates_dir.glob("*.png")) + list(templates_dir.glob("*.jpg"))
            if not template_files:
                return None

            # Sort by access time (most recently accessed first)
            template_files.sort(key=lambda x: x.stat().st_atime, reverse=True)

            # Get the most recently accessed template
            recent_template = template_files[0]
            template_name = recent_template.stem

            # Check if it was accessed recently (within last hour)
            import time
            current_time = time.time()
            access_time = recent_template.stat().st_atime

            if current_time - access_time < 3600:  # Within last hour
                print(f"信息: Recently accessed template: {template_name}")
                return template_name

            return None

        except Exception as e:
            print(f"警告: Failed to detect recent template usage: {e}")
            return None

    def _check_gui_state_files(self) -> Dict[str, Any]:
        \"\"\"Check GUI state files for current configuration\"\"\"
        try:
            # Check for various GUI state files
            state_files = [
                "gui_state.json",
                "configs/gui_state.json",
                "temp/gui_state.json",
                ".gui_state"
            ]

            for state_file in state_files:
                state_path = Path(state_file)
                if state_path.exists():
                    try:
                        with open(state_path, 'r', encoding='utf-8') as f:
                            state_data = json.load(f)

                        if 'template_模式' in state_data or 'current_template' in state_data:
                            print(f"信息: Found GUI state in {state_file}")
                            return state_data
                    except Exception as e:
                        print(f"调试: Could not read state file {state_file}: {e}")
                        continue

            return {}

        except Exception as e:
            print(f"警告: Failed to check GUI state files: {e}")
            return {}

    def _detect_gui_template_from_runtime(self) -> Optional[str]:
        \"\"\"Try to detect GUI template from runtime environment\"\"\"
        try:
            # This is a placeholder for more advanced GUI state detection
            # In a real implementation, this could:
            # 1. Check shared memory
            # 2. Read from GUI process communication
            # 3. Check temporary state files
            # 4. Use IPC mechanisms

            # For now, return None as this requires more complex implementation
            return None

        except Exception as e:
            print(f"警告: Failed to detect GUI template from runtime: {e}")
            return None

    def _find_template_with_enhanced_matching(self, template_name: str, template_files: List[Path]) -> Optional[Path]:
        \"\"\"Enhanced template matching with multiple strategies\"\"\"
        try:
            template_name_str = str(template_name).strip()

            # Strategy 1: Prefix matching (E -> E_*)
            print(f"信息: 尝试前缀匹配: '{template_name_str}'...")
            for candidate_file in template_files:
                candidate_stem = candidate_file.stem
                if candidate_stem.startswith(template_name_str + '_'):
                    print(f"成功: 找到前缀匹配: {candidate_stem}")
                    return candidate_file

            # Strategy 2: Suffix matching (*_E)
            print(f"信息: Trying suffix matching for '{template_name_str}'...")
            for candidate_file in template_files:
                candidate_stem = candidate_file.stem
                if candidate_stem.endswith('_' + template_name_str):
                    print(f"成功: Found suffix match: {candidate_stem}")
                    return candidate_file

            # Strategy 3: Contains matching (*E* or *_E_*)
            print(f"信息: Trying contains matching for '{template_name_str}'...")
            for candidate_file in template_files:
                candidate_stem = candidate_file.stem
                if ('_' + template_name_str + '_' in candidate_stem or
                    candidate_stem.startswith(template_name_str) or
                    candidate_stem.endswith(template_name_str)):
                    print(f"成功: Found contains match: {candidate_stem}")
                    return candidate_file

            # Strategy 4: Case-insensitive matching
            print(f"信息: Trying case-insensitive matching for '{template_name_str}'...")
            template_lower = template_name_str.lower()
            for candidate_file in template_files:
                candidate_stem = candidate_file.stem.lower()
                if (candidate_stem.startswith(template_lower + '_') or
                    candidate_stem.endswith('_' + template_lower) or
                    template_lower in candidate_stem):
                    print(f"成功: Found case-insensitive match: {candidate_file.stem}")
                    return candidate_file

            # Strategy 5: Fuzzy similarity matching
            print(f"信息: Trying fuzzy similarity matching for '{template_name_str}'...")
            best_match = None
            best_score = 0

            for candidate_file in template_files:
                candidate_stem = candidate_file.stem

                # Calculate similarity scores
                scores = []

                # Score 1: Direct substring match
                if template_name_str in candidate_stem:
                    scores.append(0.8)
                elif template_name_str.lower() in candidate_stem.lower():
                    scores.append(0.7)

                # Score 2: Prefix similarity
                if candidate_stem.startswith(template_name_str):
                    scores.append(0.9)
                elif candidate_stem.lower().startswith(template_name_str.lower()):
                    scores.append(0.8)

                # Score 3: Length-based similarity
                if len(template_name_str) > 0:
                    common_chars = sum(1 for c in template_name_str if c in candidate_stem)
                    length_score = common_chars / len(template_name_str)
                    scores.append(length_score * 0.6)

                # Use best score for this candidate
                if scores:
                    candidate_score = max(scores)
                    if candidate_score > best_score:
                        best_score = candidate_score
                        best_match = candidate_file

            # Use best match if score is good enough
            if best_match and best_score > 0.6:
                print(f"成功: Found fuzzy match: {best_match.stem} (score: {best_score:.2f})")
                return best_match

            # Strategy 6: First character matching (as last resort)
            if len(template_name_str) > 0:
                print(f"信息: Trying first character matching for '{template_name_str[0]}'...")
                first_char = template_name_str[0].lower()
                for candidate_file in template_files:
                    if candidate_file.stem.lower().startswith(first_char):
                        print(f"信息: Found first character match: {candidate_file.stem}")
                        return candidate_file

            print(f"警告: No match found for '{template_name_str}' using any strategy")
            return None

        except Exception as e:
            print(f"错误: 增强模板匹配失败: {e}")
            return None

    def _verify_template_match(self, gui_template_name: str, actual_template_name: str) -> bool:
        \"\"\"Verify if GUI template name matches actual template name\"\"\"
        try:
            gui_name = str(gui_template_name).strip()
            actual_name = str(actual_template_name).strip()

            # Exact match
            if gui_name == actual_name:
                return True

            # Prefix match (E matches E_20250706_103005)
            if actual_name.startswith(gui_name + '_'):
                return True

            # Suffix match (*_E matches E)
            if actual_name.endswith('_' + gui_name):
                return True

            # Contains match (E_*_something matches E)
            if gui_name in actual_name:
                return True

            # Case insensitive matching
            if (gui_name.lower() == actual_name.lower() or
                actual_name.lower().startswith(gui_name.lower() + '_') or
                gui_name.lower() in actual_name.lower()):
                return True

            # Clean comparison (remove separators)
            gui_clean = gui_name.replace('_', '').replace('-', '').replace(' ', '').lower()
            actual_clean = actual_name.replace('_', '').replace('-', '').replace(' ', '').lower()

            if gui_clean == actual_clean or gui_clean in actual_clean:
                return True

            return False

        except Exception as e:
            print(f"警告: 模板匹配验证失败: {e}")
            return False

    def _convert_detections(self, detections: List[Any], detection_模式: str) -> List[Dict[str, Any]]:
        \"\"\"Convert detection results to standard format\"\"\"
        results = []

        for i, det in enumerate(detections):
            try:
                if hasattr(det, 'bbox'):
                    bbox = det.bbox
                    if isinstance(bbox, (list, tuple)) and len(bbox) >= 4:
                        x, y, w, h = bbox[:4]

                        result = {
                            'id': i,
                            'class_name': getattr(det, 'class_name', 'unknown'),
                            'confidence': getattr(det, 'confidence', 0.0),
                            'bbox': {'x': int(x), 'y': int(y), 'width': int(w), 'height': int(h)},
                            'center': {'x': int(x + w / 2), 'y': int(y + h / 2)},
                            'area': int(w * h),
                            'detection_模式': detection_模式
                        }
                        results.append(result)

                elif isinstance(det, dict):
                    bbox = det.get('bbox', [0, 0, 0, 0])
                    if len(bbox) >= 4:
                        x, y, w, h = bbox[:4]

                        result = {
                            'id': i,
                            'class_name': det.get('class_name', 'unknown'),
                            'confidence': det.get('confidence', 0.0),
                            'bbox': {'x': int(x), 'y': int(y), 'width': int(w), 'height': int(h)},
                            'center': {'x': int(x + w / 2), 'y': int(y + h / 2)},
                            'area': int(w * h),
                            'detection_模式': detection_模式
                        }
                        results.append(result)
            except Exception as e:
                print(f"警告: Failed to convert detection result {i}: {e}")
                continue

        return results

    def print_results(self, results: List[Dict[str, Any]]):
        \"\"\"Print detection results\"\"\"
        if not results:
            print("信息: 未检测到目标")
            return

        print(f"RESULTS: GUI detection results: {len(results)} targets")
        print("=" * 70)

        for i, result in enumerate(results, 1):
            print(f"TARGET {i}:")
            print(f"   Class: {result['class_name']}")
            print(f"   Confidence: {result['confidence']:.3f}")
            print(f"   中心: ({result['center']['x']}, {result['center']['y']})")
            print(f"   BBox: x={result['bbox']['x']}, y={result['bbox']['y']}, w={result['bbox']['width']}, h={result['bbox']['height']}")
            print(f"   Detection Mode: {result.get('detection_模式', 'unknown')}")
            print()

        print("=" * 70)
        print(f"统计: Detection count={self.detection_count}, FPS={self.current_fps:.2f}")

    # ========================================
    # 🎯 目标操作和自动化办公功能模块
    # ========================================

    def parse_detection_results(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        \"\"\"
        解析检测结果，提取完整的目标信息

        Args:
            results: gui_detect_screen()返回的检测结果列表

        Returns:
            解析后的目标信息列表，包含完整的坐标和属性信息
        \"\"\"
        parsed_targets = []

        try:
            for i, result in enumerate(results):
                # 提取基础信息
                bbox = result.get('bbox', {})
                center = result.get('center', {})

                # 计算详细坐标信息
                x = bbox.get('x', 0)
                y = bbox.get('y', 0)
                width = bbox.get('width', 0)
                height = bbox.get('height', 0)

                # 计算边界框四个顶角坐标
                top_left = {'x': x, 'y': y}
                top_right = {'x': x + width, 'y': y}
                bottom_left = {'x': x, 'y': y + height}
                bottom_right = {'x': x + width, 'y': y + height}

                # 计算中心点坐标
                center_x = center.get('x', x + width // 2)
                center_y = center.get('y', y + height // 2)

                # 构建完整的目标信息
                target = {
                    'id': i,
                    'class_name': result.get('class_name', 'unknown'),
                    'confidence': result.get('confidence', 0.0),
                    'detection_模式': result.get('detection_模式', 'unknown'),

                    # 边界框信息
                    'bbox': {
                        'x': x, 'y': y, 'width': width, 'height': height,
                        'area': result.get('area', width * height)
                    },

                    # 坐标信息
                    'coordinates': {
                        'center': {'x': center_x, 'y': center_y},
                        'top_left': top_left,
                        'top_right': top_right,
                        'bottom_left': bottom_left,
                        'bottom_right': bottom_right,
                        'top_center': {'x': center_x, 'y': y},
                        'bottom_center': {'x': center_x, 'y': y + height},
                        'left_center': {'x': x, 'y': center_y},
                        'right_center': {'x': x + width, 'y': center_y}
                    },

                    # 尺寸信息
                    'dimensions': {
                        'width': width,
                        'height': height,
                        'area': width * height,
                        'perimeter': 2 * (width + height),
                        'aspect_ratio': width / height if height > 0 else 0
                    },

                    # 原始数据
                    'raw_data': result
                }

                parsed_targets.append(target)

        except Exception as e:
            print(f"错误: 检测结果解析失败: {e}")
            import traceback
            traceback.print_exc()

        return parsed_targets

    # ========================================
    # 🎯 目标选择策略方法
    # ========================================

    def select_target_by_confidence(self, targets: List[Dict[str, Any]], 模式: str = 'highest') -> Optional[Dict[str, Any]]:
        \"\"\"
        按置信度选择目标

        Args:
            targets: 解析后的目标列表
            模式: 'highest' 最高置信度, 'lowest' 最低置信度

        Returns:
            选中的目标，如果没有目标则返回None
        \"\"\"
        if not targets:
            return None

        try:
            if 模式 == 'highest':
                return max(targets, key=lambda t: t.get('confidence', 0))
            elif 模式 == 'lowest':
                return min(targets, key=lambda t: t.get('confidence', 0))
            else:
                print(f"警告: 未知 confidence 模式: {模式}")
                return targets[0]
        except Exception as e:
            print(f"错误: 按置信度选择目标失败: {e}")
            return None

    def select_target_by_position(self, targets: List[Dict[str, Any]], position: str) -> Optional[Dict[str, Any]]:
        \"\"\"
        按位置选择目标

        Args:
            targets: 解析后的目标列表
            position: 'leftmost', 'rightmost', 'topmost', 'bottommost', 'center'

        Returns:
            选中的目标，如果没有目标则返回None
        \"\"\"
        if not targets:
            return None

        try:
            if position == 'leftmost':
                return min(targets, key=lambda t: t['coordinates']['center']['x'])
            elif position == 'rightmost':
                return max(targets, key=lambda t: t['coordinates']['center']['x'])
            elif position == 'topmost':
                return min(targets, key=lambda t: t['coordinates']['center']['y'])
            elif position == 'bottommost':
                return max(targets, key=lambda t: t['coordinates']['center']['y'])
            elif position == 'center':
                # 选择最接近屏幕中心的目标
                screen_center_x = 960  # 假设1920x1080屏幕
                screen_center_y = 540
                return min(targets, key=lambda t:
                    ((t['coordinates']['center']['x'] - screen_center_x) ** 2 +
                     (t['coordinates']['center']['y'] - screen_center_y) ** 2) ** 0.5)
            else:
                print(f"警告: 未知 position 模式: {position}")
                return targets[0]
        except Exception as e:
            print(f"错误: 按位置选择目标失败: {e}")
            return None

    def select_target_by_size(self, targets: List[Dict[str, Any]], 模式: str = 'largest') -> Optional[Dict[str, Any]]:
        \"\"\"
        按大小选择目标

        Args:
            targets: 解析后的目标列表
            模式: 'largest' 最大面积, 'smallest' 最小面积

        Returns:
            选中的目标，如果没有目标则返回None
        \"\"\"
        if not targets:
            return None

        try:
            if 模式 == 'largest':
                return max(targets, key=lambda t: t['dimensions']['area'])
            elif 模式 == 'smallest':
                return min(targets, key=lambda t: t['dimensions']['area'])
            else:
                print(f"警告: 未知 size 模式: {模式}")
                return targets[0]
        except Exception as e:
            print(f"错误: 按大小选择目标失败: {e}")
            return None

    def select_target_by_class(self, targets: List[Dict[str, Any]], class_name: str) -> List[Dict[str, Any]]:
        \"\"\"
        按类别选择目标

        Args:
            targets: 解析后的目标列表
            class_name: 目标类别名称

        Returns:
            匹配的目标列表
        \"\"\"
        if not targets:
            return []

        try:
            return [t for t in targets if t.get('class_name', '').lower() == class_name.lower()]
        except Exception as e:
            print(f"错误: 按类别选择目标失败: {e}")
            return []

    def select_target_by_custom_condition(self, targets: List[Dict[str, Any]],
                                        condition_func) -> List[Dict[str, Any]]:
        \"\"\"
        按自定义条件选择目标

        Args:
            targets: 解析后的目标列表
            condition_func: 自定义条件函数，接受目标字典作为参数，返回布尔值

        Returns:
            匹配的目标列表
        \"\"\"
        if not targets:
            return []

        try:
            return [t for t in targets if condition_func(t)]
        except Exception as e:
            print(f"错误: 按自定义条件选择目标失败: {e}")
            return []

    # ========================================
    # 📍 坐标操作功能
    # ========================================

    def get_target_coordinates(self, target: Dict[str, Any], point_type: str = 'center') -> Optional[Dict[str, int]]:
        \"\"\"
        获取目标的指定坐标点

        Args:
            target: 目标信息字典
            point_type: 坐标点类型 ('center', 'top_left', 'top_right', 'bottom_left',
                       'bottom_right', 'top_center', 'bottom_center', 'left_center', 'right_center')

        Returns:
            坐标字典 {'x': int, 'y': int}，失败时返回None
        \"\"\"
        if not target or 'coordinates' not in target:
            return None

        try:
            coordinates = target['coordinates']
            if point_type in coordinates:
                return coordinates[point_type]
            else:
                print(f"警告: 未知 点类型: {point_type}")
                return coordinates.get('center')
        except Exception as e:
            print(f"错误: 获取目标坐标失败: {e}")
            return None

    def get_bounding_box_corners(self, target: Dict[str, Any]) -> Optional[Dict[str, Dict[str, int]]]:
        \"\"\"
        获取边界框四个顶角坐标

        Args:
            target: 目标信息字典

        Returns:
            包含四个顶角坐标的字典
        \"\"\"
        if not target or 'coordinates' not in target:
            return None

        try:
            coords = target['coordinates']
            return {
                'top_left': coords['top_left'],
                'top_right': coords['top_right'],
                'bottom_left': coords['bottom_left'],
                'bottom_right': coords['bottom_right']
            }
        except Exception as e:
            print(f"错误: 获取边界框顶角失败: {e}")
            return None

    def calculate_target_area_and_dimensions(self, target: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        \"\"\"
        计算目标区域面积和尺寸信息

        Args:
            target: 目标信息字典

        Returns:
            尺寸信息字典
        \"\"\"
        if not target or 'dimensions' not in target:
            return None

        try:
            return target['dimensions']
        except Exception as e:
            print(f"错误: 计算目标尺寸失败: {e}")
            return None

    def convert_to_relative_coordinates(self, absolute_coords: Dict[str, int],
                                      screen_width: int = 1920, screen_height: int = 1080) -> Dict[str, float]:
        \"\"\"
        将绝对坐标转换为相对坐标（0-1范围）

        Args:
            absolute_coords: 绝对坐标 {'x': int, 'y': int}
            screen_width: 屏幕宽度
            screen_height: 屏幕高度

        Returns:
            相对坐标 {'x': float, 'y': float}
        \"\"\"
        try:
            return {
                'x': absolute_coords['x'] / screen_width,
                'y': absolute_coords['y'] / screen_height
            }
        except Exception as e:
            print(f"错误: 转换为相对坐标失败: {e}")
            return {'x': 0.0, 'y': 0.0}

    def convert_to_absolute_coordinates(self, relative_coords: Dict[str, float],
                                      screen_width: int = 1920, screen_height: int = 1080) -> Dict[str, int]:
        \"\"\"
        将相对坐标转换为绝对坐标

        Args:
            relative_coords: 相对坐标 {'x': float, 'y': float}
            screen_width: 屏幕宽度
            screen_height: 屏幕高度

        Returns:
            绝对坐标 {'x': int, 'y': int}
        \"\"\"
        try:
            return {
                'x': int(relative_coords['x'] * screen_width),
                'y': int(relative_coords['y'] * screen_height)
            }
        except Exception as e:
            print(f"错误: 转换为绝对坐标失败: {e}")
            return {'x': 0, 'y': 0}

    # ========================================
    # 🤖 自动化办公操作功能
    # ========================================

    def perform_mouse_click(self, coordinates: Dict[str, int], button: str = 'left',
                           click_type: str = 'single', delay: float = 0.1) -> bool:
        \"\"\"
        执行鼠标点击操作

        Args:
            coordinates: 点击坐标 {'x': int, 'y': int}
            button: 鼠标按键 ('left', 'right', 'middle')
            click_type: 点击类型 ('single', 'double')
            delay: 点击后延时（秒）

        Returns:
            操作是否成功
        \"\"\"
        try:
            import pyautogui

            # 安全检查：确保坐标在屏幕范围内
            screen_width, screen_height = pyautogui.size()
            x = max(0, min(coordinates['x'], screen_width - 1))
            y = max(0, min(coordinates['y'], screen_height - 1))

            print(f"信息: 执行 {click_type} {button} 点击位置 ({x}, {y})")

            # 移动到目标位置
            pyautogui.moveTo(x, y, duration=0.2)

            # 执行点击
            if click_type == 'single':
                pyautogui.click(x, y, button=button)
            elif click_type == 'double':
                pyautogui.doubleClick(x, y, button=button)

            # 延时
            if delay > 0:
                import time
                time.sleep(delay)

            print(f"成功: 鼠标点击完成")
            return True

        except Exception as e:
            print(f"错误: 鼠标点击执行失败: {e}")
            return False

    def perform_mouse_drag(self, start_coords: Dict[str, int], end_coords: Dict[str, int],
                          duration: float = 1.0, button: str = 'left') -> bool:
        \"\"\"
        执行鼠标拖拽操作

        Args:
            start_coords: 起始坐标 {'x': int, 'y': int}
            end_coords: 结束坐标 {'x': int, 'y': int}
            duration: 拖拽持续时间（秒）
            button: 鼠标按键

        Returns:
            操作是否成功
        \"\"\"
        try:
            import pyautogui

            start_x = start_coords['x']
            start_y = start_coords['y']
            end_x = end_coords['x']
            end_y = end_coords['y']

            print(f"信息: 执行 拖拽从 ({start_x}, {start_y}) to ({end_x}, {end_y})")

            # 执行拖拽
            pyautogui.drag(end_x - start_x, end_y - start_y, duration=duration, button=button)

            print(f"成功: 鼠标拖拽完成")
            return True

        except Exception as e:
            print(f"错误: 鼠标拖拽执行失败: {e}")
            return False

    def perform_keyboard_input(self, text: str, typing_speed: float = 0.05) -> bool:
        \"\"\"
        执行键盘文本输入

        Args:
            text: 要输入的文本
            typing_speed: 打字速度（每个字符间隔秒数）

        Returns:
            操作是否成功
        \"\"\"
        try:
            import pyautogui

            print(f"信息: 输入文本: '{text}'")

            # 输入文本
            pyautogui.typewrite(text, interval=typing_speed)

            print(f"成功: 文本输入完成")
            return True

        except Exception as e:
            print(f"错误: 键盘输入执行失败: {e}")
            return False

    def perform_keyboard_shortcut(self, keys: List[str], delay: float = 0.1) -> bool:
        \"\"\"
        执行键盘快捷键

        Args:
            keys: 按键列表，如 ['ctrl', 'c'] 表示 Ctrl+C
            delay: 操作后延时（秒）

        Returns:
            操作是否成功
        \"\"\"
        try:
            import pyautogui

            print(f"信息: 执行 keyboard shortcut: {'+'.join(keys)}")

            # 执行快捷键
            pyautogui.hotkey(*keys)

            # 延时
            if delay > 0:
                import time
                time.sleep(delay)

            print(f"成功: 键盘快捷键完成")
            return True

        except Exception as e:
            print(f"错误: 键盘快捷键执行失败: {e}")
            return False

    def wait_and_delay(self, seconds: float) -> None:
        \"\"\"
        等待指定时间

        Args:
            seconds: 等待时间（秒）
        \"\"\"
        try:
            import time
            print(f"信息: 等待 {seconds} 秒...")
            time.sleep(seconds)
            print(f"成功: 等待完成")
        except Exception as e:
            print(f"错误: 等待失败: {e}")

    def perform_automation_sequence(self, 个操作: List[Dict[str, Any]]) -> bool:
        \"\"\"
        执行自动化操作序列

        Args:
            个操作: 操作序列列表，每个操作包含类型和参数

        Returns:
            所有操作是否成功
        \"\"\"
        try:
            print(f"信息: 开始自动化序列，包含 {len(个操作)} 个操作")

            for i, action in enumerate(个操作):
                action_type = action.get('type', '')
                params = action.get('params', {})

                print(f"信息: 执行操作 {i+1}/{len(个操作)}: {action_type}")

                success = False

                if action_type == 'click':
                    success = self.perform_mouse_click(
                        params.get('coordinates', {'x': 0, 'y': 0}),
                        params.get('button', 'left'),
                        params.get('click_type', 'single'),
                        params.get('delay', 0.1)
                    )
                elif action_type == 'drag':
                    success = self.perform_mouse_drag(
                        params.get('start_coords', {'x': 0, 'y': 0}),
                        params.get('end_coords', {'x': 0, 'y': 0}),
                        params.get('duration', 1.0),
                        params.get('button', 'left')
                    )
                elif action_type == 'type':
                    success = self.perform_keyboard_input(
                        params.get('text', ''),
                        params.get('typing_speed', 0.05)
                    )
                elif action_type == 'shortcut':
                    success = self.perform_keyboard_shortcut(
                        params.get('keys', []),
                        params.get('delay', 0.1)
                    )
                elif action_type == 'wait':
                    self.wait_and_delay(params.get('seconds', 1.0))
                    success = True
                else:
                    print(f"警告: 未知 action type: {action_type}")
                    continue

                if not success:
                    print(f"错误: 操作 {i+1} 失败，停止序列")
                    return False

            print(f"成功: 自动化序列成功完成")
            return True

        except Exception as e:
            print(f"错误: 自动化序列执行失败: {e}")
            return False

    # ========================================
    # 🛡️ 安全机制和操作日志
    # ========================================

    def validate_coordinates(self, coordinates: Dict[str, int]) -> bool:
        \"\"\"
        验证坐标是否在安全范围内

        Args:
            coordinates: 坐标字典 {'x': int, 'y': int}

        Returns:
            坐标是否有效
        \"\"\"
        try:
            import pyautogui
            screen_width, screen_height = pyautogui.size()

            x = coordinates.get('x', -1)
            y = coordinates.get('y', -1)

            if x < 0 or x >= screen_width or y < 0 or y >= screen_height:
                print(f"警告: 坐标 ({x}, {y}) 超出屏幕边界 ({screen_width}x{screen_height})")
                return False

            return True

        except Exception as e:
            print(f"错误: 坐标验证失败: {e}")
            return False

    def log_automation_action(self, action_type: str, params: Dict[str, Any], success: bool) -> None:
        \"\"\"
        记录自动化操作日志

        Args:
            action_type: 操作类型
            params: 操作参数
            success: 操作是否成功
        \"\"\"
        try:
            import time
            timestamp = time.strftime('%Y-%m-%d %H:%M:%S')
            status = "SUCCESS" if success else "FAILED"

            log_entry = f"[{timestamp}] {status}: {action_type} - {params}"
            print(f"LOG: {log_entry}")

            # 可以扩展为写入日志文件
            # with open('automation_log.txt', 'a', encoding='utf-8') as f:
            #     f.write(log_entry + '\\n')

        except Exception as e:
            print(f"错误: 自动化操作日志记录失败: {e}")

    # ========================================
    # 📚 完整使用示例和最佳实践
    # ========================================
    # 注意：详细的使用示例已移至GUI主界面的"📚 使用示例"标签页
    # 这里保留基础的示例方法供程序内部调用

    def automation_example_office_workflow(self) -> bool:
        \"\"\"
        办公自动化工作流程基础示例

        注意：详细的自动化操作示例已移至GUI主界面的"📚 使用示例"标签页
        这里仅提供基础的检测和目标选择功能演示

        Returns:
            基础检测是否成功完成
        \"\"\"
        try:
            print("信息: 执行基础检测和目标选择演示...")

            # 步骤1: 执行屏幕检测
            print("🔍 执行屏幕检测...")
            results = self.gui_detect_screen()

            if not results:
                print("⚠️ 未检测到目标")
                print("💡 建议：")
                print("   • 确保屏幕上有可见的界面元素")
                print("   • 检查YOLO模型是否正确加载")
                print("   • 尝试调整检测参数")
                return False

            # 步骤2: 解析检测结果
            print("📋 解析检测结果...")
            targets = self.parse_detection_results(results)
            print(f"✅ 找到 {len(targets)} 个目标")

            # 步骤3: 基础目标选择演示
            print("🎯 目标选择演示:")

            # 按置信度选择
            highest_conf_target = self.select_target_by_confidence(targets, 'highest')
            if highest_conf_target:
                print(f"   最高置信度目标: {highest_conf_target['class_name']} ({highest_conf_target['confidence']:.3f})")
                coords = self.get_target_coordinates(highest_conf_target, 'center')
                print(f"   中心坐标: ({coords['x']}, {coords['y']})")

            # 按位置选择
            leftmost_target = self.select_target_by_position(targets, 'leftmost')
            if leftmost_target:
                print(f"   最左侧目标: {leftmost_target['class_name']}")

            # 按大小选择
            largest_target = self.select_target_by_size(targets, 'largest')
            if largest_target:
                dimensions = self.calculate_target_area_and_dimensions(largest_target)
                print(f"   最大目标: {largest_target['class_name']} (面积: {dimensions['area']} 像素)")

            print("\\n📚 查看完整使用示例:")
            print("   请在GUI主界面右侧面板中点击 '📚 使用示例' 标签页")
            print("   那里包含详细的自动化操作代码示例和最佳实践")
            print("   包括：")
            print("   • 🏢 办公软件自动化")
            print("   • 🎯 多目标操作")
            print("   • 🔍 目标选择策略")
            print("   • ⚡ 复杂工作流程")
            print("   • 🛡️ 错误处理机制")

            return True

        except Exception as e:
            print(f"❌ 基础检测演示失败: {e}")
            print("💡 建议查看GUI主界面的 '📚 使用示例' 标签页获取详细帮助")
            return False

    def automation_example_multi_target_operation(self) -> bool:
        \"\"\"
        多目标操作基础示例

        注意：详细的多目标操作示例已移至GUI主界面的"📚 使用示例"标签页
        这里仅提供基础的多目标检测和选择功能演示

        Returns:
            基础多目标操作是否成功
        \"\"\"
        try:
            print("信息: 执行基础多目标检测和选择演示...")

            # 执行检测
            print("🔍 执行屏幕检测...")
            results = self.gui_detect_screen()
            targets = self.parse_detection_results(results)

            if len(targets) < 2:
                print("⚠️ 需要至少 2 个目标进行多目标操作演示")
                print("💡 建议：")
                print("   • 确保屏幕上有多个可检测的界面元素")
                print("   • 尝试打开更多窗口或应用")
                return False

            print(f"✅ 找到 {len(targets)} 个目标，可进行多目标操作")

            # 基础多目标选择演示
            print("🎯 多目标选择演示:")

            # 按位置选择
            leftmost_target = self.select_target_by_position(targets, 'leftmost')
            rightmost_target = self.select_target_by_position(targets, 'rightmost')
            topmost_target = self.select_target_by_position(targets, 'topmost')
            bottommost_target = self.select_target_by_position(targets, 'bottommost')

            position_targets = [
                (leftmost_target, "最左侧"),
                (rightmost_target, "最右侧"),
                (topmost_target, "最上方"),
                (bottommost_target, "最下方")
            ]

            for target, description in position_targets:
                if target:
                    coords = self.get_target_coordinates(target, 'center')
                    print(f"   {description}目标: {target['class_name']} 位置: ({coords['x']}, {coords['y']})")

            # 按大小选择
            largest_target = self.select_target_by_size(targets, 'largest')
            smallest_target = self.select_target_by_size(targets, 'smallest')

            if largest_target:
                dimensions = self.calculate_target_area_and_dimensions(largest_target)
                print(f"   最大目标: {largest_target['class_name']} 面积: {dimensions['area']} 像素")

            if smallest_target:
                dimensions = self.calculate_target_area_and_dimensions(smallest_target)
                print(f"   最小目标: {smallest_target['class_name']} 面积: {dimensions['area']} 像素")

            # 按置信度选择
            highest_conf_target = self.select_target_by_confidence(targets, 'highest')
            lowest_conf_target = self.select_target_by_confidence(targets, 'lowest')

            if highest_conf_target:
                print(f"   最高置信度: {highest_conf_target['class_name']} ({highest_conf_target['confidence']:.3f})")

            if lowest_conf_target:
                print(f"   最低置信度: {lowest_conf_target['class_name']} ({lowest_conf_target['confidence']:.3f})")

            print("\\n📚 查看完整多目标操作示例:")
            print("   请在GUI主界面右侧面板中点击 '📚 使用示例' 标签页")
            print("   选择 '🎯 多目标操作' 子标签页查看详细示例")
            print("   包括：")
            print("   • 多目标拖拽操作")
            print("   • 复杂序列操作")
            print("   • 目标间距离分析")
            print("   • 批量处理操作")

            return True

        except Exception as e:
            print(f"❌ 基础多目标操作演示失败: {e}")
            print("💡 建议查看GUI主界面的 '📚 使用示例' 标签页获取详细帮助")
            return False

# Main function with automation examples
if __name__ == "__main__":
    print("🎯 YOLO GUI检测与办公自动化系统")
    print("=" * 80)
    print("完整的办公工作流程检测和自动化解决方案")
    print("=" * 80)

    try:
        detector = GUIDetectorCopy()

        print("\\n📊 Step 1: 执行GUI检测工作流程...")
        results = detector.gui_detect_screen()

        print("\\n📋 Step 2: 显示检测结果...")
        detector.print_results(results)

        if results:
            print("\\n🎯 Step 3: 演示自动化功能...")

            # 解析检测结果
            targets = detector.parse_detection_results(results)
            print(f"信息: 已解析 {len(targets)} 个目标用于自动化")

            if targets:
                # 展示目标选择功能
                print("\\n🔍 目标选择示例:")

                highest_conf = detector.select_target_by_confidence(targets, 'highest')
                if highest_conf:
                    print(f"  - 最高置信度: {highest_conf['class_name']} ({highest_conf['confidence']:.3f})")

                leftmost = detector.select_target_by_position(targets, 'leftmost')
                if leftmost:
                    coords = detector.get_target_coordinates(leftmost, 'center')
                    print(f"  - 最左侧目标: {leftmost['class_name']} at ({coords['x']}, {coords['y']})")

                largest = detector.select_target_by_size(targets, 'largest')
                if largest:
                    dims = detector.calculate_target_area_and_dimensions(largest)
                    print(f"  - 最大目标: {largest['class_name']} (area: {dims['area']})")

                print("\\n📍 坐标信息:")
                for i, target in enumerate(targets[:3]):  # Show first 3 targets
                    corners = detector.get_bounding_box_corners(target)
                    if corners:
                        print(f"  Target {i+1} ({target['class_name']}):")
                        print(f"    - 中心: {detector.get_target_coordinates(target, 'center')}")
                        print(f"    - 左上角: {corners['top_left']}")
                        print(f"    - 右下角: {corners['bottom_right']}")

                print("\\n🤖 可用的自动化功能:")
                print("  ✅ 鼠标点击（左键/右键/双击）")
                print("  ✅ 目标间鼠标拖拽")
                print("  ✅ 键盘输入和快捷键")
                print("  ✅ 多步骤自动化序列")
                print("  ✅ 坐标验证和安全检查")
                print("  ✅ 操作日志和错误处理")

                print("\\n💡 详细使用示例请查看:")
                print("  📚 GUI主界面 → 右侧面板 → '📚 使用示例' 标签页")
                print("  包含完整的代码示例和最佳实践指导")

                # 注意：详细示例已移至GUI的使用示例标签页

            print("\\n✅ 成功: GUI检测和自动化系统就绪!")
            print("信息: 所有功能正常工作")
            print("信息: 准备进行办公自动化任务")

        else:
            print("\\n📝 信息: 未检测到目标")
            print("建议:")
            print("  1. 检查屏幕内容 - ensure there are visible UI elements")
            print("  2. 降低置信度阈值 if needed")
            print("  3. Verify YOLO 模式l is properly loaded")
            print("  4. Try template matching 模式 for specific UI elements")

    except Exception as e:
        print(f"\\n❌ 错误: 系统初始化失败: {e}")
        print("\\n故障排除:")
        print("  1. 确保已安装所有依赖项（pyautogui, opencv-python）")
        print("  2. Check that YOLO 模式l files are present")
        print("  3. 验证屏幕捕获权限")
        import traceback
        traceback.print_exc()
"""

    def get_usage_example_code(self) -> str:
        """获取完整的自动化操作使用示例代码"""
        return """#!/usr/bin/env python3
# -*- coding: utf-8 -*-
\"\"\"
YOLO OpenCV检测器自动化操作完整使用示例
演示如何使用检测结果进行智能自动化操作
\"\"\"

import sys
import time
import pyautogui
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / "src"))

def office_automation_example():
    \"\"\"办公软件自动化示例\"\"\"
    print("🏢 办公软件自动化示例")
    print("=" * 50)

    # 1. 初始化检测器
    from yolo_opencv_detector.gui.dialogs.source_code_dialog import SourceCodeDialog

    class OfficeAutomator(SourceCodeDialog):
        def __init__(self):
            super().__init__()
            self.load_gui_config()

    automator = OfficeAutomator()

    # 2. 执行屏幕检测
    print("🔍 执行屏幕检测...")
    results = automator.gui_detect_screen()

    if not results:
        print("❌ 未检测到任何目标")
        return False

    # 3. 解析检测结果
    targets = automator.parse_detection_results(results)
    print(f"✅ 检测到 {len(targets)} 个目标")

    # 4. 智能目标选择演示
    print("\\n🎯 智能目标选择演示:")

    # 按置信度选择最佳目标
    best_target = automator.select_target_by_confidence(targets, 'highest')
    if best_target:
        print(f"  最佳目标: {best_target.get('class_name', 'unknown')} (置信度: {best_target.get('confidence', 0):.3f})")

        # 获取目标坐标
        center_coords = automator.get_target_coordinates(best_target, 'center')
        print(f"  中心坐标: ({center_coords['x']}, {center_coords['y']})")

        # 坐标验证
        if automator.validate_coordinates(center_coords):
            print("  ✅ 坐标验证通过")

            # 执行点击操作
            print("\\n🖱️ 执行自动化操作:")
            success = automator.perform_mouse_click(center_coords, 'left', 'single', 0.5)
            if success:
                print("  ✅ 鼠标点击成功")
                automator.log_automation_action('mouse_click', center_coords, success)
            else:
                print("  ❌ 鼠标点击失败")
        else:
            print("  ❌ 坐标验证失败")

    return True

def multi_target_operation_example():
    \"\"\"多目标操作示例\"\"\"
    print("\\n🎯 多目标操作示例")
    print("=" * 50)

    # 初始化自动化器
    from yolo_opencv_detector.gui.dialogs.source_code_dialog import SourceCodeDialog

    class MultiTargetAutomator(SourceCodeDialog):
        def __init__(self):
            super().__init__()
            self.load_gui_config()

    automator = MultiTargetAutomator()

    # 执行检测
    results = automator.gui_detect_screen()
    targets = automator.parse_detection_results(results)

    if len(targets) < 2:
        print("⚠️ 需要至少2个目标进行多目标操作")
        return False

    print(f"✅ 找到 {len(targets)} 个目标")

    # 多目标选择策略
    print("\\n📍 多目标选择策略:")

    # 选择最左和最右的目标
    leftmost = automator.select_target_by_position(targets, 'leftmost')
    rightmost = automator.select_target_by_position(targets, 'rightmost')

    if leftmost and rightmost:
        left_coords = automator.get_target_coordinates(leftmost, 'center')
        right_coords = automator.get_target_coordinates(rightmost, 'center')

        print(f"  最左目标: ({left_coords['x']}, {left_coords['y']})")
        print(f"  最右目标: ({right_coords['x']}, {right_coords['y']})")

        # 执行拖拽操作
        print("\\n🔄 执行拖拽操作:")
        drag_success = automator.perform_mouse_drag(
            left_coords, right_coords, 1.0, 'left'
        )

        if drag_success:
            print("  ✅ 拖拽操作成功")
        else:
            print("  ❌ 拖拽操作失败")

    return True

def target_selection_strategies_example():
    \"\"\"目标选择策略示例\"\"\"
    print("\\n🔍 目标选择策略示例")
    print("=" * 50)

    # 初始化自动化器
    from yolo_opencv_detector.gui.dialogs.source_code_dialog import SourceCodeDialog

    class TargetSelector(SourceCodeDialog):
        def __init__(self):
            super().__init__()
            self.load_gui_config()

    selector = TargetSelector()

    # 执行检测
    results = selector.gui_detect_screen()
    targets = selector.parse_detection_results(results)

    if not targets:
        print("❌ 未检测到目标")
        return False

    print(f"✅ 检测到 {len(targets)} 个目标")

    # 演示各种选择策略
    print("\\n📊 选择策略演示:")

    # 1. 按置信度选择
    highest_conf = selector.select_target_by_confidence(targets, 'highest')
    if highest_conf:
        print(f"  最高置信度: {highest_conf.get('confidence', 0):.3f}")

    # 2. 按位置选择
    center_target = selector.select_target_by_position(targets, 'center')
    if center_target:
        coords = selector.get_target_coordinates(center_target, 'center')
        print(f"  中心目标: ({coords['x']}, {coords['y']})")

    # 3. 按大小选择
    largest_target = selector.select_target_by_size(targets, 'largest')
    if largest_target:
        dimensions = selector.calculate_target_area_and_dimensions(largest_target)
        print(f"  最大目标面积: {dimensions.get('area', 0)} 像素")

    # 4. 自定义条件选择
    def high_confidence_condition(target):
        return target.get('confidence', 0) > 0.8

    high_conf_targets = selector.select_target_by_custom_condition(targets, high_confidence_condition)
    print(f"  高置信度目标数量: {len(high_conf_targets)}")

    return True

def error_handling_example():
    \"\"\"错误处理机制示例\"\"\"
    print("\\n🛡️ 错误处理机制示例")
    print("=" * 50)

    # 初始化自动化器
    from yolo_opencv_detector.gui.dialogs.source_code_dialog import SourceCodeDialog

    class SafeAutomator(SourceCodeDialog):
        def __init__(self):
            super().__init__()
            self.load_gui_config()

    automator = SafeAutomator()

    try:
        # 执行检测
        results = automator.gui_detect_screen()
        targets = automator.parse_detection_results(results)

        if not targets:
            print("⚠️ 未检测到目标，使用备用策略")
            return False

        # 选择目标
        target = automator.select_target_by_confidence(targets, 'highest')

        if target:
            coords = automator.get_target_coordinates(target, 'center')

            # 坐标验证
            if automator.validate_coordinates(coords):
                print("✅ 坐标验证通过")

                # 安全执行操作
                max_retries = 3
                for attempt in range(max_retries):
                    try:
                        success = automator.perform_mouse_click(coords, 'left', 'single', 0.3)
                        if success:
                            print(f"✅ 操作成功 (尝试 {attempt + 1}/{max_retries})")
                            break
                        else:
                            print(f"⚠️ 操作失败，重试 {attempt + 1}/{max_retries}")
                            time.sleep(1)
                    except Exception as e:
                        print(f"❌ 操作异常: {e}")
                        if attempt == max_retries - 1:
                            print("❌ 达到最大重试次数，操作失败")
                            return False
                        time.sleep(1)
            else:
                print("❌ 坐标验证失败")
                return False

    except Exception as e:
        print(f"❌ 检测过程异常: {e}")
        return False

    return True

def main():
    \"\"\"主函数 - 运行所有示例\"\"\"
    print("🎯 YOLO OpenCV检测器自动化操作完整示例")
    print("=" * 60)

    examples = [
        ("办公软件自动化", office_automation_example),
        ("多目标操作", multi_target_operation_example),
        ("目标选择策略", target_selection_strategies_example),
        ("错误处理机制", error_handling_example),
    ]

    results = []
    for name, example_func in examples:
        try:
            print(f"\\n🚀 运行 {name} 示例...")
            result = example_func()
            results.append((name, result))
            if result:
                print(f"✅ {name} 示例完成")
            else:
                print(f"⚠️ {name} 示例部分完成")
        except Exception as e:
            print(f"❌ {name} 示例异常: {e}")
            results.append((name, False))

    # 总结
    print("\\n" + "=" * 60)
    print("📊 示例运行总结:")
    success_count = sum(1 for _, result in results if result)
    for name, result in results:
        status = "✅ 成功" if result else "❌ 失败"
        print(f"  {name}: {status}")

    print(f"\\n📈 成功率: {success_count}/{len(results)} ({success_count/len(results)*100:.1f}%)")

    print("\\n💡 使用提示:")
    print("  • 确保屏幕上有可检测的界面元素")
    print("  • 检查YOLO模型配置是否正确")
    print("  • 验证系统权限设置")
    print("  • 根据实际需求调整参数")

if __name__ == "__main__":
    main()
"""

    def run_current_code(self):
        """运行当前代码"""
        try:
            current_index = self.tab_widget.currentIndex()
            current_tab = self.tab_widget.widget(current_index)
            code_editor = current_tab.findChild(QTextEdit)

            if not code_editor:
                QMessageBox.warning(self, "错误", "未找到代码编辑器")
                return

            code = code_editor.toPlainText()
            if not code.strip():
                QMessageBox.warning(self, "错误", "代码为空")
                return

            self.output_text.clear()
            self.output_text.appendPlainText("🚀 开始执行代码...")
            self.output_text.appendPlainText("-" * 50)

            self.run_button.setEnabled(False)
            self.run_button.setText("⏳ 执行中...")

            self.execution_thread = CodeExecutionThread(code)
            self.execution_thread.output_signal.connect(self.on_execution_output)
            self.execution_thread.finished_signal.connect(self.on_execution_finished)
            self.execution_thread.start()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"运行失败: {e}")
            self.run_button.setEnabled(True)
            self.run_button.setText("▶️ 运行代码")

    def on_execution_output(self, output: str):
        """处理执行输出"""
        self.output_text.appendPlainText(output)
        cursor = self.output_text.textCursor()
        cursor.movePosition(QTextCursor.MoveOperation.End)
        self.output_text.setTextCursor(cursor)

    def on_execution_finished(self, return_code: int):
        """处理执行完成"""
        self.output_text.appendPlainText("-" * 50)
        if return_code == 0:
            self.output_text.appendPlainText("✅ 执行完成")
        else:
            self.output_text.appendPlainText(f"❌ 执行失败 (返回码: {return_code})")

        self.run_button.setEnabled(True)
        self.run_button.setText("▶️ 运行代码")

    def copy_current_code(self):
        """复制当前代码"""
        try:
            current_index = self.tab_widget.currentIndex()
            current_tab = self.tab_widget.widget(current_index)
            code_editor = current_tab.findChild(QTextEdit)

            if not code_editor:
                QMessageBox.warning(self, "错误", "未找到代码编辑器")
                return

            code = code_editor.toPlainText()
            clipboard = QApplication.clipboard()
            clipboard.setText(code)

            QMessageBox.information(self, "成功", "代码已复制到剪贴板")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"复制失败: {e}")

    def save_current_code(self):
        """保存当前代码"""
        try:
            from PyQt6.QtWidgets import QFileDialog

            current_index = self.tab_widget.currentIndex()
            current_tab = self.tab_widget.widget(current_index)
            code_editor = current_tab.findChild(QTextEdit)

            if not code_editor:
                QMessageBox.warning(self, "错误", "未找到代码编辑器")
                return

            code = code_editor.toPlainText()

            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "保存代码文件",
                "gui_detector_copy.py",
                "Python文件 (*.py);;所有文件 (*)"
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(code)

                QMessageBox.information(self, "成功", f"代码已保存到: {file_path}")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存失败: {e}")

    # ========================================
    # 📚 使用示例标签页优化功能方法
    # ========================================

    def setup_syntax_highlighting(self):
        """设置简单的语法高亮"""
        try:
            # 设置代码编辑器样式
            self.example_code_editor.setStyleSheet("""
                QTextEdit {
                    background-color: #f8f9fa;
                    color: #2c3e50;
                    border: 1px solid #bdc3c7;
                    border-radius: 4px;
                    font-family: 'Consolas', 'Monaco', monospace;
                    line-height: 1.4;
                }
                QTextEdit:focus {
                    border: 2px solid #3498db;
                }
            """)
        except Exception as e:
            print(f"警告: 语法高亮设置失败: {e}")

    def on_template_changed(self, template_name: str):
        """模板选择改变时的处理"""
        try:
            # 保存当前编辑内容
            if hasattr(self, 'current_template') and self.code_modified:
                current_code = self.example_code_editor.toPlainText()
                self.saved_edits[self.current_template] = current_code

            # 切换到新模板
            self.current_template = template_name

            # 加载对应的代码
            if template_name in self.saved_edits:
                # 加载已保存的编辑内容
                code = self.saved_edits[template_name]
                self.status_label.setText("📝 已编辑版本")
                self.status_label.setStyleSheet("color: #e67e22; font-size: 10px;")
            else:
                # 加载原始模板代码
                code = self.get_template_code(template_name)
                self.status_label.setText("📄 原始代码")
                self.status_label.setStyleSheet("color: #7f8c8d; font-size: 10px;")

            # 更新编辑器内容
            self.example_code_editor.setPlainText(code)
            self.code_modified = False

        except Exception as e:
            print(f"错误: 模板切换失败: {e}")

    def get_template_code(self, template_name: str) -> str:
        """根据模板名称获取对应的代码"""
        if template_name == "完整示例 (4个场景)":
            return self.get_usage_example_code()
        elif template_name == "办公软件自动化":
            return self.get_office_automation_code()
        elif template_name == "多目标操作":
            return self.get_multi_target_code()
        elif template_name == "目标选择策略":
            return self.get_target_selection_code()
        elif template_name == "错误处理机制":
            return self.get_error_handling_code()
        elif template_name == "独立运行版本":
            return self.get_standalone_code()
        else:
            return self.get_usage_example_code()

    def on_code_changed(self):
        """代码内容改变时的处理"""
        self.code_modified = True
        self.status_label.setText("✏️ 已修改")
        self.status_label.setStyleSheet("color: #e74c3c; font-size: 10px;")

    def save_code_edits(self):
        """保存代码编辑"""
        try:
            current_template = getattr(self, 'current_template', '完整示例 (4个场景)')
            current_code = self.example_code_editor.toPlainText()

            # 保存到内存
            self.saved_edits[current_template] = current_code

            # 更新状态
            self.status_label.setText("💾 已保存")
            self.status_label.setStyleSheet("color: #27ae60; font-size: 10px;")
            self.code_modified = False

            # 显示成功消息
            self.result_display.append(f"✅ 代码编辑已保存: {current_template}")

        except Exception as e:
            self.result_display.append(f"❌ 保存失败: {e}")

    def reset_to_original(self):
        """恢复到原始代码"""
        try:
            current_template = getattr(self, 'current_template', '完整示例 (4个场景)')

            # 清除保存的编辑
            if current_template in self.saved_edits:
                del self.saved_edits[current_template]

            # 重新加载原始代码
            original_code = self.get_template_code(current_template)
            self.example_code_editor.setPlainText(original_code)

            # 更新状态
            self.status_label.setText("📄 原始代码")
            self.status_label.setStyleSheet("color: #7f8c8d; font-size: 10px;")
            self.code_modified = False

            self.result_display.append(f"🔄 已恢复原始代码: {current_template}")

        except Exception as e:
            self.result_display.append(f"❌ 恢复失败: {e}")

    def export_code_to_file(self):
        """导出代码到外部文件"""
        try:
            current_code = self.example_code_editor.toPlainText()
            current_template = getattr(self, 'current_template', '完整示例 (4个场景)')

            # 生成默认文件名
            template_map = {
                "完整示例 (4个场景)": "complete_automation_examples",
                "办公软件自动化": "office_automation_example",
                "多目标操作": "multi_target_example",
                "目标选择策略": "target_selection_example",
                "错误处理机制": "error_handling_example",
                "独立运行版本": "standalone_detector_example"
            }

            default_name = template_map.get(current_template, "automation_example")

            # 打开文件保存对话框
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "导出代码文件",
                f"{default_name}.py",
                "Python文件 (*.py);;所有文件 (*.*)"
            )

            if file_path:
                # 添加独立运行所需的导入和配置
                standalone_code = self.make_code_standalone(current_code)

                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(standalone_code)

                self.result_display.append(f"📤 代码已导出: {file_path}")

        except Exception as e:
            self.result_display.append(f"❌ 导出失败: {e}")

    def clear_run_results(self):
        """清空运行结果"""
        self.result_display.clear()

    def get_office_automation_code(self) -> str:
        """获取办公软件自动化示例代码"""
        return '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
办公软件自动化示例
智能点击和操作演示 - 安全版本
"""

import sys
import time
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

def office_automation_example():
    """办公软件自动化示例 - 安全版本"""
    print("🏢 办公软件自动化示例")
    print("=" * 50)

    try:
        # 安全导入PyAutoGUI
        try:
            import pyautogui
            # 设置安全模式
            pyautogui.FAILSAFE = True
            pyautogui.PAUSE = 0.5
            print("✅ PyAutoGUI初始化成功")
        except ImportError:
            print("❌ PyAutoGUI未安装")
            return False
        except Exception as e:
            print(f"⚠️ PyAutoGUI初始化警告: {e}")

        # 导入检测器
        from yolo_opencv_detector.core.yolo_detector import YOLODetector
        from yolo_opencv_detector.core.screen_capture import ScreenCapture

        # 初始化组件
        screen_capture = ScreenCapture()
        yolo_detector = YOLODetector()

        # 执行屏幕检测
        print("🔍 执行屏幕检测...")
        image = screen_capture.capture_fullscreen()

        if image is None:
            print("❌ 截图失败")
            return False

        print(f"✅ 截图成功: {image.shape}")

        # YOLO检测
        print("🤖 执行YOLO检测...")
        detections = yolo_detector.detect(image, confidence=0.5)

        if not detections:
            print("❌ 未检测到任何目标")
            print("💡 建议: 确保屏幕上有可检测的界面元素")
            return False

        print(f"✅ 检测到 {len(detections)} 个目标")

        # 显示检测结果
        for i, detection in enumerate(detections[:3]):  # 只显示前3个
            bbox = detection.bbox
            center_x = int(bbox[0] + bbox[2] / 2)
            center_y = int(bbox[1] + bbox[3] / 2)
            print(f"  {i+1}. {detection.class_name}: ({center_x}, {center_y}) - {detection.confidence:.3f}")

        # 选择最佳目标（最高置信度）
        best_detection = max(detections, key=lambda d: d.confidence)
        bbox = best_detection.bbox
        center_x = int(bbox[0] + bbox[2] / 2)
        center_y = int(bbox[1] + bbox[3] / 2)

        print(f"\\n🎯 选择目标: {best_detection.class_name}")
        print(f"📍 坐标: ({center_x}, {center_y})")
        print(f"🎯 置信度: {best_detection.confidence:.3f}")

        # 坐标安全检查
        screen_width, screen_height = pyautogui.size()
        if not (0 <= center_x <= screen_width and 0 <= center_y <= screen_height):
            print(f"❌ 坐标超出屏幕范围: ({center_x}, {center_y})")
            print(f"   屏幕尺寸: {screen_width} x {screen_height}")
            return False

        # 询问用户确认（安全措施）
        print(f"\\n⚠️ 即将点击坐标 ({center_x}, {center_y})")
        print("💡 提示: 移动鼠标到屏幕左上角可中断操作")

        # 等待3秒让用户准备
        for i in range(3, 0, -1):
            print(f"⏳ {i}秒后执行点击...")
            time.sleep(1)

        # 执行点击操作
        try:
            print("🖱️ 执行点击操作...")
            pyautogui.click(center_x, center_y)
            print("✅ 点击完成")

            # 获取点击后的鼠标位置确认
            current_pos = pyautogui.position()
            print(f"📍 当前鼠标位置: {current_pos}")

        except pyautogui.FailSafeException:
            print("🛡️ 安全中断: 鼠标移动到了屏幕角落")
            return False
        except Exception as e:
            print(f"❌ 点击操作失败: {e}")
            return False

        return True

    except Exception as e:
        print(f"❌ 执行失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        success = office_automation_example()
        if success:
            print("\\n🎉 办公软件自动化示例执行成功!")
        else:
            print("\\n⚠️ 办公软件自动化示例执行失败")
    except KeyboardInterrupt:
        print("\\n👋 用户中断操作")
    except Exception as e:
        print(f"\\n❌ 程序异常: {e}")
'''

    def get_multi_target_code(self) -> str:
        """获取多目标操作示例代码"""
        return '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多目标操作示例
复杂拖拽和协调操作演示
"""

import sys
import time
import pyautogui
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

def multi_target_operation_example():
    """多目标操作示例"""
    print("🎯 多目标操作示例")
    print("=" * 50)

    try:
        # 导入检测器
        from yolo_opencv_detector.core.yolo_detector import YOLODetector
        from yolo_opencv_detector.core.screen_capture import ScreenCapture

        # 初始化组件
        screen_capture = ScreenCapture()
        yolo_detector = YOLODetector()

        # 执行屏幕检测
        print("🔍 执行屏幕检测...")
        image = screen_capture.capture_fullscreen()
        detections = yolo_detector.detect(image, confidence=0.5)

        if len(detections) < 2:
            print("⚠️ 需要至少2个目标进行多目标操作")
            return False

        print(f"✅ 检测到 {len(detections)} 个目标")

        # 按位置排序（从左到右）
        sorted_detections = sorted(detections, key=lambda d: d.bbox[0])

        # 选择最左和最右的目标
        leftmost = sorted_detections[0]
        rightmost = sorted_detections[-1]

        # 计算坐标
        left_x = int(leftmost.bbox[0] + leftmost.bbox[2] / 2)
        left_y = int(leftmost.bbox[1] + leftmost.bbox[3] / 2)

        right_x = int(rightmost.bbox[0] + rightmost.bbox[2] / 2)
        right_y = int(rightmost.bbox[1] + rightmost.bbox[3] / 2)

        print(f"🎯 左侧目标: {leftmost.class_name} at ({left_x}, {left_y})")
        print(f"🎯 右侧目标: {rightmost.class_name} at ({right_x}, {right_y})")

        # 执行拖拽操作
        print("🖱️ 执行拖拽操作...")
        pyautogui.drag(right_x - left_x, right_y - left_y, duration=1.0, button='left')
        print("✅ 拖拽完成")

        return True

    except Exception as e:
        print(f"❌ 执行失败: {e}")
        return False

if __name__ == "__main__":
    multi_target_operation_example()
'''

    def get_target_selection_code(self) -> str:
        """获取目标选择策略示例代码"""
        return '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
目标选择策略示例
各种选择方法演示
"""

import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

def target_selection_strategies_example():
    """目标选择策略示例"""
    print("🔍 目标选择策略示例")
    print("=" * 50)

    try:
        # 导入检测器
        from yolo_opencv_detector.core.yolo_detector import YOLODetector
        from yolo_opencv_detector.core.screen_capture import ScreenCapture

        # 初始化组件
        screen_capture = ScreenCapture()
        yolo_detector = YOLODetector()

        # 执行屏幕检测
        print("🔍 执行屏幕检测...")
        image = screen_capture.capture_fullscreen()
        detections = yolo_detector.detect(image, confidence=0.3)

        if not detections:
            print("❌ 未检测到目标")
            return False

        print(f"✅ 检测到 {len(detections)} 个目标")

        # 演示各种选择策略
        print("\\n📊 选择策略演示:")

        # 1. 按置信度选择
        highest_conf = max(detections, key=lambda d: d.confidence)
        print(f"  最高置信度: {highest_conf.class_name} ({highest_conf.confidence:.3f})")

        # 2. 按位置选择（最左侧）
        leftmost = min(detections, key=lambda d: d.bbox[0])
        print(f"  最左侧目标: {leftmost.class_name}")

        # 3. 按大小选择（最大面积）
        largest = max(detections, key=lambda d: d.bbox[2] * d.bbox[3])
        area = largest.bbox[2] * largest.bbox[3]
        print(f"  最大目标: {largest.class_name} (面积: {area:.0f})")

        # 4. 自定义条件选择
        high_conf_targets = [d for d in detections if d.confidence > 0.8]
        print(f"  高置信度目标数量: {len(high_conf_targets)}")

        # 5. 按类别选择
        class_counts = {}
        for detection in detections:
            class_name = detection.class_name
            class_counts[class_name] = class_counts.get(class_name, 0) + 1

        print("  类别统计:")
        for class_name, count in class_counts.items():
            print(f"    {class_name}: {count}个")

        return True

    except Exception as e:
        print(f"❌ 执行失败: {e}")
        return False

if __name__ == "__main__":
    target_selection_strategies_example()
'''

    def get_error_handling_code(self) -> str:
        """获取错误处理机制示例代码"""
        return '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
错误处理机制示例
安全和重试机制演示
"""

import sys
import time
import pyautogui
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

def error_handling_example():
    """错误处理机制示例"""
    print("🛡️ 错误处理机制示例")
    print("=" * 50)

    max_retries = 3
    retry_delay = 1.0

    for attempt in range(max_retries):
        try:
            print(f"🔄 尝试 {attempt + 1}/{max_retries}")

            # 导入检测器
            from yolo_opencv_detector.core.yolo_detector import YOLODetector
            from yolo_opencv_detector.core.screen_capture import ScreenCapture

            # 初始化组件
            screen_capture = ScreenCapture()
            yolo_detector = YOLODetector()

            # 执行屏幕检测
            print("🔍 执行屏幕检测...")
            image = screen_capture.capture_fullscreen()

            if image is None:
                raise Exception("截图失败")

            detections = yolo_detector.detect(image, confidence=0.5)

            if not detections:
                raise Exception("未检测到目标")

            # 选择目标
            target = max(detections, key=lambda d: d.confidence)

            # 坐标验证
            bbox = target.bbox
            center_x = int(bbox[0] + bbox[2] / 2)
            center_y = int(bbox[1] + bbox[3] / 2)

            # 检查坐标是否在屏幕范围内
            screen_width, screen_height = pyautogui.size()
            if not (0 <= center_x <= screen_width and 0 <= center_y <= screen_height):
                raise Exception(f"坐标超出屏幕范围: ({center_x}, {center_y})")

            print(f"✅ 目标验证通过: {target.class_name}")
            print(f"📍 坐标: ({center_x}, {center_y})")

            # 安全执行操作
            print("🖱️ 执行点击操作...")
            pyautogui.click(center_x, center_y)
            print("✅ 操作成功完成")

            return True

        except Exception as e:
            print(f"❌ 尝试 {attempt + 1} 失败: {e}")

            if attempt < max_retries - 1:
                print(f"⏳ 等待 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)
            else:
                print("❌ 达到最大重试次数，操作失败")
                return False

    return False

if __name__ == "__main__":
    error_handling_example()
'''

    def get_standalone_code(self) -> str:
        """获取独立运行版本代码"""
        return '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLO OpenCV检测器独立运行版本
无需GUI环境，可直接执行的完整示例
"""

import sys
import time
import cv2
import numpy as np
from pathlib import Path

def standalone_detector_example():
    """独立检测器示例"""
    print("🚀 独立检测器示例")
    print("=" * 50)

    try:
        # 简化的屏幕捕获
        import pyautogui

        print("📷 执行屏幕截图...")
        screenshot = pyautogui.screenshot()

        # 转换为OpenCV格式
        image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        print(f"✅ 截图完成: {image.shape}")

        # 简化的目标检测（使用OpenCV特征检测）
        print("🔍 执行特征检测...")

        # 转换为灰度图
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # 使用SIFT特征检测
        sift = cv2.SIFT_create()
        keypoints, descriptors = sift.detectAndCompute(gray, None)

        print(f"✅ 检测到 {len(keypoints)} 个特征点")

        if len(keypoints) > 0:
            # 选择最强的特征点
            best_keypoint = max(keypoints, key=lambda kp: kp.response)

            x, y = int(best_keypoint.pt[0]), int(best_keypoint.pt[1])
            print(f"🎯 最佳特征点: ({x}, {y})")
            print(f"💪 响应强度: {best_keypoint.response:.3f}")

            # 执行点击操作
            print("🖱️ 执行点击操作...")
            pyautogui.click(x, y)
            print("✅ 点击完成")

            return True
        else:
            print("❌ 未检测到有效特征点")
            return False

    except ImportError as e:
        print(f"❌ 缺少依赖库: {e}")
        print("💡 请安装: pip install opencv-python pyautogui")
        return False
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        return False

def simple_automation_example():
    """简单自动化示例"""
    print("\\n🤖 简单自动化示例")
    print("=" * 50)

    try:
        import pyautogui

        # 获取屏幕尺寸
        screen_width, screen_height = pyautogui.size()
        print(f"📺 屏幕尺寸: {screen_width} x {screen_height}")

        # 获取当前鼠标位置
        current_x, current_y = pyautogui.position()
        print(f"🖱️ 当前鼠标位置: ({current_x}, {current_y})")

        # 移动到屏幕中心
        center_x, center_y = screen_width // 2, screen_height // 2
        print(f"🎯 移动到屏幕中心: ({center_x}, {center_y})")

        pyautogui.moveTo(center_x, center_y, duration=1.0)
        print("✅ 鼠标移动完成")

        # 执行点击
        pyautogui.click()
        print("✅ 点击完成")

        return True

    except Exception as e:
        print(f"❌ 执行失败: {e}")
        return False

if __name__ == "__main__":
    print("🎮 选择运行模式:")
    print("1. 独立检测器示例")
    print("2. 简单自动化示例")

    try:
        choice = input("请输入选择 (1-2): ").strip()

        if choice == "1":
            standalone_detector_example()
        elif choice == "2":
            simple_automation_example()
        else:
            print("运行所有示例...")
            standalone_detector_example()
            simple_automation_example()

    except KeyboardInterrupt:
        print("\\n👋 用户取消操作")
    except Exception as e:
        print(f"❌ 程序异常: {e}")
'''

    def make_code_standalone(self, code: str) -> str:
        """将代码转换为独立运行版本"""
        try:
            # 添加独立运行所需的头部信息
            standalone_header = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLO OpenCV检测器自动化脚本
从源代码对话框导出的独立运行版本
生成时间: ''' + time.strftime("%Y-%m-%d %H:%M:%S") + '''

依赖库安装:
pip install opencv-python pyautogui ultralytics numpy pillow

使用说明:
1. 确保已安装所需依赖库
2. 运行脚本: python script_name.py
3. 根据提示进行操作
"""

import sys
import os
import time
from pathlib import Path

# 检查依赖库
def check_dependencies():
    """检查必要的依赖库"""
    required_packages = ['cv2', 'numpy', 'PIL', 'pyautogui']
    missing_packages = []

    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)

    if missing_packages:
        print("❌ 缺少以下依赖库:")
        for pkg in missing_packages:
            print(f"   • {pkg}")
        print("\\n💡 请运行以下命令安装:")
        print("pip install opencv-python pyautogui ultralytics numpy pillow")
        return False

    return True

# 检查依赖
if not check_dependencies():
    sys.exit(1)

'''

            # 添加安全提示
            safety_notice = '''
# ⚠️ 安全提示
print("⚠️ 自动化脚本安全提示:")
print("• 本脚本将控制鼠标和键盘")
print("• 请确保在安全的环境中运行")
print("• 按 Ctrl+C 可随时中断执行")
print("• 建议先在测试环境中验证")
print()

try:
    input("按 Enter 键继续，或 Ctrl+C 取消...")
except KeyboardInterrupt:
    print("\\n👋 用户取消操作")
    sys.exit(0)

'''

            # 组合完整的独立代码
            full_code = standalone_header + safety_notice + code

            return full_code

        except Exception as e:
            print(f"错误: 代码转换失败: {e}")
            return code

if __name__ == "__main__":
    app = QApplication(sys.argv)
    dialog = SourceCodeDialog()
    dialog.show()
    sys.exit(app.exec())