# 🤖 YOLO自动化操作配置窗口 - 完整版

## 📋 项目简介

这是一个完整的、直接可用的自动化操作配置窗口，专为YOLO目标检测结果的自动化操作而设计。该应用程序提供了完整的GUI界面，支持智能屏幕适配、自动化操作配置、模板管理等功能。

## ✨ 主要特性

### 🎯 核心功能
- **检测结果可视化**: 直观显示检测到的目标对象
- **智能目标选择**: 支持单选、多选、全选操作
- **自动化操作配置**: 鼠标、键盘、组合操作配置
- **操作模板管理**: 保存、加载、复用常用操作序列
- **实时预览执行**: 操作预览和模拟执行功能

### 🖥️ 界面优化
- **智能屏幕适配**: 自动适应不同分辨率（1024px-4K）
- **响应式布局**: 优化的分割器比例和组件尺寸
- **无滚动设计**: 所有功能无需滚动即可访问
- **专业界面**: 现代化的UI设计和交互体验

### 🔧 技术特性
- **完全独立**: 单文件包含所有功能，无需额外依赖
- **跨平台支持**: 支持Windows、macOS、Linux
- **内置测试**: 完整的测试套件和性能测试
- **调试支持**: 详细的日志记录和调试模式

## 🚀 快速开始

### 环境要求
```bash
Python 3.8+
PyQt6 (pip install PyQt6)
```

### 安装和运行
```bash
# 1. 下载脚本
# 将 automation_complete.py 保存到本地

# 2. 安装依赖
pip install PyQt6

# 3. 运行应用程序
python automation_complete.py

# 4. 运行测试
python automation_complete.py --test

# 5. 查看帮助
python automation_complete.py --help
```

## 📖 使用指南

### 基本操作流程

#### 1. 启动应用程序
```bash
python automation_complete.py
```

#### 2. 界面布局说明
- **左侧面板**: 检测结果可视化和目标选择
  - 可视化区域：显示检测结果和选择框
  - 控制按钮：标签显示、置信度显示、全选、清除
  - 目标列表：表格形式显示所有检测目标

- **右侧面板**: 自动化操作配置
  - 基础操作：鼠标点击、键盘输入、延时设置
  - 操作模板：预设模板、自定义模板管理
  - 高级操作：批量操作、条件执行、循环控制

- **底部面板**: 控制和执行
  - 状态信息：目标数量、选择数量
  - 操作按钮：刷新、预览、执行

#### 3. 操作配置步骤
1. **选择目标**: 在左侧选择要操作的检测目标
2. **配置操作**: 在右侧配置具体的自动化操作
3. **预览操作**: 点击预览按钮查看操作序列
4. **执行操作**: 确认无误后执行自动化操作

### 高级功能

#### 模板管理
```python
# 内置模板
- 复制粘贴 (Ctrl+C, Ctrl+V)
- 保存文档 (Ctrl+S)
- 全选文本 (Ctrl+A)

# 自定义模板
1. 配置操作序列
2. 点击"保存模板"
3. 输入模板名称和描述
4. 保存后可在模板列表中使用
```

#### 批量操作
```python
# 启用批量操作
1. 选择多个目标
2. 在高级操作中启用"批量操作"
3. 设置操作间隔时间
4. 配置要执行的操作
5. 执行时会对所有选中目标依次执行
```

#### 条件执行
```python
# 条件执行设置
- 置信度 > 阈值：只对高置信度目标执行
- 目标数量 > 数值：只在目标数量满足条件时执行
- 位置在区域内：只对特定区域内的目标执行
```

## 🧪 测试功能

### 运行测试套件
```bash
# 基础测试
python automation_complete.py --test

# 性能测试
python automation_complete.py --test --performance

# 调试模式
python automation_complete.py --debug

# 无GUI测试
python automation_complete.py --no-gui --test
```

### 测试覆盖范围
- ✅ 组件导入测试
- ✅ 数据类测试
- ✅ 屏幕适配测试
- ✅ 检测处理器测试
- ✅ 自动化执行器测试
- ✅ 模板管理器测试
- ✅ GUI组件测试

## 🖥️ 屏幕适配支持

### 支持的分辨率
| 屏幕类型 | 分辨率范围 | 窗口尺寸 | 适配效果 |
|---------|-----------|----------|----------|
| 超大屏幕 | ≥3840px | 1600×1000 | ⭐⭐⭐⭐⭐ |
| 大屏幕 | ≥1920px | 1400×900 | ⭐⭐⭐⭐⭐ |
| 中等屏幕 | ≥1366px | 1200×800 | ⭐⭐⭐⭐ |
| 小屏幕 | ≥1024px | 1000×700 | ⭐⭐⭐ |
| 超小屏幕 | <1024px | 900×600 | ⭐⭐ |

### 特殊屏幕支持
- **超宽屏** (21:9): 自动增加宽度
- **竖屏显示**: 自动调整宽高比
- **高DPI屏幕**: 智能缩放适配
- **多显示器**: 基础多显示器支持

## 🔧 命令行选项

```bash
python automation_complete.py [选项]

选项:
  --help, -h     显示帮助信息
  --test, -t     运行测试模式
  --debug, -d    启用调试模式
  --performance  运行性能测试
  --no-gui       无GUI模式（仅测试）

示例:
  python automation_complete.py                    # 启动GUI
  python automation_complete.py --test             # 运行测试
  python automation_complete.py --debug            # 调试模式
  python automation_complete.py --test --performance  # 测试+性能
```

## 📊 性能指标

### 测试结果
- **屏幕适配**: <1ms (100次调用)
- **检测处理**: <100ms (500个目标)
- **操作创建**: <1ms (1000次创建)
- **GUI响应**: <50ms (界面交互)

### 内存使用
- **基础内存**: ~50MB
- **GUI运行**: ~80MB
- **大量目标**: ~120MB

## 🐛 故障排除

### 常见问题

#### PyQt6安装问题
```bash
# 问题：ImportError: No module named 'PyQt6'
# 解决：
pip install PyQt6

# 或使用conda
conda install pyqt
```

#### 屏幕显示问题
```bash
# 问题：窗口显示不完整
# 解决：
1. 检查屏幕分辨率设置
2. 尝试调整窗口大小
3. 使用F11全屏模式
```

#### 性能问题
```bash
# 问题：界面响应缓慢
# 解决：
1. 减少检测目标数量
2. 关闭不必要的可视化选项
3. 使用--debug查看性能日志
```

## 📝 开发说明

### 代码结构
```
automation_complete.py
├── 数据类定义 (DetectionTarget, AutomationAction)
├── 屏幕适配器 (ScreenAdapter)
├── 核心功能类 (DetectionProcessor, AutomationExecutor, TemplateManager)
├── GUI组件 (DetectionVisualizerWidget, OperationConfigWidget)
├── 主窗口类 (AutomationWindow)
├── 测试功能 (TestRunner)
└── 主函数和命令行接口
```

### 扩展开发
```python
# 添加新的操作类型
class CustomAction(AutomationAction):
    def __init__(self, custom_param):
        super().__init__(
            id=f"custom_{int(time.time())}",
            action_type="custom",
            description="Custom operation"
        )

# 添加新的GUI组件
class CustomWidget(QWidget):
    def __init__(self):
        super().__init__()
        # 自定义组件实现
```

## 📄 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 👥 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 📞 联系方式

- 邮箱: <EMAIL>
- 项目: YOLO OpenCV Detector Team

---

**🎉 享受自动化操作配置的便利！**
