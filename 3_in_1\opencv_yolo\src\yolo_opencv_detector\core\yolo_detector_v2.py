# -*- coding: utf-8 -*-
"""
重构的YOLO检测器 - 支持多种模型和优化
作者: Cursor AI
创建时间: 2025-07-05
编码标准: UTF-8无BOM
"""

import time
from typing import List, Dict, Any, Optional, Tuple
import numpy as np
from pathlib import Path

from ..utils.logger import Logger
from ..utils.config_manager import ConfigManager


class YOLODetectorV2:
    """重构的YOLO检测器类"""
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.logger = Logger()
        
        self.model = None
        self.device = 'cpu'
        self.model_path = None
        self.is_loaded = False
        
        # 检测参数
        self.confidence_threshold = 0.5
        self.nms_threshold = 0.4
        self.max_detections = 100
        
        # 性能统计
        self.detection_count = 0
        self.total_time = 0.0
        
        self._load_config()
        self.logger.info("YOLO检测器初始化完成")
    
    def _load_config(self) -> None:
        """加载配置"""
        try:
            # 使用默认配置
            config = {
                'model_path': 'models/yolov8n.pt',
                'confidence_threshold': 0.5,
                'nms_threshold': 0.4,
                'max_detections': 100,
                'device': 'auto'
            }
            
            self.model_path = config.get('model_path', 'models/yolov8n.pt')
            self.confidence_threshold = config.get('confidence_threshold', 0.5)
            self.nms_threshold = config.get('nms_threshold', 0.4)
            self.max_detections = config.get('max_detections', 100)
            
            # 设备选择
            device_config = config.get('device', 'auto')
            if device_config == 'auto':
                self.device = self._auto_select_device()
            else:
                self.device = device_config
                
        except Exception as e:
            self.logger.error(f"加载YOLO配置失败: {e}")
    
    def _auto_select_device(self) -> str:
        """自动选择设备"""
        try:
            import torch
            if torch.cuda.is_available():
                self.logger.info("检测到CUDA，使用GPU加速")
                return 'cuda'
            else:
                self.logger.info("未检测到CUDA，使用CPU")
                return 'cpu'
        except ImportError:
            self.logger.warning("PyTorch未安装，使用CPU")
            return 'cpu'
    
    def load_model(self, model_path: Optional[str] = None) -> bool:
        """
        加载YOLO模型
        
        Args:
            model_path: 模型文件路径，None表示使用配置中的路径
            
        Returns:
            是否加载成功
        """
        if model_path is None:
            model_path = self.model_path
        
        try:
            # 检查模型文件是否存在
            if not Path(model_path).exists():
                self.logger.error(f"YOLO模型文件不存在: {model_path}")
                return False
            
            # 尝试导入ultralytics
            try:
                from ultralytics import YOLO
            except ImportError:
                self.logger.error("ultralytics包未安装，请运行: pip install ultralytics")
                return False
            
            # 加载模型
            self.logger.info(f"正在加载YOLO模型: {model_path}")
            self.model = YOLO(model_path)
            
            # 设置设备
            if hasattr(self.model, 'to'):
                self.model.to(self.device)
            
            self.model_path = model_path
            self.is_loaded = True
            
            self.logger.info(f"YOLO模型加载成功，设备: {self.device}")
            return True
            
        except Exception as e:
            self.logger.error(f"加载YOLO模型失败: {e}")
            self.is_loaded = False
            return False
    
    def detect(self, image: np.ndarray, 
               confidence: Optional[float] = None,
               nms_threshold: Optional[float] = None) -> List[Dict[str, Any]]:
        """
        执行目标检测
        
        Args:
            image: 输入图像 (BGR格式)
            confidence: 置信度阈值，None表示使用默认值
            nms_threshold: NMS阈值，None表示使用默认值
            
        Returns:
            检测结果列表
        """
        if not self.is_loaded:
            if not self.load_model():
                self.logger.error("YOLO模型未加载")
                return []
        
        # 使用参数或默认值
        conf_thresh = confidence if confidence is not None else self.confidence_threshold
        nms_thresh = nms_threshold if nms_threshold is not None else self.nms_threshold
        
        try:
            start_time = time.time()
            
            # 执行检测
            results = self.model(
                image,
                conf=conf_thresh,
                iou=nms_thresh,
                max_det=self.max_detections,
                verbose=False
            )
            
            # 解析结果
            detections = []
            if results and len(results) > 0:
                result = results[0]  # 单张图像
                
                if hasattr(result, 'boxes') and result.boxes is not None:
                    boxes = result.boxes
                    
                    # 提取检测框信息
                    if hasattr(boxes, 'xyxy') and boxes.xyxy is not None:
                        xyxy = boxes.xyxy.cpu().numpy()
                        confidences = boxes.conf.cpu().numpy() if hasattr(boxes, 'conf') else []
                        classes = boxes.cls.cpu().numpy() if hasattr(boxes, 'cls') else []
                        
                        for i in range(len(xyxy)):
                            x1, y1, x2, y2 = xyxy[i]
                            
                            detection = {
                                'source': 'yolo',
                                'bbox': [int(x1), int(y1), int(x2 - x1), int(y2 - y1)],  # [x, y, w, h]
                                'confidence': float(confidences[i]) if i < len(confidences) else 0.0,
                                'class_id': int(classes[i]) if i < len(classes) else 0,
                                'class_name': self._get_class_name(int(classes[i]) if i < len(classes) else 0)
                            }
                            detections.append(detection)
            
            # 更新性能统计
            detection_time = time.time() - start_time
            self.detection_count += 1
            self.total_time += detection_time
            
            self.logger.debug(f"YOLO检测完成: {len(detections)}个目标, 耗时: {detection_time:.3f}s")
            
            return detections
            
        except Exception as e:
            self.logger.error(f"YOLO检测失败: {e}")
            return []
    
    def _get_class_name(self, class_id: int) -> str:
        """获取类别名称"""
        try:
            if self.model and hasattr(self.model, 'names'):
                return self.model.names.get(class_id, f"class_{class_id}")
            else:
                return f"class_{class_id}"
        except:
            return f"class_{class_id}"
    
    def detect_async(self, image: np.ndarray, callback=None) -> None:
        """
        异步检测（在线程中执行）
        
        Args:
            image: 输入图像
            callback: 回调函数，接收检测结果
        """
        import threading
        
        def _detect_worker():
            try:
                results = self.detect(image)
                if callback:
                    callback(results)
            except Exception as e:
                self.logger.error(f"异步检测失败: {e}")
                if callback:
                    callback([])
        
        thread = threading.Thread(target=_detect_worker, daemon=True)
        thread.start()
    
    def update_config(self, config: Dict[str, Any]) -> None:
        """更新检测配置"""
        try:
            if 'confidence_threshold' in config:
                self.confidence_threshold = float(config['confidence_threshold'])
            
            if 'nms_threshold' in config:
                self.nms_threshold = float(config['nms_threshold'])
            
            if 'max_detections' in config:
                self.max_detections = int(config['max_detections'])
            
            if 'model_path' in config and config['model_path'] != self.model_path:
                self.load_model(config['model_path'])
            
            self.logger.info("YOLO检测配置已更新")
            
        except Exception as e:
            self.logger.error(f"更新YOLO配置失败: {e}")
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        if self.detection_count > 0:
            avg_time = self.total_time / self.detection_count
            fps = 1.0 / avg_time if avg_time > 0 else 0.0
        else:
            avg_time = 0.0
            fps = 0.0
        
        return {
            'detection_count': self.detection_count,
            'total_time': self.total_time,
            'average_time': avg_time,
            'fps': fps,
            'device': self.device,
            'model_loaded': self.is_loaded,
            'model_path': self.model_path
        }
    
    def reset_stats(self) -> None:
        """重置性能统计"""
        self.detection_count = 0
        self.total_time = 0.0
        self.logger.info("YOLO性能统计已重置")
    
    def warmup(self, size: Tuple[int, int] = (640, 640)) -> bool:
        """
        模型预热
        
        Args:
            size: 预热图像尺寸
            
        Returns:
            是否预热成功
        """
        if not self.is_loaded:
            if not self.load_model():
                return False
        
        try:
            # 创建随机图像进行预热
            dummy_image = np.random.randint(0, 255, (size[1], size[0], 3), dtype=np.uint8)
            
            self.logger.info("正在进行YOLO模型预热...")
            
            # 执行几次检测进行预热
            for i in range(3):
                self.detect(dummy_image)
            
            self.logger.info("YOLO模型预热完成")
            return True
            
        except Exception as e:
            self.logger.error(f"YOLO模型预热失败: {e}")
            return False
    
    def is_model_loaded(self) -> bool:
        """检查模型是否已加载"""
        return self.is_loaded
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        info = {
            'model_path': self.model_path,
            'is_loaded': self.is_loaded,
            'device': self.device,
            'confidence_threshold': self.confidence_threshold,
            'nms_threshold': self.nms_threshold,
            'max_detections': self.max_detections
        }
        
        if self.is_loaded and self.model:
            try:
                if hasattr(self.model, 'names'):
                    info['class_names'] = list(self.model.names.values())
                    info['num_classes'] = len(self.model.names)
            except:
                pass
        
        return info
    
    def unload_model(self) -> None:
        """卸载模型释放内存"""
        try:
            if self.model is not None:
                del self.model
                self.model = None
            
            self.is_loaded = False
            
            # 清理GPU内存
            if self.device == 'cuda':
                try:
                    import torch
                    torch.cuda.empty_cache()
                except:
                    pass
            
            self.logger.info("YOLO模型已卸载")
            
        except Exception as e:
            self.logger.error(f"卸载YOLO模型失败: {e}")
    
    def __del__(self):
        """析构函数"""
        self.unload_model()
