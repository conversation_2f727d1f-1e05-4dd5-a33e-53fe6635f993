#!/usr/bin/env python3
"""
自动化操作窗口演示脚本
快速演示所有功能和特性
"""

import sys
import time
from pathlib import Path

def run_demo():
    """运行演示"""
    print("🎬 自动化操作窗口功能演示")
    print("=" * 50)
    
    print("\n📋 **演示内容**:")
    print("1. 🧪 运行完整测试套件")
    print("2. ⚡ 性能测试")
    print("3. 🖥️ GUI应用程序启动")
    print("4. 📊 屏幕适配演示")
    
    # 1. 运行测试
    print("\n" + "="*50)
    print("🧪 第一步：运行完整测试套件")
    print("="*50)
    
    import subprocess
    result = subprocess.run([
        sys.executable, "automation_complete.py", "--test", "--performance"
    ], capture_output=True, text=True)
    
    if result.returncode == 0:
        print("✅ 测试套件通过")
        print("📊 测试输出:")
        print(result.stdout[-500:])  # 显示最后500字符
    else:
        print("❌ 测试失败")
        print(result.stderr)
    
    # 2. 屏幕适配演示
    print("\n" + "="*50)
    print("📊 第二步：屏幕适配演示")
    print("="*50)
    
    # 导入必要的模块
    sys.path.insert(0, str(Path.cwd()))
    
    try:
        from automation_complete import ScreenAdapter, ScreenCategory
        
        adapter = ScreenAdapter()
        
        # 演示不同分辨率的适配
        test_resolutions = [
            ("4K显示器", 3840, 2160),
            ("2K显示器", 2560, 1440),
            ("Full HD", 1920, 1080),
            ("标准HD", 1366, 768),
            ("小屏幕", 1280, 720),
            ("超小屏", 1024, 768)
        ]
        
        print("分辨率适配测试:")
        print("-" * 40)
        
        for name, width, height in test_resolutions:
            category = adapter.get_screen_category(width, height)
            config = adapter.calculate_optimal_size(width, height)
            
            utilization = (config.width * config.height) / (width * height) * 100
            
            print(f"{name:12} ({width:4}×{height:4}) -> {config.width:4}×{config.height:3} ({utilization:4.1f}%)")
        
        print("✅ 屏幕适配演示完成")
        
    except Exception as e:
        print(f"❌ 屏幕适配演示失败: {e}")
    
    # 3. 功能特性演示
    print("\n" + "="*50)
    print("🎯 第三步：功能特性演示")
    print("="*50)
    
    try:
        from automation_complete import (
            DetectionProcessor, AutomationExecutor, TemplateManager
        )
        
        # 检测处理器演示
        print("🔍 检测处理器:")
        processor = DetectionProcessor()
        targets = processor.generate_mock_targets(3)
        print(f"   生成 {len(targets)} 个模拟目标")
        
        # 选择目标
        targets[0].selected = True
        targets[1].selected = True
        selected = processor.get_selected_targets()
        print(f"   选中 {len(selected)} 个目标")
        
        # 自动化执行器演示
        print("\n⚙️ 自动化执行器:")
        executor = AutomationExecutor()
        
        # 创建操作
        click_action = executor.create_click_action((100, 100))
        type_action = executor.create_type_action("Hello World")
        hotkey_action = executor.create_hotkey_action(["ctrl", "c"])
        
        operations = [click_action, type_action, hotkey_action]
        print(f"   创建 {len(operations)} 个操作:")
        for i, op in enumerate(operations, 1):
            print(f"     {i}. {op.description}")
        
        # 模拟执行
        success = executor.execute_operations(operations, dry_run=True)
        print(f"   执行结果: {'成功' if success else '失败'}")
        
        # 模板管理器演示
        print("\n📋 模板管理器:")
        manager = TemplateManager()
        templates = manager.get_all_templates()
        print(f"   默认模板数量: {len(templates)}")
        
        for template in templates:
            print(f"     - {template['name']}: {template['description']}")
        
        # 创建自定义模板
        template_id = manager.create_template(
            "演示模板", "演示用的自定义模板", "demo", operations
        )
        print(f"   创建自定义模板: {template_id}")
        
        print("✅ 功能特性演示完成")
        
    except Exception as e:
        print(f"❌ 功能特性演示失败: {e}")
    
    # 4. GUI启动演示
    print("\n" + "="*50)
    print("🖥️ 第四步：GUI应用程序演示")
    print("="*50)
    
    print("💡 启动GUI应用程序...")
    print("   命令: python automation_complete.py")
    print("   功能: 完整的图形界面应用程序")
    print("   特点: 智能屏幕适配、完整功能、专业界面")
    
    # 询问是否启动GUI
    try:
        response = input("\n是否启动GUI应用程序? (y/N): ").strip().lower()
        if response in ['y', 'yes']:
            print("🚀 启动GUI应用程序...")
            subprocess.run([sys.executable, "automation_complete.py"])
        else:
            print("⏭️ 跳过GUI启动")
    except KeyboardInterrupt:
        print("\n⏭️ 跳过GUI启动")
    
    # 演示总结
    print("\n" + "="*50)
    print("🎉 演示完成总结")
    print("="*50)
    
    print("✅ **演示内容回顾**:")
    print("   1. 🧪 完整测试套件 - 验证所有功能正常")
    print("   2. 📊 屏幕适配演示 - 支持多种分辨率")
    print("   3. 🎯 功能特性演示 - 核心功能展示")
    print("   4. 🖥️ GUI应用程序 - 完整图形界面")
    
    print("\n💡 **主要特点**:")
    print("   ✅ 完全独立运行，无需额外配置")
    print("   ✅ 智能屏幕适配，支持1024px-4K分辨率")
    print("   ✅ 完整的自动化操作配置功能")
    print("   ✅ 内置测试套件，确保功能可靠")
    print("   ✅ 专业的GUI界面，用户体验优秀")
    
    print("\n🚀 **使用方法**:")
    print("   • 基础使用: python automation_complete.py")
    print("   • 运行测试: python automation_complete.py --test")
    print("   • 调试模式: python automation_complete.py --debug")
    print("   • 查看帮助: python automation_complete.py --help")
    
    print("\n📞 **技术支持**:")
    print("   • 邮箱: <EMAIL>")
    print("   • 项目: YOLO OpenCV Detector Team")
    print("   • 文档: README_automation_complete.md")

def main():
    """主函数"""
    print("🎬 自动化操作窗口演示启动器")
    print("=" * 50)
    
    # 检查文件是否存在
    if not Path("automation_complete.py").exists():
        print("❌ 找不到 automation_complete.py 文件")
        print("💡 请确保在正确的目录中运行此演示脚本")
        return 1
    
    try:
        run_demo()
        return 0
    except KeyboardInterrupt:
        print("\n\n👋 演示被用户中断")
        return 0
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
