# -*- coding: utf-8 -*-
"""
图像加载工具模块 - 解决中文文件名兼容性问题

本模块提供了支持中文文件名的图像加载功能，解决了OpenCV在Windows系统上
对中文路径支持不完善的问题。

Created: 2025-07-13
Author: Augment Agent
"""

import cv2
import numpy as np
from pathlib import Path
from typing import Optional, Union
import logging

logger = logging.getLogger(__name__)


class ImageLoader:
    """图像加载器 - 支持中文文件名"""
    
    @staticmethod
    def load_image(file_path: Union[str, Path]) -> Optional[np.ndarray]:
        """
        加载图像文件，支持中文文件名
        
        Args:
            file_path: 图像文件路径
            
        Returns:
            加载的图像数组，失败返回None
        """
        try:
            file_path = str(file_path)
            
            # 方法1: 尝试标准的cv2.imread
            image = cv2.imread(file_path)
            if image is not None:
                logger.debug(f"标准方法加载图像成功: {file_path}")
                return image
            
            # 方法2: 使用字节解码方式（解决中文文件名问题）
            path_obj = Path(file_path)
            if path_obj.exists():
                try:
                    # 读取文件为字节数据
                    with open(file_path, 'rb') as f:
                        data = f.read()
                    
                    # 将字节数据转换为numpy数组
                    nparr = np.frombuffer(data, np.uint8)
                    
                    # 使用cv2.imdecode解码图像
                    image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
                    
                    if image is not None:
                        logger.debug(f"字节解码方法加载图像成功: {file_path}")
                        return image
                        
                except Exception as decode_error:
                    logger.warning(f"字节解码加载图像失败: {decode_error}")
            else:
                logger.warning(f"图像文件不存在: {file_path}")
            
            return None
            
        except Exception as e:
            logger.error(f"加载图像时发生异常: {e}")
            return None
    
    @staticmethod
    def load_image_safe(file_path: Union[str, Path], 
                       default_image: Optional[np.ndarray] = None) -> Optional[np.ndarray]:
        """
        安全加载图像文件，失败时返回默认图像
        
        Args:
            file_path: 图像文件路径
            default_image: 加载失败时返回的默认图像
            
        Returns:
            加载的图像数组或默认图像
        """
        image = ImageLoader.load_image(file_path)
        if image is not None:
            return image
        
        if default_image is not None:
            logger.info(f"图像加载失败，使用默认图像: {file_path}")
            return default_image
        
        logger.warning(f"图像加载失败且无默认图像: {file_path}")
        return None
    
    @staticmethod
    def validate_image_file(file_path: Union[str, Path]) -> bool:
        """
        验证图像文件是否可以正常加载
        
        Args:
            file_path: 图像文件路径
            
        Returns:
            是否可以正常加载
        """
        try:
            image = ImageLoader.load_image(file_path)
            return image is not None
        except Exception:
            return False
    
    @staticmethod
    def get_image_info(file_path: Union[str, Path]) -> Optional[dict]:
        """
        获取图像文件信息
        
        Args:
            file_path: 图像文件路径
            
        Returns:
            图像信息字典，包含宽度、高度、通道数等
        """
        try:
            image = ImageLoader.load_image(file_path)
            if image is not None:
                height, width = image.shape[:2]
                channels = image.shape[2] if len(image.shape) > 2 else 1
                
                return {
                    'width': width,
                    'height': height,
                    'channels': channels,
                    'shape': image.shape,
                    'dtype': str(image.dtype),
                    'size': image.size,
                    'file_path': str(file_path)
                }
            return None
        except Exception as e:
            logger.error(f"获取图像信息失败: {e}")
            return None


def load_template_image(template_data: dict) -> Optional[np.ndarray]:
    """
    从模板数据加载图像的便捷函数
    
    Args:
        template_data: 模板数据字典
        
    Returns:
        加载的图像数组
    """
    try:
        # 首先尝试从内存加载
        if 'image' in template_data:
            return template_data['image']
        
        # 然后尝试从文件加载
        template_path = template_data.get('path', '')
        if template_path:
            return ImageLoader.load_image(template_path)
        
        # 尝试基于模板名称查找文件
        template_name = template_data.get('name', '')
        if template_name:
            templates_dir = Path('templates')
            if templates_dir.exists():
                for ext in ['.png', '.jpg', '.jpeg', '.bmp']:
                    template_file = templates_dir / f"{template_name}{ext}"
                    if template_file.exists():
                        image = ImageLoader.load_image(template_file)
                        if image is not None:
                            return image
        
        return None
        
    except Exception as e:
        logger.error(f"从模板数据加载图像失败: {e}")
        return None


# 向后兼容的函数别名
load_image_with_chinese_support = ImageLoader.load_image
