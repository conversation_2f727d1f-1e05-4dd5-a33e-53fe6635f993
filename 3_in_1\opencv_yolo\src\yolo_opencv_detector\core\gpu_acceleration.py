# -*- coding: utf-8 -*-
"""
GPU加速和内存优化模块

提供CUDA加速、内存池管理、批处理优化等功能，
显著提升检测性能和系统稳定性。

Created: 2025-07-13
Author: Augment Agent
"""

import torch
import torch.nn as nn
import torch.cuda as cuda
import numpy as np
import cv2
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass
import logging
import threading
import time
from contextlib import contextmanager

logger = logging.getLogger(__name__)


@dataclass
class GPUConfig:
    """GPU配置"""
    device_id: int = 0  # GPU设备ID
    memory_fraction: float = 0.8  # 内存使用比例
    enable_mixed_precision: bool = True  # 混合精度
    batch_size: int = 4  # 批处理大小
    max_memory_cache: int = 1024  # 最大内存缓存(MB)
    enable_memory_pool: bool = True  # 启用内存池
    stream_count: int = 2  # CUDA流数量
    enable_tensorrt: bool = False  # TensorRT优化
    optimization_level: str = "O1"  # 优化级别


class CUDAMemoryPool:
    """CUDA内存池管理器"""
    
    def __init__(self, config: GPUConfig):
        self.config = config
        self.device = torch.device(f'cuda:{config.device_id}')
        self.memory_pool = {}
        self.allocation_stats = {
            'total_allocated': 0,
            'peak_allocated': 0,
            'cache_hits': 0,
            'cache_misses': 0
        }
        self.lock = threading.Lock()
        
    def allocate_tensor(self, shape: Tuple[int, ...], 
                       dtype: torch.dtype = torch.float32,
                       requires_grad: bool = False) -> torch.Tensor:
        """分配张量"""
        key = (shape, dtype, requires_grad)
        
        with self.lock:
            if key in self.memory_pool and self.memory_pool[key]:
                # 从池中获取
                tensor = self.memory_pool[key].pop()
                self.allocation_stats['cache_hits'] += 1
                return tensor
            else:
                # 新分配
                tensor = torch.empty(shape, dtype=dtype, device=self.device,
                                   requires_grad=requires_grad)
                self.allocation_stats['cache_misses'] += 1
                self.allocation_stats['total_allocated'] += tensor.numel() * tensor.element_size()
                self.allocation_stats['peak_allocated'] = max(
                    self.allocation_stats['peak_allocated'],
                    self.allocation_stats['total_allocated']
                )
                return tensor
    
    def release_tensor(self, tensor: torch.Tensor):
        """释放张量到池中"""
        if not tensor.is_cuda or tensor.device != self.device:
            return
        
        key = (tuple(tensor.shape), tensor.dtype, tensor.requires_grad)
        
        with self.lock:
            if key not in self.memory_pool:
                self.memory_pool[key] = []
            
            # 限制池大小
            if len(self.memory_pool[key]) < 10:  # 最多缓存10个相同规格的张量
                tensor.zero_()  # 清零数据
                self.memory_pool[key].append(tensor)
    
    def clear_pool(self):
        """清空内存池"""
        with self.lock:
            self.memory_pool.clear()
            torch.cuda.empty_cache()
            self.allocation_stats['total_allocated'] = 0
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self.lock:
            return {
                **self.allocation_stats,
                'pool_size': sum(len(tensors) for tensors in self.memory_pool.values()),
                'memory_cached': torch.cuda.memory_cached(self.device),
                'memory_allocated': torch.cuda.memory_allocated(self.device)
            }


class CUDAStreamManager:
    """CUDA流管理器"""
    
    def __init__(self, config: GPUConfig):
        self.config = config
        self.device = torch.device(f'cuda:{config.device_id}')
        self.streams = [torch.cuda.Stream(device=self.device) 
                       for _ in range(config.stream_count)]
        self.current_stream_idx = 0
        
    def get_stream(self) -> torch.cuda.Stream:
        """获取下一个可用流"""
        stream = self.streams[self.current_stream_idx]
        self.current_stream_idx = (self.current_stream_idx + 1) % len(self.streams)
        return stream
    
    @contextmanager
    def stream_context(self):
        """流上下文管理器"""
        stream = self.get_stream()
        with torch.cuda.stream(stream):
            yield stream
    
    def synchronize_all(self):
        """同步所有流"""
        for stream in self.streams:
            stream.synchronize()


class BatchProcessor:
    """批处理器"""
    
    def __init__(self, config: GPUConfig, memory_pool: CUDAMemoryPool):
        self.config = config
        self.memory_pool = memory_pool
        self.device = torch.device(f'cuda:{config.device_id}')
        
    def batch_images(self, images: List[np.ndarray]) -> torch.Tensor:
        """批量处理图像"""
        if not images:
            return torch.empty(0)
        
        # 统一图像尺寸
        target_size = (640, 640)  # 标准YOLO输入尺寸
        processed_images = []
        
        for img in images:
            # 调整尺寸
            resized = cv2.resize(img, target_size)
            # 归一化
            normalized = resized.astype(np.float32) / 255.0
            # 转换通道顺序 HWC -> CHW
            transposed = np.transpose(normalized, (2, 0, 1))
            processed_images.append(transposed)
        
        # 转换为批量张量
        batch_array = np.stack(processed_images)
        batch_tensor = self.memory_pool.allocate_tensor(
            batch_array.shape, torch.float32
        )
        batch_tensor.copy_(torch.from_numpy(batch_array))
        
        return batch_tensor
    
    def process_batch(self, batch_tensor: torch.Tensor, 
                     model_func: callable) -> List[Any]:
        """处理批量数据"""
        try:
            with torch.no_grad():
                if self.config.enable_mixed_precision:
                    with torch.cuda.amp.autocast():
                        results = model_func(batch_tensor)
                else:
                    results = model_func(batch_tensor)
            
            # 转换结果为列表
            if isinstance(results, torch.Tensor):
                return [results[i] for i in range(results.size(0))]
            elif isinstance(results, (list, tuple)):
                return list(results)
            else:
                return [results]
                
        finally:
            # 释放批量张量
            self.memory_pool.release_tensor(batch_tensor)


class TensorRTOptimizer:
    """TensorRT优化器"""
    
    def __init__(self, config: GPUConfig):
        self.config = config
        self.optimized_models = {}
        
    def optimize_model(self, model: nn.Module, 
                      input_shape: Tuple[int, ...],
                      model_name: str) -> Optional[Any]:
        """优化模型"""
        if not self.config.enable_tensorrt:
            return None
        
        try:
            import torch_tensorrt
            
            # 创建示例输入
            example_input = torch.randn(input_shape, device=f'cuda:{self.config.device_id}')
            
            # TensorRT编译
            optimized_model = torch_tensorrt.compile(
                model,
                inputs=[example_input],
                enabled_precisions={torch.float, torch.half} if self.config.enable_mixed_precision else {torch.float},
                workspace_size=1 << 30,  # 1GB
                max_batch_size=self.config.batch_size
            )
            
            self.optimized_models[model_name] = optimized_model
            logger.info(f"TensorRT优化完成: {model_name}")
            
            return optimized_model
            
        except ImportError:
            logger.warning("torch-tensorrt未安装，跳过TensorRT优化")
            return None
        except Exception as e:
            logger.error(f"TensorRT优化失败: {e}")
            return None
    
    def get_optimized_model(self, model_name: str) -> Optional[Any]:
        """获取优化后的模型"""
        return self.optimized_models.get(model_name)


class GPUImageProcessor:
    """GPU图像处理器"""
    
    def __init__(self, config: GPUConfig, memory_pool: CUDAMemoryPool):
        self.config = config
        self.memory_pool = memory_pool
        self.device = torch.device(f'cuda:{config.device_id}')
        
    def preprocess_image(self, image: np.ndarray) -> torch.Tensor:
        """GPU图像预处理"""
        # 转换为张量
        if len(image.shape) == 3:
            h, w, c = image.shape
            tensor_shape = (c, h, w)
        else:
            h, w = image.shape
            tensor_shape = (1, h, w)
        
        image_tensor = self.memory_pool.allocate_tensor(tensor_shape, torch.float32)
        
        # 数据传输到GPU
        if len(image.shape) == 3:
            # HWC -> CHW
            image_transposed = np.transpose(image, (2, 0, 1))
            image_tensor.copy_(torch.from_numpy(image_transposed.astype(np.float32)) / 255.0)
        else:
            image_tensor.copy_(torch.from_numpy(image.astype(np.float32)) / 255.0)
        
        return image_tensor
    
    def postprocess_detections(self, detections: torch.Tensor) -> List[Dict]:
        """GPU检测结果后处理"""
        # 转换回CPU进行后处理
        cpu_detections = detections.cpu().numpy()
        
        results = []
        for detection in cpu_detections:
            # 解析检测结果
            # 这里需要根据具体的检测模型格式进行调整
            if len(detection) >= 6:  # x, y, w, h, conf, class
                x, y, w, h, conf, cls = detection[:6]
                results.append({
                    'bbox': [int(x), int(y), int(w), int(h)],
                    'confidence': float(conf),
                    'class_id': int(cls)
                })
        
        return results
    
    def apply_nms_gpu(self, boxes: torch.Tensor, scores: torch.Tensor,
                     iou_threshold: float = 0.5) -> torch.Tensor:
        """GPU上的NMS"""
        try:
            import torchvision
            
            # 使用torchvision的GPU NMS
            keep_indices = torchvision.ops.nms(boxes, scores, iou_threshold)
            return keep_indices
            
        except ImportError:
            logger.warning("torchvision未安装，使用CPU NMS")
            # 回退到CPU NMS
            boxes_cpu = boxes.cpu()
            scores_cpu = scores.cpu()
            
            # 简单的CPU NMS实现
            keep = []
            order = scores_cpu.argsort(descending=True)
            
            while len(order) > 0:
                i = order[0]
                keep.append(i)
                
                if len(order) == 1:
                    break
                
                # 计算IoU
                ious = self._calculate_iou_cpu(boxes_cpu[i], boxes_cpu[order[1:]])
                
                # 保留IoU小于阈值的框
                mask = ious <= iou_threshold
                order = order[1:][mask]
            
            return torch.tensor(keep, device=self.device)
    
    def _calculate_iou_cpu(self, box1: torch.Tensor, boxes: torch.Tensor) -> torch.Tensor:
        """CPU上计算IoU"""
        # 计算交集
        inter_x1 = torch.max(box1[0], boxes[:, 0])
        inter_y1 = torch.max(box1[1], boxes[:, 1])
        inter_x2 = torch.min(box1[2], boxes[:, 2])
        inter_y2 = torch.min(box1[3], boxes[:, 3])
        
        inter_area = torch.clamp(inter_x2 - inter_x1, min=0) * torch.clamp(inter_y2 - inter_y1, min=0)
        
        # 计算并集
        box1_area = (box1[2] - box1[0]) * (box1[3] - box1[1])
        boxes_area = (boxes[:, 2] - boxes[:, 0]) * (boxes[:, 3] - boxes[:, 1])
        union_area = box1_area + boxes_area - inter_area
        
        return inter_area / union_area


class GPUAccelerationManager:
    """GPU加速管理器"""
    
    def __init__(self, config: GPUConfig):
        self.config = config
        self.device = torch.device(f'cuda:{config.device_id}')
        
        # 初始化组件
        self.memory_pool = CUDAMemoryPool(config)
        self.stream_manager = CUDAStreamManager(config)
        self.batch_processor = BatchProcessor(config, self.memory_pool)
        self.tensorrt_optimizer = TensorRTOptimizer(config)
        self.image_processor = GPUImageProcessor(config, self.memory_pool)
        
        # 设置CUDA内存分配策略
        self._setup_cuda_memory()
        
    def _setup_cuda_memory(self):
        """设置CUDA内存"""
        try:
            # 设置内存分配比例
            if self.config.memory_fraction < 1.0:
                torch.cuda.set_per_process_memory_fraction(
                    self.config.memory_fraction, self.config.device_id
                )
            
            # 启用内存池
            if self.config.enable_memory_pool:
                torch.cuda.empty_cache()
            
            logger.info(f"CUDA内存设置完成 - 设备: {self.device}, "
                       f"内存比例: {self.config.memory_fraction}")
            
        except Exception as e:
            logger.error(f"CUDA内存设置失败: {e}")
    
    def process_images_batch(self, images: List[np.ndarray],
                           model_func: callable) -> List[Dict]:
        """批量处理图像"""
        if not images:
            return []
        
        try:
            with self.stream_manager.stream_context() as stream:
                # 批量预处理
                batch_tensor = self.batch_processor.batch_images(images)
                
                # 批量推理
                results = self.batch_processor.process_batch(batch_tensor, model_func)
                
                # 后处理
                processed_results = []
                for result in results:
                    if isinstance(result, torch.Tensor):
                        detections = self.image_processor.postprocess_detections(result)
                        processed_results.append(detections)
                    else:
                        processed_results.append(result)
                
                return processed_results
                
        except Exception as e:
            logger.error(f"批量处理失败: {e}")
            return []
    
    def optimize_model_for_inference(self, model: nn.Module,
                                   input_shape: Tuple[int, ...],
                                   model_name: str) -> nn.Module:
        """优化模型用于推理"""
        try:
            # 设置为评估模式
            model.eval()
            model.to(self.device)
            
            # TensorRT优化
            optimized_model = self.tensorrt_optimizer.optimize_model(
                model, input_shape, model_name
            )
            
            if optimized_model is not None:
                return optimized_model
            
            # 如果TensorRT优化失败，使用torch.jit优化
            try:
                example_input = torch.randn(input_shape, device=self.device)
                traced_model = torch.jit.trace(model, example_input)
                traced_model = torch.jit.optimize_for_inference(traced_model)
                
                logger.info(f"Torch JIT优化完成: {model_name}")
                return traced_model
                
            except Exception as jit_error:
                logger.warning(f"Torch JIT优化失败: {jit_error}")
                return model
            
        except Exception as e:
            logger.error(f"模型优化失败: {e}")
            return model
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """获取内存统计"""
        return {
            'pool_stats': self.memory_pool.get_stats(),
            'cuda_memory_allocated': torch.cuda.memory_allocated(self.device),
            'cuda_memory_cached': torch.cuda.memory_cached(self.device),
            'cuda_memory_reserved': torch.cuda.memory_reserved(self.device),
            'max_memory_allocated': torch.cuda.max_memory_allocated(self.device),
            'max_memory_cached': torch.cuda.max_memory_cached(self.device)
        }
    
    def cleanup(self):
        """清理资源"""
        try:
            # 同步所有流
            self.stream_manager.synchronize_all()
            
            # 清空内存池
            self.memory_pool.clear_pool()
            
            # 清空CUDA缓存
            torch.cuda.empty_cache()
            
            logger.info("GPU加速管理器清理完成")
            
        except Exception as e:
            logger.error(f"GPU加速管理器清理失败: {e}")


# 全局GPU加速管理器
_global_gpu_manager: Optional[GPUAccelerationManager] = None


def get_gpu_manager(config: GPUConfig = None) -> Optional[GPUAccelerationManager]:
    """获取全局GPU加速管理器"""
    global _global_gpu_manager
    
    if not torch.cuda.is_available():
        logger.warning("CUDA不可用，无法启用GPU加速")
        return None
    
    if _global_gpu_manager is None:
        if config is None:
            config = GPUConfig()
        _global_gpu_manager = GPUAccelerationManager(config)
    
    return _global_gpu_manager


def cleanup_gpu_manager():
    """清理全局GPU管理器"""
    global _global_gpu_manager
    
    if _global_gpu_manager is not None:
        _global_gpu_manager.cleanup()
        _global_gpu_manager = None
