#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
悬浮检测配置
管理悬浮模式和智能检测的配置参数
作者: Cursor AI
创建时间: 2025-07-05
编码标准: UTF-8无BOM
"""

from dataclasses import dataclass
from typing import Dict, Any, Optional
from pathlib import Path


@dataclass
class FloatingDetectionConfig:
    """悬浮检测配置"""
    
    # 悬浮窗口配置
    floating_window_size: tuple = (60, 60)
    floating_window_opacity: float = 0.9
    floating_window_position: Optional[tuple] = None  # None表示自动定位
    
    # 屏幕叠加配置
    overlay_enabled: bool = True
    overlay_auto_fade: bool = True
    overlay_fade_duration: int = 3000  # 毫秒
    overlay_box_color: tuple = (0, 255, 0, 180)  # RGBA
    overlay_text_color: tuple = (255, 255, 255, 255)  # RGBA
    overlay_background_color: tuple = (0, 0, 0, 120)  # RGBA
    overlay_box_thickness: int = 2
    overlay_show_labels: bool = True
    overlay_show_confidence: bool = True
    
    # 智能检测配置
    position_threshold: int = 50  # 位置变化阈值（像素）
    confidence_threshold: float = 0.05  # 置信度变化阈值
    time_threshold: float = 2.0  # 时间间隔阈值（秒）
    
    # 保存配置
    save_directory: str = "screenshots"
    auto_save_enabled: bool = True
    save_format: str = "png"
    filename_template: str = "detection_{timestamp}_{targets}"
    
    # 性能配置
    max_history_size: int = 100
    cleanup_interval: int = 50
    memory_optimization: bool = True
    
    # 用户界面配置
    show_statistics: bool = True
    show_fps: bool = True
    show_detection_count: bool = True
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'floating_window': {
                'size': self.floating_window_size,
                'opacity': self.floating_window_opacity,
                'position': self.floating_window_position
            },
            'overlay': {
                'enabled': self.overlay_enabled,
                'auto_fade': self.overlay_auto_fade,
                'fade_duration': self.overlay_fade_duration,
                'box_color': self.overlay_box_color,
                'text_color': self.overlay_text_color,
                'background_color': self.overlay_background_color,
                'box_thickness': self.overlay_box_thickness,
                'show_labels': self.overlay_show_labels,
                'show_confidence': self.overlay_show_confidence
            },
            'smart_detection': {
                'position_threshold': self.position_threshold,
                'confidence_threshold': self.confidence_threshold,
                'time_threshold': self.time_threshold
            },
            'save': {
                'directory': self.save_directory,
                'auto_save_enabled': self.auto_save_enabled,
                'format': self.save_format,
                'filename_template': self.filename_template
            },
            'performance': {
                'max_history_size': self.max_history_size,
                'cleanup_interval': self.cleanup_interval,
                'memory_optimization': self.memory_optimization
            },
            'ui': {
                'show_statistics': self.show_statistics,
                'show_fps': self.show_fps,
                'show_detection_count': self.show_detection_count
            }
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'FloatingDetectionConfig':
        """从字典创建配置"""
        config = cls()
        
        # 悬浮窗口配置
        floating = data.get('floating_window', {})
        config.floating_window_size = tuple(floating.get('size', config.floating_window_size))
        config.floating_window_opacity = floating.get('opacity', config.floating_window_opacity)
        config.floating_window_position = floating.get('position', config.floating_window_position)
        
        # 叠加层配置
        overlay = data.get('overlay', {})
        config.overlay_enabled = overlay.get('enabled', config.overlay_enabled)
        config.overlay_auto_fade = overlay.get('auto_fade', config.overlay_auto_fade)
        config.overlay_fade_duration = overlay.get('fade_duration', config.overlay_fade_duration)
        config.overlay_box_color = tuple(overlay.get('box_color', config.overlay_box_color))
        config.overlay_text_color = tuple(overlay.get('text_color', config.overlay_text_color))
        config.overlay_background_color = tuple(overlay.get('background_color', config.overlay_background_color))
        config.overlay_box_thickness = overlay.get('box_thickness', config.overlay_box_thickness)
        config.overlay_show_labels = overlay.get('show_labels', config.overlay_show_labels)
        config.overlay_show_confidence = overlay.get('show_confidence', config.overlay_show_confidence)
        
        # 智能检测配置
        smart = data.get('smart_detection', {})
        config.position_threshold = smart.get('position_threshold', config.position_threshold)
        config.confidence_threshold = smart.get('confidence_threshold', config.confidence_threshold)
        config.time_threshold = smart.get('time_threshold', config.time_threshold)
        
        # 保存配置
        save = data.get('save', {})
        config.save_directory = save.get('directory', config.save_directory)
        config.auto_save_enabled = save.get('auto_save_enabled', config.auto_save_enabled)
        config.save_format = save.get('format', config.save_format)
        config.filename_template = save.get('filename_template', config.filename_template)
        
        # 性能配置
        performance = data.get('performance', {})
        config.max_history_size = performance.get('max_history_size', config.max_history_size)
        config.cleanup_interval = performance.get('cleanup_interval', config.cleanup_interval)
        config.memory_optimization = performance.get('memory_optimization', config.memory_optimization)
        
        # UI配置
        ui = data.get('ui', {})
        config.show_statistics = ui.get('show_statistics', config.show_statistics)
        config.show_fps = ui.get('show_fps', config.show_fps)
        config.show_detection_count = ui.get('show_detection_count', config.show_detection_count)
        
        return config


class FloatingDetectionConfigManager:
    """悬浮检测配置管理器"""
    
    def __init__(self, config_file: str = "floating_detection_config.json"):
        self.config_file = Path(config_file)
        self.config = FloatingDetectionConfig()
        self.load_config()
    
    def load_config(self) -> FloatingDetectionConfig:
        """加载配置"""
        try:
            if self.config_file.exists():
                import json
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                self.config = FloatingDetectionConfig.from_dict(data)
                print(f"悬浮检测配置已加载: {self.config_file}")
            else:
                print(f"配置文件不存在，使用默认配置: {self.config_file}")
        except Exception as e:
            print(f"加载悬浮检测配置失败: {e}")
            self.config = FloatingDetectionConfig()
        
        return self.config
    
    def save_config(self) -> bool:
        """保存配置"""
        try:
            import json
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config.to_dict(), f, indent=2, ensure_ascii=False)
            print(f"悬浮检测配置已保存: {self.config_file}")
            return True
        except Exception as e:
            print(f"保存悬浮检测配置失败: {e}")
            return False
    
    def get_config(self) -> FloatingDetectionConfig:
        """获取配置"""
        return self.config
    
    def update_config(self, **kwargs) -> bool:
        """更新配置"""
        try:
            for key, value in kwargs.items():
                if hasattr(self.config, key):
                    setattr(self.config, key, value)
            return self.save_config()
        except Exception as e:
            print(f"更新悬浮检测配置失败: {e}")
            return False


# 全局配置管理器实例
_config_manager = None

def get_floating_detection_config() -> FloatingDetectionConfigManager:
    """获取全局悬浮检测配置管理器"""
    global _config_manager
    if _config_manager is None:
        _config_manager = FloatingDetectionConfigManager()
    return _config_manager
