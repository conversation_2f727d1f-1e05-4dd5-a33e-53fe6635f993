# -*- coding: utf-8 -*-
"""
性能监控模块
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

import time
import psutil
import threading
from typing import Dict, Any, List, Optional, Callable
from collections import deque
from dataclasses import dataclass, asdict
import statistics

from .logger import Logger


@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    timestamp: float
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    fps: float
    detection_time: float
    frame_count: int


class PerformanceMonitor:
    """性能监控器类"""
    
    def __init__(self, max_history: int = 1000):
        """
        初始化性能监控器
        
        Args:
            max_history: 最大历史记录数量
        """
        self.logger = Logger().get_logger(__name__)
        
        # 性能数据存储
        self.max_history = max_history
        self.metrics_history: deque = deque(maxlen=max_history)
        
        # 监控状态
        self.is_monitoring = False
        self.monitor_thread: Optional[threading.Thread] = None
        self.monitor_interval = 1.0  # 监控间隔（秒）
        
        # 性能统计
        self.frame_count = 0
        self.total_detection_time = 0.0
        self.detection_times: deque = deque(maxlen=100)
        self.start_time = time.time()
        
        # 回调函数
        self.callbacks: List[Callable] = []
        
        # 系统信息
        self.process = psutil.Process()
        
        self.logger.info("性能监控器初始化完成")
    
    def start_monitoring(self, interval: float = 1.0) -> None:
        """
        开始性能监控
        
        Args:
            interval: 监控间隔（秒）
        """
        if self.is_monitoring:
            self.logger.warning("性能监控已在运行")
            return
        
        self.monitor_interval = interval
        self.is_monitoring = True
        
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop,
            daemon=True
        )
        self.monitor_thread.start()
        
        self.logger.info(f"性能监控已启动，间隔: {interval}秒")
    
    def stop_monitoring(self) -> None:
        """停止性能监控"""
        if not self.is_monitoring:
            return
        
        self.is_monitoring = False
        
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5.0)
        
        self.logger.info("性能监控已停止")
    
    def _monitor_loop(self) -> None:
        """监控循环"""
        while self.is_monitoring:
            try:
                # 收集性能指标
                metrics = self._collect_metrics()
                
                # 存储到历史记录
                self.metrics_history.append(metrics)
                
                # 调用回调函数
                for callback in self.callbacks:
                    try:
                        callback(metrics)
                    except Exception as e:
                        self.logger.error(f"性能监控回调失败: {e}")
                
                # 等待下次监控
                time.sleep(self.monitor_interval)
                
            except Exception as e:
                self.logger.error(f"性能监控循环出错: {e}")
                time.sleep(self.monitor_interval)
    
    def _collect_metrics(self) -> PerformanceMetrics:
        """收集性能指标"""
        try:
            # CPU使用率
            cpu_percent = self.process.cpu_percent()
            
            # 内存使用情况
            memory_info = self.process.memory_info()
            memory_percent = self.process.memory_percent()
            memory_used_mb = memory_info.rss / 1024 / 1024
            
            # FPS计算
            current_time = time.time()
            elapsed_time = current_time - self.start_time
            fps = self.frame_count / elapsed_time if elapsed_time > 0 else 0
            
            # 平均检测时间
            avg_detection_time = (
                statistics.mean(self.detection_times) 
                if self.detection_times else 0
            )
            
            return PerformanceMetrics(
                timestamp=current_time,
                cpu_percent=cpu_percent,
                memory_percent=memory_percent,
                memory_used_mb=memory_used_mb,
                fps=fps,
                detection_time=avg_detection_time,
                frame_count=self.frame_count
            )
            
        except Exception as e:
            self.logger.error(f"收集性能指标失败: {e}")
            return PerformanceMetrics(
                timestamp=time.time(),
                cpu_percent=0,
                memory_percent=0,
                memory_used_mb=0,
                fps=0,
                detection_time=0,
                frame_count=self.frame_count
            )
    
    def record_detection_time(self, detection_time: float) -> None:
        """
        记录检测时间
        
        Args:
            detection_time: 检测耗时（秒）
        """
        self.detection_times.append(detection_time)
        self.total_detection_time += detection_time
    
    def record_frame(self) -> None:
        """记录处理的帧数"""
        self.frame_count += 1
    
    def get_current_metrics(self) -> PerformanceMetrics:
        """获取当前性能指标"""
        return self._collect_metrics()
    
    def get_average_metrics(self, last_n: Optional[int] = None) -> Dict[str, float]:
        """
        获取平均性能指标
        
        Args:
            last_n: 最近N个记录，None表示所有记录
            
        Returns:
            Dict[str, float]: 平均指标
        """
        if not self.metrics_history:
            return {}
        
        # 获取指定数量的最近记录
        if last_n:
            recent_metrics = list(self.metrics_history)[-last_n:]
        else:
            recent_metrics = list(self.metrics_history)
        
        if not recent_metrics:
            return {}
        
        # 计算平均值
        avg_metrics = {
            "cpu_percent": statistics.mean(m.cpu_percent for m in recent_metrics),
            "memory_percent": statistics.mean(m.memory_percent for m in recent_metrics),
            "memory_used_mb": statistics.mean(m.memory_used_mb for m in recent_metrics),
            "fps": statistics.mean(m.fps for m in recent_metrics),
            "detection_time": statistics.mean(m.detection_time for m in recent_metrics),
            "sample_count": len(recent_metrics)
        }
        
        return avg_metrics
    
    def get_peak_metrics(self) -> Dict[str, float]:
        """获取峰值指标"""
        if not self.metrics_history:
            return {}
        
        metrics_list = list(self.metrics_history)
        
        return {
            "peak_cpu_percent": max(m.cpu_percent for m in metrics_list),
            "peak_memory_percent": max(m.memory_percent for m in metrics_list),
            "peak_memory_used_mb": max(m.memory_used_mb for m in metrics_list),
            "peak_fps": max(m.fps for m in metrics_list),
            "max_detection_time": max(m.detection_time for m in metrics_list),
            "min_detection_time": min(m.detection_time for m in metrics_list)
        }
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能总结"""
        current_time = time.time()
        elapsed_time = current_time - self.start_time
        
        summary = {
            "monitoring_duration": elapsed_time,
            "total_frames": self.frame_count,
            "average_fps": self.frame_count / elapsed_time if elapsed_time > 0 else 0,
            "total_detection_time": self.total_detection_time,
            "average_detection_time": (
                self.total_detection_time / len(self.detection_times) 
                if self.detection_times else 0
            ),
            "metrics_count": len(self.metrics_history),
            "is_monitoring": self.is_monitoring
        }
        
        # 添加当前指标
        current_metrics = self.get_current_metrics()
        summary["current"] = asdict(current_metrics)
        
        # 添加平均指标
        avg_metrics = self.get_average_metrics(last_n=60)  # 最近60个记录
        if avg_metrics:
            summary["recent_average"] = avg_metrics
        
        # 添加峰值指标
        peak_metrics = self.get_peak_metrics()
        if peak_metrics:
            summary["peaks"] = peak_metrics
        
        return summary
    
    def add_callback(self, callback: Callable[[PerformanceMetrics], None]) -> None:
        """
        添加性能监控回调函数
        
        Args:
            callback: 回调函数，接收PerformanceMetrics参数
        """
        self.callbacks.append(callback)
    
    def remove_callback(self, callback: Callable) -> None:
        """
        移除性能监控回调函数
        
        Args:
            callback: 要移除的回调函数
        """
        if callback in self.callbacks:
            self.callbacks.remove(callback)
    
    def reset_statistics(self) -> None:
        """重置统计信息"""
        self.frame_count = 0
        self.total_detection_time = 0.0
        self.detection_times.clear()
        self.metrics_history.clear()
        self.start_time = time.time()
        
        self.logger.info("性能统计信息已重置")
    
    def export_metrics(self, file_path: str) -> bool:
        """
        导出性能指标到文件
        
        Args:
            file_path: 导出文件路径
            
        Returns:
            bool: 是否成功导出
        """
        try:
            import json
            
            # 准备导出数据
            export_data = {
                "summary": self.get_performance_summary(),
                "metrics_history": [asdict(m) for m in self.metrics_history]
            }
            
            # 写入文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"性能指标已导出到: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"导出性能指标失败: {e}")
            return False
    
    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        try:
            return {
                "cpu_count": psutil.cpu_count(),
                "cpu_freq": psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None,
                "memory_total": psutil.virtual_memory().total,
                "memory_available": psutil.virtual_memory().available,
                "disk_usage": psutil.disk_usage('/').percent if hasattr(psutil, 'disk_usage') else None,
                "boot_time": psutil.boot_time(),
                "process_id": self.process.pid,
                "process_create_time": self.process.create_time()
            }
        except Exception as e:
            self.logger.error(f"获取系统信息失败: {e}")
            return {}
    
    def check_performance_alerts(self) -> List[str]:
        """检查性能警告"""
        alerts = []
        
        if not self.metrics_history:
            return alerts
        
        current_metrics = self.get_current_metrics()
        
        # CPU使用率警告
        if current_metrics.cpu_percent > 80:
            alerts.append(f"CPU使用率过高: {current_metrics.cpu_percent:.1f}%")
        
        # 内存使用率警告
        if current_metrics.memory_percent > 80:
            alerts.append(f"内存使用率过高: {current_metrics.memory_percent:.1f}%")
        
        # FPS过低警告
        if current_metrics.fps < 10 and self.frame_count > 10:
            alerts.append(f"FPS过低: {current_metrics.fps:.1f}")
        
        # 检测时间过长警告
        if current_metrics.detection_time > 1.0:
            alerts.append(f"检测时间过长: {current_metrics.detection_time:.3f}秒")
        
        return alerts


# 全局性能监控器实例
_global_monitor: Optional[PerformanceMonitor] = None


def get_global_monitor() -> PerformanceMonitor:
    """获取全局性能监控器实例"""
    global _global_monitor
    if _global_monitor is None:
        _global_monitor = PerformanceMonitor()
    return _global_monitor


def start_global_monitoring(interval: float = 1.0) -> None:
    """启动全局性能监控"""
    monitor = get_global_monitor()
    monitor.start_monitoring(interval)


def stop_global_monitoring() -> None:
    """停止全局性能监控"""
    global _global_monitor
    if _global_monitor:
        _global_monitor.stop_monitoring()


def record_detection_time(detection_time: float) -> None:
    """记录检测时间到全局监控器"""
    monitor = get_global_monitor()
    monitor.record_detection_time(detection_time)


def record_frame() -> None:
    """记录帧数到全局监控器"""
    monitor = get_global_monitor()
    monitor.record_frame()


def get_performance_summary() -> Dict[str, Any]:
    """获取全局性能总结"""
    monitor = get_global_monitor()
    return monitor.get_performance_summary()
