#!/usr/bin/env python3
"""
操作模板管理器
管理和复用常用的自动化操作序列
"""

import json
import time
from pathlib import Path
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, asdict
from .automation_executor import AutomationAction, AutomationExecutor

@dataclass
class OperationTemplate:
    """操作模板数据类"""
    id: str
    name: str
    description: str
    category: str  # office, design, system, custom
    actions: List[AutomationAction]
    created_time: float
    last_used: float = 0.0
    use_count: int = 0
    tags: List[str] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []

class TemplateManager:
    """操作模板管理器"""
    
    def __init__(self, templates_dir: str = "templates"):
        """初始化模板管理器"""
        self.templates_dir = Path(templates_dir)
        self.templates_dir.mkdir(exist_ok=True)
        
        self.templates: Dict[str, OperationTemplate] = {}
        self.categories = {
            "office": "办公软件操作",
            "design": "设计软件操作", 
            "system": "系统界面操作",
            "custom": "自定义操作"
        }
        
        self._load_templates()
        self._create_default_templates()
    
    def create_template(self, name: str, description: str, category: str,
                       actions: List[AutomationAction], tags: List[str] = None) -> str:
        """
        创建新模板
        
        Args:
            name: 模板名称
            description: 模板描述
            category: 模板分类
            actions: 操作序列
            tags: 标签列表
            
        Returns:
            模板ID
        """
        template_id = f"template_{int(time.time())}_{len(self.templates)}"
        
        template = OperationTemplate(
            id=template_id,
            name=name,
            description=description,
            category=category,
            actions=actions,
            created_time=time.time(),
            tags=tags or []
        )
        
        self.templates[template_id] = template
        self._save_template(template)
        
        return template_id
    
    def get_template(self, template_id: str) -> Optional[OperationTemplate]:
        """获取模板"""
        return self.templates.get(template_id)
    
    def get_templates_by_category(self, category: str) -> List[OperationTemplate]:
        """根据分类获取模板"""
        return [t for t in self.templates.values() if t.category == category]
    
    def search_templates(self, keyword: str) -> List[OperationTemplate]:
        """搜索模板"""
        keyword = keyword.lower()
        results = []
        
        for template in self.templates.values():
            if (keyword in template.name.lower() or 
                keyword in template.description.lower() or
                any(keyword in tag.lower() for tag in template.tags)):
                results.append(template)
        
        return results
    
    def update_template_usage(self, template_id: str):
        """更新模板使用统计"""
        if template_id in self.templates:
            template = self.templates[template_id]
            template.last_used = time.time()
            template.use_count += 1
            self._save_template(template)
    
    def delete_template(self, template_id: str) -> bool:
        """删除模板"""
        if template_id in self.templates:
            template = self.templates[template_id]
            template_file = self.templates_dir / f"{template_id}.json"
            
            if template_file.exists():
                template_file.unlink()
            
            del self.templates[template_id]
            return True
        
        return False
    
    def export_template(self, template_id: str, export_path: str) -> bool:
        """导出模板"""
        template = self.get_template(template_id)
        if not template:
            return False
        
        try:
            export_data = self._template_to_dict(template)
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"导出模板失败: {e}")
            return False
    
    def import_template(self, import_path: str) -> Optional[str]:
        """导入模板"""
        try:
            with open(import_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            template = self._dict_to_template(data)
            # 生成新的ID避免冲突
            template.id = f"imported_{int(time.time())}_{len(self.templates)}"
            
            self.templates[template.id] = template
            self._save_template(template)
            
            return template.id
        except Exception as e:
            print(f"导入模板失败: {e}")
            return None
    
    def get_popular_templates(self, limit: int = 10) -> List[OperationTemplate]:
        """获取热门模板"""
        sorted_templates = sorted(
            self.templates.values(),
            key=lambda t: t.use_count,
            reverse=True
        )
        return sorted_templates[:limit]
    
    def get_recent_templates(self, limit: int = 10) -> List[OperationTemplate]:
        """获取最近使用的模板"""
        sorted_templates = sorted(
            self.templates.values(),
            key=lambda t: t.last_used,
            reverse=True
        )
        return [t for t in sorted_templates if t.last_used > 0][:limit]
    
    def _load_templates(self):
        """加载所有模板"""
        for template_file in self.templates_dir.glob("*.json"):
            try:
                with open(template_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                template = self._dict_to_template(data)
                self.templates[template.id] = template
                
            except Exception as e:
                print(f"加载模板失败 {template_file}: {e}")
    
    def _save_template(self, template: OperationTemplate):
        """保存模板到文件"""
        template_file = self.templates_dir / f"{template.id}.json"
        
        try:
            data = self._template_to_dict(template)
            with open(template_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存模板失败: {e}")
    
    def _template_to_dict(self, template: OperationTemplate) -> Dict[str, Any]:
        """将模板转换为字典"""
        data = asdict(template)
        
        # 转换AutomationAction对象
        data['actions'] = [asdict(action) for action in template.actions]
        
        return data
    
    def _dict_to_template(self, data: Dict[str, Any]) -> OperationTemplate:
        """将字典转换为模板"""
        # 转换actions
        actions = []
        for action_data in data.get('actions', []):
            action = AutomationAction(**action_data)
            actions.append(action)
        
        data['actions'] = actions
        return OperationTemplate(**data)
    
    def _create_default_templates(self):
        """创建默认模板"""
        if len(self.templates) > 0:
            return  # 已有模板，不创建默认模板
        
        executor = AutomationExecutor()
        
        # 1. 复制粘贴模板
        copy_paste_actions = [
            executor.create_hotkey_action(['ctrl', 'c']),
            executor.create_hotkey_action(['ctrl', 'v'], delay_before=0.1)
        ]
        self.create_template(
            "复制粘贴",
            "快速复制和粘贴操作",
            "office",
            copy_paste_actions,
            ["复制", "粘贴", "快捷键"]
        )
        
        # 2. 保存文档模板
        save_actions = [
            executor.create_hotkey_action(['ctrl', 's'])
        ]
        self.create_template(
            "保存文档",
            "保存当前文档",
            "office",
            save_actions,
            ["保存", "文档", "快捷键"]
        )

        # 3. 新建文档模板
        new_doc_actions = [
            executor.create_hotkey_action(['ctrl', 'n'])
        ]
        self.create_template(
            "新建文档",
            "创建新文档",
            "office",
            new_doc_actions,
            ["新建", "文档", "快捷键"]
        )

        # 4. 查找替换模板
        find_replace_actions = [
            executor.create_hotkey_action(['ctrl', 'h'])
        ]
        self.create_template(
            "查找替换",
            "打开查找替换功能",
            "office",
            find_replace_actions,
            ["查找", "替换", "搜索"]
        )

        # 5. 撤销重做模板
        undo_redo_actions = [
            executor.create_hotkey_action(['ctrl', 'z']),
            executor.create_hotkey_action(['ctrl', 'y'], delay_before=0.1)
        ]
        self.create_template(
            "撤销重做",
            "撤销和重做操作",
            "office",
            undo_redo_actions,
            ["撤销", "重做", "编辑"]
        )
    
    def get_template_statistics(self) -> Dict[str, Any]:
        """获取模板统计信息"""
        total_templates = len(self.templates)
        category_counts = {}
        
        for template in self.templates.values():
            category = template.category
            category_counts[category] = category_counts.get(category, 0) + 1
        
        total_usage = sum(t.use_count for t in self.templates.values())
        
        return {
            "total_templates": total_templates,
            "category_distribution": category_counts,
            "total_usage": total_usage,
            "categories": self.categories
        }
