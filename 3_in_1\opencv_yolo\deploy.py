# -*- coding: utf-8 -*-
"""
YOLO OpenCV检测器一键部署脚本
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

import os
import sys
import subprocess
import platform
from pathlib import Path
import urllib.request
import time

class DeploymentManager:
    """部署管理器"""
    
    def __init__(self):
        self.project_root = Path.cwd()
        self.venv_path = self.project_root / "venv"
        self.system = platform.system().lower()
        self.python_executable = sys.executable
        
        # 根据系统设置激活脚本路径
        if self.system == "windows":
            self.activate_script = self.venv_path / "Scripts" / "activate.bat"
            self.venv_python = self.venv_path / "Scripts" / "python.exe"
            self.venv_pip = self.venv_path / "Scripts" / "pip.exe"
        else:
            self.activate_script = self.venv_path / "bin" / "activate"
            self.venv_python = self.venv_path / "bin" / "python"
            self.venv_pip = self.venv_path / "bin" / "pip"
    
    def print_step(self, step_num, title, description=""):
        """打印步骤信息"""
        print(f"\n{'='*60}")
        print(f"步骤 {step_num}: {title}")
        if description:
            print(f"描述: {description}")
        print(f"{'='*60}")
    
    def run_command(self, command, description="", use_venv=False):
        """运行命令"""
        if isinstance(command, str):
            command = command.split()
        
        # 如果使用虚拟环境，修改命令
        if use_venv and self.venv_python.exists():
            if command[0] == "python":
                command[0] = str(self.venv_python)
            elif command[0] == "pip":
                command[0] = str(self.venv_pip)
        
        print(f"🔧 执行: {' '.join(command)}")
        
        try:
            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                cwd=self.project_root
            )
            
            if result.returncode == 0:
                print(f"✅ {description or '命令执行成功'}")
                if result.stdout.strip():
                    print(f"输出: {result.stdout.strip()}")
                return True
            else:
                print(f"❌ {description or '命令执行失败'}")
                print(f"错误: {result.stderr.strip()}")
                return False
                
        except Exception as e:
            print(f"💥 命令执行异常: {e}")
            return False
    
    def check_python_version(self):
        """检查Python版本"""
        self.print_step(0, "检查Python版本")
        
        version = sys.version_info
        print(f"当前Python版本: {version.major}.{version.minor}.{version.micro}")
        
        if version.major < 3 or (version.major == 3 and version.minor < 8):
            print("❌ Python版本过低，需要3.8或更高版本")
            return False
        
        print("✅ Python版本符合要求")
        return True
    
    def create_virtual_environment(self):
        """创建虚拟环境"""
        self.print_step(1, "创建虚拟环境", f"在 {self.venv_path} 创建虚拟环境")
        
        if self.venv_path.exists():
            print("⚠️ 虚拟环境已存在，跳过创建")
            return True
        
        return self.run_command([
            self.python_executable, "-m", "venv", str(self.venv_path)
        ], "创建虚拟环境")
    
    def upgrade_pip(self):
        """升级pip"""
        self.print_step(2, "升级pip")
        
        return self.run_command([
            "python", "-m", "pip", "install", "--upgrade", "pip"
        ], "升级pip", use_venv=True)
    
    def create_project_structure(self):
        """创建项目结构"""
        self.print_step(3, "创建项目结构")
        
        directories = [
            "src/yolo_opencv_detector/core",
            "src/yolo_opencv_detector/gui/widgets",
            "src/yolo_opencv_detector/utils",
            "tests",
            "configs",
            "models",
            "scripts",
            "examples"
        ]
        
        for directory in directories:
            dir_path = self.project_root / directory
            dir_path.mkdir(parents=True, exist_ok=True)
            
            # 创建__init__.py文件
            if "src/" in directory:
                init_file = dir_path / "__init__.py"
                if not init_file.exists():
                    init_file.write_text('# -*- coding: utf-8 -*-\n')
        
        print("✅ 项目结构创建完成")
        return True
    
    def create_requirements_file(self):
        """创建requirements.txt"""
        self.print_step(4, "创建requirements.txt")
        
        requirements_content = """# YOLO OpenCV检测器核心依赖
ultralytics>=8.0.0
opencv-python>=4.5.0
PyQt6>=6.0.0
numpy>=1.21.0
loguru>=0.6.0
PyYAML>=6.0
psutil>=5.8.0
mss>=6.1.0
Pillow>=8.0.0

# 深度学习框架
torch>=1.9.0
torchvision>=0.10.0

# 可选OCR支持
easyocr>=1.6.0

# 开发和测试工具
pytest>=6.0.0
pytest-cov>=2.0.0
flake8>=3.8.0

# 构建工具
pyinstaller>=4.0
build>=0.7.0
"""
        
        requirements_file = self.project_root / "requirements.txt"
        try:
            with open(requirements_file, "w", encoding="utf-8") as f:
                f.write(requirements_content)
            print("✅ requirements.txt创建成功")
            return True
        except Exception as e:
            print(f"❌ 创建requirements.txt失败: {e}")
            return False
    
    def install_dependencies(self):
        """安装依赖"""
        self.print_step(5, "安装项目依赖")
        
        # 首先安装核心依赖
        core_packages = [
            "wheel",
            "setuptools",
            "numpy",
            "opencv-python",
            "PyQt6",
            "ultralytics",
            "loguru",
            "PyYAML",
            "psutil",
            "mss",
            "Pillow"
        ]
        
        print("📦 安装核心依赖包...")
        for package in core_packages:
            success = self.run_command([
                "pip", "install", package
            ], f"安装 {package}", use_venv=True)
            
            if not success:
                print(f"⚠️ {package} 安装失败，继续安装其他包")
        
        # 然后安装requirements.txt中的其他依赖
        requirements_file = self.project_root / "requirements.txt"
        if requirements_file.exists():
            print("📋 从requirements.txt安装剩余依赖...")
            self.run_command([
                "pip", "install", "-r", "requirements.txt"
            ], "安装requirements.txt依赖", use_venv=True)
        
        return True
    
    def download_yolo_model(self):
        """下载YOLO模型"""
        self.print_step(6, "下载YOLO模型文件")
        
        models_dir = self.project_root / "models"
        models_dir.mkdir(exist_ok=True)
        
        model_file = models_dir / "yolov8n.pt"
        
        if model_file.exists():
            print("✅ YOLO模型文件已存在")
            return True
        
        model_url = "https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8n.pt"
        
        try:
            print(f"📥 从 {model_url} 下载模型...")
            urllib.request.urlretrieve(model_url, model_file)
            
            if model_file.exists() and model_file.stat().st_size > 1000000:  # 至少1MB
                print("✅ YOLO模型下载成功")
                return True
            else:
                print("❌ 模型文件下载不完整")
                return False
                
        except Exception as e:
            print(f"❌ 模型下载失败: {e}")
            print("💡 您可以手动下载模型文件:")
            print(f"   URL: {model_url}")
            print(f"   保存到: {model_file}")
            return False
    
    def create_basic_config(self):
        """创建基本配置文件"""
        self.print_step(7, "创建配置文件")
        
        config_content = """# YOLO OpenCV检测器配置文件
app:
  name: "YOLO OpenCV Detector"
  version: "1.0.0"
  debug: false

detection:
  yolo_model_path: "models/yolov8n.pt"
  confidence_threshold: 0.5
  nms_threshold: 0.4
  max_detections: 100
  input_size: [640, 640]

template:
  threshold: 0.8
  scale_range: [0.8, 1.2]
  angle_range: 15
  max_templates: 50

fusion:
  enable: true
  iou_threshold: 0.5
  confidence_weight: 0.6
  template_weight: 0.4

gui:
  theme: "default"
  window_size: [1200, 800]
  auto_save: true
  language: "zh_CN"

performance:
  max_workers: 4
  batch_size: 1
  cache_size: 100
  gpu_enabled: true
"""
        
        config_file = self.project_root / "configs" / "default.yaml"
        try:
            with open(config_file, "w", encoding="utf-8") as f:
                f.write(config_content)
            print("✅ 配置文件创建成功")
            return True
        except Exception as e:
            print(f"❌ 创建配置文件失败: {e}")
            return False
    
    def create_test_script(self):
        """创建测试脚本"""
        self.print_step(8, "创建验证测试脚本")
        
        test_content = '''# -*- coding: utf-8 -*-
"""环境验证测试脚本"""

import sys
import importlib
from pathlib import Path

def test_imports():
    """测试包导入"""
    print("🔍 测试包导入...")
    
    packages = [
        ("numpy", "numpy"),
        ("opencv-python", "cv2"),
        ("PyQt6", "PyQt6.QtWidgets"),
        ("ultralytics", "ultralytics"),
        ("loguru", "loguru"),
        ("yaml", "yaml"),
        ("psutil", "psutil"),
        ("mss", "mss"),
        ("PIL", "PIL")
    ]
    
    success_count = 0
    for package_name, import_name in packages:
        try:
            importlib.import_module(import_name)
            print(f"   ✅ {package_name}")
            success_count += 1
        except ImportError as e:
            print(f"   ❌ {package_name} - {e}")
    
    print(f"\\n📊 导入测试结果: {success_count}/{len(packages)} 成功")
    return success_count >= len(packages) * 0.8  # 80%成功率

def test_yolo_model():
    """测试YOLO模型"""
    print("\\n🎯 测试YOLO模型...")
    
    try:
        from ultralytics import YOLO
        
        model_path = Path("models/yolov8n.pt")
        if not model_path.exists():
            print("   ❌ 模型文件不存在")
            return False
        
        print(f"   📁 模型文件大小: {model_path.stat().st_size / 1024 / 1024:.1f}MB")
        
        # 尝试加载模型
        model = YOLO(str(model_path))
        print("   ✅ YOLO模型加载成功")
        
        return True
        
    except Exception as e:
        print(f"   ❌ YOLO模型测试失败: {e}")
        return False

def test_gui():
    """测试GUI组件"""
    print("\\n🖥️ 测试GUI组件...")
    
    try:
        from PyQt6.QtWidgets import QApplication, QWidget
        from PyQt6.QtCore import Qt
        
        # 创建应用程序实例（不显示）
        app = QApplication([])
        widget = QWidget()
        
        print("   ✅ PyQt6 GUI组件正常")
        app.quit()
        return True
        
    except Exception as e:
        print(f"   ❌ GUI组件测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 YOLO OpenCV检测器环境验证")
    print("=" * 50)
    
    tests = [
        ("包导入测试", test_imports),
        ("YOLO模型测试", test_yolo_model),
        ("GUI组件测试", test_gui)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name} - 通过")
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"💥 {test_name} - 异常: {e}")
    
    print("\\n" + "=" * 50)
    success_rate = passed_tests / total_tests
    print(f"📊 测试结果: {passed_tests}/{total_tests} 通过 ({success_rate:.1%})")
    
    if success_rate >= 0.75:
        print("🎉 环境验证通过！项目可以正常使用。")
        return 0
    else:
        print("⚠️ 环境验证未完全通过，可能影响部分功能。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
'''
        
        test_file = self.project_root / "verify_deployment.py"
        try:
            with open(test_file, "w", encoding="utf-8") as f:
                f.write(test_content)
            print("✅ 验证测试脚本创建成功")
            return True
        except Exception as e:
            print(f"❌ 创建测试脚本失败: {e}")
            return False
    
    def run_verification(self):
        """运行验证测试"""
        self.print_step(9, "运行环境验证")
        
        return self.run_command([
            "python", "verify_deployment.py"
        ], "环境验证测试", use_venv=True)
    
    def create_activation_scripts(self):
        """创建激活脚本"""
        self.print_step(10, "创建便捷脚本")
        
        # Windows激活脚本
        if self.system == "windows":
            activate_content = f"""@echo off
echo 🚀 激活YOLO OpenCV检测器虚拟环境
call "{self.venv_path}\\Scripts\\activate.bat"
echo ✅ 虚拟环境已激活
echo 💡 运行 'python verify_deployment.py' 验证环境
echo 💡 运行 'deactivate' 退出虚拟环境
cmd /k
"""
            script_file = self.project_root / "activate_env.bat"
        else:
            activate_content = f"""#!/bin/bash
echo "🚀 激活YOLO OpenCV检测器虚拟环境"
source "{self.venv_path}/bin/activate"
echo "✅ 虚拟环境已激活"
echo "💡 运行 'python verify_deployment.py' 验证环境"
echo "💡 运行 'deactivate' 退出虚拟环境"
bash
"""
            script_file = self.project_root / "activate_env.sh"
        
        try:
            with open(script_file, "w", encoding="utf-8") as f:
                f.write(activate_content)
            
            # 给shell脚本添加执行权限
            if self.system != "windows":
                os.chmod(script_file, 0o755)
            
            print(f"✅ 激活脚本创建成功: {script_file}")
            return True
        except Exception as e:
            print(f"❌ 创建激活脚本失败: {e}")
            return False
    
    def deploy(self):
        """执行完整部署"""
        print("🚀 开始YOLO OpenCV检测器部署")
        print(f"📁 项目目录: {self.project_root}")
        print(f"🖥️ 操作系统: {self.system}")
        print(f"🐍 Python路径: {self.python_executable}")
        
        start_time = time.time()
        
        steps = [
            self.check_python_version,
            self.create_virtual_environment,
            self.upgrade_pip,
            self.create_project_structure,
            self.create_requirements_file,
            self.install_dependencies,
            self.download_yolo_model,
            self.create_basic_config,
            self.create_test_script,
            self.run_verification,
            self.create_activation_scripts
        ]
        
        success_count = 0
        for step in steps:
            if step():
                success_count += 1
            else:
                print(f"⚠️ 步骤失败，但继续执行...")
        
        deploy_time = time.time() - start_time
        
        print(f"\n{'='*60}")
        print("🎯 部署完成总结")
        print(f"{'='*60}")
        print(f"📊 成功步骤: {success_count}/{len(steps)}")
        print(f"⏱️ 部署耗时: {deploy_time:.1f}秒")
        
        if success_count >= len(steps) * 0.8:  # 80%成功率
            print("🎉 部署成功！")
            self.print_usage_instructions()
            return True
        else:
            print("⚠️ 部署部分成功，可能需要手动修复一些问题")
            return False
    
    def print_usage_instructions(self):
        """打印使用说明"""
        print(f"\n📋 使用说明:")
        print(f"{'='*40}")
        
        if self.system == "windows":
            print("1. 激活虚拟环境:")
            print(f"   双击运行: activate_env.bat")
            print(f"   或手动运行: {self.venv_path}\\Scripts\\activate.bat")
        else:
            print("1. 激活虚拟环境:")
            print(f"   运行: ./activate_env.sh")
            print(f"   或手动运行: source {self.venv_path}/bin/activate")
        
        print("\n2. 验证环境:")
        print("   python verify_deployment.py")
        
        print("\n3. 运行项目:")
        print("   python src/yolo_opencv_detector/main.py")
        
        print("\n4. 退出虚拟环境:")
        print("   deactivate")
        
        print(f"\n📁 重要文件位置:")
        print(f"   虚拟环境: {self.venv_path}")
        print(f"   配置文件: configs/default.yaml")
        print(f"   YOLO模型: models/yolov8n.pt")
        print(f"   验证脚本: verify_deployment.py")

def main():
    """主函数"""
    deployer = DeploymentManager()
    success = deployer.deploy()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
