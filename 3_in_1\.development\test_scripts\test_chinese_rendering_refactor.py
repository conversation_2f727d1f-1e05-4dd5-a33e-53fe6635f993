#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试中文渲染模块重构后的功能
验证迁移到YOLO项目utils目录后的中文字符渲染
"""

import sys
import os
import cv2
import numpy as np
from pathlib import Path

def test_yolo_chinese_rendering():
    """测试YOLO项目中的中文渲染功能"""
    print("=" * 60)
    print("YOLO项目中文渲染功能测试")
    print("=" * 60)
    
    try:
        # 添加YOLO项目路径
        yolo_src_path = Path(__file__).parent / "opencv_yolo" / "src"
        sys.path.insert(0, str(yolo_src_path))
        
        # 导入中文渲染器
        from yolo_opencv_detector.utils.chinese_text_renderer import put_chinese_text, ChineseTextRenderer
        print("✅ 中文渲染器导入成功（相对导入）")
        
        # 创建测试图像
        test_image = np.zeros((400, 600, 3), dtype=np.uint8)
        test_image.fill(50)  # 深灰色背景
        
        # 测试中文文本
        test_texts = [
            ("置信度: 0.95", (50, 50)),
            ("本地感盒", (50, 100)),
            ("未地感盒", (50, 150)),
            ("检测结果", (50, 200)),
            ("类别: 人员", (50, 250)),
            ("English + 中文", (50, 300)),
        ]
        
        print("\n🎨 开始渲染测试文本...")
        
        for text, position in test_texts:
            try:
                test_image = put_chinese_text(
                    test_image, 
                    text, 
                    position,
                    font_size=18,
                    color=(0, 255, 0),  # 绿色
                    background=True
                )
                print(f"  ✅ 渲染成功: '{text}'")
                
            except Exception as e:
                print(f"  ❌ 渲染失败: '{text}' - {e}")
                return False
        
        # 保存测试结果
        output_dir = Path("test_results")
        output_dir.mkdir(exist_ok=True)
        
        result_path = output_dir / "yolo_chinese_rendering_refactor_test.png"
        cv2.imwrite(str(result_path), test_image)
        
        print(f"\n💾 测试结果已保存: {result_path}")
        
        # 测试渲染器实例
        renderer = ChineseTextRenderer()
        print(f"\n🔤 字体信息:")
        print(f"  可用字体数量: {len(renderer.available_fonts)}")
        for i, font_path in enumerate(renderer.available_fonts[:3]):  # 只显示前3个
            if font_path:
                print(f"  字体 {i}: {Path(font_path).name}")
            else:
                print(f"  字体 {i}: 默认字体")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_smart_detection_manager_integration():
    """测试智能检测管理器的集成"""
    print("\n" + "=" * 60)
    print("智能检测管理器集成测试")
    print("=" * 60)
    
    try:
        # 添加YOLO项目路径
        yolo_src_path = Path(__file__).parent / "opencv_yolo" / "src"
        sys.path.insert(0, str(yolo_src_path))
        
        # 导入智能检测管理器
        from yolo_opencv_detector.core.smart_detection_manager import SmartDetectionManager
        print("✅ 智能检测管理器导入成功")
        
        # 检查中文渲染器是否可用
        import yolo_opencv_detector.core.smart_detection_manager as sdm
        if hasattr(sdm, 'CHINESE_RENDERER_AVAILABLE'):
            if sdm.CHINESE_RENDERER_AVAILABLE:
                print("✅ 中文渲染器在智能检测管理器中可用")
            else:
                print("❌ 中文渲染器在智能检测管理器中不可用")
                return False
        else:
            print("❌ 智能检测管理器中未找到中文渲染器标志")
            return False
        
        # 测试put_chinese_text函数是否可用
        if hasattr(sdm, 'put_chinese_text'):
            print("✅ put_chinese_text函数在智能检测管理器中可用")
        else:
            print("❌ put_chinese_text函数在智能检测管理器中不可用")
            return False
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入智能检测管理器失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        return False

def test_detection_result_rendering():
    """测试检测结果渲染功能"""
    print("\n" + "=" * 60)
    print("检测结果渲染功能测试")
    print("=" * 60)
    
    try:
        # 添加YOLO项目路径
        yolo_src_path = Path(__file__).parent / "opencv_yolo" / "src"
        sys.path.insert(0, str(yolo_src_path))
        
        from yolo_opencv_detector.utils.chinese_text_renderer import put_chinese_text
        
        # 创建模拟检测结果图像
        test_image = np.zeros((500, 700, 3), dtype=np.uint8)
        test_image.fill(30)  # 深色背景
        
        # 模拟检测框和标签
        detections = [
            {"bbox": (100, 100, 200, 150), "label": "本地感盒", "confidence": 0.95},
            {"bbox": (300, 200, 400, 250), "label": "未地感盒", "confidence": 0.87},
            {"bbox": (150, 300, 250, 350), "label": "确认按钮", "confidence": 0.92},
        ]
        
        print("🎯 渲染检测结果...")
        
        for detection in detections:
            x1, y1, x2, y2 = detection["bbox"]
            label = detection["label"]
            confidence = detection["confidence"]
            
            # 绘制检测框
            cv2.rectangle(test_image, (x1, y1), (x2, y2), (0, 255, 0), 2)
            
            # 绘制标签和置信度
            label_text = f"{label}"
            confidence_text = f"置信度: {confidence:.2f}"
            
            # 使用中文渲染器绘制文字
            test_image = put_chinese_text(
                test_image, label_text, (x1, y1 - 30), 
                font_size=16, color=(0, 255, 0), background=True
            )
            
            test_image = put_chinese_text(
                test_image, confidence_text, (x1, y1 - 10), 
                font_size=12, color=(255, 255, 0), background=True
            )
            
            print(f"  ✅ 渲染检测结果: {label} ({confidence:.2f})")
        
        # 保存结果
        output_dir = Path("test_results")
        output_dir.mkdir(exist_ok=True)
        
        result_path = output_dir / "detection_result_rendering_test.png"
        cv2.imwrite(str(result_path), test_image)
        
        print(f"\n💾 检测结果渲染测试已保存: {result_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检测结果渲染测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_comparison_demo():
    """创建重构前后的对比演示"""
    print("\n" + "=" * 60)
    print("创建重构对比演示")
    print("=" * 60)
    
    try:
        # 创建对比图像
        demo_image = np.zeros((600, 1000, 3), dtype=np.uint8)
        demo_image.fill(40)
        
        # 添加YOLO项目路径
        yolo_src_path = Path(__file__).parent / "opencv_yolo" / "src"
        sys.path.insert(0, str(yolo_src_path))
        
        from yolo_opencv_detector.utils.chinese_text_renderer import put_chinese_text
        
        # 标题
        demo_image = put_chinese_text(demo_image, "中文渲染模块重构对比", (50, 30), 
                                     font_size=28, color=(255, 255, 255), background=False)
        
        # 重构前
        demo_image = put_chinese_text(demo_image, "重构前:", (50, 100), 
                                     font_size=20, color=(255, 100, 100), background=False)
        demo_image = put_chinese_text(demo_image, "• 位于 3_in_1/utils/ 目录", (70, 140), 
                                     font_size=16, color=(255, 200, 200), background=False)
        demo_image = put_chinese_text(demo_image, "• 跨项目依赖", (70, 170),
                                     font_size=16, color=(255, 200, 200), background=False)
        demo_image = put_chinese_text(demo_image, "• 命名空间冲突风险", (70, 200), 
                                     font_size=16, color=(255, 200, 200), background=False)
        
        # 重构后
        demo_image = put_chinese_text(demo_image, "重构后:", (50, 280), 
                                     font_size=20, color=(100, 255, 100), background=False)
        demo_image = put_chinese_text(demo_image, "• 位于 YOLO项目内部 utils/ 目录", (70, 320), 
                                     font_size=16, color=(200, 255, 200), background=False)
        demo_image = put_chinese_text(demo_image, "• 使用相对导入", (70, 350), 
                                     font_size=16, color=(200, 255, 200), background=False)
        demo_image = put_chinese_text(demo_image, "• 项目独立性", (70, 380), 
                                     font_size=16, color=(200, 255, 200), background=False)
        demo_image = put_chinese_text(demo_image, "• 避免命名空间冲突", (70, 410), 
                                     font_size=16, color=(200, 255, 200), background=False)
        
        # 功能演示
        demo_image = put_chinese_text(demo_image, "功能演示:", (50, 480), 
                                     font_size=20, color=(255, 255, 100), background=False)
        
        # 绘制示例检测框
        cv2.rectangle(demo_image, (500, 200), (700, 300), (0, 255, 0), 3)
        demo_image = put_chinese_text(demo_image, "本地感盒", (510, 180), 
                                     font_size=16, color=(255, 255, 255), background=True)
        demo_image = put_chinese_text(demo_image, "置信度: 0.95", (510, 310), 
                                     font_size=14, color=(255, 255, 255), background=True)
        
        # 保存演示
        output_path = Path("test_results") / "chinese_rendering_refactor_demo.png"
        cv2.imwrite(str(output_path), demo_image)
        
        print(f"✅ 重构对比演示已保存: {output_path}")
        return True
        
    except Exception as e:
        print(f"❌ 创建对比演示失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始中文渲染模块重构测试...")
    
    success_count = 0
    total_tests = 4
    
    # 基础渲染测试
    if test_yolo_chinese_rendering():
        success_count += 1
    
    # 智能检测管理器集成测试
    if test_smart_detection_manager_integration():
        success_count += 1
    
    # 检测结果渲染测试
    if test_detection_result_rendering():
        success_count += 1
    
    # 对比演示创建
    if create_comparison_demo():
        success_count += 1
    
    print("\n" + "=" * 60)
    print("重构测试总结")
    print("=" * 60)
    
    if success_count == total_tests:
        print("🎉 所有测试通过！中文渲染模块重构成功！")
        print("✅ 模块已成功迁移到YOLO项目内部")
        print("✅ 相对导入工作正常")
        print("✅ 中文字符渲染功能完全正常")
        print("✅ 项目结构更加整洁和模块化")
    else:
        print(f"⚠️ {success_count}/{total_tests} 个测试通过")
        print("部分功能可能需要进一步调试")
    
    print("\n📁 测试结果文件保存在 test_results/ 目录中")
    print("请查看生成的图像文件以验证重构效果")
