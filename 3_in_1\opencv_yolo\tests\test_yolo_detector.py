# -*- coding: utf-8 -*-
"""
YOLO检测器测试
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

import pytest
import numpy as np
from PIL import Image
from pathlib import Path
import tempfile
import cv2

from yolo_opencv_detector.core.yolo_detector import YoloDetector
from yolo_opencv_detector.utils.data_structures import DetectionResult, DetectionSource


class TestYoloDetector:
    """YOLO检测器测试类"""
    
    @pytest.fixture
    def detector(self):
        """创建检测器实例"""
        return YoloDetector(
            model_path=None,  # 使用默认模型
            device="cpu",     # 强制使用CPU以确保测试稳定性
            confidence_threshold=0.5,
            nms_threshold=0.4
        )
    
    @pytest.fixture
    def sample_image(self):
        """创建示例图像"""
        # 创建一个简单的测试图像
        image = np.zeros((640, 640, 3), dtype=np.uint8)
        # 添加一些简单的形状
        cv2.rectangle(image, (100, 100), (200, 200), (255, 0, 0), -1)
        cv2.circle(image, (400, 400), 50, (0, 255, 0), -1)
        return image
    
    @pytest.fixture
    def sample_image_file(self, sample_image, tmp_path):
        """创建示例图像文件"""
        image_path = tmp_path / "test_image.jpg"
        cv2.imwrite(str(image_path), sample_image)
        return image_path
    
    def test_detector_initialization(self, detector):
        """测试检测器初始化"""
        assert detector is not None
        assert detector.confidence_threshold == 0.5
        assert detector.nms_threshold == 0.4
        assert detector.device == "cpu"
        assert not detector.is_loaded
    
    def test_model_loading(self, detector):
        """测试模型加载"""
        # 注意：这个测试需要实际的模型文件或网络连接
        # 在CI环境中可能需要跳过
        try:
            success = detector.load_model()
            if success:
                assert detector.is_loaded
                assert detector.model is not None
                assert detector.model_info is not None
        except Exception as e:
            pytest.skip(f"模型加载失败，可能是网络问题: {e}")
    
    def test_image_preprocessing(self, detector, sample_image):
        """测试图像预处理"""
        # 测试numpy数组
        processed = detector._preprocess_image(sample_image)
        assert processed is not None
        assert isinstance(processed, np.ndarray)
        assert processed.shape == sample_image.shape
        
        # 测试PIL图像
        pil_image = Image.fromarray(sample_image)
        processed = detector._preprocess_image(pil_image)
        assert processed is not None
        assert isinstance(processed, np.ndarray)
        
        # 测试灰度图
        gray_image = cv2.cvtColor(sample_image, cv2.COLOR_BGR2GRAY)
        processed = detector._preprocess_image(gray_image)
        assert processed is not None
        assert processed.ndim == 3  # 应该转换为3通道
    
    def test_image_preprocessing_file(self, detector, sample_image_file):
        """测试文件路径预处理"""
        processed = detector._preprocess_image(sample_image_file)
        assert processed is not None
        assert isinstance(processed, np.ndarray)
        
        # 测试不存在的文件
        non_existent = Path("non_existent_file.jpg")
        processed = detector._preprocess_image(non_existent)
        assert processed is None
    
    def test_detect_without_model(self, detector, sample_image):
        """测试未加载模型时的检测"""
        # 模拟模型加载失败
        detector.is_loaded = False
        detector.model = None
        
        results = detector.detect(sample_image)
        # 应该返回空列表或尝试加载模型
        assert isinstance(results, list)
    
    @pytest.mark.skipif(True, reason="需要实际的YOLO模型文件")
    def test_detect_with_model(self, detector, sample_image):
        """测试有模型时的检测"""
        # 这个测试需要实际的模型
        if detector.load_model():
            results = detector.detect(sample_image)
            assert isinstance(results, list)
            
            # 检查结果格式
            for result in results:
                assert isinstance(result, DetectionResult)
                assert result.source == DetectionSource.YOLO
                assert 0 <= result.confidence <= 1
                assert result.bbox is not None
    
    def test_config_update(self, detector):
        """测试配置更新"""
        original_confidence = detector.confidence_threshold
        original_nms = detector.nms_threshold
        
        detector.update_config(
            confidence_threshold=0.7,
            nms_threshold=0.3,
            max_detections=50
        )
        
        assert detector.confidence_threshold == 0.7
        assert detector.nms_threshold == 0.3
        assert detector.max_detections == 50
        
        # 恢复原始配置
        detector.update_config(
            confidence_threshold=original_confidence,
            nms_threshold=original_nms
        )
    
    def test_performance_stats(self, detector):
        """测试性能统计"""
        # 初始状态
        stats = detector.get_performance_stats()
        assert isinstance(stats, dict)
        
        # 添加一些模拟的推理时间
        detector.inference_times = [0.1, 0.2, 0.15, 0.18]
        detector.total_detections = 10
        
        stats = detector.get_performance_stats()
        assert "total_inferences" in stats
        assert "avg_inference_time" in stats
        assert "avg_fps" in stats
        assert stats["total_inferences"] == 4
        assert stats["total_detections"] == 10
        
        # 重置统计
        detector.reset_stats()
        assert len(detector.inference_times) == 0
        assert detector.total_detections == 0
    
    def test_model_info(self, detector):
        """测试模型信息获取"""
        info = detector.get_model_info()
        assert isinstance(info, dict)
        
        # 如果模型已加载，应该有更多信息
        if detector.is_loaded:
            assert "model_path" in info
            assert "device" in info
            assert "input_size" in info
    
    def test_device_resolution(self):
        """测试设备解析"""
        # 测试自动设备选择
        detector_auto = YoloDetector(device="auto")
        assert detector_auto.device in ["cpu", "cuda:0"]
        
        # 测试指定CPU
        detector_cpu = YoloDetector(device="cpu")
        assert detector_cpu.device == "cpu"
    
    def test_model_path_resolution(self):
        """测试模型路径解析"""
        # 测试默认路径
        detector = YoloDetector()
        assert detector.model_path.name.endswith(".pt")
        
        # 测试自定义路径
        custom_path = "custom_model.pt"
        detector = YoloDetector(model_path=custom_path)
        assert detector.model_path.name == "custom_model.pt"
    
    def test_invalid_inputs(self, detector):
        """测试无效输入处理"""
        # 测试None输入
        result = detector._preprocess_image(None)
        assert result is None
        
        # 测试空数组
        empty_array = np.array([])
        result = detector._preprocess_image(empty_array)
        assert result is None
        
        # 测试错误维度
        wrong_dim = np.zeros((10, 10, 10, 10))
        result = detector._preprocess_image(wrong_dim)
        assert result is None
    
    def test_memory_cleanup(self, detector):
        """测试内存清理"""
        # 模拟加载模型
        detector.is_loaded = True
        detector.model = "mock_model"  # 模拟模型对象
        
        # 卸载模型
        detector.unload_model()
        assert not detector.is_loaded
        assert detector.model is None
