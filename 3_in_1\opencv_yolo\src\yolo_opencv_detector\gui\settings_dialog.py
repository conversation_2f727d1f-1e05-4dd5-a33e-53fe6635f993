# -*- coding: utf-8 -*-
"""
设置对话框
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTabWidget,
    QWidget, QGroupBox, QLabel, QLineEdit, QSpinBox,
    QDoubleSpinBox, QCheckBox, QComboBox, QPushButton,
    QFileDialog, QMessageBox, QSlider, QTextEdit
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont

from typing import Dict, Any
from ..utils.logger import Logger


class SettingsDialog(QDialog):
    """设置对话框"""
    
    # 信号定义
    settings_changed = pyqtSignal(dict)
    
    def __init__(self, parent=None, settings: Dict[str, Any] = None):
        """
        初始化设置对话框
        
        Args:
            parent: 父窗口
            settings: 当前设置
        """
        super().__init__(parent)
        self.logger = Logger().get_logger(__name__)
        
        # 设置数据
        self.settings = settings or {}
        self.original_settings = self.settings.copy()
        
        self._setup_ui()
        self._load_settings()
        self._connect_signals()
        
        self.logger.info("设置对话框初始化完成")
    
    def _setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("设置")
        self.setModal(True)
        self.resize(600, 500)
        
        layout = QVBoxLayout(self)
        
        # 创建选项卡
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # 检测设置选项卡
        detection_tab = self._create_detection_tab()
        self.tab_widget.addTab(detection_tab, "检测设置")
        
        # 模板设置选项卡
        template_tab = self._create_template_tab()
        self.tab_widget.addTab(template_tab, "模板设置")
        
        # 界面设置选项卡
        ui_tab = self._create_ui_tab()
        self.tab_widget.addTab(ui_tab, "界面设置")
        
        # 性能设置选项卡
        performance_tab = self._create_performance_tab()
        self.tab_widget.addTab(performance_tab, "性能设置")
        
        # 按钮
        button_layout = QHBoxLayout()
        
        self.reset_btn = QPushButton("重置默认")
        button_layout.addWidget(self.reset_btn)
        
        button_layout.addStretch()
        
        self.cancel_btn = QPushButton("取消")
        button_layout.addWidget(self.cancel_btn)
        
        self.apply_btn = QPushButton("应用")
        button_layout.addWidget(self.apply_btn)
        
        self.ok_btn = QPushButton("确定")
        self.ok_btn.setDefault(True)
        button_layout.addWidget(self.ok_btn)
        
        layout.addLayout(button_layout)
    
    def _create_detection_tab(self) -> QWidget:
        """创建检测设置选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # YOLO设置组
        yolo_group = QGroupBox("YOLO设置")
        yolo_layout = QVBoxLayout(yolo_group)
        
        # 模型路径
        model_layout = QHBoxLayout()
        model_layout.addWidget(QLabel("模型路径:"))
        self.model_path_edit = QLineEdit()
        model_layout.addWidget(self.model_path_edit)
        self.browse_model_btn = QPushButton("浏览")
        model_layout.addWidget(self.browse_model_btn)
        yolo_layout.addLayout(model_layout)
        
        # 置信度阈值
        conf_layout = QHBoxLayout()
        conf_layout.addWidget(QLabel("置信度阈值:"))
        self.confidence_spin = QDoubleSpinBox()
        self.confidence_spin.setRange(0.0, 1.0)
        self.confidence_spin.setSingleStep(0.05)
        self.confidence_spin.setDecimals(2)
        conf_layout.addWidget(self.confidence_spin)
        yolo_layout.addLayout(conf_layout)
        
        # NMS阈值
        nms_layout = QHBoxLayout()
        nms_layout.addWidget(QLabel("NMS阈值:"))
        self.nms_spin = QDoubleSpinBox()
        self.nms_spin.setRange(0.0, 1.0)
        self.nms_spin.setSingleStep(0.05)
        self.nms_spin.setDecimals(2)
        nms_layout.addWidget(self.nms_spin)
        yolo_layout.addLayout(nms_layout)
        
        # 最大检测数
        max_det_layout = QHBoxLayout()
        max_det_layout.addWidget(QLabel("最大检测数:"))
        self.max_detections_spin = QSpinBox()
        self.max_detections_spin.setRange(1, 1000)
        max_det_layout.addWidget(self.max_detections_spin)
        yolo_layout.addLayout(max_det_layout)
        
        layout.addWidget(yolo_group)
        
        # 融合设置组
        fusion_group = QGroupBox("结果融合")
        fusion_layout = QVBoxLayout(fusion_group)
        
        self.enable_fusion_cb = QCheckBox("启用结果融合")
        fusion_layout.addWidget(self.enable_fusion_cb)
        
        # IOU阈值
        iou_layout = QHBoxLayout()
        iou_layout.addWidget(QLabel("IOU阈值:"))
        self.iou_spin = QDoubleSpinBox()
        self.iou_spin.setRange(0.0, 1.0)
        self.iou_spin.setSingleStep(0.05)
        self.iou_spin.setDecimals(2)
        iou_layout.addWidget(self.iou_spin)
        fusion_layout.addLayout(iou_layout)
        
        layout.addWidget(fusion_group)
        
        layout.addStretch()
        return widget
    
    def _create_template_tab(self) -> QWidget:
        """创建模板设置选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 模板匹配设置
        template_group = QGroupBox("模板匹配")
        template_layout = QVBoxLayout(template_group)
        
        # 默认阈值
        threshold_layout = QHBoxLayout()
        threshold_layout.addWidget(QLabel("默认阈值:"))
        self.template_threshold_spin = QDoubleSpinBox()
        self.template_threshold_spin.setRange(0.0, 1.0)
        self.template_threshold_spin.setSingleStep(0.05)
        self.template_threshold_spin.setDecimals(2)
        threshold_layout.addWidget(self.template_threshold_spin)
        template_layout.addLayout(threshold_layout)
        
        # 缩放范围
        scale_layout = QHBoxLayout()
        scale_layout.addWidget(QLabel("缩放范围:"))
        self.scale_min_spin = QDoubleSpinBox()
        self.scale_min_spin.setRange(0.1, 2.0)
        self.scale_min_spin.setSingleStep(0.1)
        self.scale_min_spin.setDecimals(1)
        scale_layout.addWidget(self.scale_min_spin)
        scale_layout.addWidget(QLabel("-"))
        self.scale_max_spin = QDoubleSpinBox()
        self.scale_max_spin.setRange(0.1, 2.0)
        self.scale_max_spin.setSingleStep(0.1)
        self.scale_max_spin.setDecimals(1)
        scale_layout.addWidget(self.scale_max_spin)
        template_layout.addLayout(scale_layout)
        
        # 角度范围
        angle_layout = QHBoxLayout()
        angle_layout.addWidget(QLabel("角度范围:"))
        self.angle_range_spin = QSpinBox()
        self.angle_range_spin.setRange(0, 180)
        self.angle_range_spin.setSuffix("°")
        angle_layout.addWidget(self.angle_range_spin)
        template_layout.addLayout(angle_layout)
        
        # 最大模板数
        max_templates_layout = QHBoxLayout()
        max_templates_layout.addWidget(QLabel("最大模板数:"))
        self.max_templates_spin = QSpinBox()
        self.max_templates_spin.setRange(1, 1000)
        max_templates_layout.addWidget(self.max_templates_spin)
        template_layout.addLayout(max_templates_layout)
        
        layout.addWidget(template_group)
        
        layout.addStretch()
        return widget
    
    def _create_ui_tab(self) -> QWidget:
        """创建界面设置选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 外观设置
        appearance_group = QGroupBox("外观")
        appearance_layout = QVBoxLayout(appearance_group)
        
        # 主题
        theme_layout = QHBoxLayout()
        theme_layout.addWidget(QLabel("主题:"))
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["默认", "深色", "浅色"])
        theme_layout.addWidget(self.theme_combo)
        appearance_layout.addLayout(theme_layout)
        
        # 语言
        language_layout = QHBoxLayout()
        language_layout.addWidget(QLabel("语言:"))
        self.language_combo = QComboBox()
        self.language_combo.addItems(["中文", "English"])
        language_layout.addWidget(self.language_combo)
        appearance_layout.addLayout(language_layout)
        
        # 字体大小
        font_layout = QHBoxLayout()
        font_layout.addWidget(QLabel("字体大小:"))
        self.font_size_spin = QSpinBox()
        self.font_size_spin.setRange(8, 24)
        self.font_size_spin.setSuffix("pt")
        font_layout.addWidget(self.font_size_spin)
        appearance_layout.addLayout(font_layout)
        
        layout.addWidget(appearance_group)
        
        # 行为设置
        behavior_group = QGroupBox("行为")
        behavior_layout = QVBoxLayout(behavior_group)
        
        self.auto_save_cb = QCheckBox("自动保存设置")
        behavior_layout.addWidget(self.auto_save_cb)
        
        self.show_tooltips_cb = QCheckBox("显示工具提示")
        behavior_layout.addWidget(self.show_tooltips_cb)
        
        self.confirm_exit_cb = QCheckBox("退出时确认")
        behavior_layout.addWidget(self.confirm_exit_cb)
        
        layout.addWidget(behavior_group)
        
        layout.addStretch()
        return widget
    
    def _create_performance_tab(self) -> QWidget:
        """创建性能设置选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 处理设置
        processing_group = QGroupBox("处理设置")
        processing_layout = QVBoxLayout(processing_group)
        
        # 最大工作线程
        workers_layout = QHBoxLayout()
        workers_layout.addWidget(QLabel("最大工作线程:"))
        self.max_workers_spin = QSpinBox()
        self.max_workers_spin.setRange(1, 16)
        workers_layout.addWidget(self.max_workers_spin)
        processing_layout.addLayout(workers_layout)
        
        # 批处理大小
        batch_layout = QHBoxLayout()
        batch_layout.addWidget(QLabel("批处理大小:"))
        self.batch_size_spin = QSpinBox()
        self.batch_size_spin.setRange(1, 32)
        batch_layout.addWidget(self.batch_size_spin)
        processing_layout.addLayout(batch_layout)
        
        # 缓存大小
        cache_layout = QHBoxLayout()
        cache_layout.addWidget(QLabel("缓存大小:"))
        self.cache_size_spin = QSpinBox()
        self.cache_size_spin.setRange(10, 1000)
        cache_layout.addWidget(self.cache_size_spin)
        processing_layout.addLayout(cache_layout)
        
        layout.addWidget(processing_group)
        
        # GPU设置
        gpu_group = QGroupBox("GPU设置")
        gpu_layout = QVBoxLayout(gpu_group)
        
        self.gpu_enabled_cb = QCheckBox("启用GPU加速")
        gpu_layout.addWidget(self.gpu_enabled_cb)
        
        # GPU设备
        gpu_device_layout = QHBoxLayout()
        gpu_device_layout.addWidget(QLabel("GPU设备:"))
        self.gpu_device_combo = QComboBox()
        self.gpu_device_combo.addItems(["自动", "cuda:0", "cuda:1"])
        gpu_device_layout.addWidget(self.gpu_device_combo)
        gpu_layout.addLayout(gpu_device_layout)
        
        layout.addWidget(gpu_group)
        
        # 内存设置
        memory_group = QGroupBox("内存设置")
        memory_layout = QVBoxLayout(memory_group)
        
        # 内存限制
        memory_limit_layout = QHBoxLayout()
        memory_limit_layout.addWidget(QLabel("内存限制:"))
        self.memory_limit_spin = QSpinBox()
        self.memory_limit_spin.setRange(512, 16384)
        self.memory_limit_spin.setSuffix(" MB")
        memory_limit_layout.addWidget(self.memory_limit_spin)
        memory_layout.addLayout(memory_limit_layout)
        
        self.auto_cleanup_cb = QCheckBox("自动内存清理")
        memory_layout.addWidget(self.auto_cleanup_cb)
        
        layout.addWidget(memory_group)
        
        layout.addStretch()
        return widget
    
    def _connect_signals(self):
        """连接信号"""
        # 按钮
        self.ok_btn.clicked.connect(self.accept)
        self.cancel_btn.clicked.connect(self.reject)
        self.apply_btn.clicked.connect(self._apply_settings)
        self.reset_btn.clicked.connect(self._reset_settings)
        
        # 浏览按钮
        self.browse_model_btn.clicked.connect(self._browse_model_file)
    
    def _load_settings(self):
        """加载设置"""
        # 检测设置
        detection = self.settings.get("detection", {})
        self.model_path_edit.setText(detection.get("yolo_model_path", ""))
        self.confidence_spin.setValue(detection.get("confidence_threshold", 0.5))
        self.nms_spin.setValue(detection.get("nms_threshold", 0.4))
        self.max_detections_spin.setValue(detection.get("max_detections", 100))
        
        # 融合设置
        fusion = self.settings.get("fusion", {})
        self.enable_fusion_cb.setChecked(fusion.get("enable", True))
        self.iou_spin.setValue(fusion.get("iou_threshold", 0.5))
        
        # 模板设置
        template = self.settings.get("template", {})
        self.template_threshold_spin.setValue(template.get("threshold", 0.8))
        scale_range = template.get("scale_range", [0.8, 1.2])
        self.scale_min_spin.setValue(scale_range[0])
        self.scale_max_spin.setValue(scale_range[1])
        self.angle_range_spin.setValue(template.get("angle_range", 15))
        self.max_templates_spin.setValue(template.get("max_templates", 50))
        
        # 界面设置
        gui = self.settings.get("gui", {})
        theme = gui.get("theme", "默认")
        if theme in ["默认", "深色", "浅色"]:
            self.theme_combo.setCurrentText(theme)
        
        language = gui.get("language", "zh_CN")
        self.language_combo.setCurrentText("中文" if language == "zh_CN" else "English")
        self.font_size_spin.setValue(gui.get("font_size", 10))
        self.auto_save_cb.setChecked(gui.get("auto_save", True))
        self.show_tooltips_cb.setChecked(gui.get("show_tooltips", True))
        self.confirm_exit_cb.setChecked(gui.get("confirm_exit", True))
        
        # 性能设置
        performance = self.settings.get("performance", {})
        self.max_workers_spin.setValue(performance.get("max_workers", 4))
        self.batch_size_spin.setValue(performance.get("batch_size", 1))
        self.cache_size_spin.setValue(performance.get("cache_size", 100))
        self.gpu_enabled_cb.setChecked(performance.get("gpu_enabled", True))
        self.gpu_device_combo.setCurrentText(performance.get("gpu_device", "自动"))
        self.memory_limit_spin.setValue(performance.get("memory_limit_mb", 2048))
        self.auto_cleanup_cb.setChecked(performance.get("auto_cleanup", True))
    
    def _save_settings(self) -> Dict[str, Any]:
        """保存设置"""
        settings = {
            "detection": {
                "yolo_model_path": self.model_path_edit.text(),
                "confidence_threshold": self.confidence_spin.value(),
                "nms_threshold": self.nms_spin.value(),
                "max_detections": self.max_detections_spin.value()
            },
            "fusion": {
                "enable": self.enable_fusion_cb.isChecked(),
                "iou_threshold": self.iou_spin.value()
            },
            "template": {
                "threshold": self.template_threshold_spin.value(),
                "scale_range": [self.scale_min_spin.value(), self.scale_max_spin.value()],
                "angle_range": self.angle_range_spin.value(),
                "max_templates": self.max_templates_spin.value()
            },
            "gui": {
                "theme": self.theme_combo.currentText(),
                "language": "zh_CN" if self.language_combo.currentText() == "中文" else "en_US",
                "font_size": self.font_size_spin.value(),
                "auto_save": self.auto_save_cb.isChecked(),
                "show_tooltips": self.show_tooltips_cb.isChecked(),
                "confirm_exit": self.confirm_exit_cb.isChecked()
            },
            "performance": {
                "max_workers": self.max_workers_spin.value(),
                "batch_size": self.batch_size_spin.value(),
                "cache_size": self.cache_size_spin.value(),
                "gpu_enabled": self.gpu_enabled_cb.isChecked(),
                "gpu_device": self.gpu_device_combo.currentText(),
                "memory_limit_mb": self.memory_limit_spin.value(),
                "auto_cleanup": self.auto_cleanup_cb.isChecked()
            }
        }
        
        return settings
    
    def _apply_settings(self):
        """应用设置"""
        self.settings = self._save_settings()
        self.settings_changed.emit(self.settings)
        
        QMessageBox.information(self, "设置", "设置已应用")
    
    def _reset_settings(self):
        """重置设置"""
        reply = QMessageBox.question(
            self,
            "重置设置",
            "确定要重置所有设置为默认值吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            # 重置为默认设置
            self.settings = {}
            self._load_settings()
    
    def _browse_model_file(self):
        """浏览模型文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择YOLO模型文件",
            "",
            "模型文件 (*.pt *.onnx *.engine)"
        )
        
        if file_path:
            self.model_path_edit.setText(file_path)
    
    def accept(self):
        """确定按钮"""
        self.settings = self._save_settings()
        self.settings_changed.emit(self.settings)
        super().accept()
    
    def get_settings(self) -> Dict[str, Any]:
        """获取设置"""
        return self.settings
