#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试检测结果错误修复
用于验证screenshot_widget.py中的AttributeError修复是否有效
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

from yolo_opencv_detector.utils.data_structures import DetectionResult, BoundingBox, DetectionSource
from yolo_opencv_detector.gui.widgets.screenshot_widget import ScreenshotWidget, ScreenshotLabel
from PyQt6.QtWidgets import QApplication
from PyQt6.QtGui import QPixmap
import numpy as np

def create_test_detection_result():
    """创建测试用的DetectionResult对象"""
    bbox = BoundingBox(x=100, y=100, width=200, height=150)
    return DetectionResult(
        bbox=bbox,
        confidence=0.85,
        class_id=0,
        class_name="person",
        source=DetectionSource.YOLO
    )

def create_test_dict_result():
    """创建测试用的字典格式结果"""
    return {
        "bbox": {"x": 150, "y": 120, "width": 180, "height": 140},
        "confidence": 0.92,
        "class_id": 1,
        "class_name": "car",
        "source": "yolo"
    }

def create_test_list_bbox_result():
    """创建测试用的列表格式bbox结果"""
    return {
        "bbox": [200, 80, 160, 120],  # [x, y, width, height]
        "confidence": 0.78,
        "class_id": 2,
        "class_name": "bicycle",
        "source": "template"
    }

def test_screenshot_widget():
    """测试ScreenshotLabel的检测结果处理"""
    print("开始测试ScreenshotLabel...")

    app = QApplication(sys.argv)

    try:
        # 创建测试组件 - 直接测试ScreenshotLabel，因为它包含修复的代码
        widget = ScreenshotLabel()

        # 创建一个简单的测试图像
        test_pixmap = QPixmap(800, 600)
        test_pixmap.fill()
        widget.set_screenshot(test_pixmap)
        
        # 测试1: DetectionResult对象
        print("\n测试1: DetectionResult对象")
        detection_result = create_test_detection_result()
        try:
            widget.set_detection_results([detection_result])
            print("✅ DetectionResult对象处理成功")
        except Exception as e:
            print(f"❌ DetectionResult对象处理失败: {e}")
        
        # 测试2: 字典格式结果
        print("\n测试2: 字典格式结果")
        dict_result = create_test_dict_result()
        try:
            widget.set_detection_results([dict_result])
            print("✅ 字典格式结果处理成功")
        except Exception as e:
            print(f"❌ 字典格式结果处理失败: {e}")
        
        # 测试3: 列表格式bbox结果
        print("\n测试3: 列表格式bbox结果")
        list_bbox_result = create_test_list_bbox_result()
        try:
            widget.set_detection_results([list_bbox_result])
            print("✅ 列表格式bbox结果处理成功")
        except Exception as e:
            print(f"❌ 列表格式bbox结果处理失败: {e}")
        
        # 测试4: 混合格式结果
        print("\n测试4: 混合格式结果")
        mixed_results = [
            detection_result,
            dict_result,
            list_bbox_result
        ]
        try:
            widget.set_detection_results(mixed_results)
            print("✅ 混合格式结果处理成功")
        except Exception as e:
            print(f"❌ 混合格式结果处理失败: {e}")
        
        # 测试5: 错误格式结果（应该被优雅处理）
        print("\n测试5: 错误格式结果")
        invalid_results = [
            "invalid_string",
            123,
            None,
            [],
            detection_result  # 这个应该成功
        ]
        try:
            widget.set_detection_results(invalid_results)
            print("✅ 错误格式结果被优雅处理")
        except Exception as e:
            print(f"❌ 错误格式结果处理失败: {e}")
        
        print("\n所有测试完成!")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        app.quit()

def test_data_conversion():
    """测试数据转换功能"""
    print("\n开始测试数据转换...")
    
    # 测试DetectionResult.to_dict()
    detection_result = create_test_detection_result()
    try:
        result_dict = detection_result.to_dict()
        print("✅ DetectionResult.to_dict() 转换成功")
        print(f"   转换结果: {result_dict}")
        
        # 验证转换后的格式
        assert "bbox" in result_dict
        assert "confidence" in result_dict
        assert "class_name" in result_dict
        assert "source" in result_dict
        print("✅ 转换后的字典格式验证通过")
        
    except Exception as e:
        print(f"❌ DetectionResult.to_dict() 转换失败: {e}")

if __name__ == "__main__":
    print("=" * 60)
    print("YOLO检测工具AttributeError错误修复测试")
    print("=" * 60)
    
    # 测试数据转换
    test_data_conversion()
    
    # 测试组件
    test_screenshot_widget()
    
    print("\n" + "=" * 60)
    print("测试完成!")
    print("=" * 60)
