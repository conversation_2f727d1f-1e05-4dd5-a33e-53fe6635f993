# -*- coding: utf-8 -*-
"""
重构的配置面板 - 简化布局避免重叠
作者: Cursor AI
创建时间: 2025-07-05
编码标准: UTF-8无BOM
"""

from typing import Dict, Any
from pathlib import Path
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, QTabWidget,
    QLabel, QLineEdit, QSpinBox, QDoubleSpinBox, QCheckBox,
    QComboBox, QSlider, QPushButton, QFileDialog, QMessageBox
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont

from ...utils.logger import Logger
from ...utils.config_manager import ConfigManager


class ConfigPanelV2(QWidget):
    """重构的配置面板类 - 简化布局"""
    
    # 信号定义
    config_changed = pyqtSignal(dict)
    
    def __init__(self, config_manager: ConfigManager):
        super().__init__()
        self.config_manager = config_manager
        self.logger = Logger()
        
        # 设置固定尺寸避免布局问题
        self.setMinimumWidth(260)
        self.setMaximumWidth(320)
        
        self._init_ui()
        self._load_config()
        
        self.logger.info("重构配置面板初始化完成")
    
    def _init_ui(self) -> None:
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("⚙️ 参数配置")
        title_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # YOLO配置组
        self._create_yolo_group(layout)
        
        # 模板匹配配置组
        self._create_template_group(layout)
        
        # 界面配置组
        self._create_ui_group(layout)
        
        # 控制按钮
        self._create_control_buttons(layout)
        
        # 添加弹性空间
        layout.addStretch()
    
    def _create_yolo_group(self, parent_layout: QVBoxLayout) -> None:
        """创建YOLO配置组"""
        group = QGroupBox("🎯 YOLO配置")
        group.setMaximumHeight(250)  # 增加高度以容纳新的模型选择组件
        layout = QVBoxLayout(group)
        layout.setSpacing(8)
        
        # 模型选择
        model_layout = QVBoxLayout()

        # 预设模型选择
        preset_layout = QHBoxLayout()
        preset_label = QLabel("预设模型:")
        preset_label.setToolTip("选择预设的YOLO模型\n"
                               "包含不同规模和用途的模型\n"
                               "根据硬件条件和精度需求选择")
        preset_layout.addWidget(preset_label)

        self.model_preset_combo = QComboBox()
        self.model_preset_combo.setToolTip("🎯 界面检测模型选择指南:\n\n"
                                          "📋 常规办公软件 (Word/Excel/PPT):\n"
                                          "   推荐: YOLOv8n - 轻量快速，CPU友好\n\n"
                                          "🎨 复杂界面应用 (IDE/设计软件):\n"
                                          "   推荐: YOLOv8s - 平衡型，适应性强\n\n"
                                          "🔬 专业软件 (CAD/医疗/金融):\n"
                                          "   推荐: YOLOv8m/l - 高精度检测\n\n"
                                          "✂️ 复杂布局分割 (游戏/自定义UI):\n"
                                          "   推荐: YOLOv8n-seg - 像素级精确分割\n\n"
                                          "💡 默认推荐YOLOv8n，适合大多数场景")
        self._setup_model_presets()
        self.model_preset_combo.currentTextChanged.connect(self._on_preset_model_changed)
        preset_layout.addWidget(self.model_preset_combo)
        model_layout.addLayout(preset_layout)

        # 自定义模型路径
        custom_layout = QHBoxLayout()
        custom_label = QLabel("自定义:")
        custom_label.setToolTip("使用自定义的YOLO模型文件\n"
                               "支持.pt、.onnx、.engine格式\n"
                               "可以使用自己训练的模型")
        custom_layout.addWidget(custom_label)

        self.model_path_edit = QLineEdit()
        self.model_path_edit.setPlaceholderText("或选择自定义模型文件...")
        self.model_path_edit.setToolTip("输入自定义模型文件路径\n"
                                       "支持.pt、.onnx、.engine格式\n"
                                       "留空则使用上方选择的预设模型")
        custom_layout.addWidget(self.model_path_edit)

        browse_button = QPushButton("浏览")
        browse_button.setMaximumWidth(60)
        browse_button.setToolTip("打开文件选择对话框\n"
                                "选择自定义YOLO模型文件")
        browse_button.clicked.connect(self._browse_model)
        custom_layout.addWidget(browse_button)
        model_layout.addLayout(custom_layout)

        layout.addLayout(model_layout)

        # 模型信息显示
        self.model_info_label = QLabel("选择模型查看详细信息")
        self.model_info_label.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 8px;
                font-size: 11px;
                color: #6c757d;
            }
        """)
        self.model_info_label.setWordWrap(True)
        self.model_info_label.setMaximumHeight(60)
        layout.addWidget(self.model_info_label)

        # 置信度阈值
        conf_layout = QHBoxLayout()
        conf_label = QLabel("置信度:")
        conf_label.setToolTip("检测结果的可信程度，范围0.01-1.00\n"
                             "值越高越严格，只保留高质量检测\n"
                             "推荐值：0.3-0.7，默认0.5")
        conf_layout.addWidget(conf_label)

        self.yolo_confidence_slider = QSlider(Qt.Orientation.Horizontal)
        self.yolo_confidence_slider.setRange(1, 100)
        self.yolo_confidence_slider.setValue(50)
        self.yolo_confidence_slider.setToolTip("拖动调整置信度阈值\n"
                                              "左侧：检测更宽松，可能有误检\n"
                                              "右侧：检测更严格，可能漏检")
        self.yolo_confidence_slider.valueChanged.connect(self._update_yolo_confidence_label)
        conf_layout.addWidget(self.yolo_confidence_slider)

        self.yolo_confidence_label = QLabel("0.50")
        self.yolo_confidence_label.setMinimumWidth(40)
        self.yolo_confidence_label.setToolTip("当前置信度阈值\n"
                                             "低于此值的检测结果将被过滤")
        conf_layout.addWidget(self.yolo_confidence_label)
        layout.addLayout(conf_layout)
        
        # NMS阈值
        nms_layout = QHBoxLayout()
        nms_label = QLabel("NMS:")
        nms_label.setToolTip("非极大值抑制阈值，范围0.01-1.00\n"
                            "用于去除重复检测框\n"
                            "值越小去重越严格，推荐0.4-0.6")
        nms_layout.addWidget(nms_label)

        self.yolo_nms_slider = QSlider(Qt.Orientation.Horizontal)
        self.yolo_nms_slider.setRange(1, 100)
        self.yolo_nms_slider.setValue(50)
        self.yolo_nms_slider.setToolTip("调整重复检测框的去除程度\n"
                                       "左侧：去重更严格，可能合并相近目标\n"
                                       "右侧：去重更宽松，可能保留重复框")
        self.yolo_nms_slider.valueChanged.connect(self._update_yolo_nms_label)
        nms_layout.addWidget(self.yolo_nms_slider)

        self.yolo_nms_label = QLabel("0.50")
        self.yolo_nms_label.setMinimumWidth(40)
        self.yolo_nms_label.setToolTip("当前NMS阈值\n"
                                      "控制重复检测框的过滤强度")
        nms_layout.addWidget(self.yolo_nms_label)
        layout.addLayout(nms_layout)

        # 最大检测数
        max_det_layout = QHBoxLayout()
        max_det_label = QLabel("最大检测数:")
        max_det_label.setToolTip("单次检测的最大目标数量\n"
                                "范围1-1000，默认100\n"
                                "过高会影响性能，过低可能漏检")
        max_det_layout.addWidget(max_det_label)

        self.max_detections_spinbox = QSpinBox()
        self.max_detections_spinbox.setRange(1, 1000)
        self.max_detections_spinbox.setValue(100)
        self.max_detections_spinbox.setToolTip("限制每次检测的最大目标数\n"
                                              "一般场景100个足够\n"
                                              "密集场景可适当增加")
        max_det_layout.addWidget(self.max_detections_spinbox)
        layout.addLayout(max_det_layout)
        
        parent_layout.addWidget(group)
    
    def _create_template_group(self, parent_layout: QVBoxLayout) -> None:
        """创建模板匹配配置组"""
        group = QGroupBox("🔍 模板匹配")
        group.setMaximumHeight(120)  # 固定高度
        layout = QVBoxLayout(group)
        layout.setSpacing(8)
        
        # 启用模板匹配
        self.enable_template_checkbox = QCheckBox("启用模板匹配")
        self.enable_template_checkbox.setChecked(True)
        self.enable_template_checkbox.setToolTip("开启后使用模板匹配进行检测\n"
                                                 "需要先在模板面板创建模板\n"
                                                 "适合检测特定的界面元素")
        layout.addWidget(self.enable_template_checkbox)

        # 匹配阈值
        threshold_layout = QHBoxLayout()
        threshold_label = QLabel("匹配阈值:")
        threshold_label.setToolTip("模板匹配的相似度阈值，范围0.01-1.00\n"
                                  "值越高要求越严格\n"
                                  "推荐值：0.7-0.9，默认0.8")
        threshold_layout.addWidget(threshold_label)

        self.template_threshold_slider = QSlider(Qt.Orientation.Horizontal)
        self.template_threshold_slider.setRange(1, 100)
        self.template_threshold_slider.setValue(80)
        self.template_threshold_slider.setToolTip("调整模板匹配的严格程度\n"
                                                 "左侧：匹配更宽松，可能误检\n"
                                                 "右侧：匹配更严格，可能漏检")
        self.template_threshold_slider.valueChanged.connect(self._update_template_threshold_label)
        threshold_layout.addWidget(self.template_threshold_slider)

        self.template_threshold_label = QLabel("0.80")
        self.template_threshold_label.setMinimumWidth(40)
        self.template_threshold_label.setToolTip("当前匹配阈值\n"
                                                "低于此值的匹配将被忽略")
        threshold_layout.addWidget(self.template_threshold_label)
        layout.addLayout(threshold_layout)

        # 匹配方法
        method_layout = QHBoxLayout()
        method_label = QLabel("匹配方法:")
        method_label.setToolTip("模板匹配算法类型\n"
                               "CCOEFF_NORMED：推荐，效果最好\n"
                               "CCORR_NORMED：速度快，精度中等\n"
                               "SQDIFF_NORMED：特殊场景使用")
        method_layout.addWidget(method_label)

        self.template_method_combo = QComboBox()
        self.template_method_combo.addItems([
            "TM_CCOEFF_NORMED",
            "TM_CCORR_NORMED",
            "TM_SQDIFF_NORMED"
        ])
        self.template_method_combo.setToolTip("选择模板匹配算法\n"
                                             "TM_CCOEFF_NORMED：最常用，推荐\n"
                                             "TM_CCORR_NORMED：计算快速\n"
                                             "TM_SQDIFF_NORMED：差值匹配")
        method_layout.addWidget(self.template_method_combo)
        layout.addLayout(method_layout)
        
        parent_layout.addWidget(group)
    
    def _create_ui_group(self, parent_layout: QVBoxLayout) -> None:
        """创建界面配置组"""
        group = QGroupBox("🎨 界面设置")
        group.setMaximumHeight(100)  # 固定高度
        layout = QVBoxLayout(group)
        layout.setSpacing(8)
        
        # 主题选择
        theme_layout = QHBoxLayout()
        theme_label = QLabel("主题:")
        theme_label.setToolTip("选择界面主题风格\n"
                              "默认：标准主题\n"
                              "深色：护眼暗色主题\n"
                              "浅色：明亮清爽主题")
        theme_layout.addWidget(theme_label)

        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["默认", "深色", "浅色"])
        self.theme_combo.setToolTip("切换界面主题\n"
                                   "重启应用后生效\n"
                                   "深色主题适合长时间使用")
        theme_layout.addWidget(self.theme_combo)
        layout.addLayout(theme_layout)

        # 显示设置
        display_layout = QHBoxLayout()
        self.show_fps_checkbox = QCheckBox("显示FPS")
        self.show_fps_checkbox.setChecked(True)
        self.show_fps_checkbox.setToolTip("在界面上显示检测帧率\n"
                                         "用于监控检测性能\n"
                                         "关闭可节省少量资源")
        display_layout.addWidget(self.show_fps_checkbox)

        self.show_confidence_checkbox = QCheckBox("显示置信度")
        self.show_confidence_checkbox.setChecked(True)
        self.show_confidence_checkbox.setToolTip("在检测框上显示置信度数值\n"
                                                "帮助判断检测结果的可靠性\n"
                                                "关闭可简化显示效果")
        display_layout.addWidget(self.show_confidence_checkbox)
        layout.addLayout(display_layout)
        
        parent_layout.addWidget(group)
    
    def _create_control_buttons(self, parent_layout: QVBoxLayout) -> None:
        """创建控制按钮"""
        button_layout = QHBoxLayout()
        
        # 重置按钮
        reset_button = QPushButton("🔄 重置")
        reset_button.setMinimumHeight(35)
        reset_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        reset_button.clicked.connect(self._reset_config)
        button_layout.addWidget(reset_button)
        
        # 应用按钮
        apply_button = QPushButton("✅ 应用")
        apply_button.setMinimumHeight(35)
        apply_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
        """)
        apply_button.clicked.connect(self._apply_config)
        button_layout.addWidget(apply_button)
        
        parent_layout.addLayout(button_layout)
    
    def _load_config(self) -> None:
        """加载配置"""
        try:
            # 使用默认配置
            yolo_config = {
                'model_path': '',
                'confidence_threshold': 0.5,
                'nms_threshold': 0.5,
                'max_detections': 100
            }
            self.model_path_edit.setText(yolo_config.get('model_path', ''))
            self.yolo_confidence_slider.setValue(int(yolo_config.get('confidence_threshold', 0.5) * 100))
            self.yolo_nms_slider.setValue(int(yolo_config.get('nms_threshold', 0.5) * 100))
            self.max_detections_spinbox.setValue(yolo_config.get('max_detections', 100))

            # 模板配置
            template_config = {
                'enabled': True,
                'threshold': 0.8,
                'method': 'TM_CCOEFF_NORMED'
            }
            self.enable_template_checkbox.setChecked(template_config.get('enabled', True))
            self.template_threshold_slider.setValue(int(template_config.get('threshold', 0.8) * 100))

            method = template_config.get('method', 'TM_CCOEFF_NORMED')
            index = self.template_method_combo.findText(method)
            if index >= 0:
                self.template_method_combo.setCurrentIndex(index)

            # 界面配置
            ui_config = {
                'theme': '默认',
                'show_fps': True,
                'show_confidence': True
            }
            theme = ui_config.get('theme', '默认')
            theme_index = self.theme_combo.findText(theme)
            if theme_index >= 0:
                self.theme_combo.setCurrentIndex(theme_index)

            self.show_fps_checkbox.setChecked(ui_config.get('show_fps', True))
            self.show_confidence_checkbox.setChecked(ui_config.get('show_confidence', True))

            # 更新标签
            self._update_yolo_confidence_label()
            self._update_yolo_nms_label()
            self._update_template_threshold_label()

        except Exception as e:
            self.logger.error(f"加载配置失败: {e}")
    
    def _update_yolo_confidence_label(self) -> None:
        """更新YOLO置信度标签"""
        value = self.yolo_confidence_slider.value() / 100.0
        self.yolo_confidence_label.setText(f"{value:.2f}")
    
    def _update_yolo_nms_label(self) -> None:
        """更新YOLO NMS标签"""
        value = self.yolo_nms_slider.value() / 100.0
        self.yolo_nms_label.setText(f"{value:.2f}")
    
    def _update_template_threshold_label(self) -> None:
        """更新模板阈值标签"""
        value = self.template_threshold_slider.value() / 100.0
        self.template_threshold_label.setText(f"{value:.2f}")
    
    def _browse_model(self) -> None:
        """浏览模型文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择YOLO模型文件", "", 
            "模型文件 (*.pt *.onnx *.engine);;所有文件 (*)"
        )
        if file_path:
            self.model_path_edit.setText(file_path)
    
    def _reset_config(self) -> None:
        """重置配置"""
        reply = QMessageBox.question(
            self, "确认重置", "确定要重置所有配置为默认值吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            try:
                # 重置为默认值
                self.model_path_edit.setText("")
                self.yolo_confidence_slider.setValue(50)
                self.yolo_nms_slider.setValue(50)
                self.max_detections_spinbox.setValue(100)

                self.enable_template_checkbox.setChecked(True)
                self.template_threshold_slider.setValue(80)
                self.template_method_combo.setCurrentIndex(0)

                self.theme_combo.setCurrentIndex(0)
                self.show_fps_checkbox.setChecked(True)
                self.show_confidence_checkbox.setChecked(True)

                # 更新标签
                self._update_yolo_confidence_label()
                self._update_yolo_nms_label()
                self._update_template_threshold_label()

                self.logger.info("配置已重置为默认值")
                QMessageBox.information(self, "成功", "配置已重置为默认值")

            except Exception as e:
                QMessageBox.warning(self, "错误", f"重置配置失败: {e}")
                self.logger.error(f"重置配置失败: {e}")
    
    def _apply_config(self) -> None:
        """应用配置"""
        try:
            # 收集当前配置
            config = self._get_current_config()

            # 验证配置
            if not self._validate_config(config):
                return

            # 发送配置变更信号
            self.config_changed.emit(config)

            self.logger.info("配置已应用")
            QMessageBox.information(self, "成功", "配置已应用并生效")

        except Exception as e:
            QMessageBox.warning(self, "错误", f"应用配置失败: {e}")
            self.logger.error(f"应用配置失败: {e}")

    def _validate_config(self, config: Dict[str, Any]) -> bool:
        """验证配置"""
        try:
            # 验证YOLO模型路径
            model_path = config['yolo']['model_path']
            if model_path and not Path(model_path).exists():
                reply = QMessageBox.question(
                    self, "模型文件不存在",
                    f"YOLO模型文件不存在: {model_path}\n是否继续应用配置？",
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
                )
                if reply == QMessageBox.StandardButton.No:
                    return False

            # 验证阈值范围
            if not (0.0 <= config['yolo']['confidence_threshold'] <= 1.0):
                QMessageBox.warning(self, "配置错误", "YOLO置信度阈值必须在0.0-1.0之间")
                return False

            if not (0.0 <= config['yolo']['nms_threshold'] <= 1.0):
                QMessageBox.warning(self, "配置错误", "YOLO NMS阈值必须在0.0-1.0之间")
                return False

            if not (0.0 <= config['template']['threshold'] <= 1.0):
                QMessageBox.warning(self, "配置错误", "模板匹配阈值必须在0.0-1.0之间")
                return False

            return True

        except Exception as e:
            QMessageBox.warning(self, "验证失败", f"配置验证失败: {e}")
            return False
    
    def _get_current_config(self) -> Dict[str, Any]:
        """获取当前配置"""
        return {
            'yolo': {
                'model_path': self._get_effective_model_path(),
                'confidence_threshold': self.yolo_confidence_slider.value() / 100.0,
                'nms_threshold': self.yolo_nms_slider.value() / 100.0,
                'max_detections': self.max_detections_spinbox.value()
            },
            'template': {
                'enabled': self.enable_template_checkbox.isChecked(),
                'threshold': self.template_threshold_slider.value() / 100.0,
                'method': self.template_method_combo.currentText()
            },
            'ui': {
                'theme': self.theme_combo.currentText(),
                'show_fps': self.show_fps_checkbox.isChecked(),
                'show_confidence': self.show_confidence_checkbox.isChecked()
            }
        }

    def _setup_model_presets(self) -> None:
        """设置模型预设选项"""
        # 定义YOLO模型预设
        self.model_presets = {
            "请选择模型": {
                "path": "",
                "size": "",
                "speed": "",
                "accuracy": "",
                "hardware": "",
                "memory": "",
                "use_case": "",
                "description": "请从下方选择合适的YOLO模型"
            },
            "YOLOv8n (6.2MB) - 轻量级": {
                "path": "models/yolov8n.pt",
                "size": "6.2MB",
                "speed": "极快 (~50ms)",
                "accuracy": "中等 (85%)",
                "hardware": "CPU推荐",
                "memory": "~200MB",
                "use_case": "常规办公软件",
                "description": "🎯 最适合界面自动化的轻量级模型。检测速度极快，CPU友好，适合Word/Excel/PPT等常规办公软件的按钮、菜单、对话框检测。推荐作为默认选择。"
            },
            "YOLOv8s (21.5MB) - 平衡型": {
                "path": "models/yolov8s.pt",
                "size": "21.5MB",
                "speed": "快 (~80ms)",
                "accuracy": "良好 (88%)",
                "hardware": "CPU/GPU均可",
                "memory": "~350MB",
                "use_case": "复杂界面应用",
                "description": "⚖️ 速度与精度的最佳平衡。适合复杂界面的检测，如IDE、设计软件、多窗口应用。在保持较快速度的同时提供更高的检测准确率。"
            },
            "YOLOv8m (49.7MB) - 中等规模": {
                "path": "models/yolov8m.pt",
                "size": "49.7MB",
                "speed": "中等 (~120ms)",
                "accuracy": "高 (91%)",
                "hardware": "GPU推荐",
                "memory": "~500MB",
                "use_case": "专业软件",
                "description": "🎨 适合专业软件和复杂界面。推荐用于Photoshop、CAD、视频编辑等专业软件的界面检测，能够准确识别复杂布局中的小目标。"
            },
            "YOLOv8l (83.7MB) - 大型模型": {
                "path": "models/yolov8l.pt",
                "size": "83.7MB",
                "speed": "较慢 (~180ms)",
                "accuracy": "很高 (93%)",
                "hardware": "GPU必需",
                "memory": "~700MB",
                "use_case": "高精度需求",
                "description": "🔬 高精度检测模型。适合对检测准确率要求极高的场景，如医疗软件、金融系统等关键应用的界面自动化。"
            },
            "YOLOv8x (136.7MB) - 超大模型": {
                "path": "models/yolov8x.pt",
                "size": "136.7MB",
                "speed": "慢 (~250ms)",
                "accuracy": "极高 (95%)",
                "hardware": "高性能GPU",
                "memory": "~1GB",
                "use_case": "研究和极限精度",
                "description": "🚀 最高精度的通用检测模型。适合研究用途或对检测精度要求极限的应用场景。需要高性能GPU支持。"
            },
            "YOLOv8n-seg (6.7MB) - 实例分割": {
                "path": "models/yolov8n-seg.pt",
                "size": "6.7MB",
                "speed": "快 (~70ms)",
                "accuracy": "中等 (像素级)",
                "hardware": "CPU/GPU均可",
                "memory": "~250MB",
                "use_case": "复杂布局分割",
                "description": "✂️ 像素级精确分割模型。专门用于复杂界面布局、重叠窗口、不规则UI元素的精确识别。能提供比边界框更准确的轮廓信息，适合游戏界面、自定义控件等特殊场景。"
            }
        }

        # 添加到下拉框
        for model_name in self.model_presets.keys():
            self.model_preset_combo.addItem(model_name)

        # 设置默认选择为YOLOv8n（推荐的轻量级模型）
        self.model_preset_combo.setCurrentText("YOLOv8n (6.2MB) - 轻量级")

    def _on_preset_model_changed(self, model_name: str) -> None:
        """处理预设模型选择变化"""
        try:
            if model_name in self.model_presets:
                model_info = self.model_presets[model_name]

                # 更新模型信息显示
                if model_name == "请选择模型":
                    info_text = model_info["description"]
                else:
                    info_text = (
                        f"📊 {model_name}\n"
                        f"📏 大小: {model_info['size']} | "
                        f"⚡ 速度: {model_info['speed']} | "
                        f"🎯 精度: {model_info['accuracy']}\n"
                        f"💻 硬件: {model_info['hardware']} | "
                        f"🧠 内存: {model_info['memory']} | "
                        f"🎨 场景: {model_info['use_case']}\n"
                        f"💡 {model_info['description']}"
                    )

                self.model_info_label.setText(info_text)

                # 如果选择了具体模型，清空自定义路径
                if model_name != "请选择模型":
                    self.model_path_edit.clear()
                    self.logger.info(f"选择预设模型: {model_name}")

        except Exception as e:
            self.logger.error(f"处理模型选择变化失败: {e}")



    def _get_effective_model_path(self) -> str:
        """获取有效的模型路径"""
        # 优先使用自定义路径
        custom_path = self.model_path_edit.text().strip()
        if custom_path:
            return custom_path

        # 使用预设模型路径
        current_model = self.model_preset_combo.currentText()
        if current_model != "请选择模型" and current_model in self.model_presets:
            return self.model_presets[current_model]["path"]

        return ""
