# -*- coding: utf-8 -*-
"""
发布前最终检查脚本
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

import sys
import subprocess
from pathlib import Path

def main():
    """最终检查"""
    print("🔍 发布前最终检查")
    print("=" * 40)
    
    checks = [
        "python scripts/verify_environment.py",
        "python scripts/quick_test.py", 
        "python scripts/mvp_test.py"
    ]
    
    all_passed = True
    
    for check in checks:
        print(f"\n运行: {check}")
        result = subprocess.run(check.split(), capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 通过")
        else:
            print("❌ 失败")
            print(result.stdout)
            print(result.stderr)
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有检查通过，可以发布！")
        return 0
    else:
        print("\n⚠️  存在问题，请修复后重试")
        return 1

if __name__ == "__main__":
    sys.exit(main())
