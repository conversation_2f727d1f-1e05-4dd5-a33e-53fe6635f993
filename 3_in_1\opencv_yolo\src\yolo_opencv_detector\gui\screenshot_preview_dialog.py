# -*- coding: utf-8 -*-
"""
增强的截图预览对话框 - 支持预览、区域选择和模板创建
作者: Cursor AI
创建时间: 2025-07-05
编码标准: UTF-8无BOM
"""

import time
from typing import Optional, Dict, Any, Tuple
import numpy as np
from pathlib import Path

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QGroupBox, QMessageBox, QScrollArea, QFrame, QSplitter,
    QTextEdit, QLineEdit, QComboBox, QSpinBox, QWidget
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer, QRect, QPoint
from PyQt6.QtGui import QPixmap, QFont, QPainter, QPen, QColor, QCursor

from ..utils.logger import Logger
from .widgets.interactive_image_viewer import InteractiveImageViewerWidget


class RegionSelectionWidget(QLabel):
    """区域选择控件 - 支持在截图上选择矩形区域"""
    
    region_selected = pyqtSignal(tuple)  # (x, y, width, height)
    selection_changed = pyqtSignal(tuple)  # 实时选择变化
    
    def __init__(self):
        super().__init__()
        self.logger = Logger()
        
        # 设置基本属性
        self.setMinimumSize(600, 400)
        self.setStyleSheet("""
            QLabel {
                border: 2px solid #3498db;
                background-color: #f8f9fa;
                border-radius: 8px;
            }
        """)
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.setScaledContents(False)
        
        # 选择状态
        self.selecting = False
        self.start_point = None
        self.end_point = None
        self.current_rect = QRect()
        
        # 图像数据
        self.original_pixmap = None
        self.scale_factor = 1.0
        
        # 设置光标
        self.setCursor(QCursor(Qt.CursorShape.CrossCursor))
        
        # 显示提示
        self.setText("📸 加载截图后可进行区域选择")
        font = QFont()
        font.setPointSize(14)
        font.setBold(True)
        self.setFont(font)
    
    def set_screenshot(self, pixmap: QPixmap):
        """设置截图"""
        try:
            if pixmap and not pixmap.isNull():
                self.original_pixmap = pixmap
                self._update_display()
                self.setText("")  # 清除提示文字
                self.logger.info(f"区域选择控件已设置截图: {pixmap.size()}")
            else:
                self.original_pixmap = None
                self.setText("❌ 无效的截图")
                self.logger.warning("设置了无效的截图")
        except Exception as e:
            self.logger.error(f"设置截图失败: {e}")
            self.setText("❌ 设置失败")
    
    def _update_display(self):
        """更新显示"""
        if not self.original_pixmap:
            return
        
        try:
            # 计算缩放比例
            widget_size = self.size()
            pixmap_size = self.original_pixmap.size()
            
            scale_x = widget_size.width() / pixmap_size.width()
            scale_y = widget_size.height() / pixmap_size.height()
            self.scale_factor = min(scale_x, scale_y, 1.0)  # 不放大
            
            # 缩放图像
            scaled_size = pixmap_size * self.scale_factor
            scaled_pixmap = self.original_pixmap.scaled(
                scaled_size, 
                Qt.AspectRatioMode.KeepAspectRatio, 
                Qt.TransformationMode.SmoothTransformation
            )
            
            # 如果有选择区域，绘制选择框
            if self.start_point and self.end_point:
                scaled_pixmap = scaled_pixmap.copy()  # 创建副本以便绘制
                painter = QPainter(scaled_pixmap)
                
                # 计算选择框在缩放图像上的坐标
                x1 = int(self.start_point.x() * self.scale_factor)
                y1 = int(self.start_point.y() * self.scale_factor)
                x2 = int(self.end_point.x() * self.scale_factor)
                y2 = int(self.end_point.y() * self.scale_factor)
                
                # 绘制选择框
                pen = QPen(QColor(255, 0, 0), 2)
                painter.setPen(pen)
                painter.drawRect(min(x1, x2), min(y1, y2), abs(x2 - x1), abs(y2 - y1))
                
                # 绘制尺寸信息
                width = abs(self.end_point.x() - self.start_point.x())
                height = abs(self.end_point.y() - self.start_point.y())
                size_text = f"{width} × {height}"
                
                painter.setPen(QPen(QColor(255, 255, 255), 1))
                painter.drawText(min(x1, x2) + 5, min(y1, y2) - 5, size_text)
                
                painter.end()
            
            self.setPixmap(scaled_pixmap)
            
        except Exception as e:
            self.logger.error(f"更新显示失败: {e}")
    
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if not self.original_pixmap or event.button() != Qt.MouseButton.LeftButton:
            return
        
        # 转换坐标到原始图像坐标系
        pos = event.position()
        x = int(pos.x() / self.scale_factor)
        y = int(pos.y() / self.scale_factor)
        
        # 限制在图像范围内
        pixmap_size = self.original_pixmap.size()
        x = max(0, min(x, pixmap_size.width() - 1))
        y = max(0, min(y, pixmap_size.height() - 1))
        
        self.start_point = QPoint(x, y)
        self.end_point = self.start_point
        self.selecting = True
        
        self._update_display()
    
    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if not self.selecting or not self.original_pixmap:
            return
        
        # 转换坐标到原始图像坐标系
        pos = event.position()
        x = int(pos.x() / self.scale_factor)
        y = int(pos.y() / self.scale_factor)
        
        # 限制在图像范围内
        pixmap_size = self.original_pixmap.size()
        x = max(0, min(x, pixmap_size.width() - 1))
        y = max(0, min(y, pixmap_size.height() - 1))
        
        self.end_point = QPoint(x, y)
        
        # 发送实时选择变化信号
        if self.start_point:
            x1, y1 = self.start_point.x(), self.start_point.y()
            x2, y2 = self.end_point.x(), self.end_point.y()
            width = abs(x2 - x1)
            height = abs(y2 - y1)
            self.selection_changed.emit((min(x1, x2), min(y1, y2), width, height))
        
        self._update_display()
    
    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if not self.selecting or event.button() != Qt.MouseButton.LeftButton:
            return
        
        self.selecting = False
        
        if self.start_point and self.end_point:
            # 计算选择区域
            x1, y1 = self.start_point.x(), self.start_point.y()
            x2, y2 = self.end_point.x(), self.end_point.y()
            
            x = min(x1, x2)
            y = min(y1, y2)
            width = abs(x2 - x1)
            height = abs(y2 - y1)
            
            # 检查选择区域是否有效
            if width >= 10 and height >= 10:  # 最小尺寸限制
                self.region_selected.emit((x, y, width, height))
                self.logger.info(f"区域选择完成: ({x}, {y}, {width}, {height})")
            else:
                # 区域太小，清除选择
                self.start_point = None
                self.end_point = None
                self._update_display()
                self.logger.warning("选择区域太小，已清除")
    
    def clear_selection(self):
        """清除选择"""
        self.start_point = None
        self.end_point = None
        self.selecting = False
        self._update_display()
    
    def get_selected_region(self) -> Optional[Tuple[int, int, int, int]]:
        """获取当前选择的区域"""
        if self.start_point and self.end_point:
            x1, y1 = self.start_point.x(), self.start_point.y()
            x2, y2 = self.end_point.x(), self.end_point.y()
            
            x = min(x1, x2)
            y = min(y1, y2)
            width = abs(x2 - x1)
            height = abs(y2 - y1)
            
            if width >= 10 and height >= 10:
                return (x, y, width, height)
        
        return None


class ScreenshotPreviewDialog(QDialog):
    """增强的截图预览对话框"""
    
    # 信号定义
    screenshot_saved = pyqtSignal(str)  # 截图保存信号
    template_created = pyqtSignal(dict)  # 模板创建信号
    region_extracted = pyqtSignal(object, tuple)  # 区域提取信号 (image, region)
    
    def __init__(self, screenshot_image: np.ndarray, filepath: str = None, parent=None):
        super().__init__(parent)
        self.logger = Logger()
        
        # 数据
        self.screenshot_image = screenshot_image
        self.screenshot_filepath = filepath
        self.selected_region = None
        self.region_image = None
        
        # 转换为QPixmap
        self.screenshot_pixmap = self._numpy_to_pixmap(screenshot_image)
        
        self._init_ui()
        self._init_connections()
        
        # 设置截图
        if self.screenshot_pixmap:
            self.image_viewer.set_image(self.screenshot_pixmap)
        
        self.logger.info("截图预览对话框初始化完成")
    
    def _numpy_to_pixmap(self, image: np.ndarray) -> Optional[QPixmap]:
        """将numpy数组转换为QPixmap"""
        try:
            if image is None or image.size == 0:
                return None
            
            import cv2
            from PyQt6.QtGui import QImage
            
            # 转换BGR到RGB
            if len(image.shape) == 3:
                rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                height, width, channel = rgb_image.shape
                bytes_per_line = 3 * width
                q_image = QImage(rgb_image.data, width, height, bytes_per_line, QImage.Format.Format_RGB888)
            else:
                # 灰度图像
                height, width = image.shape
                bytes_per_line = width
                q_image = QImage(image.data, width, height, bytes_per_line, QImage.Format.Format_Grayscale8)
            
            return QPixmap.fromImage(q_image)
            
        except Exception as e:
            self.logger.error(f"图像转换失败: {e}")
            return None

    def _init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("📷 截图预览与区域选择")
        self.setModal(True)
        self.resize(1000, 700)

        # 主布局
        main_layout = QHBoxLayout(self)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(15, 15, 15, 15)

        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)

        # 左侧：截图预览和区域选择
        left_widget = self._create_preview_widget()
        splitter.addWidget(left_widget)

        # 右侧：操作面板
        right_widget = self._create_control_widget()
        splitter.addWidget(right_widget)

        # 设置分割器比例
        splitter.setStretchFactor(0, 3)  # 左侧占3/4
        splitter.setStretchFactor(1, 1)  # 右侧占1/4

        main_layout.addWidget(splitter)

    def _create_preview_widget(self) -> QWidget:
        """创建预览控件"""
        widget = QFrame()
        widget.setFrameStyle(QFrame.Shape.StyledPanel)
        layout = QVBoxLayout(widget)
        layout.setSpacing(10)
        layout.setContentsMargins(10, 10, 10, 10)

        # 标题
        title_label = QLabel("🖼️ 截图预览与区域选择")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                padding: 8px;
                background-color: #ecf0f1;
                border-radius: 6px;
                text-align: center;
                qproperty-alignment: AlignCenter;
            }
        """)
        layout.addWidget(title_label)

        # 交互式图像查看器
        self.image_viewer = InteractiveImageViewerWidget()
        layout.addWidget(self.image_viewer)

        # 保持向后兼容性的引用
        self.region_selector = self.image_viewer

        return widget

    def _create_control_widget(self) -> QWidget:
        """创建控制面板"""
        widget = QFrame()
        widget.setFrameStyle(QFrame.Shape.StyledPanel)
        widget.setMaximumWidth(300)
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        layout.setContentsMargins(15, 15, 15, 15)

        # 截图信息组
        self._create_info_group(layout)

        # 区域选择组
        self._create_region_group(layout)

        # 模板创建组
        self._create_template_group(layout)

        # 操作按钮组
        self._create_action_group(layout)

        # 添加弹性空间
        layout.addStretch()

        return widget

    def _create_info_group(self, parent_layout):
        """创建信息组"""
        group = QGroupBox("ℹ️ 截图信息")
        layout = QVBoxLayout(group)
        layout.setSpacing(8)

        # 尺寸信息
        if self.screenshot_pixmap:
            size = self.screenshot_pixmap.size()
            size_text = f"📐 尺寸: {size.width()}×{size.height()}"
        else:
            size_text = "📐 尺寸: 未知"

        self.size_label = QLabel(size_text)
        self.size_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        layout.addWidget(self.size_label)

        # 文件路径
        if self.screenshot_filepath:
            filepath_text = f"📁 文件: {Path(self.screenshot_filepath).name}"
        else:
            filepath_text = "📁 文件: 未保存"

        self.filepath_label = QLabel(filepath_text)
        self.filepath_label.setStyleSheet("color: #7f8c8d; font-size: 11px;")
        self.filepath_label.setWordWrap(True)
        layout.addWidget(self.filepath_label)

        parent_layout.addWidget(group)

    def _create_region_group(self, parent_layout):
        """创建区域选择组"""
        group = QGroupBox("🎯 区域选择")
        layout = QVBoxLayout(group)
        layout.setSpacing(8)

        # 区域信息
        self.region_label = QLabel("📍 未选择区域")
        self.region_label.setStyleSheet("""
            QLabel {
                padding: 8px;
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                color: #495057;
            }
        """)
        layout.addWidget(self.region_label)

        # 清除选择按钮
        self.clear_button = QPushButton("🗑️ 清除选择")
        self.clear_button.setEnabled(False)
        self.clear_button.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
            QPushButton:disabled {
                background-color: #e9ecef;
                color: #6c757d;
            }
        """)
        layout.addWidget(self.clear_button)

        parent_layout.addWidget(group)

    def _create_template_group(self, parent_layout):
        """创建模板创建组"""
        group = QGroupBox("🏷️ 创建模板")
        layout = QVBoxLayout(group)
        layout.setSpacing(8)

        # 模板名称
        name_layout = QHBoxLayout()
        name_layout.addWidget(QLabel("名称:"))
        self.template_name_edit = QLineEdit()
        self.template_name_edit.setPlaceholderText("输入模板名称...")
        name_layout.addWidget(self.template_name_edit)
        layout.addLayout(name_layout)

        # 模板类别
        category_layout = QHBoxLayout()
        category_layout.addWidget(QLabel("类别:"))
        self.template_category_combo = QComboBox()
        self.template_category_combo.addItems(["通用", "按钮", "图标", "文本", "其他"])
        category_layout.addWidget(self.template_category_combo)
        layout.addLayout(category_layout)

        # 模板描述
        layout.addWidget(QLabel("描述:"))
        self.template_desc_edit = QTextEdit()
        self.template_desc_edit.setMaximumHeight(60)
        self.template_desc_edit.setPlaceholderText("输入模板描述...")
        layout.addWidget(self.template_desc_edit)

        # 创建模板按钮
        self.create_template_button = QPushButton("✨ 创建模板")
        self.create_template_button.setEnabled(False)
        self.create_template_button.setToolTip(
            "✨ 创建匹配模板\n\n"
            "🎯 功能说明：\n"
            "• 🏷️ 从选择区域创建精确匹配模板\n"
            "• 📝 添加模板名称和详细描述\n"
            "• 💾 自动保存到模板库中\n"
            "• 🔄 立即更新模板列表显示\n"
            "• 🎯 支持后续的模板匹配检测\n\n"
            "💡 使用场景：\n"
            "• 创建游戏界面元素识别模板\n"
            "• 制作应用程序UI自动化模板\n"
            "• 建立图像识别的标准参考\n"
            "• 构建自动化测试的素材库\n\n"
            "📋 操作步骤：\n"
            "1. 在截图上拖拽选择目标区域\n"
            "2. 输入模板名称（必填）\n"
            "3. 添加模板描述（可选）\n"
            "4. 点击此按钮创建模板\n\n"
            "⚠️ 注意：需要选择区域并输入名称才能创建"
        )
        self.create_template_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:disabled {
                background-color: #e9ecef;
                color: #6c757d;
            }
        """)
        layout.addWidget(self.create_template_button)

        parent_layout.addWidget(group)

    def _create_action_group(self, parent_layout):
        """创建操作按钮组"""
        group = QGroupBox("🔧 操作")
        layout = QVBoxLayout(group)
        layout.setSpacing(8)

        # 另存为按钮
        self.save_as_button = QPushButton("💾 另存为...")
        self.save_as_button.setToolTip(
            "💾 保存完整截图\n\n"
            "🎯 功能说明：\n"
            "• 📸 保存整个截图到指定位置\n"
            "• 📁 自定义文件名和保存路径\n"
            "• 🖼️ 支持PNG、JPEG等多种格式\n"
            "• 📋 保留原始图像质量和尺寸\n\n"
            "💡 使用场景：\n"
            "• 保存截图用于文档和报告\n"
            "• 备份重要的屏幕内容\n"
            "• 分享完整的界面截图\n"
            "• 创建教程和说明材料\n\n"
            "⚡ 操作：点击选择保存位置和文件名"
        )
        self.save_as_button.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        layout.addWidget(self.save_as_button)

        # 提取区域按钮
        self.extract_button = QPushButton("✂️ 提取区域")
        self.extract_button.setEnabled(False)
        self.extract_button.setToolTip(
            "✂️ 提取选择区域\n\n"
            "🎯 功能说明：\n"
            "• ✂️ 从完整截图中裁剪选择的区域\n"
            "• 📊 将区域数据发送给检测组件\n"
            "• 🔍 减少检测范围，提高分析精度\n"
            "• ⚡ 优化处理速度，节省计算资源\n\n"
            "💡 使用场景：\n"
            "• 只检测屏幕中的特定区域\n"
            "• 排除干扰元素，专注目标区域\n"
            "• 提高检测准确性和效率\n"
            "• 创建精确的检测范围\n\n"
            "📋 操作步骤：\n"
            "1. 在截图上拖拽选择目标区域\n"
            "2. 点击此按钮提取选择的区域\n"
            "3. 区域数据自动发送给检测组件\n\n"
            "⚠️ 注意：需要先选择区域才能使用此功能"
        )
        self.extract_button.setStyleSheet("""
            QPushButton {
                background-color: #fd7e14;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e8650e;
            }
            QPushButton:disabled {
                background-color: #e9ecef;
                color: #6c757d;
            }
        """)
        layout.addWidget(self.extract_button)

        # 分隔线
        line = QFrame()
        line.setFrameShape(QFrame.Shape.HLine)
        line.setFrameShadow(QFrame.Shadow.Sunken)
        layout.addWidget(line)

        # 关闭按钮
        close_button = QPushButton("❌ 关闭")
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        close_button.clicked.connect(self.reject)
        layout.addWidget(close_button)

        parent_layout.addWidget(group)

    def _init_connections(self):
        """初始化信号连接"""
        # 区域选择信号
        self.image_viewer.region_selected.connect(self._on_region_selected)
        self.image_viewer.selection_changed.connect(self._on_selection_changed)

        # 按钮信号
        self.clear_button.clicked.connect(self._clear_selection)
        self.save_as_button.clicked.connect(self._save_as)
        self.extract_button.clicked.connect(self._extract_region)
        self.create_template_button.clicked.connect(self._create_template)

        # 文本变化信号
        self.template_name_edit.textChanged.connect(self._validate_template_input)

    def _on_region_selected(self, region):
        """处理区域选择完成"""
        try:
            x, y, width, height = region
            self.selected_region = region

            # 更新区域信息显示
            self.region_label.setText(f"📍 区域: ({x}, {y}) - {width}×{height}")
            self.region_label.setStyleSheet("""
                QLabel {
                    padding: 8px;
                    background-color: #d4edda;
                    border: 1px solid #c3e6cb;
                    border-radius: 4px;
                    color: #155724;
                    font-weight: bold;
                }
            """)

            # 启用相关按钮
            self.clear_button.setEnabled(True)
            self.extract_button.setEnabled(True)
            self._validate_template_input()

            self.logger.info(f"区域选择完成: {region}")

        except Exception as e:
            self.logger.error(f"处理区域选择失败: {e}")

    def _on_selection_changed(self, region):
        """处理选择变化（实时更新）"""
        try:
            x, y, width, height = region
            # 实时更新区域信息（但不改变样式）
            temp_text = f"📍 选择中: ({x}, {y}) - {width}×{height}"
            if not self.selected_region:  # 只在没有确定选择时更新
                self.region_label.setText(temp_text)

        except Exception as e:
            self.logger.error(f"处理选择变化失败: {e}")

    def _clear_selection(self):
        """清除选择"""
        try:
            self.image_viewer.clear_selection()
            self.selected_region = None
            self.region_image = None

            # 重置区域信息显示
            self.region_label.setText("📍 未选择区域")
            self.region_label.setStyleSheet("""
                QLabel {
                    padding: 8px;
                    background-color: #f8f9fa;
                    border: 1px solid #dee2e6;
                    border-radius: 4px;
                    color: #495057;
                }
            """)

            # 禁用相关按钮
            self.clear_button.setEnabled(False)
            self.extract_button.setEnabled(False)
            self.create_template_button.setEnabled(False)

            self.logger.info("选择已清除")

        except Exception as e:
            self.logger.error(f"清除选择失败: {e}")

    def _save_as(self):
        """另存为"""
        try:
            from PyQt6.QtWidgets import QFileDialog

            filename, _ = QFileDialog.getSaveFileName(
                self,
                "保存截图",
                f"screenshot_{int(time.time())}.png",
                "PNG文件 (*.png);;JPEG文件 (*.jpg);;所有文件 (*)"
            )

            if filename:
                import cv2
                if cv2.imwrite(filename, self.screenshot_image):
                    self.screenshot_saved.emit(filename)
                    QMessageBox.information(self, "成功", f"截图已保存到: {filename}")
                    self.logger.info(f"截图另存为: {filename}")
                else:
                    QMessageBox.warning(self, "错误", "保存截图失败")

        except Exception as e:
            self.logger.error(f"另存为失败: {e}")
            QMessageBox.warning(self, "错误", f"另存为失败: {e}")

    def _extract_region(self):
        """提取选择的区域"""
        try:
            # 从新的图像查看器获取选择区域
            selected_region = self.image_viewer.get_selected_region()
            if not selected_region:
                QMessageBox.warning(self, "提示", "请先选择一个区域")
                return

            x, y, width, height = selected_region
            self.selected_region = selected_region

            # 从原始图像中提取区域
            self.region_image = self.screenshot_image[y:y+height, x:x+width].copy()

            # 发送区域提取信号
            self.region_extracted.emit(self.region_image, self.selected_region)

            QMessageBox.information(self, "成功", f"区域已提取: {width}×{height}")
            self.logger.info(f"区域提取完成: {self.selected_region}")

        except Exception as e:
            self.logger.error(f"提取区域失败: {e}")
            QMessageBox.warning(self, "错误", f"提取区域失败: {e}")

    def _create_template(self):
        """创建模板"""
        try:
            # 从新的图像查看器获取选择区域
            selected_region = self.image_viewer.get_selected_region()
            if not selected_region:
                QMessageBox.warning(self, "提示", "请先选择一个区域")
                return

            name = self.template_name_edit.text().strip()
            if not name:
                QMessageBox.warning(self, "提示", "请输入模板名称")
                return

            # 提取区域图像
            x, y, width, height = selected_region
            region_image = self.screenshot_image[y:y+height, x:x+width].copy()
            self.selected_region = selected_region

            # 创建模板数据
            template_data = {
                'name': name,
                'category': self.template_category_combo.currentText(),
                'description': self.template_desc_edit.toPlainText().strip(),
                'image': region_image,
                'region': self.selected_region,
                'created_time': time.time(),
                'source_screenshot': self.screenshot_filepath
            }

            # 发送模板创建信号
            self.template_created.emit(template_data)

            QMessageBox.information(self, "成功", f"模板 '{name}' 创建成功！")
            self.logger.info(f"模板创建成功: {name}")

            # 可选择是否关闭对话框
            reply = QMessageBox.question(
                self, "提示", "模板已创建，是否关闭对话框？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                self.accept()

        except Exception as e:
            self.logger.error(f"创建模板失败: {e}")
            QMessageBox.warning(self, "错误", f"创建模板失败: {e}")

    def _validate_template_input(self):
        """验证模板输入"""
        try:
            name_valid = bool(self.template_name_edit.text().strip())
            region_valid = self.selected_region is not None

            self.create_template_button.setEnabled(name_valid and region_valid)

        except Exception as e:
            self.logger.error(f"验证模板输入失败: {e}")

    def get_selected_region_image(self) -> Optional[np.ndarray]:
        """获取选择区域的图像"""
        return self.region_image

    def get_selected_region_info(self) -> Optional[Tuple[int, int, int, int]]:
        """获取选择区域的信息"""
        return self.selected_region
