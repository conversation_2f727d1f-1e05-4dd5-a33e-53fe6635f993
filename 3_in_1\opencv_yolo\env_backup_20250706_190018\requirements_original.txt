# 基于YOLO深度学习检测结合OpenCV模板匹配的Windows屏幕识别工具
# 本文件由AI辅助生成 - Generated with AI Assistance
# 生成时间: 2025-01-27 CST
# AI模型: Claude-4-Sonnet
# 审核状态: 待人工安全审查
# 编码标准: UTF-8无BOM

# 核心深度学习框架
ultralytics>=8.0.0
torch>=2.0.0
torchvision>=0.15.0

# 计算机视觉库
opencv-python>=4.8.0
opencv-contrib-python>=4.8.0
Pillow>=10.0.0

# GUI框架
PyQt6>=6.4.0
PyQt6-tools>=6.4.0

# 数据处理和科学计算
numpy>=1.24.0
scipy>=1.10.0
pandas>=2.0.0

# 图像处理和增强
scikit-image>=0.20.0
imageio>=2.28.0

# 自动化操作
PyAutoGUI>=0.9.54
pywin32>=306
pynput>=1.7.6

# 数据库
sqlite3  # Python内置，无需安装

# 配置文件处理
PyYAML>=6.0
configparser>=5.3.0

# 日志和监控
loguru>=0.7.0
psutil>=5.9.0

# 异步处理
asyncio  # Python内置
aiofiles>=23.0.0

# 测试框架
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
pytest-qt>=4.2.0

# 代码质量
black>=23.0.0
flake8>=6.0.0
mypy>=1.5.0

# 开发工具
ipython>=8.14.0
jupyter>=1.0.0

# 打包和分发
pyinstaller>=5.13.0
setuptools>=68.0.0
wheel>=0.41.0

# GPU加速支持（可选）
# 注意：需要根据CUDA版本选择对应的torch版本
# torch+cu118 --extra-index-url https://download.pytorch.org/whl/cu118
