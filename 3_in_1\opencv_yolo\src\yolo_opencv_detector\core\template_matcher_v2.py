# -*- coding: utf-8 -*-
"""
重构的模板匹配器 - 支持多种匹配方法和优化
作者: Cursor AI
创建时间: 2025-07-05
编码标准: UTF-8无BOM
"""

import time
from typing import List, Dict, Any, Optional, Tuple
import numpy as np
import cv2
from pathlib import Path

from ..utils.logger import Logger
from ..utils.config_manager import ConfigManager


class TemplateMatcherV2:
    """重构的模板匹配器类"""
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.logger = Logger()
        
        # 匹配参数
        self.threshold = 0.8
        self.method = cv2.TM_CCOEFF_NORMED
        self.scale_range = [0.8, 1.2]
        self.scale_steps = 5
        self.enable_rotation = False
        self.rotation_range = 15
        self.rotation_steps = 3
        
        # 性能统计
        self.match_count = 0
        self.total_time = 0.0
        
        self._load_config()
        self.logger.info("模板匹配器初始化完成")
    
    def _load_config(self) -> None:
        """加载配置"""
        try:
            # 使用默认配置
            config = {
                'threshold': 0.8,
                'method': 'TM_CCOEFF_NORMED',
                'scale_range': [0.8, 1.2],
                'scale_steps': 5,
                'enable_rotation': False,
                'rotation_range': 15,
                'rotation_steps': 3
            }
            
            self.threshold = config.get('threshold', 0.8)
            self.scale_range = config.get('scale_range', [0.8, 1.2])
            self.scale_steps = config.get('scale_steps', 5)
            self.enable_rotation = config.get('enable_rotation', False)
            self.rotation_range = config.get('rotation_range', 15)
            self.rotation_steps = config.get('rotation_steps', 3)
            
            # 设置匹配方法
            method_name = config.get('method', 'TM_CCOEFF_NORMED')
            self.method = self._get_cv_method(method_name)
            
        except Exception as e:
            self.logger.error(f"加载模板匹配配置失败: {e}")
    
    def _get_cv_method(self, method_name: str) -> int:
        """获取OpenCV匹配方法"""
        methods = {
            'TM_CCOEFF': cv2.TM_CCOEFF,
            'TM_CCOEFF_NORMED': cv2.TM_CCOEFF_NORMED,
            'TM_CCORR': cv2.TM_CCORR,
            'TM_CCORR_NORMED': cv2.TM_CCORR_NORMED,
            'TM_SQDIFF': cv2.TM_SQDIFF,
            'TM_SQDIFF_NORMED': cv2.TM_SQDIFF_NORMED
        }
        return methods.get(method_name, cv2.TM_CCOEFF_NORMED)
    
    def match_template(self, image: np.ndarray, template: np.ndarray,
                      threshold: Optional[float] = None,
                      template_id: str = "unknown") -> List[Dict[str, Any]]:
        """
        执行模板匹配
        
        Args:
            image: 输入图像 (BGR格式)
            template: 模板图像 (BGR格式)
            threshold: 匹配阈值，None表示使用默认值
            template_id: 模板ID
            
        Returns:
            匹配结果列表
        """
        if image is None or template is None:
            self.logger.error("输入图像或模板为空")
            return []
        
        # 使用参数或默认值
        match_threshold = threshold if threshold is not None else self.threshold
        
        try:
            start_time = time.time()
            
            # 转换为灰度图像
            gray_image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY) if len(image.shape) == 3 else image
            gray_template = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY) if len(template.shape) == 3 else template
            
            matches = []
            
            # 多尺度匹配
            if self.scale_range[0] != 1.0 or self.scale_range[1] != 1.0:
                matches.extend(self._multi_scale_match(
                    gray_image, gray_template, match_threshold, template_id
                ))
            else:
                # 单尺度匹配
                matches.extend(self._single_scale_match(
                    gray_image, gray_template, match_threshold, template_id, 1.0
                ))
            
            # 非极大值抑制
            matches = self._non_max_suppression(matches)
            
            # 更新性能统计
            match_time = time.time() - start_time
            self.match_count += 1
            self.total_time += match_time
            
            self.logger.debug(f"模板匹配完成: {len(matches)}个匹配, 耗时: {match_time:.3f}s")
            
            return matches
            
        except Exception as e:
            self.logger.error(f"模板匹配失败: {e}")
            return []
    
    def _single_scale_match(self, image: np.ndarray, template: np.ndarray,
                           threshold: float, template_id: str, scale: float) -> List[Dict[str, Any]]:
        """单尺度模板匹配"""
        matches = []
        
        try:
            # 缩放模板
            if scale != 1.0:
                h, w = template.shape
                new_h, new_w = int(h * scale), int(w * scale)
                if new_h <= 0 or new_w <= 0:
                    return matches
                scaled_template = cv2.resize(template, (new_w, new_h))
            else:
                scaled_template = template
            
            # 检查模板尺寸
            if scaled_template.shape[0] > image.shape[0] or scaled_template.shape[1] > image.shape[1]:
                return matches
            
            # 执行模板匹配
            result = cv2.matchTemplate(image, scaled_template, self.method)
            
            # 查找匹配位置
            if self.method in [cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED]:
                # 对于SQDIFF方法，值越小越好
                locations = np.where(result <= (1.0 - threshold))
                scores = 1.0 - result[locations]
            else:
                # 对于其他方法，值越大越好
                locations = np.where(result >= threshold)
                scores = result[locations]
            
            # 创建匹配结果
            template_h, template_w = scaled_template.shape
            for i, (y, x) in enumerate(zip(locations[0], locations[1])):
                match = {
                    'source': 'template',
                    'bbox': [int(x), int(y), template_w, template_h],
                    'confidence': float(scores[i]),
                    'template_id': template_id,
                    'scale': scale,
                    'method': self._get_method_name()
                }
                matches.append(match)
        
        except Exception as e:
            self.logger.error(f"单尺度匹配失败: {e}")
        
        return matches
    
    def _multi_scale_match(self, image: np.ndarray, template: np.ndarray,
                          threshold: float, template_id: str) -> List[Dict[str, Any]]:
        """多尺度模板匹配"""
        matches = []
        
        # 生成尺度列表
        scales = np.linspace(self.scale_range[0], self.scale_range[1], self.scale_steps)
        
        for scale in scales:
            scale_matches = self._single_scale_match(image, template, threshold, template_id, scale)
            matches.extend(scale_matches)
        
        return matches
    
    def _non_max_suppression(self, matches: List[Dict[str, Any]], 
                           overlap_threshold: float = 0.3) -> List[Dict[str, Any]]:
        """非极大值抑制"""
        if len(matches) <= 1:
            return matches
        
        # 按置信度排序
        matches = sorted(matches, key=lambda x: x['confidence'], reverse=True)
        
        suppressed = []
        
        for i, match in enumerate(matches):
            if i in suppressed:
                continue
            
            bbox1 = match['bbox']
            
            for j in range(i + 1, len(matches)):
                if j in suppressed:
                    continue
                
                bbox2 = matches[j]['bbox']
                
                # 计算IoU
                iou = self._calculate_iou(bbox1, bbox2)
                
                if iou > overlap_threshold:
                    suppressed.append(j)
        
        # 返回未被抑制的匹配
        return [matches[i] for i in range(len(matches)) if i not in suppressed]
    
    def _calculate_iou(self, bbox1: List[int], bbox2: List[int]) -> float:
        """计算两个边界框的IoU"""
        x1, y1, w1, h1 = bbox1
        x2, y2, w2, h2 = bbox2
        
        # 计算交集
        x_left = max(x1, x2)
        y_top = max(y1, y2)
        x_right = min(x1 + w1, x2 + w2)
        y_bottom = min(y1 + h1, y2 + h2)
        
        if x_right <= x_left or y_bottom <= y_top:
            return 0.0
        
        intersection = (x_right - x_left) * (y_bottom - y_top)
        
        # 计算并集
        area1 = w1 * h1
        area2 = w2 * h2
        union = area1 + area2 - intersection
        
        if union <= 0:
            return 0.0
        
        return intersection / union
    
    def _get_method_name(self) -> str:
        """获取匹配方法名称"""
        methods = {
            cv2.TM_CCOEFF: 'TM_CCOEFF',
            cv2.TM_CCOEFF_NORMED: 'TM_CCOEFF_NORMED',
            cv2.TM_CCORR: 'TM_CCORR',
            cv2.TM_CCORR_NORMED: 'TM_CCORR_NORMED',
            cv2.TM_SQDIFF: 'TM_SQDIFF',
            cv2.TM_SQDIFF_NORMED: 'TM_SQDIFF_NORMED'
        }
        return methods.get(self.method, 'UNKNOWN')
    
    def match_multiple_templates(self, image: np.ndarray, 
                               templates: List[Dict[str, Any]],
                               threshold: Optional[float] = None) -> List[Dict[str, Any]]:
        """
        匹配多个模板
        
        Args:
            image: 输入图像
            templates: 模板列表，每个元素包含 {'id': str, 'image': np.ndarray, 'name': str}
            threshold: 匹配阈值
            
        Returns:
            所有匹配结果
        """
        all_matches = []
        
        for template_info in templates:
            template_id = template_info.get('id', 'unknown')
            template_image = template_info.get('image')
            template_name = template_info.get('name', template_id)
            
            if template_image is not None:
                matches = self.match_template(image, template_image, threshold, template_id)
                
                # 添加模板名称信息
                for match in matches:
                    match['template_name'] = template_name
                
                all_matches.extend(matches)
        
        # 对所有匹配结果进行NMS
        all_matches = self._non_max_suppression(all_matches)
        
        return all_matches
    
    def update_config(self, config: Dict[str, Any]) -> None:
        """更新匹配配置"""
        try:
            if 'threshold' in config:
                self.threshold = float(config['threshold'])
            
            if 'method' in config:
                self.method = self._get_cv_method(config['method'])
            
            if 'scale_range' in config:
                self.scale_range = config['scale_range']
            
            if 'scale_steps' in config:
                self.scale_steps = int(config['scale_steps'])
            
            if 'enable_rotation' in config:
                self.enable_rotation = bool(config['enable_rotation'])
            
            if 'rotation_range' in config:
                self.rotation_range = float(config['rotation_range'])
            
            if 'rotation_steps' in config:
                self.rotation_steps = int(config['rotation_steps'])
            
            self.logger.info("模板匹配配置已更新")
            
        except Exception as e:
            self.logger.error(f"更新模板匹配配置失败: {e}")
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        if self.match_count > 0:
            avg_time = self.total_time / self.match_count
        else:
            avg_time = 0.0
        
        return {
            'match_count': self.match_count,
            'total_time': self.total_time,
            'average_time': avg_time,
            'threshold': self.threshold,
            'method': self._get_method_name(),
            'scale_range': self.scale_range,
            'enable_rotation': self.enable_rotation
        }
    
    def reset_stats(self) -> None:
        """重置性能统计"""
        self.match_count = 0
        self.total_time = 0.0
        self.logger.info("模板匹配性能统计已重置")
    
    def preprocess_template(self, template: np.ndarray) -> np.ndarray:
        """预处理模板图像"""
        try:
            # 转换为灰度图像
            if len(template.shape) == 3:
                gray_template = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
            else:
                gray_template = template.copy()
            
            # 直方图均衡化
            gray_template = cv2.equalizeHist(gray_template)
            
            # 高斯滤波降噪
            gray_template = cv2.GaussianBlur(gray_template, (3, 3), 0)
            
            return gray_template
            
        except Exception as e:
            self.logger.error(f"模板预处理失败: {e}")
            return template
    
    def get_config_info(self) -> Dict[str, Any]:
        """获取配置信息"""
        return {
            'threshold': self.threshold,
            'method': self._get_method_name(),
            'scale_range': self.scale_range,
            'scale_steps': self.scale_steps,
            'enable_rotation': self.enable_rotation,
            'rotation_range': self.rotation_range,
            'rotation_steps': self.rotation_steps
        }
