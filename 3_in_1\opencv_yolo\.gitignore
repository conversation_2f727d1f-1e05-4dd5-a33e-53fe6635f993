# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# 虚拟环境
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# 日志文件
logs/
*.log

# 数据文件
data/*.db
data/*.sqlite
data/cache/

# 模型文件（大文件）
models/*.pt
models/*.onnx
models/*.engine

# 临时文件
temp/
tmp/
*.tmp

# 测试覆盖率
.coverage
htmlcov/
.pytest_cache/

# 系统文件
.DS_Store
Thumbs.db
desktop.ini

# 配置文件（包含敏感信息）
configs/local_config.yaml
configs/production_config.yaml

# 截图和测试数据
screenshots/
test_images/
*.png
*.jpg
*.jpeg
!templates/sample_*.png
