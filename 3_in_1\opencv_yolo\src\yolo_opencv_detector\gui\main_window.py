# -*- coding: utf-8 -*-
"""
主窗口界面
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

import sys
from pathlib import Path
from typing import Optional, List, Dict, Any
from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QSplitter,
    QMenuBar, QStatusBar, QToolBar, QMessageBox,
    QFileDialog, QProgressBar, QLabel
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt6.QtGui import QIcon, QPixmap, QKeySequence, QAction

from ..utils.logger import Logger
from ..utils.config_manager import ConfigManager
from ..utils.constants import DEFAULT_WINDOW_SIZE, MIN_WINDOW_SIZE
from .widgets.detection_panel import DetectionPanel
from .widgets.template_panel import TemplatePanel
from .widgets.result_panel import ResultPanel
from .widgets.config_panel import ConfigPanel
from .widgets.status_panel import StatusPanel


class MainWindow(QMainWindow):
    """主窗口类"""
    
    # 信号定义
    detection_started = pyqtSignal()
    detection_finished = pyqtSignal(list)  # 检测结果
    template_added = pyqtSignal(str)  # 模板ID
    config_changed = pyqtSignal(dict)  # 配置变更
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        初始化主窗口
        
        Args:
            config_manager: 配置管理器
        """
        super().__init__()
        
        self.logger = Logger().get_logger(__name__)
        self.config_manager = config_manager or ConfigManager()
        
        # 窗口属性
        self.setWindowTitle("YOLO OpenCV 屏幕识别工具")
        self.setMinimumSize(1200, 800)  # 直接设置最小尺寸
        self.resize(1400, 900)  # 直接设置默认尺寸
        
        # 组件初始化
        self.central_widget = None
        self.detection_panel = None
        self.template_panel = None
        self.result_panel = None
        self.config_panel = None
        self.status_panel = None
        
        # 菜单和工具栏
        self.menu_bar = None
        self.tool_bar = None
        self.status_bar = None
        
        # 状态管理
        self.is_detecting = False
        self.current_results = []
        
        # 定时器
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self._update_status)
        self.status_timer.start(1000)  # 每秒更新状态
        
        # 初始化界面
        self._init_ui()
        self._init_connections()
        self._load_settings()
        
        self.logger.info("主窗口初始化完成")
    
    def _init_ui(self) -> None:
        """初始化用户界面"""
        try:
            # 创建菜单栏
            self._create_menu_bar()
            
            # 创建工具栏
            self._create_tool_bar()
            
            # 创建状态栏
            self._create_status_bar()
            
            # 创建中央部件
            self._create_central_widget()
            
            # 设置窗口图标
            self._set_window_icon()
            
        except Exception as e:
            self.logger.error(f"界面初始化失败: {e}")
            QMessageBox.critical(self, "错误", f"界面初始化失败: {e}")
    
    def _create_menu_bar(self) -> None:
        """创建菜单栏"""
        self.menu_bar = self.menuBar()
        
        # 文件菜单
        file_menu = self.menu_bar.addMenu("文件(&F)")
        
        # 新建检测会话
        new_action = QAction("新建检测会话(&N)", self)
        new_action.setShortcut(QKeySequence.StandardKey.New)
        new_action.triggered.connect(self._new_detection_session)
        file_menu.addAction(new_action)
        
        # 打开配置
        open_config_action = QAction("打开配置(&O)", self)
        open_config_action.setShortcut(QKeySequence.StandardKey.Open)
        open_config_action.triggered.connect(self._open_config)
        file_menu.addAction(open_config_action)
        
        # 保存配置
        save_config_action = QAction("保存配置(&S)", self)
        save_config_action.setShortcut(QKeySequence.StandardKey.Save)
        save_config_action.triggered.connect(self._save_config)
        file_menu.addAction(save_config_action)
        
        file_menu.addSeparator()
        
        # 导入模板
        import_template_action = QAction("导入模板(&I)", self)
        import_template_action.triggered.connect(self._import_templates)
        file_menu.addAction(import_template_action)
        
        # 导出模板
        export_template_action = QAction("导出模板(&E)", self)
        export_template_action.triggered.connect(self._export_templates)
        file_menu.addAction(export_template_action)
        
        file_menu.addSeparator()
        
        # 退出
        exit_action = QAction("退出(&X)", self)
        exit_action.setShortcut(QKeySequence.StandardKey.Quit)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 检测菜单
        detection_menu = self.menu_bar.addMenu("检测(&D)")
        
        # 开始检测
        start_detection_action = QAction("开始检测(&S)", self)
        start_detection_action.setShortcut(QKeySequence("F5"))
        start_detection_action.triggered.connect(self._start_detection)
        detection_menu.addAction(start_detection_action)
        
        # 停止检测
        stop_detection_action = QAction("停止检测(&T)", self)
        stop_detection_action.setShortcut(QKeySequence("F6"))
        stop_detection_action.triggered.connect(self._stop_detection)
        detection_menu.addAction(stop_detection_action)
        
        detection_menu.addSeparator()
        
        # 截图
        screenshot_action = QAction("立即截图(&C)", self)
        screenshot_action.setShortcut(QKeySequence("F12"))
        screenshot_action.triggered.connect(self._take_screenshot)
        detection_menu.addAction(screenshot_action)
        
        # 工具菜单
        tools_menu = self.menu_bar.addMenu("工具(&T)")
        
        # 配置
        config_action = QAction("配置(&C)", self)
        config_action.triggered.connect(self._show_config_dialog)
        tools_menu.addAction(config_action)
        
        # 性能监控
        performance_action = QAction("性能监控(&P)", self)
        performance_action.triggered.connect(self._show_performance_monitor)
        tools_menu.addAction(performance_action)
        
        # 帮助菜单
        help_menu = self.menu_bar.addMenu("帮助(&H)")

        # 使用指南
        help_guide_action = QAction("📖 使用指南(&G)", self)
        help_guide_action.setShortcut(QKeySequence("F1"))
        help_guide_action.triggered.connect(self._show_help_guide)
        help_menu.addAction(help_guide_action)

        # 快速入门
        quick_start_action = QAction("🚀 快速入门(&Q)", self)
        quick_start_action.triggered.connect(self._show_quick_start)
        help_menu.addAction(quick_start_action)

        help_menu.addSeparator()

        # 关于
        about_action = QAction("关于(&A)", self)
        about_action.triggered.connect(self._show_about)
        help_menu.addAction(about_action)
    
    def _create_tool_bar(self) -> None:
        """创建工具栏"""
        self.tool_bar = self.addToolBar("主工具栏")
        self.tool_bar.setMovable(False)
        
        # 开始检测按钮
        start_action = QAction("开始检测", self)
        start_action.setToolTip("开始检测 (F5)")
        start_action.triggered.connect(self._start_detection)
        self.tool_bar.addAction(start_action)
        
        # 停止检测按钮
        stop_action = QAction("停止检测", self)
        stop_action.setToolTip("停止检测 (F6)")
        stop_action.triggered.connect(self._stop_detection)
        self.tool_bar.addAction(stop_action)
        
        self.tool_bar.addSeparator()
        
        # 截图按钮
        screenshot_action = QAction("截图", self)
        screenshot_action.setToolTip("立即截图 (F12)")
        screenshot_action.triggered.connect(self._take_screenshot)
        self.tool_bar.addAction(screenshot_action)
        
        self.tool_bar.addSeparator()
        
        # 配置按钮
        config_action = QAction("配置", self)
        config_action.setToolTip("打开配置")
        config_action.triggered.connect(self._show_config_dialog)
        self.tool_bar.addAction(config_action)
    
    def _create_status_bar(self) -> None:
        """创建状态栏"""
        self.status_bar = self.statusBar()
        
        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_bar.addWidget(self.status_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.status_bar.addPermanentWidget(self.progress_bar)
        
        # 性能信息
        self.performance_label = QLabel("CPU: 0% | 内存: 0MB")
        self.status_bar.addPermanentWidget(self.performance_label)
    
    def _create_central_widget(self) -> None:
        """创建中央部件"""
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(self.central_widget)
        
        # 创建分割器
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(main_splitter)
        
        # 左侧面板（检测和模板）
        left_splitter = QSplitter(Qt.Orientation.Vertical)
        
        # 检测面板
        self.detection_panel = DetectionPanel(self.config_manager)
        left_splitter.addWidget(self.detection_panel)
        
        # 模板面板
        self.template_panel = TemplatePanel(self.config_manager)
        left_splitter.addWidget(self.template_panel)
        
        # 设置左侧面板比例 - 优化空间分配避免重叠
        left_splitter.setSizes([350, 350])  # 平衡分配空间

        # 右侧面板（结果和配置）
        right_splitter = QSplitter(Qt.Orientation.Vertical)

        # 结果面板
        self.result_panel = ResultPanel(self.config_manager)
        right_splitter.addWidget(self.result_panel)

        # 配置面板
        self.config_panel = ConfigPanel(self.config_manager)
        right_splitter.addWidget(self.config_panel)

        # 状态面板
        self.status_panel = StatusPanel(self.config_manager)
        right_splitter.addWidget(self.status_panel)

        # 设置右侧面板比例 - 大幅增加配置面板空间
        right_splitter.setSizes([120, 650, 120])  # 配置面板获得650px空间

        # 添加到主分割器
        main_splitter.addWidget(left_splitter)
        main_splitter.addWidget(right_splitter)

        # 设置主分割器比例 - 基于1400px窗口宽度优化
        main_splitter.setSizes([400, 1000])  # 右侧面板获得1000px宽度
    
    def _set_window_icon(self) -> None:
        """设置窗口图标"""
        try:
            # 尝试加载图标文件
            icon_path = Path(__file__).parent / "icons" / "app_icon.png"
            if icon_path.exists():
                self.setWindowIcon(QIcon(str(icon_path)))
        except Exception as e:
            self.logger.warning(f"无法加载窗口图标: {e}")
    
    def _init_connections(self) -> None:
        """初始化信号连接"""
        try:
            # 检测面板信号
            if self.detection_panel:
                self.detection_panel.detection_requested.connect(self._handle_detection_request)
                self.detection_panel.screenshot_requested.connect(self._handle_screenshot_request)
            
            # 模板面板信号
            if self.template_panel:
                self.template_panel.template_selected.connect(self._handle_template_selection)
                self.template_panel.template_added.connect(self._handle_template_added)
            
            # 配置面板信号
            if self.config_panel:
                self.config_panel.config_changed.connect(self._handle_config_change)
            
            # 结果面板信号
            if self.result_panel:
                self.result_panel.result_selected.connect(self._handle_result_selection)
            
        except Exception as e:
            self.logger.error(f"信号连接失败: {e}")
    
    def _load_settings(self) -> None:
        """加载设置"""
        try:
            # 加载窗口设置
            gui_config = self.config_manager.gui
            
            # 设置窗口大小 - 强制使用1400×900
            if hasattr(gui_config, 'window_size'):
                # 确保窗口尺寸至少为1400×900
                width, height = gui_config.window_size
                width = max(width, 1400)
                height = max(height, 900)
                self.resize(width, height)
            else:
                self.resize(1400, 900)  # 默认尺寸
            
            # 设置主题
            if hasattr(gui_config, 'theme'):
                self._apply_theme(gui_config.theme)
            
        except Exception as e:
            self.logger.error(f"设置加载失败: {e}")
    
    def _apply_theme(self, theme_name: str) -> None:
        """应用主题"""
        try:
            # 这里可以实现主题切换逻辑
            self.logger.info(f"应用主题: {theme_name}")
        except Exception as e:
            self.logger.error(f"主题应用失败: {e}")
    
    def _update_status(self) -> None:
        """更新状态信息"""
        try:
            # 更新状态标签
            if self.is_detecting:
                self.status_label.setText("检测中...")
            else:
                self.status_label.setText("就绪")
            
            # 更新性能信息
            # 这里可以添加实际的性能监控代码
            self.performance_label.setText("CPU: 0% | 内存: 0MB")
            
        except Exception as e:
            self.logger.error(f"状态更新失败: {e}")
    
    # 槽函数实现
    def _new_detection_session(self) -> None:
        """新建检测会话"""
        self.logger.info("新建检测会话")
        # 实现新建会话逻辑
    
    def _open_config(self) -> None:
        """打开配置"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "打开配置文件", "", "YAML文件 (*.yaml *.yml);;所有文件 (*)"
        )
        if file_path:
            self.logger.info(f"打开配置文件: {file_path}")
    
    def _save_config(self) -> None:
        """保存配置"""
        try:
            self.config_manager.save_config()
            self.status_label.setText("配置已保存")
            QTimer.singleShot(2000, lambda: self.status_label.setText("就绪"))
        except Exception as e:
            QMessageBox.warning(self, "警告", f"配置保存失败: {e}")
    
    def _import_templates(self) -> None:
        """导入模板"""
        self.logger.info("导入模板")
        # 实现模板导入逻辑
    
    def _export_templates(self) -> None:
        """导出模板"""
        self.logger.info("导出模板")
        # 实现模板导出逻辑
    
    def _start_detection(self) -> None:
        """开始检测"""
        if not self.is_detecting:
            self.is_detecting = True
            self.detection_started.emit()
            self.logger.info("开始检测")
    
    def _stop_detection(self) -> None:
        """停止检测"""
        if self.is_detecting:
            self.is_detecting = False
            self.logger.info("停止检测")
    
    def _take_screenshot(self) -> None:
        """立即截图"""
        self.logger.info("立即截图")
        # 实现截图逻辑
    
    def _show_config_dialog(self) -> None:
        """显示配置对话框"""
        self.logger.info("显示配置对话框")
        # 实现配置对话框
    
    def _show_performance_monitor(self) -> None:
        """显示性能监控"""
        self.logger.info("显示性能监控")
        # 实现性能监控界面

    def _show_help_guide(self) -> None:
        """显示使用指南"""
        try:
            from .help_dialog import HelpDialog
            dialog = HelpDialog(self)
            dialog.exec()
        except Exception as e:
            self.logger.error(f"显示帮助指南失败: {e}")
            QMessageBox.critical(self, "错误", f"显示帮助指南失败: {e}")

    def _show_quick_start(self) -> None:
        """显示快速入门"""
        try:
            from .help_dialog import HelpDialog
            dialog = HelpDialog(self)
            # 默认显示快速入门标签页
            dialog.exec()
        except Exception as e:
            self.logger.error(f"显示快速入门失败: {e}")
            QMessageBox.critical(self, "错误", f"显示快速入门失败: {e}")

    def _show_about(self) -> None:
        """显示关于对话框"""
        QMessageBox.about(
            self,
            "关于 YOLO OpenCV 检测器",
            "<h2>🎯 YOLO OpenCV 检测器</h2>"
            "<p><strong>版本:</strong> 1.0.0</p>"
            "<p><strong>描述:</strong> 基于 YOLO 和 OpenCV 的智能目标检测工具</p>"
            "<p><strong>功能特点:</strong></p>"
            "<ul>"
            "<li>🔍 实时目标检测</li>"
            "<li>📋 模板匹配</li>"
            "<li>🎯 结果融合</li>"
            "<li>📷 屏幕截图检测</li>"
            "</ul>"
            "<p><strong>技术栈:</strong> Python, PyQt6, OpenCV, YOLO</p>"
            "<p><strong>开发:</strong> AI Assistant</p>"
            "<p><strong>帮助:</strong> 按 F1 查看使用指南</p>"
        )
    
    # 信号处理函数
    def _handle_detection_request(self, params: Dict[str, Any]) -> None:
        """处理检测请求"""
        self.logger.info(f"处理检测请求: {params}")
    
    def _handle_screenshot_request(self, params: Dict[str, Any]) -> None:
        """处理截图请求"""
        self.logger.info(f"处理截图请求: {params}")
    
    def _handle_template_selection(self, template_id: str) -> None:
        """处理模板选择"""
        self.logger.info(f"选择模板: {template_id}")
    
    def _handle_template_added(self, template_id: str) -> None:
        """处理模板添加"""
        self.template_added.emit(template_id)
        self.logger.info(f"添加模板: {template_id}")
    
    def _handle_config_change(self, config: Dict[str, Any]) -> None:
        """处理配置变更"""
        self.config_changed.emit(config)
        self.logger.info(f"配置变更: {config}")
    
    def _handle_result_selection(self, result_id: str) -> None:
        """处理结果选择"""
        self.logger.info(f"选择结果: {result_id}")
    
    def closeEvent(self, event) -> None:
        """窗口关闭事件"""
        try:
            # 保存设置
            self._save_settings()
            
            # 停止定时器
            self.status_timer.stop()
            
            # 停止检测
            if self.is_detecting:
                self._stop_detection()
            
            self.logger.info("主窗口关闭")
            event.accept()
            
        except Exception as e:
            self.logger.error(f"窗口关闭失败: {e}")
            event.accept()
    
    def _save_settings(self) -> None:
        """保存设置"""
        try:
            # 保存窗口大小
            size = self.size()
            self.config_manager.gui.window_size = (size.width(), size.height())
            
            # 保存配置
            self.config_manager.save_config()
            
        except Exception as e:
            self.logger.error(f"设置保存失败: {e}")
