# -*- coding: utf-8 -*-
"""
模板匹配器测试
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

import pytest
import numpy as np
import cv2
from PIL import Image
from pathlib import Path

from yolo_opencv_detector.core.template_matcher import TemplateMatcher
from yolo_opencv_detector.utils.data_structures import DetectionResult, DetectionSource, TemplateInfo


class TestTemplateMatcher:
    """模板匹配器测试类"""
    
    @pytest.fixture
    def matcher(self):
        """创建匹配器实例"""
        return TemplateMatcher(
            threshold=0.8,
            method="cv2.TM_CCOEFF_NORMED",
            scale_range=(0.8, 1.2),
            scale_steps=3,
            angle_range=(-5, 5),
            angle_steps=3
        )
    
    @pytest.fixture
    def sample_image(self):
        """创建示例图像"""
        image = np.zeros((400, 400, 3), dtype=np.uint8)
        # 添加一个矩形作为目标
        cv2.rectangle(image, (150, 150), (250, 250), (255, 255, 255), -1)
        cv2.rectangle(image, (160, 160), (240, 240), (128, 128, 128), -1)
        return image
    
    @pytest.fixture
    def sample_template(self):
        """创建示例模板"""
        template = np.zeros((50, 50, 3), dtype=np.uint8)
        cv2.rectangle(template, (10, 10), (40, 40), (255, 255, 255), -1)
        cv2.rectangle(template, (15, 15), (35, 35), (128, 128, 128), -1)
        return template
    
    @pytest.fixture
    def template_file(self, sample_template, tmp_path):
        """创建模板文件"""
        template_path = tmp_path / "template.png"
        cv2.imwrite(str(template_path), sample_template)
        return template_path
    
    @pytest.fixture
    def template_info(self, template_file):
        """创建模板信息"""
        return TemplateInfo(
            template_id="test_template",
            name="测试模板",
            description="用于测试的模板",
            file_path=template_file,
            category="test",
            tags=["test", "rectangle"]
        )
    
    def test_matcher_initialization(self, matcher):
        """测试匹配器初始化"""
        assert matcher is not None
        assert matcher.threshold == 0.8
        assert matcher.method == cv2.TM_CCOEFF_NORMED
        assert matcher.scale_range == (0.8, 1.2)
        assert matcher.scale_steps == 3
        assert matcher.angle_range == (-5, 5)
        assert matcher.angle_steps == 3
    
    def test_method_resolution(self):
        """测试匹配方法解析"""
        # 测试有效方法
        matcher = TemplateMatcher(method="cv2.TM_CCOEFF_NORMED")
        assert matcher.method == cv2.TM_CCOEFF_NORMED
        
        matcher = TemplateMatcher(method="cv2.TM_CCORR_NORMED")
        assert matcher.method == cv2.TM_CCORR_NORMED
        
        # 测试无效方法
        matcher = TemplateMatcher(method="invalid_method")
        assert matcher.method == cv2.TM_CCOEFF_NORMED  # 应该使用默认方法
    
    def test_image_preprocessing(self, matcher, sample_image):
        """测试图像预处理"""
        # 测试numpy数组
        processed = matcher._preprocess_image(sample_image)
        assert processed is not None
        assert isinstance(processed, np.ndarray)
        
        # 测试PIL图像
        pil_image = Image.fromarray(cv2.cvtColor(sample_image, cv2.COLOR_BGR2RGB))
        processed = matcher._preprocess_image(pil_image)
        assert processed is not None
        
        # 测试灰度图
        gray_image = cv2.cvtColor(sample_image, cv2.COLOR_BGR2GRAY)
        processed = matcher._preprocess_image(gray_image)
        assert processed is not None
    
    def test_template_preprocessing(self, matcher, sample_template, template_file, template_info):
        """测试模板预处理"""
        # 测试numpy数组
        processed, info = matcher._preprocess_template(sample_template)
        assert processed is not None
        assert isinstance(info, dict)
        
        # 测试文件路径
        processed, info = matcher._preprocess_template(template_file)
        assert processed is not None
        assert "file_path" in info
        
        # 测试TemplateInfo对象
        processed, info = matcher._preprocess_template(template_info)
        assert processed is not None
        assert info["template_id"] == "test_template"
        
        # 测试不存在的文件
        non_existent = Path("non_existent_template.png")
        processed, info = matcher._preprocess_template(non_existent)
        assert processed is None
        assert info is None
    
    def test_template_transformation(self, matcher, sample_template):
        """测试模板变换"""
        # 测试缩放
        scaled = matcher._transform_template(sample_template, scale=1.5, angle=0)
        assert scaled is not None
        assert scaled.shape[0] > sample_template.shape[0]
        assert scaled.shape[1] > sample_template.shape[1]
        
        # 测试旋转
        rotated = matcher._transform_template(sample_template, scale=1.0, angle=45)
        assert rotated is not None
        
        # 测试过小的缩放
        too_small = matcher._transform_template(sample_template, scale=0.01, angle=0)
        assert too_small is None
    
    def test_single_match(self, matcher, sample_image, sample_template):
        """测试单次匹配"""
        matches = matcher._single_match(
            sample_image, sample_template, 
            scale=1.0, angle=0, 
            template_info={"template_id": "test"}
        )
        
        assert isinstance(matches, list)
        # 检查匹配结果格式
        for match in matches:
            assert isinstance(match, DetectionResult)
            assert match.source == DetectionSource.TEMPLATE
            assert match.bbox is not None
            assert 0 <= match.confidence <= 1
    
    def test_nms_application(self, matcher):
        """测试非极大值抑制"""
        # 创建重叠的检测结果
        from yolo_opencv_detector.utils.data_structures import BoundingBox
        
        results = [
            DetectionResult(
                bbox=BoundingBox(100, 100, 50, 50),
                confidence=0.9,
                source=DetectionSource.TEMPLATE
            ),
            DetectionResult(
                bbox=BoundingBox(110, 110, 50, 50),  # 重叠
                confidence=0.8,
                source=DetectionSource.TEMPLATE
            ),
            DetectionResult(
                bbox=BoundingBox(200, 200, 50, 50),  # 不重叠
                confidence=0.7,
                source=DetectionSource.TEMPLATE
            )
        ]
        
        filtered = matcher._apply_nms(results, iou_threshold=0.3)
        assert len(filtered) <= len(results)
        # 应该保留置信度最高的和不重叠的
        assert len(filtered) >= 1
    
    def test_match_function(self, matcher, sample_image, sample_template):
        """测试主匹配函数"""
        results = matcher.match(sample_image, sample_template, template_id="test")
        
        assert isinstance(results, list)
        for result in results:
            assert isinstance(result, DetectionResult)
            assert result.source == DetectionSource.TEMPLATE
            assert result.template_id == "test"
    
    def test_batch_match(self, matcher, sample_image, sample_template):
        """测试批量匹配"""
        # 创建多个模板
        template2 = cv2.resize(sample_template, (30, 30))
        templates = [sample_template, template2]
        
        results = matcher.batch_match(sample_image, templates)
        assert isinstance(results, list)
    
    def test_performance_stats(self, matcher):
        """测试性能统计"""
        # 初始状态
        stats = matcher.get_performance_stats()
        assert isinstance(stats, dict)
        
        # 添加模拟数据
        matcher.match_times = [0.1, 0.2, 0.15]
        matcher.total_matches = 5
        
        stats = matcher.get_performance_stats()
        assert "total_matches" in stats
        assert "avg_match_time" in stats
        assert stats["total_matches"] == 3
        assert stats["total_detections"] == 5
        
        # 重置统计
        matcher.reset_stats()
        assert len(matcher.match_times) == 0
        assert matcher.total_matches == 0
    
    def test_config_update(self, matcher):
        """测试配置更新"""
        original_threshold = matcher.threshold
        
        matcher.update_config(
            threshold=0.9,
            scale_range=(0.5, 2.0),
            enable_preprocessing=False
        )
        
        assert matcher.threshold == 0.9
        assert matcher.scale_range == (0.5, 2.0)
        assert matcher.enable_preprocessing == False
        
        # 恢复原始配置
        matcher.update_config(threshold=original_threshold)
    
    def test_preprocessing_toggle(self, matcher, sample_image):
        """测试预处理开关"""
        # 启用预处理
        matcher.enable_preprocessing = True
        processed_with = matcher._apply_preprocessing(sample_image)
        
        # 禁用预处理
        matcher.enable_preprocessing = False
        processed_without = matcher._preprocess_image(sample_image)
        
        # 两者应该不同（如果启用了预处理）
        if matcher.enable_preprocessing:
            assert not np.array_equal(processed_with, sample_image)
    
    def test_invalid_inputs(self, matcher):
        """测试无效输入处理"""
        # 测试None输入
        result = matcher._preprocess_image(None)
        assert result is None
        
        # 测试空数组
        empty_array = np.array([])
        result = matcher._preprocess_image(empty_array)
        assert result is None
        
        # 测试错误维度
        wrong_dim = np.zeros((10, 10, 10, 10))
        result = matcher._preprocess_image(wrong_dim)
        assert result is None
    
    def test_edge_cases(self, matcher, sample_image):
        """测试边界情况"""
        # 测试模板比图像大的情况
        large_template = np.zeros((500, 500, 3), dtype=np.uint8)
        results = matcher.match(sample_image, large_template)
        assert isinstance(results, list)
        # 应该返回空列表或处理错误
        
        # 测试极小模板
        tiny_template = np.zeros((2, 2, 3), dtype=np.uint8)
        results = matcher.match(sample_image, tiny_template)
        assert isinstance(results, list)
