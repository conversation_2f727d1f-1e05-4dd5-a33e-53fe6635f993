#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLO OpenCV检测器应用图标生成器
创建漂亮的应用程序图标和任务栏图标
"""

import os
from PIL import Image, ImageDraw, ImageFont
import numpy as np

def create_detection_icon(size=256, style="main"):
    """
    创建检测器图标
    
    Args:
        size: 图标尺寸
        style: 图标样式 ("main", "taskbar", "simple")
    """
    # 创建画布
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # 定义颜色
    colors = {
        'primary_blue': '#2E86AB',      # 主蓝色
        'accent_orange': '#F24236',     # 强调橙色
        'light_blue': '#A23B72',       # 浅蓝色
        'white': '#FFFFFF',             # 白色
        'dark_gray': '#2C3E50',        # 深灰色
        'light_gray': '#BDC3C7'        # 浅灰色
    }
    
    if style == "main":
        # 主应用图标 - 复杂版本
        create_main_app_icon(draw, size, colors)
    elif style == "taskbar":
        # 任务栏图标 - 简化版本
        create_taskbar_icon(draw, size, colors)
    else:
        # 简单图标
        create_simple_icon(draw, size, colors)
    
    return img

def create_main_app_icon(draw, size, colors):
    """创建主应用图标"""
    center = size // 2
    
    # 1. 背景渐变圆形
    for i in range(center):
        alpha = int(255 * (1 - i / center))
        color = (*hex_to_rgb(colors['primary_blue']), alpha)
        draw.ellipse([center-i, center-i, center+i, center+i], 
                    fill=color, outline=None)
    
    # 2. 主检测框 (大)
    box_size = size * 0.6
    box_x = center - box_size // 2
    box_y = center - box_size // 2
    
    # 检测框边框
    draw.rectangle([box_x, box_y, box_x + box_size, box_y + box_size],
                  outline=colors['accent_orange'], width=max(3, size//64))
    
    # 检测框角标
    corner_size = size // 16
    for corner in [(box_x, box_y), (box_x + box_size - corner_size, box_y),
                   (box_x, box_y + box_size - corner_size), 
                   (box_x + box_size - corner_size, box_y + box_size - corner_size)]:
        draw.rectangle([corner[0], corner[1], corner[0] + corner_size, corner[1] + corner_size],
                      fill=colors['accent_orange'])
    
    # 3. 小检测框们
    small_boxes = [
        (size * 0.15, size * 0.2, size * 0.25, size * 0.3),
        (size * 0.75, size * 0.15, size * 0.9, size * 0.25),
        (size * 0.1, size * 0.7, size * 0.2, size * 0.85),
    ]
    
    for box in small_boxes:
        draw.rectangle(box, outline=colors['light_blue'], width=max(2, size//96))
    
    # 4. 鼠标光标
    cursor_x = center + size * 0.2
    cursor_y = center - size * 0.1
    cursor_size = size * 0.15
    
    # 光标主体
    cursor_points = [
        (cursor_x, cursor_y),
        (cursor_x, cursor_y + cursor_size),
        (cursor_x + cursor_size * 0.4, cursor_y + cursor_size * 0.7),
        (cursor_x + cursor_size * 0.6, cursor_y + cursor_size * 0.9),
        (cursor_x + cursor_size * 0.8, cursor_y + cursor_size * 0.7),
        (cursor_x + cursor_size * 0.5, cursor_y + cursor_size * 0.5)
    ]
    
    draw.polygon(cursor_points, fill=colors['white'], outline=colors['dark_gray'])
    
    # 5. YOLO文字 (如果尺寸足够大)
    if size >= 128:
        try:
            font_size = max(12, size // 16)
            font = ImageFont.truetype("arial.ttf", font_size)
        except:
            font = ImageFont.load_default()
        
        text = "YOLO"
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        text_x = center - text_width // 2
        text_y = center + size * 0.25
        
        # 文字阴影
        draw.text((text_x + 1, text_y + 1), text, fill=colors['dark_gray'], font=font)
        # 主文字
        draw.text((text_x, text_y), text, fill=colors['white'], font=font)

def create_taskbar_icon(draw, size, colors):
    """创建任务栏图标 - 简化版本"""
    center = size // 2
    
    # 1. 简单背景
    draw.ellipse([2, 2, size-2, size-2], fill=colors['primary_blue'])
    
    # 2. 主检测框
    box_size = size * 0.5
    box_x = center - box_size // 2
    box_y = center - box_size // 2
    
    draw.rectangle([box_x, box_y, box_x + box_size, box_y + box_size],
                  outline=colors['accent_orange'], width=max(2, size//32))
    
    # 3. 检测框角标
    corner_size = max(2, size // 20)
    corners = [(box_x, box_y), (box_x + box_size - corner_size, box_y),
               (box_x, box_y + box_size - corner_size), 
               (box_x + box_size - corner_size, box_y + box_size - corner_size)]
    
    for corner in corners:
        draw.rectangle([corner[0], corner[1], corner[0] + corner_size, corner[1] + corner_size],
                      fill=colors['accent_orange'])
    
    # 4. 简化的光标
    cursor_x = center + size * 0.15
    cursor_y = center - size * 0.05
    cursor_size = size * 0.2
    
    cursor_points = [
        (cursor_x, cursor_y),
        (cursor_x, cursor_y + cursor_size),
        (cursor_x + cursor_size * 0.6, cursor_y + cursor_size * 0.6),
        (cursor_x + cursor_size * 0.4, cursor_y + cursor_size * 0.4)
    ]
    
    draw.polygon(cursor_points, fill=colors['white'])

def create_simple_icon(draw, size, colors):
    """创建简单图标"""
    center = size // 2
    
    # 简单的检测框
    box_size = size * 0.7
    box_x = center - box_size // 2
    box_y = center - box_size // 2
    
    draw.rectangle([box_x, box_y, box_x + box_size, box_y + box_size],
                  outline=colors['primary_blue'], width=max(3, size//32))
    
    # 中心点
    dot_size = size // 16
    draw.ellipse([center - dot_size, center - dot_size, 
                 center + dot_size, center + dot_size],
                fill=colors['accent_orange'])

def hex_to_rgb(hex_color):
    """将十六进制颜色转换为RGB"""
    hex_color = hex_color.lstrip('#')
    return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))

def create_ico_file(png_path, ico_path):
    """将PNG转换为ICO文件"""
    try:
        img = Image.open(png_path)
        
        # 创建多尺寸ICO
        sizes = [16, 24, 32, 48, 64, 128, 256]
        images = []
        
        for size in sizes:
            resized = img.resize((size, size), Image.Resampling.LANCZOS)
            images.append(resized)
        
        # 保存为ICO
        images[0].save(ico_path, format='ICO', sizes=[(img.width, img.height) for img in images])
        print(f"✅ ICO文件已创建: {ico_path}")
        
    except Exception as e:
        print(f"❌ ICO创建失败: {e}")

def main():
    """主函数 - 生成所有图标"""
    print("🎨 YOLO OpenCV检测器图标生成器")
    print("=" * 50)
    
    # 创建图标目录
    icons_dir = "icons"
    os.makedirs(icons_dir, exist_ok=True)
    
    # 生成不同尺寸和样式的图标
    icon_configs = [
        # 主应用图标
        {"name": "app_icon_256", "size": 256, "style": "main"},
        {"name": "app_icon_128", "size": 128, "style": "main"},
        {"name": "app_icon_64", "size": 64, "style": "main"},
        {"name": "app_icon_32", "size": 32, "style": "main"},
        
        # 任务栏图标
        {"name": "taskbar_icon_32", "size": 32, "style": "taskbar"},
        {"name": "taskbar_icon_24", "size": 24, "style": "taskbar"},
        {"name": "taskbar_icon_16", "size": 16, "style": "taskbar"},
        
        # 简单图标
        {"name": "simple_icon_48", "size": 48, "style": "simple"},
        {"name": "simple_icon_24", "size": 24, "style": "simple"},
    ]
    
    png_files = []
    
    print("\n📝 生成PNG图标...")
    for config in icon_configs:
        print(f"  生成 {config['name']} ({config['size']}x{config['size']}, {config['style']})")
        
        # 创建图标
        icon = create_detection_icon(config['size'], config['style'])
        
        # 保存PNG
        png_path = os.path.join(icons_dir, f"{config['name']}.png")
        icon.save(png_path, "PNG")
        png_files.append(png_path)
        
        print(f"    ✅ 已保存: {png_path}")
    
    print(f"\n🎯 成功生成 {len(png_files)} 个PNG图标")
    
    # 创建ICO文件
    print("\n📦 生成ICO文件...")
    
    # 主应用ICO
    main_ico_path = os.path.join(icons_dir, "yolo_detector_app.ico")
    create_ico_file(os.path.join(icons_dir, "app_icon_256.png"), main_ico_path)
    
    # 任务栏ICO
    taskbar_ico_path = os.path.join(icons_dir, "yolo_detector_taskbar.ico")
    create_ico_file(os.path.join(icons_dir, "taskbar_icon_32.png"), taskbar_ico_path)
    
    print("\n🎨 图标生成完成!")
    print("=" * 50)
    print("📁 生成的文件:")
    print(f"  📂 图标目录: {icons_dir}/")
    print(f"  🖼️  主应用图标: yolo_detector_app.ico")
    print(f"  📌 任务栏图标: yolo_detector_taskbar.ico")
    print(f"  📄 PNG文件: {len(png_files)} 个")
    
    print("\n💡 使用方法:")
    print("  1. 将 yolo_detector_app.ico 设置为应用程序图标")
    print("  2. 将 yolo_detector_taskbar.ico 设置为任务栏图标")
    print("  3. PNG文件可用于文档、网页等场景")
    
    return True

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ 图标生成失败: {e}")
        import traceback
        traceback.print_exc()
