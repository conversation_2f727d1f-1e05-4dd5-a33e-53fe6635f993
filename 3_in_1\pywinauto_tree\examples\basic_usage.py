#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
增强控件拾取器基本使用示例

演示如何使用增强控件拾取器进行控件定位和操作。
"""

import asyncio
import logging
import sys
import os

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from enhanced_control_picker import EnhancedControlPicker, ControlContext
from smart_waiter import SmartWaiter, WaitContext, WaitCondition


async def basic_control_picking_example():
    """基本控件拾取示例"""
    print("🔍 基本控件拾取示例")
    print("=" * 50)
    
    # 创建增强控件拾取器
    picker = EnhancedControlPicker()
    
    try:
        # 在指定位置拾取控件
        print("正在拾取鼠标位置 (500, 300) 的控件...")
        result = await picker.pick_control_at_position(500, 300)
        
        if result:
            print("✅ 控件拾取成功!")
            print(f"   策略: {result['strategy']}")
            print(f"   置信度: {result['confidence']:.2f}")
            print(f"   执行时间: {result['execution_time']:.4f}s")
            print(f"   控件信息: {result['control_info']}")
        else:
            print("❌ 未找到控件")
    
    except Exception as e:
        print(f"❌ 控件拾取失败: {e}")
    
    # 显示性能统计
    stats = picker.get_performance_stats()
    print(f"\n📊 性能统计:")
    print(f"   总拾取次数: {stats['total_picks']}")
    print(f"   成功率: {stats['success_rate']:.2%}")
    print(f"   缓存命中率: {stats['cache_hit_rate']:.2%}")
    print(f"   平均响应时间: {stats['average_response_time']:.4f}s")


async def smart_waiting_example():
    """智能等待示例"""
    print("\n⏳ 智能等待示例")
    print("=" * 50)
    
    # 创建智能等待器
    waiter = SmartWaiter()
    
    # 模拟一个控件（实际使用中应该是真实的控件对象）
    from unittest.mock import Mock
    
    mock_control = Mock()
    mock_control.is_visible.return_value = True
    mock_control.is_enabled.return_value = True
    mock_control.exists.return_value = True
    
    try:
        # 等待控件可见
        print("等待控件可见...")
        context = WaitContext(
            control=mock_control,
            condition=WaitCondition.VISIBLE,
            timeout=5.0,
            app_type="win32"
        )
        
        result = await waiter.wait_for_condition(context)
        
        if result.success:
            print("✅ 控件已可见!")
            print(f"   等待时间: {result.elapsed_time:.4f}s")
            print(f"   检查次数: {result.check_count}")
        else:
            print("❌ 等待超时")
            print(f"   错误: {result.error}")
    
    except Exception as e:
        print(f"❌ 等待失败: {e}")


async def advanced_strategy_example():
    """高级策略使用示例"""
    print("\n🎯 高级策略使用示例")
    print("=" * 50)
    
    from enhanced_control_picker import (
        AutomationIdStrategy, CombinedStrategy, FuzzyTitleStrategy,
        StrategyManager
    )
    from unittest.mock import Mock
    
    # 创建策略管理器
    strategy_manager = StrategyManager()
    
    # 注册自定义策略
    strategy_manager.register_strategy(AutomationIdStrategy())
    strategy_manager.register_strategy(CombinedStrategy(['class_name', 'title']))
    strategy_manager.register_strategy(FuzzyTitleStrategy())
    
    # 创建模拟上下文
    context = ControlContext(
        hwnd=12345,
        app_type="win32",
        control_info={
            'automation_id': 'submit_button',
            'class_name': 'Button',
            'title': '提交'
        }
    )
    
    # 获取适用的策略
    strategies = strategy_manager.get_strategies_for_context(context)
    
    print(f"找到 {len(strategies)} 个适用策略:")
    for i, strategy in enumerate(strategies, 1):
        print(f"   {i}. {strategy.name} (优先级: {strategy.priority.value})")
        print(f"      成功率: {strategy.success_rate:.2%}")
    
    # 模拟策略执行
    mock_window = Mock()
    mock_control = Mock()
    mock_control.exists.return_value = True
    mock_window.child_window.return_value = mock_control
    
    print("\n执行策略:")
    for strategy in strategies[:2]:  # 只执行前两个策略
        try:
            result = await strategy.locate(mock_window, context)
            if result.success:
                print(f"✅ {strategy.name}: 成功 (置信度: {result.confidence:.2f})")
                break
            else:
                print(f"❌ {strategy.name}: 失败")
        except Exception as e:
            print(f"❌ {strategy.name}: 异常 - {e}")


async def error_handling_example():
    """错误处理示例"""
    print("\n🚨 错误处理示例")
    print("=" * 50)
    
    from enhanced_control_picker import DiagnosticEngine, ErrorType
    
    # 创建诊断引擎
    diagnostic_engine = DiagnosticEngine()
    
    # 模拟不同类型的错误
    errors = [
        RuntimeError("Control not found"),
        TimeoutError("Operation timed out"),
        PermissionError("Access denied"),
        ConnectionError("Network connection failed")
    ]
    
    context = ControlContext(
        hwnd=12345,
        app_type="electron",
        control_info={'automation_id': 'test_control'}
    )
    
    for error in errors:
        print(f"\n处理错误: {error}")
        
        # 生成诊断报告
        diagnosis = diagnostic_engine.diagnose(error, context)
        
        print(f"   错误类型: {diagnosis.error_type.value}")
        print(f"   建议:")
        for suggestion in diagnosis.suggestions:
            print(f"     - {suggestion}")
        print(f"   恢复选项:")
        for option in diagnosis.recovery_options:
            print(f"     - {option}")


async def caching_example():
    """缓存使用示例"""
    print("\n💾 缓存使用示例")
    print("=" * 50)
    
    from enhanced_control_picker import SmartCache
    
    # 创建缓存
    cache = SmartCache(max_size=100, default_ttl=60)
    
    # 创建测试上下文
    contexts = []
    for i in range(10):
        context = ControlContext(
            hwnd=12345 + i,
            app_type="win32",
            control_info={'automation_id': f'control_{i}'}
        )
        contexts.append(context)
    
    # 设置缓存数据
    print("设置缓存数据...")
    for i, context in enumerate(contexts):
        data = {
            'control': f'mock_control_{i}',
            'strategy': 'AutomationId',
            'confidence': 0.95,
            'timestamp': f'2024-01-01 10:{i:02d}:00'
        }
        cache.set(context, data)
    
    # 获取缓存数据
    print("获取缓存数据...")
    for i, context in enumerate(contexts[:5]):  # 只获取前5个
        cached_data = cache.get(context)
        if cached_data:
            print(f"✅ 缓存命中 {i}: {cached_data['control']}")
        else:
            print(f"❌ 缓存未命中 {i}")
    
    # 显示缓存统计
    stats = cache.get_stats()
    print(f"\n📊 缓存统计:")
    print(f"   大小: {stats['size']}/{stats['max_size']}")
    print(f"   命中率: {stats['hit_rate']:.2%}")


async def performance_monitoring_example():
    """性能监控示例"""
    print("\n📈 性能监控示例")
    print("=" * 50)
    
    picker = EnhancedControlPicker()
    
    # 模拟多次控件拾取操作
    print("执行多次控件拾取操作...")
    
    import time
    from unittest.mock import patch
    
    with patch('win32gui.WindowFromPoint', return_value=12345), \
         patch('win32gui.GetAncestor', return_value=12345), \
         patch('win32gui.GetWindowText', return_value="Test Window"), \
         patch('win32gui.GetClassName', return_value="TestClass"), \
         patch('win32gui.GetWindowThreadProcessId', return_value=(1, 1234)):
        
        # 执行10次拾取操作
        for i in range(10):
            try:
                result = await picker.pick_control_at_position(100 + i * 10, 200)
                if result:
                    print(f"   操作 {i+1}: ✅ 成功")
                else:
                    print(f"   操作 {i+1}: ❌ 失败")
            except Exception as e:
                print(f"   操作 {i+1}: ❌ 异常 - {e}")
            
            # 短暂延迟
            await asyncio.sleep(0.1)
    
    # 显示最终统计
    final_stats = picker.get_performance_stats()
    print(f"\n📊 最终性能统计:")
    print(f"   总操作数: {final_stats['total_picks']}")
    print(f"   成功操作数: {final_stats['successful_picks']}")
    print(f"   成功率: {final_stats['success_rate']:.2%}")
    print(f"   缓存命中数: {final_stats['cache_hits']}")
    print(f"   缓存命中率: {final_stats['cache_hit_rate']:.2%}")
    print(f"   平均响应时间: {final_stats['average_response_time']:.4f}s")


async def main():
    """主函数"""
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    print("🚀 增强控件拾取器使用示例")
    print("=" * 60)
    
    try:
        # 运行各种示例
        await basic_control_picking_example()
        await smart_waiting_example()
        await advanced_strategy_example()
        await error_handling_example()
        await caching_example()
        await performance_monitoring_example()
        
        print("\n🎉 所有示例执行完成!")
        
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断执行")
    except Exception as e:
        print(f"\n💥 执行过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # 运行示例
    asyncio.run(main())
