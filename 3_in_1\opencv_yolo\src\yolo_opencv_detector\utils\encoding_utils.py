# -*- coding: utf-8 -*-
"""
中文编码支持工具
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

import os
import sys
import locale
import codecs
from pathlib import Path
from typing import Optional, Union, List, Dict
import logging

# Handle optional chardet import
try:
    import chardet
except ImportError:
    chardet = None


class EncodingManager:
    """中文编码管理器"""
    
    def __init__(self):
        """初始化编码管理器"""
        self.logger = logging.getLogger(__name__)
        self._setup_encoding_environment()
    
    def _setup_encoding_environment(self) -> None:
        """设置编码环境"""
        try:
            # 设置Python默认编码
            if sys.version_info >= (3, 7):
                # Python 3.7+ 默认使用UTF-8
                pass
            else:
                # 为旧版本Python设置UTF-8
                import importlib
                importlib.reload(sys)
                sys.setdefaultencoding('utf-8')
            
            # 设置环境变量
            os.environ['PYTHONIOENCODING'] = 'utf-8'
            os.environ['LANG'] = 'zh_CN.UTF-8'
            os.environ['LC_ALL'] = 'zh_CN.UTF-8'
            
            # Windows特定设置
            if sys.platform == 'win32':
                # 设置控制台代码页为UTF-8
                try:
                    os.system('chcp 65001 >nul 2>&1')
                except:
                    pass
                
                # 设置Windows环境变量
                os.environ['PYTHONLEGACYWINDOWSSTDIO'] = '0'
            
            self.logger.info("中文编码环境设置完成")
            
        except Exception as e:
            self.logger.error(f"编码环境设置失败: {e}")
    
    def detect_file_encoding(self, file_path: Union[str, Path]) -> Optional[str]:
        """
        检测文件编码

        Args:
            file_path: 文件路径

        Returns:
            Optional[str]: 检测到的编码，如果检测失败返回None
        """
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                self.logger.warning(f"文件不存在: {file_path}")
                return None

            # If chardet is not available, try common encodings
            if chardet is None:
                common_encodings = ['utf-8', 'gbk', 'gb2312', 'ascii']
                for encoding in common_encodings:
                    try:
                        with open(file_path, 'r', encoding=encoding) as f:
                            f.read()
                        self.logger.debug(f"检测到文件编码: {file_path} -> {encoding}")
                        return encoding
                    except UnicodeDecodeError:
                        continue
                return 'utf-8'  # Default fallback

            with open(file_path, 'rb') as f:
                raw_data = f.read()
                result = chardet.detect(raw_data)

                if result and result['confidence'] > 0.7:
                    encoding = result['encoding']
                    self.logger.debug(f"检测到文件编码: {file_path} -> {encoding} (置信度: {result['confidence']:.2f})")
                    return encoding
                else:
                    self.logger.warning(f"无法可靠检测文件编码: {file_path}")
                    return 'utf-8'  # Default fallback

        except Exception as e:
            self.logger.error(f"文件编码检测失败 {file_path}: {e}")
            return 'utf-8'  # Default fallback
    
    def convert_file_encoding(self, file_path: Union[str, Path], 
                            target_encoding: str = 'utf-8',
                            source_encoding: Optional[str] = None,
                            backup: bool = True) -> bool:
        """
        转换文件编码
        
        Args:
            file_path: 文件路径
            target_encoding: 目标编码
            source_encoding: 源编码，如果为None则自动检测
            backup: 是否创建备份
            
        Returns:
            bool: 转换是否成功
        """
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                self.logger.error(f"文件不存在: {file_path}")
                return False
            
            # 检测源编码
            if source_encoding is None:
                source_encoding = self.detect_file_encoding(file_path)
                if source_encoding is None:
                    self.logger.error(f"无法检测源文件编码: {file_path}")
                    return False
            
            # 如果已经是目标编码，跳过转换
            if source_encoding.lower() == target_encoding.lower():
                self.logger.info(f"文件已经是目标编码: {file_path}")
                return True
            
            # 创建备份
            if backup:
                backup_path = file_path.with_suffix(file_path.suffix + '.bak')
                file_path.rename(backup_path)
                source_file = backup_path
            else:
                source_file = file_path
            
            # 读取源文件
            with open(source_file, 'r', encoding=source_encoding) as f:
                content = f.read()
            
            # 写入目标文件
            with open(file_path, 'w', encoding=target_encoding) as f:
                f.write(content)
            
            self.logger.info(f"文件编码转换成功: {file_path} ({source_encoding} -> {target_encoding})")
            return True
            
        except Exception as e:
            self.logger.error(f"文件编码转换失败 {file_path}: {e}")
            return False
    
    def validate_utf8_file(self, file_path: Union[str, Path]) -> bool:
        """
        验证文件是否为UTF-8编码
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 是否为UTF-8编码
        """
        try:
            file_path = Path(file_path)
            with open(file_path, 'r', encoding='utf-8') as f:
                f.read()
            return True
        except UnicodeDecodeError:
            return False
        except Exception as e:
            self.logger.error(f"UTF-8验证失败 {file_path}: {e}")
            return False
    
    def check_bom(self, file_path: Union[str, Path]) -> bool:
        """
        检查文件是否包含BOM标记
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 是否包含BOM
        """
        try:
            file_path = Path(file_path)
            with open(file_path, 'rb') as f:
                bom = f.read(3)
                return bom == codecs.BOM_UTF8
        except Exception as e:
            self.logger.error(f"BOM检查失败 {file_path}: {e}")
            return False
    
    def remove_bom(self, file_path: Union[str, Path], backup: bool = True) -> bool:
        """
        移除文件的BOM标记
        
        Args:
            file_path: 文件路径
            backup: 是否创建备份
            
        Returns:
            bool: 移除是否成功
        """
        try:
            file_path = Path(file_path)
            
            if not self.check_bom(file_path):
                self.logger.info(f"文件不包含BOM: {file_path}")
                return True
            
            # 创建备份
            if backup:
                backup_path = file_path.with_suffix(file_path.suffix + '.bom_bak')
                with open(file_path, 'rb') as src, open(backup_path, 'wb') as dst:
                    dst.write(src.read())
            
            # 读取文件内容（跳过BOM）
            with open(file_path, 'r', encoding='utf-8-sig') as f:
                content = f.read()
            
            # 写回文件（不包含BOM）
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            self.logger.info(f"BOM移除成功: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"BOM移除失败 {file_path}: {e}")
            return False
    
    def safe_path_handling(self, path: Union[str, Path]) -> Path:
        """
        安全的路径处理，确保中文路径正确处理
        
        Args:
            path: 路径字符串或Path对象
            
        Returns:
            Path: 处理后的Path对象
        """
        try:
            if isinstance(path, str):
                # 确保字符串是UTF-8编码
                if isinstance(path, bytes):
                    path = path.decode('utf-8')
                path = Path(path)
            
            # 规范化路径
            path = path.resolve()
            
            return path
            
        except Exception as e:
            self.logger.error(f"路径处理失败 {path}: {e}")
            raise
    
    def validate_chinese_string(self, text: str) -> bool:
        """
        验证中文字符串是否正确编码
        
        Args:
            text: 待验证的文本
            
        Returns:
            bool: 是否正确编码
        """
        try:
            # 尝试编码和解码
            encoded = text.encode('utf-8')
            decoded = encoded.decode('utf-8')
            return decoded == text
        except Exception:
            return False
    
    def batch_convert_files(self, directory: Union[str, Path], 
                          file_patterns: List[str] = None,
                          target_encoding: str = 'utf-8',
                          remove_bom: bool = True) -> Dict[str, bool]:
        """
        批量转换目录中的文件编码
        
        Args:
            directory: 目录路径
            file_patterns: 文件模式列表，如['*.py', '*.txt']
            target_encoding: 目标编码
            remove_bom: 是否移除BOM
            
        Returns:
            Dict[str, bool]: 文件路径到转换结果的映射
        """
        results = {}
        
        try:
            directory = Path(directory)
            if not directory.exists():
                self.logger.error(f"目录不存在: {directory}")
                return results
            
            # 默认文件模式
            if file_patterns is None:
                file_patterns = ['*.py', '*.txt', '*.md', '*.yaml', '*.yml', '*.json']
            
            # 收集所有匹配的文件
            files = []
            for pattern in file_patterns:
                files.extend(directory.rglob(pattern))
            
            self.logger.info(f"找到 {len(files)} 个文件需要处理")
            
            # 处理每个文件
            for file_path in files:
                try:
                    # 转换编码
                    success = self.convert_file_encoding(file_path, target_encoding)
                    
                    # 移除BOM
                    if success and remove_bom:
                        success = self.remove_bom(file_path)
                    
                    results[str(file_path)] = success
                    
                except Exception as e:
                    self.logger.error(f"处理文件失败 {file_path}: {e}")
                    results[str(file_path)] = False
            
            success_count = sum(results.values())
            self.logger.info(f"批量转换完成: {success_count}/{len(files)} 个文件成功")
            
        except Exception as e:
            self.logger.error(f"批量转换失败: {e}")
        
        return results


# 全局编码管理器实例
encoding_manager = EncodingManager()


def ensure_utf8_encoding():
    """确保UTF-8编码环境"""
    encoding_manager._setup_encoding_environment()


def safe_open(file_path: Union[str, Path], mode: str = 'r', 
              encoding: str = 'utf-8', **kwargs):
    """
    安全的文件打开函数，自动处理编码问题
    
    Args:
        file_path: 文件路径
        mode: 打开模式
        encoding: 编码格式
        **kwargs: 其他参数
        
    Returns:
        文件对象
    """
    file_path = encoding_manager.safe_path_handling(file_path)
    
    # 如果是读取模式且文件存在，先检测编码
    if 'r' in mode and file_path.exists():
        detected_encoding = encoding_manager.detect_file_encoding(file_path)
        if detected_encoding and detected_encoding.lower() != encoding.lower():
            encoding = detected_encoding
    
    return open(file_path, mode, encoding=encoding, **kwargs)
