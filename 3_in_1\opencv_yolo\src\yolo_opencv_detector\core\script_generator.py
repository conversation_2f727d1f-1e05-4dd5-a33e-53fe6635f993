# -*- coding: utf-8 -*-
"""
脚本生成器模块
本文件由AI辅助生成 - Generated with AI Assistance
生成时间: 2025-01-27 CST
AI模型: Claude-4-Sonnet
审核状态: 待人工安全审查
编码标准: UTF-8无BOM
"""

import time
from pathlib import Path
from typing import List, Dict, Any, Optional, Union
from dataclasses import dataclass
import json

from ..utils.logger import Logger
from ..utils.data_structures import DetectionResult, BoundingBox


@dataclass
class ScriptAction:
    """脚本动作数据类"""
    action_type: str  # "click", "move", "key", "wait", "scroll"
    target: Optional[BoundingBox] = None
    coordinates: Optional[tuple] = None
    key: Optional[str] = None
    duration: float = 0.0
    description: str = ""
    timestamp: float = 0.0


class ScriptGenerator:
    """脚本生成器类"""
    
    def __init__(self):
        """初始化脚本生成器"""
        self.logger = Logger().get_logger(__name__)
        
        # 脚本记录
        self.actions: List[ScriptAction] = []
        self.recording = False
        self.start_time = 0.0
        
        # 配置
        self.auto_wait = True
        self.min_wait_time = 0.1
        self.max_wait_time = 5.0
        self.click_offset = (0, 0)  # 点击偏移
        
        self.logger.info("脚本生成器初始化完成")
    
    def start_recording(self) -> None:
        """开始录制脚本"""
        self.recording = True
        self.start_time = time.time()
        self.actions.clear()
        
        self.logger.info("开始录制脚本")
    
    def stop_recording(self) -> None:
        """停止录制脚本"""
        self.recording = False
        
        self.logger.info(f"停止录制脚本，共记录 {len(self.actions)} 个动作")
    
    def add_click_action(self, 
                        detection_result: DetectionResult,
                        description: str = "") -> None:
        """
        添加点击动作
        
        Args:
            detection_result: 检测结果
            description: 动作描述
        """
        if not self.recording:
            return
        
        # 计算点击坐标（中心点 + 偏移）
        center_x = detection_result.bbox.center_x + self.click_offset[0]
        center_y = detection_result.bbox.center_y + self.click_offset[1]
        
        action = ScriptAction(
            action_type="click",
            target=detection_result.bbox,
            coordinates=(center_x, center_y),
            description=description or f"点击 {detection_result.class_name or '目标'}",
            timestamp=time.time() - self.start_time
        )
        
        self.actions.append(action)
        self.logger.debug(f"添加点击动作: {action.description}")
    
    def add_move_action(self, 
                       detection_result: DetectionResult,
                       description: str = "") -> None:
        """
        添加移动动作
        
        Args:
            detection_result: 检测结果
            description: 动作描述
        """
        if not self.recording:
            return
        
        center_x = detection_result.bbox.center_x
        center_y = detection_result.bbox.center_y
        
        action = ScriptAction(
            action_type="move",
            target=detection_result.bbox,
            coordinates=(center_x, center_y),
            description=description or f"移动到 {detection_result.class_name or '目标'}",
            timestamp=time.time() - self.start_time
        )
        
        self.actions.append(action)
        self.logger.debug(f"添加移动动作: {action.description}")
    
    def add_key_action(self, 
                      key: str,
                      description: str = "") -> None:
        """
        添加按键动作
        
        Args:
            key: 按键
            description: 动作描述
        """
        if not self.recording:
            return
        
        action = ScriptAction(
            action_type="key",
            key=key,
            description=description or f"按键 {key}",
            timestamp=time.time() - self.start_time
        )
        
        self.actions.append(action)
        self.logger.debug(f"添加按键动作: {action.description}")
    
    def add_wait_action(self, 
                       duration: float,
                       description: str = "") -> None:
        """
        添加等待动作
        
        Args:
            duration: 等待时长（秒）
            description: 动作描述
        """
        if not self.recording:
            return
        
        action = ScriptAction(
            action_type="wait",
            duration=duration,
            description=description or f"等待 {duration:.1f}秒",
            timestamp=time.time() - self.start_time
        )
        
        self.actions.append(action)
        self.logger.debug(f"添加等待动作: {action.description}")
    
    def add_scroll_action(self, 
                         direction: str,
                         amount: int = 3,
                         coordinates: Optional[tuple] = None,
                         description: str = "") -> None:
        """
        添加滚动动作
        
        Args:
            direction: 滚动方向 ("up", "down", "left", "right")
            amount: 滚动量
            coordinates: 滚动位置
            description: 动作描述
        """
        if not self.recording:
            return
        
        action = ScriptAction(
            action_type="scroll",
            coordinates=coordinates,
            key=f"{direction}:{amount}",
            description=description or f"向{direction}滚动{amount}次",
            timestamp=time.time() - self.start_time
        )
        
        self.actions.append(action)
        self.logger.debug(f"添加滚动动作: {action.description}")
    
    def generate_python_script(self, 
                              output_path: Optional[Union[str, Path]] = None) -> str:
        """
        生成Python脚本
        
        Args:
            output_path: 输出文件路径
            
        Returns:
            str: 生成的脚本内容
        """
        script_lines = [
            "# -*- coding: utf-8 -*-",
            '"""',
            "自动生成的操作脚本",
            f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}",
            f"动作数量: {len(self.actions)}",
            '"""',
            "",
            "import time",
            "import pyautogui",
            "",
            "# 设置PyAutoGUI安全设置",
            "pyautogui.FAILSAFE = True",
            "pyautogui.PAUSE = 0.1",
            "",
            "def main():",
            '    """执行自动化脚本"""',
            '    print("开始执行自动化脚本...")',
            ""
        ]
        
        for i, action in enumerate(self.actions):
            script_lines.append(f"    # 动作 {i+1}: {action.description}")
            
            if action.action_type == "click":
                script_lines.append(f"    pyautogui.click({action.coordinates[0]}, {action.coordinates[1]})")
            
            elif action.action_type == "move":
                script_lines.append(f"    pyautogui.moveTo({action.coordinates[0]}, {action.coordinates[1]})")
            
            elif action.action_type == "key":
                script_lines.append(f"    pyautogui.press('{action.key}')")
            
            elif action.action_type == "wait":
                script_lines.append(f"    time.sleep({action.duration})")
            
            elif action.action_type == "scroll":
                direction, amount = action.key.split(":")
                if direction in ["up", "down"]:
                    scroll_amount = int(amount) if direction == "up" else -int(amount)
                    if action.coordinates:
                        script_lines.append(f"    pyautogui.scroll({scroll_amount}, x={action.coordinates[0]}, y={action.coordinates[1]})")
                    else:
                        script_lines.append(f"    pyautogui.scroll({scroll_amount})")
            
            # 添加自动等待
            if self.auto_wait and i < len(self.actions) - 1:
                next_action = self.actions[i + 1]
                wait_time = max(self.min_wait_time, 
                              min(self.max_wait_time, next_action.timestamp - action.timestamp))
                if wait_time > 0.05:  # 只有等待时间大于50ms才添加
                    script_lines.append(f"    time.sleep({wait_time:.2f})")
            
            script_lines.append("")
        
        script_lines.extend([
            '    print("脚本执行完成")',
            "",
            "",
            'if __name__ == "__main__":',
            "    main()"
        ])
        
        script_content = "\n".join(script_lines)
        
        # 保存到文件
        if output_path:
            output_path = Path(output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(script_content)
            
            self.logger.info(f"Python脚本已保存到: {output_path}")
        
        return script_content
    
    def generate_json_script(self, 
                            output_path: Optional[Union[str, Path]] = None) -> str:
        """
        生成JSON格式脚本
        
        Args:
            output_path: 输出文件路径
            
        Returns:
            str: 生成的JSON内容
        """
        script_data = {
            "metadata": {
                "generated_time": time.strftime('%Y-%m-%d %H:%M:%S'),
                "action_count": len(self.actions),
                "total_duration": self.actions[-1].timestamp if self.actions else 0,
                "generator": "YOLO OpenCV Detector Script Generator"
            },
            "actions": []
        }
        
        for action in self.actions:
            action_data = {
                "type": action.action_type,
                "description": action.description,
                "timestamp": action.timestamp
            }
            
            if action.coordinates:
                action_data["coordinates"] = action.coordinates
            
            if action.target:
                action_data["target"] = {
                    "x": action.target.x,
                    "y": action.target.y,
                    "width": action.target.width,
                    "height": action.target.height
                }
            
            if action.key:
                action_data["key"] = action.key
            
            if action.duration > 0:
                action_data["duration"] = action.duration
            
            script_data["actions"].append(action_data)
        
        json_content = json.dumps(script_data, ensure_ascii=False, indent=2)
        
        # 保存到文件
        if output_path:
            output_path = Path(output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(json_content)
            
            self.logger.info(f"JSON脚本已保存到: {output_path}")
        
        return json_content
    
    def load_json_script(self, script_path: Union[str, Path]) -> bool:
        """
        从JSON文件加载脚本
        
        Args:
            script_path: 脚本文件路径
            
        Returns:
            bool: 是否成功加载
        """
        try:
            script_path = Path(script_path)
            if not script_path.exists():
                self.logger.error(f"脚本文件不存在: {script_path}")
                return False
            
            with open(script_path, 'r', encoding='utf-8') as f:
                script_data = json.load(f)
            
            self.actions.clear()
            
            for action_data in script_data.get("actions", []):
                action = ScriptAction(
                    action_type=action_data["type"],
                    description=action_data.get("description", ""),
                    timestamp=action_data.get("timestamp", 0.0)
                )
                
                if "coordinates" in action_data:
                    action.coordinates = tuple(action_data["coordinates"])
                
                if "target" in action_data:
                    target_data = action_data["target"]
                    action.target = BoundingBox(
                        x=target_data["x"],
                        y=target_data["y"],
                        width=target_data["width"],
                        height=target_data["height"]
                    )
                
                if "key" in action_data:
                    action.key = action_data["key"]
                
                if "duration" in action_data:
                    action.duration = action_data["duration"]
                
                self.actions.append(action)
            
            self.logger.info(f"脚本加载成功: {len(self.actions)} 个动作")
            return True
            
        except Exception as e:
            self.logger.error(f"加载脚本失败: {e}")
            return False
    
    def clear_actions(self) -> None:
        """清空所有动作"""
        self.actions.clear()
        self.logger.info("已清空所有动作")
    
    def get_action_summary(self) -> Dict[str, Any]:
        """获取动作摘要"""
        if not self.actions:
            return {"total": 0}
        
        action_types = {}
        for action in self.actions:
            action_types[action.action_type] = action_types.get(action.action_type, 0) + 1
        
        return {
            "total": len(self.actions),
            "duration": self.actions[-1].timestamp if self.actions else 0,
            "action_types": action_types,
            "first_action": self.actions[0].description if self.actions else None,
            "last_action": self.actions[-1].description if self.actions else None
        }
    
    def optimize_script(self) -> None:
        """优化脚本（移除重复动作、合并相似动作等）"""
        if not self.actions:
            return
        
        optimized_actions = []
        
        for i, action in enumerate(self.actions):
            # 跳过过短的等待
            if action.action_type == "wait" and action.duration < 0.05:
                continue
            
            # 合并连续的相同位置点击
            if (action.action_type == "click" and 
                optimized_actions and 
                optimized_actions[-1].action_type == "click" and
                optimized_actions[-1].coordinates == action.coordinates):
                continue
            
            optimized_actions.append(action)
        
        original_count = len(self.actions)
        self.actions = optimized_actions
        
        self.logger.info(f"脚本优化完成: {original_count} -> {len(self.actions)} 动作")
    
    def set_click_offset(self, x_offset: int, y_offset: int) -> None:
        """
        设置点击偏移
        
        Args:
            x_offset: X轴偏移
            y_offset: Y轴偏移
        """
        self.click_offset = (x_offset, y_offset)
        self.logger.info(f"点击偏移已设置: {self.click_offset}")
    
    def configure_auto_wait(self, 
                           enabled: bool = True,
                           min_time: float = 0.1,
                           max_time: float = 5.0) -> None:
        """
        配置自动等待
        
        Args:
            enabled: 是否启用自动等待
            min_time: 最小等待时间
            max_time: 最大等待时间
        """
        self.auto_wait = enabled
        self.min_wait_time = min_time
        self.max_wait_time = max_time
        
        self.logger.info(f"自动等待配置: enabled={enabled}, min={min_time}s, max={max_time}s")
