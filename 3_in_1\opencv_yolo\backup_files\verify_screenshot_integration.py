# -*- coding: utf-8 -*-
"""
截图功能整合代码结构验证
作者: Cursor AI
创建时间: 2025-07-05
编码标准: UTF-8无BOM
"""

import sys
import os
import inspect
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

def verify_detection_panel():
    """验证检测面板的截图功能"""
    print("=== 验证检测面板截图功能 ===")
    
    try:
        from yolo_opencv_detector.gui.widgets.detection_panel_v2 import DetectionPanelV2
        
        # 检查类是否存在
        print("✅ DetectionPanelV2 类导入成功")
        
        # 检查方法是否存在
        methods = [method for method in dir(DetectionPanelV2) if not method.startswith('__')]
        
        required_methods = ['_take_detection_screenshot']
        missing_methods = []
        
        for method in required_methods:
            if method in methods:
                print(f"✅ 方法存在: {method}")
            else:
                print(f"❌ 方法缺失: {method}")
                missing_methods.append(method)
        
        # 检查方法签名
        if hasattr(DetectionPanelV2, '_take_detection_screenshot'):
            method = getattr(DetectionPanelV2, '_take_detection_screenshot')
            sig = inspect.signature(method)
            print(f"✅ 方法签名: _take_detection_screenshot{sig}")
        
        return len(missing_methods) == 0
        
    except Exception as e:
        print(f"❌ 检测面板验证失败: {e}")
        return False

def verify_template_panel():
    """验证模板面板的截图功能"""
    print("\n=== 验证模板面板截图功能 ===")
    
    try:
        from yolo_opencv_detector.gui.widgets.template_panel_v2 import TemplatePanelV2
        
        # 检查类是否存在
        print("✅ TemplatePanelV2 类导入成功")
        
        # 检查方法是否存在
        methods = [method for method in dir(TemplatePanelV2) if not method.startswith('__')]
        
        required_methods = ['_capture_template']
        missing_methods = []
        
        for method in required_methods:
            if method in methods:
                print(f"✅ 方法存在: {method}")
            else:
                print(f"❌ 方法缺失: {method}")
                missing_methods.append(method)
        
        # 检查方法签名
        if hasattr(TemplatePanelV2, '_capture_template'):
            method = getattr(TemplatePanelV2, '_capture_template')
            sig = inspect.signature(method)
            print(f"✅ 方法签名: _capture_template{sig}")
        
        return len(missing_methods) == 0
        
    except Exception as e:
        print(f"❌ 模板面板验证失败: {e}")
        return False

def verify_main_window():
    """验证主窗口的截图功能"""
    print("\n=== 验证主窗口截图功能 ===")
    
    try:
        from yolo_opencv_detector.gui.main_window_v2 import MainWindowV2
        
        # 检查类是否存在
        print("✅ MainWindowV2 类导入成功")
        
        # 检查方法是否存在
        methods = [method for method in dir(MainWindowV2) if not method.startswith('__')]
        
        required_methods = ['_open_screenshot_tool', '_on_screenshot_saved', '_on_template_created', '_on_region_extracted']
        missing_methods = []
        
        for method in required_methods:
            if method in methods:
                print(f"✅ 方法存在: {method}")
            else:
                print(f"❌ 方法缺失: {method}")
                missing_methods.append(method)
        
        # 检查方法签名
        for method_name in required_methods:
            if hasattr(MainWindowV2, method_name):
                method = getattr(MainWindowV2, method_name)
                sig = inspect.signature(method)
                print(f"✅ 方法签名: {method_name}{sig}")
        
        return len(missing_methods) == 0
        
    except Exception as e:
        print(f"❌ 主窗口验证失败: {e}")
        return False

def verify_screenshot_preview_dialog():
    """验证截图预览对话框"""
    print("\n=== 验证截图预览对话框 ===")
    
    try:
        from yolo_opencv_detector.gui.screenshot_preview_dialog import ScreenshotPreviewDialog, RegionSelectionWidget
        
        # 检查类是否存在
        print("✅ ScreenshotPreviewDialog 类导入成功")
        print("✅ RegionSelectionWidget 类导入成功")
        
        # 检查ScreenshotPreviewDialog的方法
        methods = [method for method in dir(ScreenshotPreviewDialog) if not method.startswith('__')]
        
        required_methods = ['_init_ui', '_init_connections', '_on_region_selected', '_create_template']
        missing_methods = []
        
        for method in required_methods:
            if method in methods:
                print(f"✅ ScreenshotPreviewDialog方法存在: {method}")
            else:
                print(f"❌ ScreenshotPreviewDialog方法缺失: {method}")
                missing_methods.append(method)
        
        # 检查RegionSelectionWidget的方法
        region_methods = [method for method in dir(RegionSelectionWidget) if not method.startswith('__')]
        
        required_region_methods = ['set_screenshot', 'mousePressEvent', 'mouseMoveEvent', 'mouseReleaseEvent']
        
        for method in required_region_methods:
            if method in region_methods:
                print(f"✅ RegionSelectionWidget方法存在: {method}")
            else:
                print(f"❌ RegionSelectionWidget方法缺失: {method}")
                missing_methods.append(method)
        
        return len(missing_methods) == 0
        
    except Exception as e:
        print(f"❌ 截图预览对话框验证失败: {e}")
        return False

def verify_screenshot_widget():
    """验证截图显示组件"""
    print("\n=== 验证截图显示组件 ===")
    
    try:
        from yolo_opencv_detector.gui.widgets.screenshot_widget import ScreenshotWidget, ScreenshotLabel
        
        # 检查类是否存在
        print("✅ ScreenshotWidget 类导入成功")
        print("✅ ScreenshotLabel 类导入成功")
        
        # 检查ScreenshotWidget的方法
        methods = [method for method in dir(ScreenshotWidget) if not method.startswith('__')]
        
        required_methods = ['set_screenshot', 'set_detection_results', 'zoom_in', 'zoom_out', 'reset_zoom', 'fit_to_window']
        missing_methods = []
        
        for method in required_methods:
            if method in methods:
                print(f"✅ ScreenshotWidget方法存在: {method}")
            else:
                print(f"❌ ScreenshotWidget方法缺失: {method}")
                missing_methods.append(method)
        
        # 检查ScreenshotLabel的方法
        label_methods = [method for method in dir(ScreenshotLabel) if not method.startswith('__')]
        
        required_label_methods = ['set_screenshot', 'set_detection_results', '_update_display']
        
        for method in required_label_methods:
            if method in label_methods:
                print(f"✅ ScreenshotLabel方法存在: {method}")
            else:
                print(f"❌ ScreenshotLabel方法缺失: {method}")
                missing_methods.append(method)
        
        return len(missing_methods) == 0
        
    except Exception as e:
        print(f"❌ 截图显示组件验证失败: {e}")
        return False

def verify_screenshot_helper():
    """验证截图辅助工具"""
    print("\n=== 验证截图辅助工具 ===")
    
    try:
        from yolo_opencv_detector.utils.screenshot_helper import get_screenshot_helper, ScreenshotHelper
        
        # 检查函数和类是否存在
        print("✅ get_screenshot_helper 函数导入成功")
        print("✅ ScreenshotHelper 类导入成功")
        
        # 检查ScreenshotHelper的方法
        methods = [method for method in dir(ScreenshotHelper) if not method.startswith('__')]
        
        required_methods = ['is_available', 'take_screenshot', 'take_screenshot_with_pixmap', 'numpy_to_qpixmap']
        missing_methods = []
        
        for method in required_methods:
            if method in methods:
                print(f"✅ ScreenshotHelper方法存在: {method}")
            else:
                print(f"❌ ScreenshotHelper方法缺失: {method}")
                missing_methods.append(method)
        
        return len(missing_methods) == 0
        
    except Exception as e:
        print(f"❌ 截图辅助工具验证失败: {e}")
        return False

def main():
    """主验证函数"""
    print("🔍 YOLO OpenCV检测器截图功能整合代码结构验证")
    print("=" * 60)
    
    verification_results = []
    
    # 运行各项验证
    verification_results.append(("检测面板", verify_detection_panel()))
    verification_results.append(("模板面板", verify_template_panel()))
    verification_results.append(("主窗口", verify_main_window()))
    verification_results.append(("截图预览对话框", verify_screenshot_preview_dialog()))
    verification_results.append(("截图显示组件", verify_screenshot_widget()))
    verification_results.append(("截图辅助工具", verify_screenshot_helper()))
    
    # 输出验证结果
    print("\n" + "=" * 60)
    print("📊 代码结构验证结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(verification_results)
    
    for component_name, result in verification_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{component_name:<20} {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"总计: {passed}/{total} 项验证通过")
    
    if passed == total:
        print("🎉 所有代码结构验证通过！截图功能整合代码正确！")
        return True
    else:
        print("⚠️ 部分代码结构验证失败，请检查相关代码")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
