# -*- coding: utf-8 -*-
"""
重构的模板面板 - 简化布局避免重叠
作者: Cursor AI
创建时间: 2025-07-05
编码标准: UTF-8无BOM
"""

from typing import List, Dict, Any, Optional
from pathlib import Path
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, QListWidget,
    QListWidgetItem, QPushButton, QLabel, QLineEdit, QComboBox,
    QTextEdit, QFileDialog, QMessageBox, QDialog
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QPixmap, QFont, QImage

from ...utils.logger import Logger
from ...utils.config_manager import ConfigManager


class TemplatePanelV2(QWidget):
    """重构的模板面板类 - 简化布局"""
    
    # 信号定义
    template_selected = pyqtSignal(dict)
    template_changed = pyqtSignal()
    
    def __init__(self, config_manager: ConfigManager):
        super().__init__()
        self.config_manager = config_manager
        self.logger = Logger()
        
        # 设置固定尺寸避免布局问题
        self.setMinimumWidth(260)
        self.setMaximumWidth(320)
        
        # 模板数据
        self.templates = []
        self.current_template = None

        # 存储当前预览的模板，用于窗口大小变化时重新渲染
        self._current_template_for_preview = None

        self._init_ui()
        self._load_templates()

        self.logger.info("重构模板面板初始化完成")
    
    def _init_ui(self) -> None:
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("📋 模板管理")
        title_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # 模板列表组
        self._create_template_list_group(layout)
        
        # 模板操作组
        self._create_template_actions_group(layout)
        
        # 模板信息组
        self._create_template_info_group(layout)
        
        # 添加弹性空间
        layout.addStretch()
    
    def _create_template_list_group(self, parent_layout: QVBoxLayout) -> None:
        """创建模板列表组"""
        group = QGroupBox("📁 模板列表")
        group.setMaximumHeight(280)  # 增加高度以容纳预览
        layout = QVBoxLayout(group)
        layout.setSpacing(8)  # 增加间距
        layout.setContentsMargins(12, 12, 12, 12)  # 增加内边距

        # 创建水平布局：列表 + 预览
        content_layout = QHBoxLayout()
        content_layout.setSpacing(10)

        # 左侧：模板列表
        list_layout = QVBoxLayout()

        self.template_list = QListWidget()
        self.template_list.setMaximumHeight(200)  # 增加列表高度
        self.template_list.setMinimumHeight(200)  # 设置最小高度
        self.template_list.setMaximumWidth(200)   # 限制列表宽度
        self.template_list.itemClicked.connect(self._on_template_selected)
        self.template_list.setStyleSheet("""
            QListWidget {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                background-color: white;
                selection-background-color: #3498db;
                selection-color: white;
                font-size: 12px;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QListWidget::item:hover {
                background-color: #f8f9fa;
            }
            QListWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
        """)
        list_layout.addWidget(self.template_list)

        # 模板数量标签
        self.template_count_label = QLabel("模板数量: 0")
        self.template_count_label.setMinimumHeight(20)
        self.template_count_label.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                font-style: italic;
                font-size: 11px;
                padding: 4px;
                background-color: #f8f9fa;
                border-radius: 3px;
            }
        """)
        list_layout.addWidget(self.template_count_label)

        content_layout.addLayout(list_layout)

        # 右侧：图片预览
        self._create_template_preview(content_layout)

        layout.addLayout(content_layout)
        parent_layout.addWidget(group)

    def _create_template_preview(self, parent_layout: QHBoxLayout) -> None:
        """创建模板预览组件"""
        preview_layout = QVBoxLayout()

        # 预览标题
        preview_title = QLabel("🖼️ 模板预览")
        preview_title.setStyleSheet("""
            QLabel {
                font-weight: bold;
                font-size: 12px;
                color: #2c3e50;
                padding: 4px;
            }
        """)
        preview_layout.addWidget(preview_title)

        # 图片预览标签（适应窗口大小）
        self.preview_label = QLabel()
        self.preview_label.setMinimumSize(200, 150)  # 保持最小尺寸
        # 移除最大尺寸限制，允许自适应
        self.preview_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.preview_label.setScaledContents(False)  # 不自动缩放内容，我们手动控制
        self.preview_label.setStyleSheet("""
            QLabel {
                border: 2px dashed #bdc3c7;
                border-radius: 6px;
                background-color: #f8f9fa;
                color: #7f8c8d;
                font-size: 11px;
            }
        """)
        self.preview_label.setText("选择模板查看预览")
        preview_layout.addWidget(self.preview_label)

        # 预览信息
        self.preview_info = QLabel()
        self.preview_info.setStyleSheet("""
            QLabel {
                font-size: 10px;
                color: #7f8c8d;
                padding: 4px;
                background-color: #ecf0f1;
                border-radius: 3px;
            }
        """)
        self.preview_info.setText("尺寸: - | 文件: -")
        preview_layout.addWidget(self.preview_info)

        parent_layout.addLayout(preview_layout)

    def _create_template_actions_group(self, parent_layout: QVBoxLayout) -> None:
        """创建模板操作组"""
        group = QGroupBox("🛠️ 模板操作")
        group.setMaximumHeight(140)  # 增加高度
        layout = QVBoxLayout(group)
        layout.setSpacing(10)  # 增加间距
        layout.setContentsMargins(12, 12, 12, 12)  # 增加内边距

        # 第一行按钮
        row1_layout = QHBoxLayout()
        row1_layout.setSpacing(8)  # 按钮间距

        # 添加模板按钮
        self.add_button = QPushButton("📁 添加模板")
        self.add_button.setMinimumHeight(35)  # 增加按钮高度
        self.add_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
                padding: 6px 12px;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
        """)
        self.add_button.clicked.connect(self._add_template)
        row1_layout.addWidget(self.add_button)

        # 截取模板按钮
        self.capture_button = QPushButton("📷 截取模板")
        self.capture_button.setMinimumHeight(35)  # 增加按钮高度
        self.capture_button.setToolTip(
            "📷 模板专用截图工具\n\n"
            "🎯 专门功能：\n"
            "• 🏷️ 模板创建：从屏幕区域创建精确匹配模板\n"
            "• ✂️ 区域选择：拖拽选择要制作模板的区域\n"
            "• 📝 信息输入：添加模板名称和描述信息\n"
            "• 💾 自动保存：模板自动保存到模板库中\n"
            "• 🔄 即时更新：新模板立即显示在模板列表\n\n"
            "💡 最佳场景：\n"
            "• 创建游戏界面元素的匹配模板\n"
            "• 制作应用程序按钮和图标模板\n"
            "• 建立UI自动化测试的参考模板\n"
            "• 构建图像识别的标准样本库\n\n"
            "⚡ 特点：专注模板创建，流程简化高效"
        )
        self.capture_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
                padding: 6px 12px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        self.capture_button.clicked.connect(self._capture_template)
        row1_layout.addWidget(self.capture_button)

        layout.addLayout(row1_layout)

        # 第二行按钮
        row2_layout = QHBoxLayout()
        row2_layout.setSpacing(8)  # 按钮间距

        # 编辑按钮
        self.edit_button = QPushButton("✏️ 编辑模板")
        self.edit_button.setMinimumHeight(35)  # 增加按钮高度
        self.edit_button.setEnabled(False)
        self.edit_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
                padding: 6px 12px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
            QPushButton:disabled {
                background-color: #95a5a6;
            }
        """)
        self.edit_button.clicked.connect(self._edit_template)
        row2_layout.addWidget(self.edit_button)

        # 删除按钮
        self.delete_button = QPushButton("🗑️ 删除模板")
        self.delete_button.setMinimumHeight(35)  # 增加按钮高度
        self.delete_button.setEnabled(False)
        self.delete_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
                padding: 6px 12px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:disabled {
                background-color: #95a5a6;
            }
        """)
        self.delete_button.clicked.connect(self._delete_template)
        row2_layout.addWidget(self.delete_button)

        layout.addLayout(row2_layout)

        parent_layout.addWidget(group)
    
    def _create_template_info_group(self, parent_layout: QVBoxLayout) -> None:
        """创建模板信息组"""
        group = QGroupBox("ℹ️ 模板信息")
        group.setMaximumHeight(160)  # 增加高度
        layout = QVBoxLayout(group)
        layout.setSpacing(8)  # 增加间距
        layout.setContentsMargins(12, 12, 12, 12)  # 增加内边距

        # 名称
        name_layout = QHBoxLayout()
        name_layout.setSpacing(8)
        name_label = QLabel("名称:")
        name_label.setMinimumWidth(40)
        name_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 12px;")
        name_label.setToolTip("模板的名称标识\n"
                             "用于在检测时识别和区分不同模板\n"
                             "建议使用有意义的描述性名称")
        name_layout.addWidget(name_label)

        self.name_edit = QLineEdit()
        self.name_edit.setReadOnly(True)
        self.name_edit.setMinimumHeight(28)  # 增加高度
        self.name_edit.setToolTip("显示当前选中模板的名称\n"
                                 "创建模板时可以自定义名称\n"
                                 "名称将用于检测结果的标识")
        self.name_edit.setStyleSheet("""
            QLineEdit {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                padding: 6px 8px;
                background-color: #f8f9fa;
                font-size: 12px;
            }
        """)
        name_layout.addWidget(self.name_edit)
        layout.addLayout(name_layout)

        # 类别
        category_layout = QHBoxLayout()
        category_layout.setSpacing(8)
        category_label = QLabel("类别:")
        category_label.setMinimumWidth(40)
        category_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 12px;")
        category_label.setToolTip("模板的分类类型\n"
                                 "用于组织和管理不同类型的模板\n"
                                 "可选：通用、按钮、图标、文本、其他")
        category_layout.addWidget(category_label)

        self.category_combo = QComboBox()
        self.category_combo.addItems(["通用", "按钮", "图标", "文本", "其他"])
        self.category_combo.setEnabled(False)
        self.category_combo.setMinimumHeight(28)  # 增加高度
        self.category_combo.setToolTip("模板分类选择\n"
                                      "通用：一般用途模板\n"
                                      "按钮：界面按钮元素\n"
                                      "图标：小图标或符号\n"
                                      "文本：文字内容\n"
                                      "其他：特殊用途模板")
        self.category_combo.setStyleSheet("""
            QComboBox {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                padding: 6px 8px;
                background-color: #f8f9fa;
                font-size: 12px;
            }
        """)
        category_layout.addWidget(self.category_combo)
        layout.addLayout(category_layout)

        # 描述
        desc_layout = QVBoxLayout()
        desc_layout.setSpacing(5)
        desc_label = QLabel("描述:")
        desc_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 12px;")
        desc_label.setToolTip("模板的详细描述信息\n"
                             "记录模板的用途、特点等\n"
                             "帮助理解和使用模板")
        desc_layout.addWidget(desc_label)

        self.description_edit = QTextEdit()
        self.description_edit.setReadOnly(True)
        self.description_edit.setMaximumHeight(50)  # 增加高度
        self.description_edit.setMinimumHeight(50)
        self.description_edit.setToolTip("显示模板的详细描述\n"
                                        "创建模板时可以添加说明\n"
                                        "帮助识别模板的具体用途")
        self.description_edit.setStyleSheet("""
            QTextEdit {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                padding: 6px;
                background-color: #f8f9fa;
                font-size: 11px;
            }
        """)
        desc_layout.addWidget(self.description_edit)
        layout.addLayout(desc_layout)

        parent_layout.addWidget(group)
    
    def _load_templates(self) -> None:
        """加载模板"""
        try:
            print("🔄 开始加载模板...")

            # 从ConfigManager加载模板
            saved_templates = self.config_manager.load_templates()
            print(f"📋 从配置加载的模板数量: {len(saved_templates) if saved_templates else 0}")

            if saved_templates and len(saved_templates) > 0:
                self.templates = saved_templates
                print(f"✅ 使用已保存的模板: {len(self.templates)} 个")
            else:
                # 如果没有保存的模板，使用默认模板列表
                print("📝 创建默认模板列表...")
                self.templates = [
                    {
                        'name': '示例模板1',
                        'path': '',
                        'category': '通用',
                        'description': '这是一个示例模板，用于演示模板功能'
                    },
                    {
                        'name': '示例模板2',
                        'path': '',
                        'category': '按钮',
                        'description': '这是另一个示例模板，用于按钮识别'
                    },
                    {
                        'name': '示例模板3',
                        'path': '',
                        'category': '图标',
                        'description': '用于图标识别的示例模板'
                    }
                ]
                print(f"✅ 创建了 {len(self.templates)} 个默认模板")

                # 保存默认模板到配置
                try:
                    self.config_manager.save_templates(self.templates)
                    print("💾 默认模板已保存到配置文件")
                except Exception as save_error:
                    print(f"⚠️ 保存默认模板失败: {save_error}")

            # 更新列表
            print("🔄 更新模板列表UI...")
            self._update_template_list()

            self.logger.info(f"加载了 {len(self.templates)} 个模板")
            print(f"✅ 模板加载完成: {len(self.templates)} 个模板")

        except Exception as e:
            self.logger.error(f"加载模板失败: {e}")
            print(f"❌ 加载模板失败: {e}")

            # 即使出错也要提供基本模板
            self.templates = [
                {
                    'name': '错误恢复模板',
                    'path': '',
                    'category': '系统',
                    'description': '模板加载失败时的备用模板'
                }
            ]
            self._update_template_list()
            print(f"🔧 使用错误恢复模板: {len(self.templates)} 个")
    
    def _update_template_list(self) -> None:
        """更新模板列表"""
        print(f"🔄 更新模板列表UI，模板数量: {len(self.templates)}")

        # 清空现有列表
        self.template_list.clear()

        if not self.templates:
            print("⚠️ 模板列表为空")
            # 添加一个提示项
            item = QListWidgetItem("暂无模板，请点击'添加模板'按钮添加")
            item.setData(Qt.ItemDataRole.UserRole, None)
            self.template_list.addItem(item)
        else:
            # 添加模板到列表
            for i, template in enumerate(self.templates):
                template_name = template.get('name', f'未命名模板{i+1}')
                template_category = template.get('category', '未分类')

                # 创建显示文本
                display_text = f"[{template_category}] {template_name}"

                item = QListWidgetItem(display_text)
                item.setData(Qt.ItemDataRole.UserRole, template)

                # 设置工具提示
                tooltip = f"名称: {template_name}\n"
                tooltip += f"类别: {template_category}\n"
                tooltip += f"描述: {template.get('description', '无描述')}\n"
                if template.get('path'):
                    tooltip += f"路径: {template.get('path')}"
                else:
                    tooltip += "路径: 未设置"
                item.setToolTip(tooltip)

                self.template_list.addItem(item)
                print(f"  ✅ 添加模板: {display_text}")

        # 更新计数标签
        count_text = f"模板数量: {len(self.templates)}"
        self.template_count_label.setText(count_text)
        print(f"📊 {count_text}")

    def _save_templates_to_config(self):
        """保存模板列表到配置文件"""
        try:
            # 创建可序列化的模板数据
            serializable_templates = []
            for template in self.templates:
                # 排除不能序列化的数据（如numpy数组）
                serializable_template = {}
                for key, value in template.items():
                    if key != 'image':  # 排除图像数据
                        serializable_template[key] = value
                serializable_templates.append(serializable_template)

            # 保存到配置文件
            templates_dir = Path("templates")
            templates_dir.mkdir(exist_ok=True)

            config_file = templates_dir / "templates.json"
            with open(config_file, 'w', encoding='utf-8') as f:
                import json
                json.dump(serializable_templates, f, ensure_ascii=False, indent=2)

            self.logger.info(f"模板配置已保存到: {config_file}")

        except Exception as e:
            self.logger.error(f"保存模板配置失败: {e}")

        # 强制刷新UI
        self.template_list.update()
        self.template_list.repaint()

        print("✅ 模板列表UI更新完成")
    
    def _on_template_selected(self, item: QListWidgetItem) -> None:
        """模板选择事件"""
        template_data = item.data(Qt.ItemDataRole.UserRole)
        if template_data:
            self.current_template = template_data
            self._update_template_info(template_data)
            self._update_template_preview(template_data)

            # 启用编辑和删除按钮
            self.edit_button.setEnabled(True)
            self.delete_button.setEnabled(True)

            # 发送选择信号
            self.template_selected.emit(template_data)

            # 记录选择日志
            self.logger.info(f"选择模板: {template_data.get('name', 'unknown')}")

    def _update_template_preview(self, template_data: Dict[str, Any]) -> None:
        """更新模板预览"""
        try:
            # 存储当前预览的模板
            self._current_template_for_preview = template_data
            # 尝试加载模板图像
            template_image = self._load_template_image_for_preview(template_data)

            if template_image is not None:
                # 转换为QPixmap并缩放
                from PyQt6.QtGui import QPixmap
                import cv2

                # 将OpenCV图像转换为QPixmap
                height, width, channel = template_image.shape
                bytes_per_line = 3 * width
                q_image = QImage(template_image.data, width, height, bytes_per_line, QImage.Format.Format_RGB888).rgbSwapped()
                pixmap = QPixmap.fromImage(q_image)

                # 动态计算预览大小，适应窗口
                available_size = self._get_available_preview_size()
                scaled_pixmap = pixmap.scaled(
                    available_size.width(), available_size.height(),
                    Qt.AspectRatioMode.KeepAspectRatio,
                    Qt.TransformationMode.SmoothTransformation
                )

                self.preview_label.setPixmap(scaled_pixmap)

                # 更新预览信息
                file_path = template_data.get('path', '')
                file_name = Path(file_path).name if file_path else '内存图像'
                self.preview_info.setText(f"尺寸: {width}×{height} | 文件: {file_name}")

                self.logger.debug(f"模板预览已更新: {template_data.get('name', 'unknown')}")

            else:
                # 无法加载图像
                self.preview_label.clear()
                self.preview_label.setText("无法加载图像预览")
                self.preview_info.setText("尺寸: - | 文件: 加载失败")

        except Exception as e:
            self.logger.error(f"更新模板预览失败: {e}")
            self.preview_label.clear()
            self.preview_label.setText("预览加载失败")
            self.preview_info.setText("尺寸: - | 文件: 错误")

    def _get_available_preview_size(self):
        """获取可用的预览尺寸"""
        try:
            from PyQt6.QtCore import QSize

            # 获取预览标签的当前尺寸
            label_size = self.preview_label.size()

            # 如果标签尺寸太小（可能还未显示），使用父容器尺寸
            if label_size.width() < 100 or label_size.height() < 100:
                # 尝试获取父容器尺寸
                parent_size = self.size()
                # 预留空间给其他控件（列表、按钮等）
                available_width = max(200, parent_size.width() // 3)  # 至少200px，最多1/3窗口宽度
                available_height = max(150, parent_size.height() // 2)  # 至少150px，最多1/2窗口高度
            else:
                # 使用标签的实际尺寸，预留边距
                available_width = max(200, label_size.width() - 20)
                available_height = max(150, label_size.height() - 20)

            # 限制最大尺寸，避免过大
            max_width = 600
            max_height = 400

            final_width = min(available_width, max_width)
            final_height = min(available_height, max_height)

            return QSize(final_width, final_height)

        except Exception as e:
            self.logger.error(f"计算预览尺寸失败: {e}")
            # 返回默认尺寸
            from PyQt6.QtCore import QSize
            return QSize(300, 200)

    def resizeEvent(self, event):
        """处理窗口大小变化事件"""
        super().resizeEvent(event)

        # 如果有当前预览的模板，重新渲染以适应新尺寸
        if self._current_template_for_preview is not None:
            try:
                self._update_template_preview(self._current_template_for_preview)
            except Exception as e:
                self.logger.debug(f"窗口大小变化时更新预览失败: {e}")

    def _load_template_image_for_preview(self, template_data: Dict[str, Any]):
        """为预览加载模板图像 - 修复中文文件名支持"""
        try:
            import cv2
            import numpy as np
            from pathlib import Path

            # 首先尝试从内存加载
            if 'image' in template_data:
                return template_data['image']

            # 然后尝试从文件加载
            template_path = template_data.get('path', '')
            if template_path and Path(template_path).exists():
                # 使用字节解码方式解决中文文件名问题
                image = self._load_image_with_chinese_support(template_path)
                if image is not None:
                    self.logger.debug(f"从文件加载模板图像: {template_path}")
                    return image

            # 如果路径是相对路径，尝试从项目根目录加载
            if template_path:
                full_path = Path(template_path)
                if not full_path.is_absolute():
                    # 尝试相对于项目根目录
                    full_path = Path.cwd() / template_path
                    if full_path.exists():
                        image = self._load_image_with_chinese_support(str(full_path))
                        if image is not None:
                            self.logger.debug(f"从完整路径加载模板图像: {full_path}")
                            return image

            # 尝试从templates目录加载（基于模板名称）
            template_name = template_data.get('name', '')
            if template_name:
                # 尝试多种可能的文件扩展名
                templates_dir = Path('templates')
                templates_dir.mkdir(exist_ok=True)

                for ext in ['.png', '.jpg', '.jpeg', '.bmp']:
                    template_file = templates_dir / f"{template_name}{ext}"
                    if template_file.exists():
                        image = self._load_image_with_chinese_support(str(template_file))
                        if image is not None:
                            self.logger.debug(f"从templates目录加载模板图像: {template_file}")
                            return image

            # 如果有region信息，尝试从最近的截图重建
            region = template_data.get('region', [])
            if len(region) == 4:
                self.logger.debug(f"模板 {template_name} 没有图像文件，但有区域信息: {region}")
                # 这里可以添加从截图重建的逻辑

            return None

        except Exception as e:
            self.logger.error(f"加载模板图像失败: {e}")
            return None

    def _load_image_with_chinese_support(self, file_path: str):
        """支持中文文件名的图像加载函数"""
        try:
            import cv2
            import numpy as np
            from pathlib import Path

            # 首先尝试标准的cv2.imread
            image = cv2.imread(file_path)
            if image is not None:
                return image

            # 如果标准方法失败，使用字节解码方式（解决中文文件名问题）
            path_obj = Path(file_path)
            if path_obj.exists():
                try:
                    # 读取文件为字节数据
                    with open(file_path, 'rb') as f:
                        data = f.read()

                    # 将字节数据转换为numpy数组
                    nparr = np.frombuffer(data, np.uint8)

                    # 使用cv2.imdecode解码图像
                    image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

                    if image is not None:
                        self.logger.debug(f"使用字节解码成功加载中文文件名图像: {file_path}")
                        return image

                except Exception as decode_error:
                    self.logger.warning(f"字节解码加载图像失败: {decode_error}")

            return None

        except Exception as e:
            self.logger.error(f"加载图像时发生异常: {e}")
            return None

    def _update_template_info(self, template_data: Dict[str, Any]) -> None:
        """更新模板信息显示"""
        self.name_edit.setText(template_data.get('name', ''))
        
        # 设置类别
        category = template_data.get('category', '通用')
        index = self.category_combo.findText(category)
        if index >= 0:
            self.category_combo.setCurrentIndex(index)
        
        self.description_edit.setText(template_data.get('description', ''))
    
    def _add_template(self) -> None:
        """添加模板"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择模板图片", "", "图片文件 (*.png *.jpg *.jpeg *.bmp);;所有文件 (*)"
        )
        
        if file_path:
            try:
                # 创建新模板
                template_data = {
                    'name': f"模板_{len(self.templates) + 1}",
                    'path': file_path,
                    'category': '通用',
                    'description': '新添加的模板'
                }
                
                # 添加到列表
                self.templates.append(template_data)
                self._update_template_list()
                
                # 保存到配置
                self.config_manager.save_templates(self.templates)
                
                self.logger.info(f"添加模板: {template_data['name']}")
                self.template_changed.emit()
                
            except Exception as e:
                QMessageBox.warning(self, "错误", f"添加模板失败: {e}")
                self.logger.error(f"添加模板失败: {e}")
    
    def _capture_template(self) -> None:
        """截取模板 - 使用模板捕获对话框"""
        try:
            # 使用模板捕获对话框
            from ..template_capture_dialog_v2 import TemplateCaptureDialog

            dialog = TemplateCaptureDialog(self)

            # 连接模板创建信号
            dialog.template_created.connect(self._on_template_created)

            # 显示对话框
            if dialog.exec() == QDialog.DialogCode.Accepted:
                self.logger.info("模板捕获对话框已完成")
            else:
                self.logger.debug("模板捕获对话框被取消")

        except Exception as e:
            self.logger.error(f"截取模板失败: {e}")
            QMessageBox.warning(self, "错误", f"截取模板失败: {e}")

    def _on_template_created(self, template_data: dict):
        """处理模板创建完成事件"""
        try:
            self.logger.info(f"收到模板创建信号: {template_data.get('name', '未知')}")

            # 添加到模板列表
            self.templates.append(template_data)

            # 立即刷新UI列表
            self._update_template_list()

            # 保存到配置文件
            self._save_templates_to_config()

            # 选择新创建的模板
            if self.templates:
                last_index = len(self.templates) - 1
                if last_index < self.template_list.count():
                    self.template_list.setCurrentRow(last_index)
                    self._on_template_selected()

            self.logger.info(f"模板 '{template_data.get('name', '未知')}' 已成功添加到列表")
            QMessageBox.information(self, "成功", f"模板 '{template_data.get('name', '未知')}' 创建成功！")

        except Exception as e:
            self.logger.error(f"处理模板创建事件失败: {e}")
            QMessageBox.warning(self, "错误", f"处理模板创建失败: {e}")

    def _on_template_screenshot_completed(self, screenshot_data: dict):
        """处理模板截图完成事件"""
        try:
            # 这里可以添加额外的模板截图完成处理逻辑
            self.logger.info("模板截图已完成，等待用户创建模板")

        except Exception as e:
            self.logger.error(f"处理模板截图完成事件失败: {e}")

    def _on_template_screenshot_failed(self, error_msg: str):
        """处理模板截图失败事件"""
        self.logger.error(f"模板截图失败: {error_msg}")
        QMessageBox.warning(self, "错误", f"模板截图失败: {error_msg}")

    def _on_template_captured(self, template_data: Dict[str, Any]) -> None:
        """处理模板截取完成事件"""
        try:
            # 保存模板图像到文件
            template_image_path = self._save_template_image(template_data)
            if template_image_path:
                # 更新模板数据中的路径
                template_data['path'] = template_image_path
                self.logger.info(f"模板图像已保存: {template_image_path}")

            # 添加到模板列表
            self.templates.append(template_data)
            self._update_template_list()

            # 保存到配置（排除不能序列化的数据）
            try:
                # 创建可序列化的模板数据副本
                serializable_templates = []
                for template in self.templates:
                    serializable_template = {
                        'name': template.get('name', ''),
                        'category': template.get('category', ''),
                        'description': template.get('description', ''),
                        'path': template.get('path', ''),
                        'region': template.get('region', []),
                        'created_time': template.get('created_time', 0)
                        # 排除 'image' 字段，因为numpy数组不能JSON序列化
                    }
                    serializable_templates.append(serializable_template)

                self.config_manager.save_templates(serializable_templates)
            except Exception as e:
                self.logger.warning(f"保存模板配置失败: {e}")

            self.logger.info(f"模板创建成功: {template_data.get('name', 'Unknown')}")
            self.template_changed.emit()

            # 显示成功消息
            QMessageBox.information(self, "成功", f"模板 '{template_data.get('name', 'Unknown')}' 已成功创建！")

        except Exception as e:
            self.logger.error(f"处理截取的模板失败: {e}")
            QMessageBox.warning(self, "错误", f"处理截取的模板失败: {e}")

    def _save_template_image(self, template_data: Dict[str, Any]) -> str:
        """保存模板图像到文件

        Args:
            template_data: 模板数据，包含图像和名称

        Returns:
            保存的文件路径，失败时返回空字符串
        """
        try:
            import cv2
            from datetime import datetime

            # 获取模板图像
            template_image = template_data.get('image')
            if template_image is None:
                self.logger.warning("模板数据中没有图像")
                return ""

            # 创建templates目录
            templates_dir = Path('templates')
            templates_dir.mkdir(exist_ok=True)

            # 生成文件名
            template_name = template_data.get('name', 'template')
            # 清理文件名，移除不安全字符
            safe_name = "".join(c for c in template_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
            if not safe_name:
                safe_name = 'template'

            # 添加时间戳避免重名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{safe_name}_{timestamp}.png"
            file_path = templates_dir / filename

            # 保存图像
            success = cv2.imwrite(str(file_path), template_image)
            if success:
                self.logger.info(f"模板图像保存成功: {file_path}")
                return str(file_path)
            else:
                self.logger.error(f"模板图像保存失败: {file_path}")
                return ""

        except Exception as e:
            self.logger.error(f"保存模板图像时发生错误: {e}")
            return ""

    def _edit_template(self) -> None:
        """编辑模板"""
        if self.current_template:
            QMessageBox.information(self, "提示", "编辑模板功能正在开发中...")
            self.logger.info(f"编辑模板: {self.current_template.get('name', 'Unknown')}")
    
    def _delete_template(self) -> None:
        """删除模板"""
        if self.current_template:
            reply = QMessageBox.question(
                self, "确认删除", 
                f"确定要删除模板 '{self.current_template.get('name', 'Unknown')}' 吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                try:
                    # 从列表中移除
                    self.templates.remove(self.current_template)
                    self._update_template_list()
                    
                    # 清空信息显示
                    self.name_edit.clear()
                    self.description_edit.clear()
                    self.category_combo.setCurrentIndex(0)
                    
                    # 禁用按钮
                    self.edit_button.setEnabled(False)
                    self.delete_button.setEnabled(False)
                    
                    # 保存到配置
                    self.config_manager.save_templates(self.templates)
                    
                    self.logger.info(f"删除模板: {self.current_template.get('name', 'Unknown')}")
                    self.current_template = None
                    self.template_changed.emit()
                    
                except Exception as e:
                    QMessageBox.warning(self, "错误", f"删除模板失败: {e}")
                    self.logger.error(f"删除模板失败: {e}")
    
    def get_selected_template(self) -> Optional[Dict[str, Any]]:
        """获取选中的模板"""
        return self.current_template
    
    def refresh_templates(self) -> None:
        """刷新模板列表"""
        self._load_templates()
