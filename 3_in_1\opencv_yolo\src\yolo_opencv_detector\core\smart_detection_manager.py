#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能检测管理器
实现智能去重、自动保存、性能优化等功能
作者: Cursor AI
创建时间: 2025-07-05
编码标准: UTF-8无BOM
"""

import os
import time
import hashlib
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
from dataclasses import dataclass
from datetime import datetime
import sys

from ..utils.logger import Logger

# 导入中文渲染器（使用相对导入）
try:
    from ..utils.chinese_text_renderer import put_chinese_text
    CHINESE_RENDERER_AVAILABLE = True
except ImportError:
    CHINESE_RENDERER_AVAILABLE = False


@dataclass
class DetectionResult:
    """检测结果数据类"""
    bbox: Tuple[int, int, int, int]  # x, y, w, h
    confidence: float
    class_id: int
    class_name: str
    timestamp: float
    
    def center(self) -> <PERSON><PERSON>[int, int]:
        """获取边界框中心点"""
        x, y, w, h = self.bbox
        return (x + w // 2, y + h // 2)
    
    def area(self) -> int:
        """获取边界框面积"""
        _, _, w, h = self.bbox
        return w * h


@dataclass
class DetectionSession:
    """检测会话统计"""
    start_time: float
    end_time: Optional[float] = None
    total_detections: int = 0
    saved_screenshots: int = 0
    target_types: Dict[str, int] = None
    
    def __post_init__(self):
        if self.target_types is None:
            self.target_types = {}
    
    @property
    def duration(self) -> float:
        """获取会话持续时间（秒）"""
        end = self.end_time or time.time()
        return end - self.start_time


class SmartDetectionManager:
    """智能检测管理器"""

    def __init__(self, save_directory: str = "screenshots"):
        self.logger = Logger()

        # 保存配置
        self.save_directory = Path(save_directory)
        self.save_directory.mkdir(exist_ok=True)

        # 去重配置 - 调整为更合理的阈值
        self.position_threshold = 30  # 位置变化阈值（像素）- 减小以提高敏感度
        self.confidence_threshold = 0.15  # 置信度变化阈值 - 增大以减少噪音
        self.time_threshold = 5.0  # 时间间隔阈值（秒）- 增大以减少频繁保存

        # 模板匹配专用去重配置
        self.template_confidence_threshold = 0.02  # 模板匹配置信度阈值（更小）
        self.template_time_threshold = 10.0  # 模板匹配时间阈值（更大）

        # 模板匹配配置
        self.template_matching_enabled = False  # 是否启用模板匹配
        self.selected_template = None  # 当前选定的模板
        self.template_matcher = None  # 模板匹配器

        # 历史记录
        self.previous_detections: List[DetectionResult] = []
        self.detection_history: List[List[DetectionResult]] = []

        # 会话统计
        self.current_session: Optional[DetectionSession] = None

        # 性能优化
        self.max_history_size = 100
        self.cleanup_interval = 50  # 每50次检测清理一次历史
        self.detection_count = 0

        self.logger.info(f"智能检测管理器初始化完成，保存目录: {self.save_directory}")

    def set_template_matching(self, enabled: bool, template_data: Dict[str, Any] = None):
        """设置模板匹配

        Args:
            enabled: 是否启用模板匹配
            template_data: 模板数据，包含模板图像和匹配参数
        """
        self.template_matching_enabled = enabled

        if enabled and template_data:
            self.selected_template = template_data
            self.logger.info(f"模板匹配已启用，模板: {template_data.get('name', 'unknown')}")
        else:
            self.selected_template = None
            self.logger.info("模板匹配已禁用")

    def _perform_template_matching(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """执行模板匹配

        Args:
            image: 输入图像

        Returns:
            模板匹配结果列表
        """
        if not self.template_matching_enabled or not self.selected_template:
            return []

        try:
            import cv2

            template_image = self.selected_template.get('image')
            if template_image is None:
                return []

            # 执行模板匹配
            result = cv2.matchTemplate(image, template_image, cv2.TM_CCOEFF_NORMED)

            # 获取匹配阈值
            threshold = self.selected_template.get('threshold', 0.8)

            # 找到所有匹配位置
            locations = np.where(result >= threshold)

            matches = []
            h, w = template_image.shape[:2]

            for pt in zip(*locations[::-1]):  # 切换x和y坐标
                matches.append({
                    'bbox': [pt[0], pt[1], w, h],
                    'confidence': float(result[pt[1], pt[0]]),
                    'class_name': self.selected_template.get('name', 'template_match'),
                    'class_id': -1  # 模板匹配使用特殊ID
                })

            self.logger.debug(f"模板匹配找到 {len(matches)} 个匹配")
            return matches

        except Exception as e:
            self.logger.error(f"模板匹配失败: {e}")
            return []

    def start_session(self):
        """开始检测会话"""
        self.current_session = DetectionSession(start_time=time.time())
        self.previous_detections.clear()
        self.detection_history.clear()
        self.detection_count = 0
        self.logger.info("检测会话已开始")
    
    def end_session(self) -> DetectionSession:
        """结束检测会话"""
        if self.current_session:
            self.current_session.end_time = time.time()
            session = self.current_session
            self.current_session = None
            self.logger.info(f"检测会话已结束，持续时间: {session.duration:.1f}秒")
            return session
        return None
    
    def process_detections(self, image: np.ndarray, detections: List[Dict[str, Any]]) -> Dict[str, Any]:
        """处理检测结果

        Args:
            image: 原始图像
            detections: 检测结果列表

        Returns:
            处理结果字典，包含是否保存、去重信息等
        """
        self.detection_count += 1
        current_time = time.time()

        # 如果启用了模板匹配，执行模板匹配
        template_matches = []
        if self.template_matching_enabled:
            template_matches = self._perform_template_matching(image)

        # 决定使用哪种检测结果
        if self.template_matching_enabled:
            # 模板匹配模式：只使用模板匹配结果
            detection_source = template_matches
            self.logger.debug(f"使用模板匹配结果: {len(template_matches)} 个匹配")
        else:
            # YOLO检测模式：使用YOLO检测结果
            detection_source = detections
            self.logger.debug(f"使用YOLO检测结果: {len(detections)} 个检测")

        # 转换检测结果格式
        current_detections = []
        for det in detection_source:
            try:
                bbox = det.get("bbox", [])
                if len(bbox) == 4:
                    result = DetectionResult(
                        bbox=tuple(bbox),
                        confidence=det.get("confidence", 0.0),
                        class_id=det.get("class_id", 0),
                        class_name=det.get("class_name", "unknown"),
                        timestamp=current_time
                    )
                    current_detections.append(result)
            except Exception as e:
                self.logger.warning(f"解析检测结果失败: {e}")
        
        # 更新会话统计
        if self.current_session:
            self.current_session.total_detections += len(current_detections)
            for det in current_detections:
                class_name = det.class_name
                self.current_session.target_types[class_name] = \
                    self.current_session.target_types.get(class_name, 0) + 1
        
        # 处理结果
        result = {
            "should_save": False,
            "should_overlay": len(current_detections) > 0,
            "is_duplicate": False,
            "new_targets": [],
            "changed_targets": [],
            "removed_targets": [],
            "save_path": None,
            "detections": current_detections,
            "detection_mode": "template_matching" if self.template_matching_enabled else "yolo_detection"
        }
        
        if not current_detections:
            # 没有检测到目标，清理临时数据
            self._cleanup_temp_data()
            self.previous_detections.clear()
            return result
        
        # 检查是否需要保存
        should_save, analysis = self._should_save_detection(current_detections)
        result.update(analysis)
        result["should_save"] = should_save
        
        if should_save:
            # 保存截图
            save_path = self._save_screenshot(image, current_detections)
            result["save_path"] = save_path
            
            if self.current_session:
                self.current_session.saved_screenshots += 1
        
        # 更新历史记录
        self.previous_detections = current_detections.copy()
        self.detection_history.append(current_detections.copy())
        
        # 定期清理历史记录
        if self.detection_count % self.cleanup_interval == 0:
            self._cleanup_history()
        
        return result
    
    def _should_save_detection(self, current_detections: List[DetectionResult]) -> Tuple[bool, Dict[str, Any]]:
        """判断是否应该保存当前检测结果"""
        analysis = {
            "is_duplicate": False,
            "new_targets": [],
            "changed_targets": [],
            "removed_targets": []
        }
        
        if not self.previous_detections:
            # 第一次检测，直接保存
            analysis["new_targets"] = current_detections.copy()
            return True, analysis
        
        # 分析变化
        current_centers = {i: det.center() for i, det in enumerate(current_detections)}
        previous_centers = {i: det.center() for i, det in enumerate(self.previous_detections)}
        
        # 检查新目标和变化的目标
        for i, current_det in enumerate(current_detections):
            current_center = current_centers[i]
            
            # 查找最相似的历史目标
            best_match = None
            min_distance = float('inf')
            
            for j, prev_det in enumerate(self.previous_detections):
                if prev_det.class_name == current_det.class_name:
                    prev_center = previous_centers[j]
                    distance = self._calculate_distance(current_center, prev_center)
                    
                    if distance < min_distance:
                        min_distance = distance
                        best_match = prev_det
            
            # 判断是否为新目标或变化的目标
            if best_match is None:
                # 新目标
                analysis["new_targets"].append(current_det)
            elif min_distance > self.position_threshold:
                # 位置变化显著
                analysis["changed_targets"].append(current_det)
            elif self._is_confidence_change_significant(current_det, best_match):
                # 置信度变化显著
                analysis["changed_targets"].append(current_det)
            elif self._is_time_interval_sufficient(current_det, best_match):
                # 时间间隔足够长
                analysis["changed_targets"].append(current_det)
            else:
                # 目标基本相同，不需要保存
                pass
        
        # 检查消失的目标
        for prev_det in self.previous_detections:
            found = False
            for current_det in current_detections:
                if (current_det.class_name == prev_det.class_name and
                    self._calculate_distance(current_det.center(), prev_det.center()) <= self.position_threshold):
                    found = True
                    break
            
            if not found:
                analysis["removed_targets"].append(prev_det)
        
        # 决定是否保存
        should_save = (
            len(analysis["new_targets"]) > 0 or
            len(analysis["changed_targets"]) > 0 or
            len(analysis["removed_targets"]) > 0
        )

        analysis["is_duplicate"] = not should_save

        # 添加详细的去重日志
        if should_save:
            reasons = []
            if analysis["new_targets"]:
                reasons.append(f"{len(analysis['new_targets'])}个新目标")
            if analysis["changed_targets"]:
                reasons.append(f"{len(analysis['changed_targets'])}个变化目标")
            if analysis["removed_targets"]:
                reasons.append(f"{len(analysis['removed_targets'])}个消失目标")
            self.logger.debug(f"保存截图 - 原因: {', '.join(reasons)}")
        else:
            self.logger.debug(f"跳过保存 - 检测到 {len(current_detections)} 个重复目标")

        return should_save, analysis
    
    def _calculate_distance(self, point1: Tuple[int, int], point2: Tuple[int, int]) -> float:
        """计算两点之间的欧几里得距离"""
        x1, y1 = point1
        x2, y2 = point2
        return ((x1 - x2) ** 2 + (y1 - y2) ** 2) ** 0.5

    def _is_confidence_change_significant(self, current_det: 'DetectionResult', best_match: 'DetectionResult') -> bool:
        """判断置信度变化是否显著"""
        if self.template_matching_enabled:
            # 模板匹配模式使用更小的阈值
            threshold = self.template_confidence_threshold
        else:
            # YOLO检测模式使用标准阈值
            threshold = self.confidence_threshold

        return abs(current_det.confidence - best_match.confidence) > threshold

    def _is_time_interval_sufficient(self, current_det: 'DetectionResult', best_match: 'DetectionResult') -> bool:
        """判断时间间隔是否足够长"""
        if self.template_matching_enabled:
            # 模板匹配模式使用更长的时间间隔
            threshold = self.template_time_threshold
        else:
            # YOLO检测模式使用标准时间间隔
            threshold = self.time_threshold

        return current_det.timestamp - best_match.timestamp > threshold
    
    def _save_screenshot(self, image: np.ndarray, detections: List[DetectionResult]) -> str:
        """保存带标注的截图"""
        try:
            import cv2
            
            # 创建图像副本
            annotated_image = image.copy()
            
            # 绘制检测结果
            for det in detections:
                x, y, w, h = det.bbox

                # 绘制边界框
                cv2.rectangle(annotated_image, (x, y), (x + w, y + h), (0, 255, 0), 2)

                # 绘制标签
                label = f"{det.class_name}: {det.confidence:.2f}"
                label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]

                # 计算标签位置 - 避免遮挡目标
                if y - label_size[1] - 15 > 0:
                    # 上方有足够空间
                    label_y = y - 10
                    bg_y1 = y - label_size[1] - 15
                    bg_y2 = y - 5
                else:
                    # 放在下方
                    label_y = y + h + label_size[1] + 10
                    bg_y1 = y + h + 5
                    bg_y2 = y + h + label_size[1] + 15

                # 使用中文渲染器绘制标签文字
                if CHINESE_RENDERER_AVAILABLE:
                    # 使用中文渲染器，支持中文字符
                    annotated_image = put_chinese_text(
                        annotated_image,
                        label,
                        (x + 5, label_y - 20),  # 调整位置以适应PIL渲染
                        font_size=16,
                        color=(0, 255, 0),  # 绿色
                        background=True
                    )
                else:
                    # 回退到OpenCV原生渲染（可能显示问号）
                    # 绘制标签背景（半透明）
                    overlay = annotated_image.copy()
                    cv2.rectangle(overlay, (x, bg_y1), (x + label_size[0] + 10, bg_y2), (0, 255, 0), -1)
                    cv2.addWeighted(overlay, 0.7, annotated_image, 0.3, 0, annotated_image)

                    # 绘制标签文字
                    cv2.putText(annotated_image, label, (x + 5, label_y),
                              cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)

                # 绘制指向箭头（如果标签不在目标上方）
                if y - label_size[1] - 15 <= 0:
                    # 从标签指向目标的箭头
                    arrow_start = (x + label_size[0] // 2, bg_y1)
                    arrow_end = (x + w // 2, y)
                    cv2.arrowedLine(annotated_image, arrow_start, arrow_end, (0, 255, 0), 2, tipLength=0.3)
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]

            # 修复文件名生成 - 正确处理类名
            if detections:
                # 获取所有唯一的类名，并限制长度
                unique_classes = list(set(det.class_name for det in detections))
                # 限制类名长度，避免文件名过长
                target_types = "_".join(cls[:10] for cls in unique_classes[:3])  # 最多3个类，每个最多10字符
                if not target_types:  # 如果为空，使用默认名称
                    target_types = "unknown"
            else:
                target_types = "empty"

            filename = f"detection_{timestamp}_{target_types}.png"
            
            save_path = self.save_directory / filename
            
            # 保存图像
            cv2.imwrite(str(save_path), annotated_image)
            
            self.logger.info(f"截图已保存: {save_path}")
            return str(save_path)
            
        except Exception as e:
            self.logger.error(f"保存截图失败: {e}")
            return None
    
    def _cleanup_temp_data(self):
        """清理临时数据"""
        # 这里可以添加清理临时文件的逻辑
        pass
    
    def _cleanup_history(self):
        """清理历史记录"""
        if len(self.detection_history) > self.max_history_size:
            # 保留最近的记录
            self.detection_history = self.detection_history[-self.max_history_size:]
            self.logger.debug("历史记录已清理")
    
    def get_session_statistics(self) -> Dict[str, Any]:
        """获取会话统计信息"""
        if not self.current_session:
            return {}
        
        return {
            "duration": int(self.current_session.duration),
            "total_detections": self.current_session.total_detections,
            "saved_screenshots": self.current_session.saved_screenshots,
            "target_types": self.current_session.target_types.copy(),
            "detection_rate": self.current_session.total_detections / max(self.current_session.duration, 1)
        }
    
    def set_thresholds(self, position_threshold: int = None, 
                      confidence_threshold: float = None,
                      time_threshold: float = None):
        """设置去重阈值"""
        if position_threshold is not None:
            self.position_threshold = position_threshold
        if confidence_threshold is not None:
            self.confidence_threshold = confidence_threshold
        if time_threshold is not None:
            self.time_threshold = time_threshold
        
        self.logger.info(f"去重阈值已更新: 位置={self.position_threshold}px, "
                        f"置信度={self.confidence_threshold}, 时间={self.time_threshold}s")
