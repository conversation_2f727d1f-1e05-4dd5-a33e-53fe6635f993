#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
性能自适应系统
监控操作性能并根据响应时间自动调整优化级别
"""

import logging
import time
import threading
import psutil
import gc
from typing import Dict, Any, Callable, Optional, List
from enum import Enum
from dataclasses import dataclass
from collections import deque
import statistics

class OptimizationLevel(Enum):
    """优化级别枚举"""
    NORMAL = "normal"
    AGGRESSIVE = "aggressive"
    MAXIMUM = "maximum"

@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    operation_name: str
    duration: float
    memory_before: float
    memory_after: float
    memory_change: float
    success: bool
    timestamp: float
    optimization_level: OptimizationLevel

class PerformanceAdaptiveSystem:
    """性能自适应系统"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.current_level = OptimizationLevel.NORMAL
        self.metrics_history = {}  # 操作名 -> deque of PerformanceMetrics
        self.max_history_size = 50
        self.lock = threading.Lock()
        
        # 性能阈值配置
        self.thresholds = {
            'slow_operation': 2.0,      # 2秒以上认为是慢操作
            'very_slow_operation': 5.0,  # 5秒以上认为是非常慢的操作
            'fast_operation': 0.5,       # 0.5秒以下认为是快操作
            'memory_warning': 100 * 1024 * 1024,  # 100MB内存增长警告
            'memory_critical': 500 * 1024 * 1024,  # 500MB内存增长严重警告
        }
        
        # 优化配置
        self.optimization_configs = {
            OptimizationLevel.NORMAL: {
                'max_tree_depth': 10,
                'max_children_per_level': 100,
                'cache_timeout': 30,
                'throttle_interval': 0.1,
                'connection_timeout': 5,
                'enable_detailed_logging': True,
                'gc_frequency': 300,  # 5分钟
                'use_threading': True
            },
            OptimizationLevel.AGGRESSIVE: {
                'max_tree_depth': 5,
                'max_children_per_level': 50,
                'cache_timeout': 60,
                'throttle_interval': 0.2,
                'connection_timeout': 3,
                'enable_detailed_logging': False,
                'gc_frequency': 180,  # 3分钟
                'use_threading': True
            },
            OptimizationLevel.MAXIMUM: {
                'max_tree_depth': 3,
                'max_children_per_level': 20,
                'cache_timeout': 120,
                'throttle_interval': 0.5,
                'connection_timeout': 2,
                'enable_detailed_logging': False,
                'gc_frequency': 60,   # 1分钟
                'use_threading': False
            }
        }
        
        # 启动性能监控线程
        self.monitoring_active = True
        self.monitor_thread = threading.Thread(target=self._performance_monitor_loop, daemon=True)
        self.monitor_thread.start()
        
        self.logger.info("Performance adaptive system initialized")
    
    def monitor_operation(self, operation_name: str, func: Callable, *args, **kwargs) -> Any:
        """
        监控操作性能
        
        Args:
            operation_name: 操作名称
            func: 要执行的函数
            *args, **kwargs: 函数参数
            
        Returns:
            函数执行结果
        """
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss
        
        try:
            # 执行操作
            result = func(*args, **kwargs)
            success = True
            
        except Exception as e:
            self.logger.error(f"Operation {operation_name} failed: {e}")
            result = None
            success = False
            raise
            
        finally:
            # 记录性能指标
            end_time = time.time()
            end_memory = psutil.Process().memory_info().rss
            
            metrics = PerformanceMetrics(
                operation_name=operation_name,
                duration=end_time - start_time,
                memory_before=start_memory / 1024 / 1024,  # MB
                memory_after=end_memory / 1024 / 1024,     # MB
                memory_change=(end_memory - start_memory) / 1024 / 1024,  # MB
                success=success,
                timestamp=time.time(),
                optimization_level=self.current_level
            )
            
            self._record_metrics(metrics)
            self._analyze_and_adapt(metrics)
        
        return result
    
    def _record_metrics(self, metrics: PerformanceMetrics):
        """记录性能指标"""
        with self.lock:
            if metrics.operation_name not in self.metrics_history:
                self.metrics_history[metrics.operation_name] = deque(maxlen=self.max_history_size)
            
            self.metrics_history[metrics.operation_name].append(metrics)
            
            # 记录日志
            if self.get_current_config()['enable_detailed_logging']:
                self.logger.debug(
                    f"Performance - {metrics.operation_name}: "
                    f"Time: {metrics.duration:.3f}s, "
                    f"Memory: {metrics.memory_change:+.2f}MB, "
                    f"Level: {metrics.optimization_level.value}"
                )
    
    def _analyze_and_adapt(self, metrics: PerformanceMetrics):
        """分析性能并自适应调整"""
        operation_name = metrics.operation_name
        
        # 获取该操作的历史数据
        with self.lock:
            history = list(self.metrics_history.get(operation_name, []))
        
        if len(history) < 3:  # 需要足够的历史数据
            return
        
        # 计算最近几次操作的平均性能
        recent_metrics = history[-5:]  # 最近5次
        avg_duration = statistics.mean([m.duration for m in recent_metrics])
        avg_memory_change = statistics.mean([m.memory_change for m in recent_metrics])
        
        # 决定是否需要调整优化级别
        should_upgrade = False
        should_downgrade = False
        
        # 检查是否需要升级优化级别
        if (avg_duration > self.thresholds['very_slow_operation'] or 
            avg_memory_change > self.thresholds['memory_critical'] / 1024 / 1024):
            should_upgrade = True
            reason = "Very slow operation or critical memory usage detected"
            
        elif (avg_duration > self.thresholds['slow_operation'] or 
              avg_memory_change > self.thresholds['memory_warning'] / 1024 / 1024):
            if self.current_level == OptimizationLevel.NORMAL:
                should_upgrade = True
                reason = "Slow operation or high memory usage detected"
        
        # 检查是否可以降级优化级别
        elif (avg_duration < self.thresholds['fast_operation'] and 
              avg_memory_change < 10):  # 内存变化小于10MB
            if self.current_level != OptimizationLevel.NORMAL:
                should_downgrade = True
                reason = "Performance is good, can reduce optimization level"
        
        # 执行级别调整
        if should_upgrade:
            self._upgrade_optimization_level(reason)
        elif should_downgrade:
            self._downgrade_optimization_level(reason)
    
    def _upgrade_optimization_level(self, reason: str):
        """升级优化级别"""
        old_level = self.current_level
        
        if self.current_level == OptimizationLevel.NORMAL:
            self.current_level = OptimizationLevel.AGGRESSIVE
        elif self.current_level == OptimizationLevel.AGGRESSIVE:
            self.current_level = OptimizationLevel.MAXIMUM
        
        if old_level != self.current_level:
            self.logger.info(f"Upgraded optimization level: {old_level.value} -> {self.current_level.value}")
            self.logger.info(f"Reason: {reason}")
            self._trigger_immediate_optimization()
    
    def _downgrade_optimization_level(self, reason: str):
        """降级优化级别"""
        old_level = self.current_level
        
        if self.current_level == OptimizationLevel.MAXIMUM:
            self.current_level = OptimizationLevel.AGGRESSIVE
        elif self.current_level == OptimizationLevel.AGGRESSIVE:
            self.current_level = OptimizationLevel.NORMAL
        
        if old_level != self.current_level:
            self.logger.info(f"Downgraded optimization level: {old_level.value} -> {self.current_level.value}")
            self.logger.info(f"Reason: {reason}")
    
    def _trigger_immediate_optimization(self):
        """触发立即优化"""
        try:
            # 强制垃圾回收
            collected = gc.collect()
            self.logger.debug(f"Immediate GC collected {collected} objects")
            
            # 清理缓存（如果有的话）
            # 这里可以添加清理各种缓存的代码
            
        except Exception as e:
            self.logger.error(f"Error during immediate optimization: {e}")
    
    def _performance_monitor_loop(self):
        """性能监控循环"""
        while self.monitoring_active:
            try:
                time.sleep(30)  # 每30秒检查一次
                
                # 检查系统整体性能
                self._check_system_performance()
                
                # 清理过期的性能数据
                self._cleanup_old_metrics()
                
            except Exception as e:
                self.logger.error(f"Error in performance monitor loop: {e}")
    
    def _check_system_performance(self):
        """检查系统整体性能"""
        try:
            process = psutil.Process()
            
            # 检查CPU使用率
            cpu_percent = process.cpu_percent()
            if cpu_percent > 80:  # CPU使用率超过80%
                self.logger.warning(f"High CPU usage detected: {cpu_percent}%")
                if self.current_level == OptimizationLevel.NORMAL:
                    self._upgrade_optimization_level("High CPU usage detected")
            
            # 检查内存使用
            memory_info = process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024
            if memory_mb > 500:  # 内存使用超过500MB
                self.logger.warning(f"High memory usage detected: {memory_mb:.2f}MB")
                if self.current_level == OptimizationLevel.NORMAL:
                    self._upgrade_optimization_level("High memory usage detected")
            
        except Exception as e:
            self.logger.error(f"Error checking system performance: {e}")
    
    def _cleanup_old_metrics(self):
        """清理过期的性能数据"""
        current_time = time.time()
        cutoff_time = current_time - 3600  # 保留1小时内的数据
        
        with self.lock:
            for operation_name, metrics_deque in self.metrics_history.items():
                # 移除过期的指标
                while metrics_deque and metrics_deque[0].timestamp < cutoff_time:
                    metrics_deque.popleft()
    
    def get_current_config(self) -> Dict[str, Any]:
        """获取当前优化配置"""
        return self.optimization_configs[self.current_level].copy()
    
    def get_current_level(self) -> OptimizationLevel:
        """获取当前优化级别"""
        return self.current_level
    
    def force_optimization_level(self, level: OptimizationLevel):
        """强制设置优化级别"""
        old_level = self.current_level
        self.current_level = level
        self.logger.info(f"Forced optimization level change: {old_level.value} -> {level.value}")
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        with self.lock:
            summary = {
                'current_level': self.current_level.value,
                'operations_monitored': len(self.metrics_history),
                'total_metrics': sum(len(deque) for deque in self.metrics_history.values()),
                'operations': {}
            }
            
            for operation_name, metrics_deque in self.metrics_history.items():
                if metrics_deque:
                    recent_metrics = list(metrics_deque)[-10:]  # 最近10次
                    summary['operations'][operation_name] = {
                        'count': len(metrics_deque),
                        'avg_duration': statistics.mean([m.duration for m in recent_metrics]),
                        'avg_memory_change': statistics.mean([m.memory_change for m in recent_metrics]),
                        'success_rate': sum(1 for m in recent_metrics if m.success) / len(recent_metrics)
                    }
        
        return summary
    
    def shutdown(self):
        """关闭性能监控系统"""
        self.monitoring_active = False
        if self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        self.logger.info("Performance adaptive system shutdown")

# 测试函数
def test_performance_adaptive_system():
    """测试性能自适应系统"""
    import random
    
    system = PerformanceAdaptiveSystem()
    
    def slow_operation():
        """模拟慢操作"""
        time.sleep(random.uniform(1, 3))
        # 模拟内存使用
        data = [0] * (random.randint(1000, 10000))
        return len(data)
    
    def fast_operation():
        """模拟快操作"""
        time.sleep(random.uniform(0.1, 0.3))
        return "fast"
    
    print("Testing Performance Adaptive System...")
    print(f"Initial level: {system.get_current_level().value}")
    
    # 执行一些操作
    for i in range(5):
        print(f"\nIteration {i+1}:")
        
        # 随机选择操作类型
        if random.random() < 0.7:  # 70%概率执行慢操作
            result = system.monitor_operation("slow_test", slow_operation)
            print(f"Slow operation result: {result}")
        else:
            result = system.monitor_operation("fast_test", fast_operation)
            print(f"Fast operation result: {result}")
        
        print(f"Current level: {system.get_current_level().value}")
        config = system.get_current_config()
        print(f"Max tree depth: {config['max_tree_depth']}")
        print(f"Throttle interval: {config['throttle_interval']}")
    
    # 显示性能摘要
    print("\nPerformance Summary:")
    summary = system.get_performance_summary()
    for key, value in summary.items():
        print(f"{key}: {value}")
    
    system.shutdown()

if __name__ == "__main__":
    test_performance_adaptive_system()
