# -*- coding: utf-8 -*-
"""
简化的截图功能测试
作者: Cursor AI
创建时间: 2025-07-05
编码标准: UTF-8无BOM
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def test_basic_screenshot():
    """测试基本截图功能"""
    print("=== 测试基本截图功能 ===")
    
    try:
        # 导入截图辅助工具
        from yolo_opencv_detector.utils.screenshot_helper import get_screenshot_helper
        
        # 获取截图辅助工具
        helper = get_screenshot_helper()
        print(f"截图服务可用: {helper.is_available()}")
        print(f"可用方法: {helper.get_available_methods()}")
        
        if helper.is_available():
            # 测试截图
            print("正在截图...")
            image, filepath = helper.take_screenshot(save_to_file=True)
            
            if image is not None:
                print(f"✅ 截图成功: {image.shape}, 文件: {filepath}")
                
                # 测试图像转换
                print("测试图像转换...")
                pixmap = helper.numpy_to_qpixmap(image)
                
                if pixmap and not pixmap.isNull():
                    print(f"✅ 图像转换成功: {pixmap.size()}")
                    return True
                else:
                    print("❌ 图像转换失败")
                    return False
            else:
                print("❌ 截图失败")
                return False
        else:
            print("❌ 截图服务不可用")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_screenshot_preview():
    """测试截图预览功能"""
    print("\n=== 测试截图预览功能 ===")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from yolo_opencv_detector.utils.screenshot_helper import get_screenshot_helper
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 获取截图
        helper = get_screenshot_helper()
        if helper.is_available():
            image, filepath = helper.take_screenshot(save_to_file=True)
            
            if image is not None:
                print(f"截图成功: {image.shape}")
                
                # 测试截图预览对话框
                try:
                    from yolo_opencv_detector.gui.screenshot_preview_dialog import ScreenshotPreviewDialog
                    
                    dialog = ScreenshotPreviewDialog(image, filepath)
                    
                    # 连接信号
                    def on_screenshot_saved(filepath):
                        print(f"截图已保存: {filepath}")
                    
                    def on_template_created(template_data):
                        print(f"模板已创建: {template_data.get('name', 'Unknown')}")
                    
                    def on_region_extracted(image, region):
                        x, y, width, height = region
                        print(f"区域已提取: ({x}, {y}, {width}, {height})")
                    
                    dialog.screenshot_saved.connect(on_screenshot_saved)
                    dialog.template_created.connect(on_template_created)
                    dialog.region_extracted.connect(on_region_extracted)
                    
                    print("截图预览对话框已创建，可以进行测试...")
                    print("提示：在对话框中可以拖拽选择区域，创建模板等")
                    
                    # 显示对话框
                    result = dialog.exec()
                    
                    if result == dialog.DialogCode.Accepted:
                        print("✅ 对话框测试完成")
                        return True
                    else:
                        print("对话框被取消")
                        return False
                        
                except Exception as e:
                    print(f"❌ 截图预览对话框测试失败: {e}")
                    import traceback
                    traceback.print_exc()
                    return False
            else:
                print("❌ 截图失败")
                return False
        else:
            print("❌ 截图服务不可用")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("截图功能简化测试开始...")
    
    # 测试基本截图功能
    basic_result = test_basic_screenshot()
    
    if basic_result:
        print("\n✅ 基本截图功能测试通过")
        
        # 询问是否测试预览功能
        try:
            choice = input("\n是否测试截图预览功能? (y/n): ").lower().strip()
            if choice == 'y':
                preview_result = test_screenshot_preview()
                if preview_result:
                    print("\n✅ 截图预览功能测试通过")
                else:
                    print("\n❌ 截图预览功能测试失败")
        except KeyboardInterrupt:
            print("\n测试被用户中断")
    else:
        print("\n❌ 基本截图功能测试失败")
    
    print("\n截图功能测试完成!")

if __name__ == "__main__":
    main()
